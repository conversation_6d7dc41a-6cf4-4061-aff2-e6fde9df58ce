
import bpy
import bmesh
from mathutils import Vector, Euler
import os

# 清除默认场景
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# 创建cat的基础网格
def create_cat_body():
    # 创建身体 - 椭圆体
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.8, location=(0, 0, 0.5))
    body = bpy.context.active_object
    body.name = "Cat_Body"
    body.scale = (1.2, 0.8, 0.6)

    # 创建头部 - 球体
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.6, location=(0, -1.2, 0.8))
    head = bpy.context.active_object
    head.name = "Cat_Head"

    # 创建耳朵
    bpy.ops.mesh.primitive_cone_add(radius1=0.2, depth=0.4, location=(-0.3, -1.4, 1.2))
    ear_left = bpy.context.active_object
    ear_left.name = "Cat_Ear_Left"
    ear_left.rotation_euler = (0.3, 0, -0.2)

    bpy.ops.mesh.primitive_cone_add(radius1=0.2, depth=0.4, location=(0.3, -1.4, 1.2))
    ear_right = bpy.context.active_object
    ear_right.name = "Cat_Ear_Right"
    ear_right.rotation_euler = (0.3, 0, 0.2)

    # 创建尾巴
    bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=1.5, location=(0, 0.8, 0.3))
    tail = bpy.context.active_object
    tail.name = "Cat_Tail"
    tail.rotation_euler = (0.5, 0, 0)

    # 创建腿部
    for i, pos in enumerate([(-0.4, -0.3, -0.2), (0.4, -0.3, -0.2), (-0.4, 0.3, -0.2), (0.4, 0.3, -0.2)]):
        bpy.ops.mesh.primitive_cylinder_add(radius=0.15, depth=0.6, location=pos)
        leg = bpy.context.active_object
        leg.name = f"Cat_Leg_{i+1}"

    return body, head

# 创建材质
def create_cat_materials():
    # 主要毛色材质
    fur_material = bpy.data.materials.new(name="Cat_Fur")
    fur_material.use_nodes = True
    nodes = fur_material.node_tree.nodes

    # 清除默认节点
    nodes.clear()

    # 添加节点
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')

    # 设置橙色毛发
    principled.inputs['Base Color'].default_value = (1.0, 0.5, 0.2, 1.0)  # 橙色
    principled.inputs['Roughness'].default_value = 0.8
    principled.inputs['Subsurface'].default_value = 0.1

    # 连接节点
    fur_material.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])

    return fur_material

# 创建骨骼系统
def create_cat_armature():
    bpy.ops.object.armature_add(location=(0, 0, 0))
    armature = bpy.context.active_object
    armature.name = "Cat_Armature"

    # 进入编辑模式添加骨骼
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='EDIT')

    # 添加主要骨骼
    bones = armature.data.edit_bones

    # 脊椎骨骼
    spine_bone = bones.new("Spine")
    spine_bone.head = (0, 0, 0)
    spine_bone.tail = (0, 0, 1)

    # 头部骨骼
    head_bone = bones.new("Head")
    head_bone.head = (0, -1, 0.8)
    head_bone.tail = (0, -1.5, 1.2)
    head_bone.parent = spine_bone

    # 尾巴骨骼
    tail_bone = bones.new("Tail")
    tail_bone.head = (0, 0.5, 0.3)
    tail_bone.tail = (0, 1.5, 0.8)
    tail_bone.parent = spine_bone

    bpy.ops.object.mode_set(mode='OBJECT')
    return armature

# 主要创建函数
def main():
    # 创建3D模型
    body, head = create_cat_body()

    # 创建材质
    fur_material = create_cat_materials()

    # 应用材质到所有对象
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH' and obj.name.startswith('Cat_'):
            if not obj.data.materials:
                obj.data.materials.append(fur_material)

    # 创建骨骼系统
    armature = create_cat_armature()

    # 选择所有猫咪对象并合并
    bpy.ops.object.select_all(action='DESELECT')
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH' and obj.name.startswith('Cat_'):
            obj.select_set(True)

    # 设置活动对象
    bpy.context.view_layer.objects.active = body

    # 合并对象
    bpy.ops.object.join()

    # 重命名最终对象
    bpy.context.active_object.name = "Orange_Cat_3D"

    # 导出为FBX
    output_path = "./assets/real-3d-pets/orange_cat_model.fbx"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    bpy.ops.export_scene.fbx(filepath=output_path, use_selection=True)

    # 导出为glTF (用于Web)
    gltf_path = "./ark-pets-frontend/public/3d-assets/orange_cat_model.gltf"
    os.makedirs(os.path.dirname(gltf_path), exist_ok=True)
    bpy.ops.export_scene.gltf(filepath=gltf_path, use_selection=True)

    print("✅ 3D猫咪模型创建并导出完成!")

if __name__ == "__main__":
    main()
