
// 3D桌面宠物 - Three.js实现
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

export class Real3DPet {
  constructor(container) {
    this.container = container;
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.mixer = null;
    this.pet = null;
    this.animations = {};

    this.init();
  }

  init() {
    // 设置渲染器
    this.renderer.setSize(200, 200);
    this.renderer.setClearColor(0x000000, 0);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.container.appendChild(this.renderer.domElement);

    // 设置相机
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 0, 0);

    // 添加光照
    this.setupLighting();

    // 创建3D宠物
    this.create3DPet();

    // 开始渲染循环
    this.animate();
  }

  setupLighting() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 1024;
    directionalLight.shadow.mapSize.height = 1024;
    this.scene.add(directionalLight);

    // 点光源
    const pointLight = new THREE.PointLight(0xff6b6b, 0.5, 10);
    pointLight.position.set(0, 3, 0);
    this.scene.add(pointLight);
  }

  create3DPet() {
    // 创建3D猫咪几何体
    const geometry = new THREE.Group();

    // 身体
    const bodyGeometry = new THREE.SphereGeometry(0.8, 32, 32);
    const bodyMaterial = new THREE.MeshPhongMaterial({
      color: 0xff8c00,
      shininess: 30
    });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.scale.set(1.2, 0.6, 0.8);
    body.position.y = 0.5;
    body.castShadow = true;
    geometry.add(body);

    // 头部
    const headGeometry = new THREE.SphereGeometry(0.6, 32, 32);
    const head = new THREE.Mesh(headGeometry, bodyMaterial);
    head.position.set(0, 0.8, -1.2);
    head.castShadow = true;
    geometry.add(head);

    // 耳朵
    const earGeometry = new THREE.ConeGeometry(0.2, 0.4, 8);
    const leftEar = new THREE.Mesh(earGeometry, bodyMaterial);
    leftEar.position.set(-0.3, 1.2, -1.4);
    leftEar.rotation.z = -0.2;
    leftEar.castShadow = true;
    geometry.add(leftEar);

    const rightEar = new THREE.Mesh(earGeometry, bodyMaterial);
    rightEar.position.set(0.3, 1.2, -1.4);
    rightEar.rotation.z = 0.2;
    rightEar.castShadow = true;
    geometry.add(rightEar);

    // 尾巴
    const tailGeometry = new THREE.CylinderGeometry(0.05, 0.15, 1.5, 8);
    const tail = new THREE.Mesh(tailGeometry, bodyMaterial);
    tail.position.set(0, 0.3, 0.8);
    tail.rotation.x = 0.5;
    tail.castShadow = true;
    geometry.add(tail);

    // 眼睛
    const eyeGeometry = new THREE.SphereGeometry(0.1, 16, 16);
    const eyeMaterial = new THREE.MeshPhongMaterial({ color: 0x87ceeb });

    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.2, 0.9, -1.7);
    geometry.add(leftEye);

    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(0.2, 0.9, -1.7);
    geometry.add(rightEye);

    // 鼻子
    const noseGeometry = new THREE.SphereGeometry(0.05, 8, 8);
    const noseMaterial = new THREE.MeshPhongMaterial({ color: 0xffc0cb });
    const nose = new THREE.Mesh(noseGeometry, noseMaterial);
    nose.position.set(0, 0.75, -1.8);
    geometry.add(nose);

    this.pet = geometry;
    this.scene.add(this.pet);

    // 添加地面阴影
    const planeGeometry = new THREE.PlaneGeometry(5, 5);
    const planeMaterial = new THREE.ShadowMaterial({ opacity: 0.3 });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    plane.position.y = -0.5;
    plane.receiveShadow = true;
    this.scene.add(plane);
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    if (this.pet) {
      // 自动旋转
      this.pet.rotation.y += 0.005;

      // 呼吸动画
      const time = Date.now() * 0.001;
      this.pet.scale.y = 1 + Math.sin(time * 2) * 0.05;

      // 尾巴摆动
      const tail = this.pet.children.find(child => child.position.z > 0.5);
      if (tail) {
        tail.rotation.z = Math.sin(time * 3) * 0.3;
      }
    }

    this.renderer.render(this.scene, this.camera);
  }

  playAnimation(animationType) {
    if (!this.pet) return;

    switch (animationType) {
      case 'happy':
        this.playHappyAnimation();
        break;
      case 'jump':
        this.playJumpAnimation();
        break;
      case 'sleep':
        this.playSleepAnimation();
        break;
    }
  }

  playHappyAnimation() {
    const duration = 1000;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      if (this.pet) {
        this.pet.rotation.y = Math.sin(progress * Math.PI * 4) * 0.3;
        this.pet.position.y = Math.sin(progress * Math.PI * 2) * 0.2;
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.pet.rotation.y = 0;
        this.pet.position.y = 0;
      }
    };

    animate();
  }

  playJumpAnimation() {
    const duration = 800;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      if (this.pet) {
        this.pet.position.y = Math.sin(progress * Math.PI) * 1.5;
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.pet.position.y = 0;
      }
    };

    animate();
  }

  playSleepAnimation() {
    if (this.pet) {
      this.pet.rotation.z = 0.3; // 侧躺
      this.pet.scale.setScalar(0.9); // 缩小一点
    }
  }

  interact(type) {
    this.playAnimation(type);
    this.createParticleEffect(type);
  }

  createParticleEffect(type) {
    const particleCount = 20;
    const particles = new THREE.Group();

    for (let i = 0; i < particleCount; i++) {
      const geometry = new THREE.SphereGeometry(0.02, 8, 8);
      let material;

      switch (type) {
        case 'happy':
          material = new THREE.MeshBasicMaterial({ color: 0xff69b4 });
          break;
        case 'jump':
          material = new THREE.MeshBasicMaterial({ color: 0xffd700 });
          break;
        default:
          material = new THREE.MeshBasicMaterial({ color: 0x87ceeb });
      }

      const particle = new THREE.Mesh(geometry, material);
      particle.position.set(
        (Math.random() - 0.5) * 2,
        Math.random() * 2 + 1,
        (Math.random() - 0.5) * 2
      );

      particles.add(particle);
    }

    this.scene.add(particles);

    // 动画粒子
    const startTime = Date.now();
    const duration = 2000;

    const animateParticles = () => {
      const elapsed = Date.now() - startTime;
      const progress = elapsed / duration;

      particles.children.forEach((particle, index) => {
        particle.position.y += 0.02;
        particle.material.opacity = 1 - progress;
      });

      if (progress < 1) {
        requestAnimationFrame(animateParticles);
      } else {
        this.scene.remove(particles);
      }
    };

    animateParticles();
  }

  resize(width, height) {
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  dispose() {
    this.container.removeChild(this.renderer.domElement);
    this.renderer.dispose();
  }
}
