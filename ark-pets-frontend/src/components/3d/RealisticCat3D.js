
// 🐾 真实3D猫咪 - 基于真实解剖结构和行为模式
import * as THREE from 'three';

export class RealisticCat3D {
  constructor(container) {
    this.container = container;
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.cat = null;
    this.animations = {};
    this.currentBehavior = 'idle';
    this.behaviorTimer = 0;
    this.walkTarget = new THREE.Vector3();
    this.isWalking = false;
    this.mood = 'content'; // content, playful, sleepy, hungry, alert
    
    this.init();
  }
  
  init() {
    // 设置渲染器
    this.renderer.setSize(200, 200);
    this.renderer.setClearColor(0x000000, 0);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.container.appendChild(this.renderer.domElement);
    
    // 设置相机 - 更好的观察角度
    this.camera.position.set(3, 2, 3);
    this.camera.lookAt(0, 0, 0);
    
    // 添加真实光照
    this.setupRealisticLighting();
    
    // 创建逼真的猫咪
    this.createRealisticCat();
    
    // 创建环境
    this.createEnvironment();
    
    // 开始行为循环
    this.startBehaviorLoop();
    
    // 渲染循环
    this.animate();
  }
  
  setupRealisticLighting() {
    // 模拟室内光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);
    
    // 主光源 - 模拟窗户光
    const mainLight = new THREE.DirectionalLight(0xffffff, 0.8);
    mainLight.position.set(5, 8, 3);
    mainLight.castShadow = true;
    mainLight.shadow.mapSize.width = 2048;
    mainLight.shadow.mapSize.height = 2048;
    mainLight.shadow.camera.near = 0.5;
    mainLight.shadow.camera.far = 50;
    this.scene.add(mainLight);
    
    // 补光 - 模拟反射光
    const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.3);
    fillLight.position.set(-3, 4, -2);
    this.scene.add(fillLight);
    
    // 暖色调光源 - 模拟室内灯光
    const warmLight = new THREE.PointLight(0xffa500, 0.4, 10);
    warmLight.position.set(0, 3, 0);
    this.scene.add(warmLight);
  }
  
  createRealisticCat() {
    const catGroup = new THREE.Group();
    
    // 真实猫咪比例和解剖结构
    const furMaterial = new THREE.MeshPhongMaterial({ 
      color: 0xd2691e, // 更真实的橙色
      shininess: 10,
      transparent: true,
      opacity: 0.95
    });
    
    const stripeMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x8b4513, // 深色条纹
      shininess: 10
    });
    
    // 身体 - 更真实的猫咪身形
    const bodyGeometry = new THREE.CapsuleGeometry(0.3, 0.8, 4, 8);
    const body = new THREE.Mesh(bodyGeometry, furMaterial);
    body.rotation.z = Math.PI / 2;
    body.position.set(0, 0.4, 0);
    body.castShadow = true;
    catGroup.add(body);
    
    // 头部 - 更真实的猫头形状
    const headGeometry = new THREE.SphereGeometry(0.25, 16, 16);
    const head = new THREE.Mesh(headGeometry, furMaterial);
    head.scale.set(1, 0.8, 1.1);
    head.position.set(0.5, 0.4, 0);
    head.castShadow = true;
    catGroup.add(head);
    
    // 口鼻部
    const muzzleGeometry = new THREE.SphereGeometry(0.12, 12, 12);
    const muzzle = new THREE.Mesh(muzzleGeometry, furMaterial);
    muzzle.scale.set(0.8, 0.6, 1.2);
    muzzle.position.set(0.65, 0.35, 0);
    catGroup.add(muzzle);
    
    // 耳朵 - 更真实的形状和位置
    const earGeometry = new THREE.ConeGeometry(0.08, 0.15, 6);
    const leftEar = new THREE.Mesh(earGeometry, furMaterial);
    leftEar.position.set(0.45, 0.55, -0.12);
    leftEar.rotation.set(0.3, 0, -0.3);
    catGroup.add(leftEar);
    
    const rightEar = new THREE.Mesh(earGeometry, furMaterial);
    rightEar.position.set(0.45, 0.55, 0.12);
    rightEar.rotation.set(0.3, 0, 0.3);
    catGroup.add(rightEar);
    
    // 腿部 - 四条腿，真实比例
    const legGeometry = new THREE.CylinderGeometry(0.04, 0.06, 0.3, 8);
    const legPositions = [
      [0.25, 0.15, -0.15], // 前左
      [0.25, 0.15, 0.15],  // 前右
      [-0.25, 0.15, -0.15], // 后左
      [-0.25, 0.15, 0.15]   // 后右
    ];
    
    this.legs = [];
    legPositions.forEach((pos, index) => {
      const leg = new THREE.Mesh(legGeometry, furMaterial);
      leg.position.set(...pos);
      leg.castShadow = true;
      catGroup.add(leg);
      this.legs.push(leg);
    });
    
    // 尾巴 - 分段的真实尾巴
    this.tailSegments = [];
    const tailSegmentGeometry = new THREE.CylinderGeometry(0.03, 0.05, 0.15, 8);
    for (let i = 0; i < 6; i++) {
      const segment = new THREE.Mesh(tailSegmentGeometry, furMaterial);
      segment.position.set(-0.5 - i * 0.12, 0.4 + Math.sin(i * 0.3) * 0.1, 0);
      segment.rotation.z = Math.sin(i * 0.5) * 0.2;
      segment.castShadow = true;
      catGroup.add(segment);
      this.tailSegments.push(segment);
    }
    
    // 眼睛 - 更真实的猫眼
    const eyeGeometry = new THREE.SphereGeometry(0.04, 12, 12);
    const eyeMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x32cd32,
      shininess: 100,
      transparent: true,
      opacity: 0.9
    });
    
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(0.7, 0.42, -0.08);
    catGroup.add(leftEye);
    
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(0.7, 0.42, 0.08);
    catGroup.add(rightEye);
    
    // 瞳孔
    const pupilGeometry = new THREE.SphereGeometry(0.02, 8, 8);
    const pupilMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
    
    const leftPupil = new THREE.Mesh(pupilGeometry, pupilMaterial);
    leftPupil.position.set(0.72, 0.42, -0.08);
    catGroup.add(leftPupil);
    
    const rightPupil = new THREE.Mesh(pupilGeometry, pupilMaterial);
    rightPupil.position.set(0.72, 0.42, 0.08);
    catGroup.add(rightPupil);
    
    // 鼻子
    const noseGeometry = new THREE.SphereGeometry(0.02, 8, 8);
    const noseMaterial = new THREE.MeshPhongMaterial({ color: 0xff69b4 });
    const nose = new THREE.Mesh(noseGeometry, noseMaterial);
    nose.position.set(0.75, 0.38, 0);
    catGroup.add(nose);
    
    // 胡须
    const whiskerMaterial = new THREE.LineBasicMaterial({ color: 0x000000 });
    for (let i = 0; i < 6; i++) {
      const whiskerGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(0.65, 0.4, (i < 3 ? -0.05 : 0.05)),
        new THREE.Vector3(0.9, 0.4 + (i % 3 - 1) * 0.02, (i < 3 ? -0.15 : 0.15))
      ]);
      const whisker = new THREE.Line(whiskerGeometry, whiskerMaterial);
      catGroup.add(whisker);
    }
    
    this.cat = catGroup;
    this.scene.add(this.cat);
    
    // 保存身体部位引用
    this.body = body;
    this.head = head;
  }
  
  createEnvironment() {
    // 地面
    const floorGeometry = new THREE.PlaneGeometry(8, 8);
    const floorMaterial = new THREE.MeshLambertMaterial({ 
      color: 0xf5f5dc,
      transparent: true,
      opacity: 0.8
    });
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.receiveShadow = true;
    this.scene.add(floor);
    
    // 添加一些环境物品
    this.createToys();
  }
  
  createToys() {
    // 毛线球
    const ballGeometry = new THREE.SphereGeometry(0.1, 12, 12);
    const ballMaterial = new THREE.MeshPhongMaterial({ color: 0xff6347 });
    const ball = new THREE.Mesh(ballGeometry, ballMaterial);
    ball.position.set(1.5, 0.1, 1);
    ball.castShadow = true;
    this.scene.add(ball);
    this.toyBall = ball;
    
    // 小鱼玩具
    const fishGeometry = new THREE.CylinderGeometry(0.02, 0.05, 0.2, 6);
    const fishMaterial = new THREE.MeshPhongMaterial({ color: 0x4169e1 });
    const fish = new THREE.Mesh(fishGeometry, fishMaterial);
    fish.position.set(-1.2, 0.02, -0.8);
    fish.rotation.z = Math.PI / 2;
    fish.castShadow = true;
    this.scene.add(fish);
    this.toyFish = fish;
  }
  
  startBehaviorLoop() {
    setInterval(() => {
      this.updateBehavior();
    }, 3000 + Math.random() * 5000); // 3-8秒随机间隔
  }
  
  updateBehavior() {
    const behaviors = ['walk', 'explore', 'play', 'groom', 'stretch', 'hunt'];
    const weights = {
      'walk': 0.3,
      'explore': 0.2, 
      'play': 0.2,
      'groom': 0.1,
      'stretch': 0.1,
      'hunt': 0.1
    };
    
    // 根据心情调整行为权重
    if (this.mood === 'playful') {
      weights.play = 0.4;
      weights.hunt = 0.2;
    } else if (this.mood === 'sleepy') {
      weights.walk = 0.1;
      weights.groom = 0.3;
    }
    
    const randomBehavior = this.weightedRandomChoice(behaviors, weights);
    this.executeBehavior(randomBehavior);
  }
  
  weightedRandomChoice(items, weights) {
    const totalWeight = Object.values(weights).reduce((a, b) => a + b, 0);
    let random = Math.random() * totalWeight;
    
    for (const item of items) {
      random -= weights[item] || 0;
      if (random <= 0) return item;
    }
    return items[0];
  }
  
  executeBehavior(behavior) {
    console.log(`🐾 猫咪开始: ${behavior}`);
    this.currentBehavior = behavior;
    
    switch (behavior) {
      case 'walk':
        this.startWalking();
        break;
      case 'explore':
        this.exploreArea();
        break;
      case 'play':
        this.playWithToy();
        break;
      case 'groom':
        this.startGrooming();
        break;
      case 'stretch':
        this.doStretch();
        break;
      case 'hunt':
        this.huntMode();
        break;
    }
  }
  
  startWalking() {
    // 随机选择目标位置
    this.walkTarget.set(
      (Math.random() - 0.5) * 3,
      0,
      (Math.random() - 0.5) * 3
    );
    this.isWalking = true;
    this.walkSpeed = 0.02;
  }
  
  exploreArea() {
    // 好奇地四处张望
    const lookAround = () => {
      if (this.cat) {
        this.cat.rotation.y = Math.sin(Date.now() * 0.003) * 0.5;
        this.head.rotation.y = Math.sin(Date.now() * 0.005) * 0.3;
      }
    };
    
    const interval = setInterval(lookAround, 50);
    setTimeout(() => clearInterval(interval), 4000);
  }
  
  playWithToy() {
    if (this.toyBall) {
      // 走向玩具球
      const direction = new THREE.Vector3()
        .subVectors(this.toyBall.position, this.cat.position)
        .normalize();
      
      this.walkTarget.copy(this.toyBall.position);
      this.isWalking = true;
      this.walkSpeed = 0.03; // 更快的速度
      
      // 到达后玩耍
      setTimeout(() => {
        this.pawAtToy();
      }, 2000);
    }
  }
  
  pawAtToy() {
    // 用爪子拍玩具
    const pawAnimation = () => {
      if (this.legs && this.legs[0]) {
        this.legs[0].rotation.x = Math.sin(Date.now() * 0.01) * 0.5;
        this.legs[1].rotation.x = Math.sin(Date.now() * 0.01 + Math.PI) * 0.5;
      }
    };
    
    const interval = setInterval(pawAnimation, 50);
    setTimeout(() => {
      clearInterval(interval);
      // 重置腿部位置
      if (this.legs) {
        this.legs.forEach(leg => leg.rotation.x = 0);
      }
    }, 3000);
  }
  
  startGrooming() {
    // 舔毛动作
    const groomAnimation = () => {
      if (this.head) {
        this.head.rotation.x = Math.sin(Date.now() * 0.008) * 0.3;
        this.head.rotation.z = Math.sin(Date.now() * 0.006) * 0.2;
      }
    };
    
    const interval = setInterval(groomAnimation, 50);
    setTimeout(() => {
      clearInterval(interval);
      if (this.head) {
        this.head.rotation.x = 0;
        this.head.rotation.z = 0;
      }
    }, 5000);
  }
  
  doStretch() {
    // 伸懒腰动作
    if (this.body && this.cat) {
      const originalScale = this.body.scale.clone();
      const stretchTween = () => {
        const time = Date.now() * 0.005;
        this.body.scale.x = originalScale.x * (1 + Math.sin(time) * 0.2);
        this.cat.rotation.x = Math.sin(time) * 0.1;
      };
      
      const interval = setInterval(stretchTween, 50);
      setTimeout(() => {
        clearInterval(interval);
        this.body.scale.copy(originalScale);
        this.cat.rotation.x = 0;
      }, 3000);
    }
  }
  
  huntMode() {
    // 狩猎模式 - 低伏身体，缓慢移动
    if (this.cat && this.body) {
      this.cat.position.y = 0.1; // 降低身体
      this.body.rotation.x = 0.2; // 前倾
      
      // 缓慢潜行
      const prowl = () => {
        if (this.cat) {
          this.cat.position.x += Math.sin(Date.now() * 0.002) * 0.01;
          this.cat.position.z += Math.cos(Date.now() * 0.002) * 0.01;
        }
      };
      
      const interval = setInterval(prowl, 50);
      setTimeout(() => {
        clearInterval(interval);
        this.cat.position.y = 0;
        this.body.rotation.x = 0;
      }, 4000);
    }
  }
  
  animate() {
    requestAnimationFrame(() => this.animate());
    
    if (this.cat) {
      // 持续的呼吸动画
      const time = Date.now() * 0.001;
      if (this.body) {
        this.body.scale.y = 1 + Math.sin(time * 2) * 0.03;
      }
      
      // 尾巴摆动
      if (this.tailSegments) {
        this.tailSegments.forEach((segment, index) => {
          segment.rotation.z = Math.sin(time * 2 + index * 0.5) * 0.3;
          segment.rotation.y = Math.sin(time * 1.5 + index * 0.3) * 0.2;
        });
      }
      
      // 行走逻辑
      if (this.isWalking) {
        const direction = new THREE.Vector3()
          .subVectors(this.walkTarget, this.cat.position)
          .normalize();
        
        if (this.cat.position.distanceTo(this.walkTarget) > 0.1) {
          this.cat.position.add(direction.multiplyScalar(this.walkSpeed));
          
          // 面向移动方向
          this.cat.lookAt(this.walkTarget);
          
          // 行走动画 - 腿部运动
          if (this.legs) {
            const walkTime = time * 8;
            this.legs[0].rotation.x = Math.sin(walkTime) * 0.5; // 前左
            this.legs[1].rotation.x = Math.sin(walkTime + Math.PI) * 0.5; // 前右
            this.legs[2].rotation.x = Math.sin(walkTime + Math.PI) * 0.5; // 后左
            this.legs[3].rotation.x = Math.sin(walkTime) * 0.5; // 后右
          }
          
          // 身体轻微上下摆动
          this.cat.position.y = Math.sin(time * 8) * 0.02;
        } else {
          this.isWalking = false;
          this.cat.position.y = 0;
          // 重置腿部
          if (this.legs) {
            this.legs.forEach(leg => leg.rotation.x = 0);
          }
        }
      }
    }
    
    this.renderer.render(this.scene, this.camera);
  }
  
  interact(type) {
    console.log(`🎮 与猫咪互动: ${type}`);
    
    switch (type) {
      case 'pet':
        this.mood = 'content';
        this.startPurring();
        break;
      case 'play':
        this.mood = 'playful';
        this.playWithToy();
        break;
      case 'feed':
        this.mood = 'content';
        this.eatAnimation();
        break;
      case 'call':
        this.comeToUser();
        break;
    }
    
    this.createParticleEffect(type);
  }
  
  startPurring() {
    // 满足的呼噜声动画
    if (this.body) {
      const purr = () => {
        this.body.scale.setScalar(1 + Math.sin(Date.now() * 0.02) * 0.02);
      };
      
      const interval = setInterval(purr, 50);
      setTimeout(() => {
        clearInterval(interval);
        this.body.scale.setScalar(1);
      }, 3000);
    }
  }
  
  eatAnimation() {
    // 进食动画
    if (this.head) {
      const eat = () => {
        this.head.rotation.x = Math.sin(Date.now() * 0.01) * 0.2 - 0.3;
      };
      
      const interval = setInterval(eat, 50);
      setTimeout(() => {
        clearInterval(interval);
        this.head.rotation.x = 0;
      }, 4000);
    }
  }
  
  comeToUser() {
    // 走向用户（屏幕中心）
    this.walkTarget.set(0, 0, 1);
    this.isWalking = true;
    this.walkSpeed = 0.04;
  }
  
  createParticleEffect(type) {
    const particleCount = 15;
    const particles = new THREE.Group();
    
    for (let i = 0; i < particleCount; i++) {
      const geometry = new THREE.SphereGeometry(0.02, 8, 8);
      let material;
      
      switch (type) {
        case 'pet':
          material = new THREE.MeshBasicMaterial({ color: 0xff69b4 });
          break;
        case 'play':
          material = new THREE.MeshBasicMaterial({ color: 0xffd700 });
          break;
        case 'feed':
          material = new THREE.MeshBasicMaterial({ color: 0x32cd32 });
          break;
        default:
          material = new THREE.MeshBasicMaterial({ color: 0x87ceeb });
      }
      
      const particle = new THREE.Mesh(geometry, material);
      particle.position.set(
        this.cat.position.x + (Math.random() - 0.5) * 1,
        this.cat.position.y + Math.random() * 1 + 0.5,
        this.cat.position.z + (Math.random() - 0.5) * 1
      );
      
      particles.add(particle);
    }
    
    this.scene.add(particles);
    
    // 粒子动画
    const startTime = Date.now();
    const duration = 2000;
    
    const animateParticles = () => {
      const elapsed = Date.now() - startTime;
      const progress = elapsed / duration;
      
      particles.children.forEach((particle, index) => {
        particle.position.y += 0.02;
        particle.rotation.y += 0.1;
        particle.material.opacity = 1 - progress;
      });
      
      if (progress < 1) {
        requestAnimationFrame(animateParticles);
      } else {
        this.scene.remove(particles);
      }
    };
    
    animateParticles();
  }
  
  dispose() {
    this.container.removeChild(this.renderer.domElement);
    this.renderer.dispose();
  }
}
