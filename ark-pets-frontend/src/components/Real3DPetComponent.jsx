
import React, { useEffect, useRef } from 'react';
import { Real3DPet } from './3d/Real3DPet.js';

export const Real3DPetComponent = ({ petData, onInteraction }) => {
  const containerRef = useRef(null);
  const petRef = useRef(null);

  useEffect(() => {
    if (containerRef.current && !petRef.current) {
      petRef.current = new Real3DPet(containerRef.current);
    }

    return () => {
      if (petRef.current) {
        petRef.current.dispose();
        petRef.current = null;
      }
    };
  }, []);

  const handleInteraction = (type) => {
    if (petRef.current) {
      petRef.current.interact(type);
    }
    if (onInteraction) {
      onInteraction(type);
    }
  };

  return (
    <div style={{ position: 'relative', width: '200px', height: '200px' }}>
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />
      <div style={{
        position: 'absolute',
        bottom: '10px',
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'flex',
        gap: '5px'
      }}>
        <button onClick={() => handleInteraction('happy')} style={{
          padding: '5px 10px',
          background: '#ff69b4',
          border: 'none',
          borderRadius: '5px',
          color: 'white',
          cursor: 'pointer'
        }}>
          😊 开心
        </button>
        <button onClick={() => handleInteraction('jump')} style={{
          padding: '5px 10px',
          background: '#ffd700',
          border: 'none',
          borderRadius: '5px',
          color: 'white',
          cursor: 'pointer'
        }}>
          🦘 跳跃
        </button>
        <button onClick={() => handleInteraction('sleep')} style={{
          padding: '5px 10px',
          background: '#87ceeb',
          border: 'none',
          borderRadius: '5px',
          color: 'white',
          cursor: 'pointer'
        }}>
          😴 睡觉
        </button>
      </div>
    </div>
  );
};
