import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Form, Input, Button, Typography, Divider, Space, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, EyeTwoTone, EyeInvisibleOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { useAuthStore } from '../hooks/useAuthStore';
import toast from 'react-hot-toast';

const { Title, Text } = Typography;

// 注册请求类型
interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  nickname?: string;
}

// 样式组件
const RegisterContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const RegisterCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 420px;
  backdrop-filter: blur(10px);
`;

const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Logo = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
`;

const StyledForm = styled(Form)`
  .ant-form-item {
    margin-bottom: 20px;
  }
  
  .ant-input-affix-wrapper {
    height: 48px;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    
    &:hover, &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .ant-btn-primary {
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #40a9ff, #9254de);
    }
  }
`;

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { register } = useAuthStore();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);

  const handleSubmit = (values: any) => {
    if (!agreeTerms) {
      toast.error('请先同意用户协议和隐私政策');
      return;
    }

    const registerData: RegisterRequest = {
      username: values.username,
      email: values.email,
      password: values.password,
      confirmPassword: values.confirmPassword,
      nickname: values.nickname,
    };

    setIsLoading(true);
    
    // 使用Promise处理异步逻辑
    register(registerData).then((success) => {
      if (success) {
        navigate('/dashboard', { replace: true });
      }
    }).catch((error) => {
      console.error('Registration failed:', error);
      toast.error('注册失败，请稍后重试');
    }).finally(() => {
      setIsLoading(false);
    });
  };

  const handleDemoRegister = () => {
    form.setFieldsValue({
      username: 'demo_user_' + Math.floor(Math.random() * 1000),
      email: '<EMAIL>',
      nickname: '演示用户',
      password: 'demo123456',
      confirmPassword: 'demo123456',
    });
    setAgreeTerms(true);
  };

  return (
    <RegisterContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <RegisterCard>
          <LogoContainer>
            <Logo>🐾</Logo>
            <Title level={2} style={{ margin: 0, color: '#262626' }}>
              加入 Ark-Pets
            </Title>
            <Text type="secondary">创建您的桌宠管理账号</Text>
          </LogoContainer>

          <StyledForm
            form={form}
            name="register"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
                { max: 20, message: '用户名最多20个字符' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="email"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="nickname"
              rules={[
                { max: 50, message: '昵称最多50个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="昵称（可选）"
                autoComplete="nickname"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
                { max: 50, message: '密码最多50个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="new-password"
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="确认密码"
                autoComplete="new-password"
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'center' }}>
                <Checkbox
                  checked={agreeTerms}
                  onChange={(e) => setAgreeTerms(e.target.checked)}
                >
                  我已阅读并同意{' '}
                  <Link to="#" style={{ color: '#1890ff' }}>
                    用户协议
                  </Link>
                  {' '}和{' '}
                  <Link to="#" style={{ color: '#1890ff' }}>
                    隐私政策
                  </Link>
                </Checkbox>
              </Space>
            </Form.Item>

            <Form.Item style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                disabled={!agreeTerms}
                block
              >
                {isLoading ? '注册中...' : '立即注册'}
              </Button>
            </Form.Item>

            <Divider>
              <Text type="secondary">🎯 快速注册</Text>
            </Divider>

            <div style={{ textAlign: 'center', marginBottom: 12 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                一键生成演示账号信息
              </Text>
            </div>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="default"
                onClick={handleDemoRegister}
                block
                style={{
                  height: '40px',
                  borderRadius: '8px',
                  borderColor: '#52c41a',
                  color: '#52c41a',
                  background: 'linear-gradient(135deg, #f6ffed, #d9f7be)',
                }}
              >
                🎲 生成演示注册信息
              </Button>
            </Form.Item>
          </StyledForm>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Text type="secondary">
              已有账号？{' '}
              <Link to="/login" style={{ color: '#1890ff' }}>
                立即登录
              </Link>
            </Text>
          </div>
        </RegisterCard>
      </motion.div>
    </RegisterContainer>
  );
};

export default RegisterPage;
