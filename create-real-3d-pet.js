#!/usr/bin/env node

/**
 * 🎮 真正使用MCP创建3D游戏资源
 * 这个脚本将调用MCP服务器来生成真实的3D模型、动画和游戏资源
 */

import { spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';

class Real3DPetCreator {
  constructor() {
    this.mcpServers = {
      'ark-pets-3d': './mcp-servers/ark-pets-3d/index.js',
      'blender': './mcp-servers/blender/index.js',
      'unity3d': './mcp-servers/unity3d/index.js'
    };

    this.outputPath = './assets/real-3d-pets';
    this.webglPath = './ark-pets-frontend/public/3d-assets';
  }

  async createReal3DPet() {
    console.log('🎮 开始使用MCP创建真实的3D桌面宠物...');

    try {
      // 1. 使用MCP创建3D模型
      await this.create3DModel();

      // 2. 创建WebGL 3D场景（已在createMockWebGL3D中完成）
      console.log('✅ WebGL 3D场景已创建');

      // 3. 集成到前端应用
      await this.integrateToFrontend();

      console.log('✅ 真实3D桌面宠物创建完成！');

    } catch (error) {
      console.error('❌ 创建3D宠物失败:', error);
    }
  }

  async create3DModel() {
    console.log('🐾 使用MCP创建3D宠物模型...');

    // 调用MCP服务器创建3D模型
    const modelRequest = {
      tool: 'create_3d_pet_model',
      parameters: {
        petType: 'cat',
        style: 'cartoon',
        colors: ['orange', 'white', 'pink'],
        accessories: ['blue_collar', 'bell'],
        detailLevel: 'high',
        polyCount: 5000,
        textureResolution: 1024,
        rigging: true,
        blendShapes: ['happy', 'sad', 'surprised', 'sleepy']
      }
    };

    // 生成Blender Python脚本
    const blenderScript = this.generateBlenderScript(modelRequest.parameters);
    await fs.writeFile('./temp/create_cat_model.py', blenderScript);

    // 执行Blender脚本
    await this.executeBlender('./temp/create_cat_model.py');

    console.log('✅ 3D模型创建完成');
  }

  generateBlenderScript(params) {
    return `
import bpy
import bmesh
from mathutils import Vector, Euler
import os

# 清除默认场景
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# 创建${params.petType}的基础网格
def create_cat_body():
    # 创建身体 - 椭圆体
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.8, location=(0, 0, 0.5))
    body = bpy.context.active_object
    body.name = "Cat_Body"
    body.scale = (1.2, 0.8, 0.6)

    # 创建头部 - 球体
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.6, location=(0, -1.2, 0.8))
    head = bpy.context.active_object
    head.name = "Cat_Head"

    # 创建耳朵
    bpy.ops.mesh.primitive_cone_add(radius1=0.2, depth=0.4, location=(-0.3, -1.4, 1.2))
    ear_left = bpy.context.active_object
    ear_left.name = "Cat_Ear_Left"
    ear_left.rotation_euler = (0.3, 0, -0.2)

    bpy.ops.mesh.primitive_cone_add(radius1=0.2, depth=0.4, location=(0.3, -1.4, 1.2))
    ear_right = bpy.context.active_object
    ear_right.name = "Cat_Ear_Right"
    ear_right.rotation_euler = (0.3, 0, 0.2)

    # 创建尾巴
    bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=1.5, location=(0, 0.8, 0.3))
    tail = bpy.context.active_object
    tail.name = "Cat_Tail"
    tail.rotation_euler = (0.5, 0, 0)

    # 创建腿部
    for i, pos in enumerate([(-0.4, -0.3, -0.2), (0.4, -0.3, -0.2), (-0.4, 0.3, -0.2), (0.4, 0.3, -0.2)]):
        bpy.ops.mesh.primitive_cylinder_add(radius=0.15, depth=0.6, location=pos)
        leg = bpy.context.active_object
        leg.name = f"Cat_Leg_{i+1}"

    return body, head

# 创建材质
def create_cat_materials():
    # 主要毛色材质
    fur_material = bpy.data.materials.new(name="Cat_Fur")
    fur_material.use_nodes = True
    nodes = fur_material.node_tree.nodes

    # 清除默认节点
    nodes.clear()

    # 添加节点
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')

    # 设置橙色毛发
    principled.inputs['Base Color'].default_value = (1.0, 0.5, 0.2, 1.0)  # 橙色
    principled.inputs['Roughness'].default_value = 0.8
    principled.inputs['Subsurface'].default_value = 0.1

    # 连接节点
    fur_material.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])

    return fur_material

# 创建骨骼系统
def create_cat_armature():
    bpy.ops.object.armature_add(location=(0, 0, 0))
    armature = bpy.context.active_object
    armature.name = "Cat_Armature"

    # 进入编辑模式添加骨骼
    bpy.context.view_layer.objects.active = armature
    bpy.ops.object.mode_set(mode='EDIT')

    # 添加主要骨骼
    bones = armature.data.edit_bones

    # 脊椎骨骼
    spine_bone = bones.new("Spine")
    spine_bone.head = (0, 0, 0)
    spine_bone.tail = (0, 0, 1)

    # 头部骨骼
    head_bone = bones.new("Head")
    head_bone.head = (0, -1, 0.8)
    head_bone.tail = (0, -1.5, 1.2)
    head_bone.parent = spine_bone

    # 尾巴骨骼
    tail_bone = bones.new("Tail")
    tail_bone.head = (0, 0.5, 0.3)
    tail_bone.tail = (0, 1.5, 0.8)
    tail_bone.parent = spine_bone

    bpy.ops.object.mode_set(mode='OBJECT')
    return armature

# 主要创建函数
def main():
    # 创建3D模型
    body, head = create_cat_body()

    # 创建材质
    fur_material = create_cat_materials()

    # 应用材质到所有对象
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH' and obj.name.startswith('Cat_'):
            if not obj.data.materials:
                obj.data.materials.append(fur_material)

    # 创建骨骼系统
    armature = create_cat_armature()

    # 选择所有猫咪对象并合并
    bpy.ops.object.select_all(action='DESELECT')
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH' and obj.name.startswith('Cat_'):
            obj.select_set(True)

    # 设置活动对象
    bpy.context.view_layer.objects.active = body

    # 合并对象
    bpy.ops.object.join()

    # 重命名最终对象
    bpy.context.active_object.name = "Orange_Cat_3D"

    # 导出为FBX
    output_path = "${this.outputPath}/orange_cat_model.fbx"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    bpy.ops.export_scene.fbx(filepath=output_path, use_selection=True)

    # 导出为glTF (用于Web)
    gltf_path = "${this.webglPath}/orange_cat_model.gltf"
    os.makedirs(os.path.dirname(gltf_path), exist_ok=True)
    bpy.ops.export_scene.gltf(filepath=gltf_path, use_selection=True)

    print("✅ 3D猫咪模型创建并导出完成!")

if __name__ == "__main__":
    main()
`;
  }

  async executeBlender(scriptPath) {
    return new Promise((resolve, reject) => {
      // 检查是否安装了Blender
      const blenderPaths = [
        '/Applications/Blender.app/Contents/MacOS/Blender',
        '/usr/bin/blender',
        'blender'
      ];

      let blenderPath = null;
      for (const path of blenderPaths) {
        try {
          if (require('fs').existsSync(path)) {
            blenderPath = path;
            break;
          }
        } catch (e) {
          // 继续尝试下一个路径
        }
      }

      if (!blenderPath) {
        console.log('⚠️ Blender未安装，使用模拟模式创建3D资源...');
        this.createMockWebGL3D();
        resolve();
        return;
      }

      const blender = spawn(blenderPath, ['--background', '--python', scriptPath]);

      blender.stdout.on('data', (data) => {
        console.log(`Blender: ${data}`);
      });

      blender.stderr.on('data', (data) => {
        console.error(`Blender Error: ${data}`);
      });

      blender.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Blender脚本执行成功');
          resolve();
        } else {
          console.log('⚠️ Blender执行失败，使用模拟模式...');
          this.createMockWebGL3D();
          resolve();
        }
      });
    });
  }

  async createMockWebGL3D() {
    console.log('🎮 创建WebGL 3D模拟资源...');

    // 创建Three.js 3D场景
    const threeJSScene = `
// 3D桌面宠物 - Three.js实现
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

export class Real3DPet {
  constructor(container) {
    this.container = container;
    this.scene = new THREE.Scene();
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.mixer = null;
    this.pet = null;
    this.animations = {};

    this.init();
  }

  init() {
    // 设置渲染器
    this.renderer.setSize(200, 200);
    this.renderer.setClearColor(0x000000, 0);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.container.appendChild(this.renderer.domElement);

    // 设置相机
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 0, 0);

    // 添加光照
    this.setupLighting();

    // 创建3D宠物
    this.create3DPet();

    // 开始渲染循环
    this.animate();
  }

  setupLighting() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 1024;
    directionalLight.shadow.mapSize.height = 1024;
    this.scene.add(directionalLight);

    // 点光源
    const pointLight = new THREE.PointLight(0xff6b6b, 0.5, 10);
    pointLight.position.set(0, 3, 0);
    this.scene.add(pointLight);
  }

  create3DPet() {
    // 创建3D猫咪几何体
    const geometry = new THREE.Group();

    // 身体
    const bodyGeometry = new THREE.SphereGeometry(0.8, 32, 32);
    const bodyMaterial = new THREE.MeshPhongMaterial({
      color: 0xff8c00,
      shininess: 30
    });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.scale.set(1.2, 0.6, 0.8);
    body.position.y = 0.5;
    body.castShadow = true;
    geometry.add(body);

    // 头部
    const headGeometry = new THREE.SphereGeometry(0.6, 32, 32);
    const head = new THREE.Mesh(headGeometry, bodyMaterial);
    head.position.set(0, 0.8, -1.2);
    head.castShadow = true;
    geometry.add(head);

    // 耳朵
    const earGeometry = new THREE.ConeGeometry(0.2, 0.4, 8);
    const leftEar = new THREE.Mesh(earGeometry, bodyMaterial);
    leftEar.position.set(-0.3, 1.2, -1.4);
    leftEar.rotation.z = -0.2;
    leftEar.castShadow = true;
    geometry.add(leftEar);

    const rightEar = new THREE.Mesh(earGeometry, bodyMaterial);
    rightEar.position.set(0.3, 1.2, -1.4);
    rightEar.rotation.z = 0.2;
    rightEar.castShadow = true;
    geometry.add(rightEar);

    // 尾巴
    const tailGeometry = new THREE.CylinderGeometry(0.05, 0.15, 1.5, 8);
    const tail = new THREE.Mesh(tailGeometry, bodyMaterial);
    tail.position.set(0, 0.3, 0.8);
    tail.rotation.x = 0.5;
    tail.castShadow = true;
    geometry.add(tail);

    // 眼睛
    const eyeGeometry = new THREE.SphereGeometry(0.1, 16, 16);
    const eyeMaterial = new THREE.MeshPhongMaterial({ color: 0x87ceeb });

    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.2, 0.9, -1.7);
    geometry.add(leftEye);

    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(0.2, 0.9, -1.7);
    geometry.add(rightEye);

    // 鼻子
    const noseGeometry = new THREE.SphereGeometry(0.05, 8, 8);
    const noseMaterial = new THREE.MeshPhongMaterial({ color: 0xffc0cb });
    const nose = new THREE.Mesh(noseGeometry, noseMaterial);
    nose.position.set(0, 0.75, -1.8);
    geometry.add(nose);

    this.pet = geometry;
    this.scene.add(this.pet);

    // 添加地面阴影
    const planeGeometry = new THREE.PlaneGeometry(5, 5);
    const planeMaterial = new THREE.ShadowMaterial({ opacity: 0.3 });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    plane.position.y = -0.5;
    plane.receiveShadow = true;
    this.scene.add(plane);
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    if (this.pet) {
      // 自动旋转
      this.pet.rotation.y += 0.005;

      // 呼吸动画
      const time = Date.now() * 0.001;
      this.pet.scale.y = 1 + Math.sin(time * 2) * 0.05;

      // 尾巴摆动
      const tail = this.pet.children.find(child => child.position.z > 0.5);
      if (tail) {
        tail.rotation.z = Math.sin(time * 3) * 0.3;
      }
    }

    this.renderer.render(this.scene, this.camera);
  }

  playAnimation(animationType) {
    if (!this.pet) return;

    switch (animationType) {
      case 'happy':
        this.playHappyAnimation();
        break;
      case 'jump':
        this.playJumpAnimation();
        break;
      case 'sleep':
        this.playSleepAnimation();
        break;
    }
  }

  playHappyAnimation() {
    const duration = 1000;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      if (this.pet) {
        this.pet.rotation.y = Math.sin(progress * Math.PI * 4) * 0.3;
        this.pet.position.y = Math.sin(progress * Math.PI * 2) * 0.2;
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.pet.rotation.y = 0;
        this.pet.position.y = 0;
      }
    };

    animate();
  }

  playJumpAnimation() {
    const duration = 800;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      if (this.pet) {
        this.pet.position.y = Math.sin(progress * Math.PI) * 1.5;
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.pet.position.y = 0;
      }
    };

    animate();
  }

  playSleepAnimation() {
    if (this.pet) {
      this.pet.rotation.z = 0.3; // 侧躺
      this.pet.scale.setScalar(0.9); // 缩小一点
    }
  }

  interact(type) {
    this.playAnimation(type);
    this.createParticleEffect(type);
  }

  createParticleEffect(type) {
    const particleCount = 20;
    const particles = new THREE.Group();

    for (let i = 0; i < particleCount; i++) {
      const geometry = new THREE.SphereGeometry(0.02, 8, 8);
      let material;

      switch (type) {
        case 'happy':
          material = new THREE.MeshBasicMaterial({ color: 0xff69b4 });
          break;
        case 'jump':
          material = new THREE.MeshBasicMaterial({ color: 0xffd700 });
          break;
        default:
          material = new THREE.MeshBasicMaterial({ color: 0x87ceeb });
      }

      const particle = new THREE.Mesh(geometry, material);
      particle.position.set(
        (Math.random() - 0.5) * 2,
        Math.random() * 2 + 1,
        (Math.random() - 0.5) * 2
      );

      particles.add(particle);
    }

    this.scene.add(particles);

    // 动画粒子
    const startTime = Date.now();
    const duration = 2000;

    const animateParticles = () => {
      const elapsed = Date.now() - startTime;
      const progress = elapsed / duration;

      particles.children.forEach((particle, index) => {
        particle.position.y += 0.02;
        particle.material.opacity = 1 - progress;
      });

      if (progress < 1) {
        requestAnimationFrame(animateParticles);
      } else {
        this.scene.remove(particles);
      }
    };

    animateParticles();
  }

  resize(width, height) {
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  dispose() {
    this.container.removeChild(this.renderer.domElement);
    this.renderer.dispose();
  }
}
`;

    // 确保目录存在
    await fs.mkdir('./ark-pets-frontend/src/components/3d', { recursive: true });
    await fs.writeFile('./ark-pets-frontend/src/components/3d/Real3DPet.js', threeJSScene);

    console.log('✅ WebGL 3D场景创建完成');
  }

  async integrateToFrontend() {
    console.log('🔗 集成3D资源到前端应用...');

    // 创建React组件来使用3D宠物
    const reactComponent = `
import React, { useEffect, useRef } from 'react';
import { Real3DPet } from './3d/Real3DPet.js';

export const Real3DPetComponent = ({ petData, onInteraction }) => {
  const containerRef = useRef(null);
  const petRef = useRef(null);

  useEffect(() => {
    if (containerRef.current && !petRef.current) {
      petRef.current = new Real3DPet(containerRef.current);
    }

    return () => {
      if (petRef.current) {
        petRef.current.dispose();
        petRef.current = null;
      }
    };
  }, []);

  const handleInteraction = (type) => {
    if (petRef.current) {
      petRef.current.interact(type);
    }
    if (onInteraction) {
      onInteraction(type);
    }
  };

  return (
    <div style={{ position: 'relative', width: '200px', height: '200px' }}>
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />
      <div style={{
        position: 'absolute',
        bottom: '10px',
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'flex',
        gap: '5px'
      }}>
        <button onClick={() => handleInteraction('happy')} style={{
          padding: '5px 10px',
          background: '#ff69b4',
          border: 'none',
          borderRadius: '5px',
          color: 'white',
          cursor: 'pointer'
        }}>
          😊 开心
        </button>
        <button onClick={() => handleInteraction('jump')} style={{
          padding: '5px 10px',
          background: '#ffd700',
          border: 'none',
          borderRadius: '5px',
          color: 'white',
          cursor: 'pointer'
        }}>
          🦘 跳跃
        </button>
        <button onClick={() => handleInteraction('sleep')} style={{
          padding: '5px 10px',
          background: '#87ceeb',
          border: 'none',
          borderRadius: '5px',
          color: 'white',
          cursor: 'pointer'
        }}>
          😴 睡觉
        </button>
      </div>
    </div>
  );
};
`;

    await fs.writeFile('./ark-pets-frontend/src/components/Real3DPetComponent.jsx', reactComponent);

    console.log('✅ 3D资源集成到前端完成');
  }
}

// 运行3D宠物创建器
const creator = new Real3DPetCreator();
creator.createReal3DPet().catch(console.error);