#!/bin/bash

# 🎮 Ark-Pets 3D MCP 安装配置脚本
# 自动安装和配置所有必要的MCP服务器和3D开发工具

set -e

echo "🎮 开始安装 Ark-Pets 3D MCP 环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    print_status "检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        print_error "Git 未安装"
        exit 1
    fi
    
    print_success "系统要求检查通过"
}

# 创建项目目录结构
create_directories() {
    print_status "创建项目目录结构..."
    
    mkdir -p assets/{3d-models,animations,materials,effects,audio}
    mkdir -p assets/3d-models/{cats,dogs,birds,fish,rabbits,hamsters}
    mkdir -p assets/animations/{idle,walk,run,jump,eat,sleep,play,emotions}
    mkdir -p assets/materials/{fur,eyes,accessories,environments}
    mkdir -p assets/effects/{particles,shaders,ui-effects}
    mkdir -p assets/audio/{sounds,music,voice}
    
    mkdir -p mcp-servers/{ark-pets-3d,blender,unity3d,spine-animation,live2d}
    mkdir -p configs/{personalities,behaviors,ui-themes}
    mkdir -p logs/{mcp,blender,unity,desktop}
    mkdir -p cache/{assets,models,animations}
    mkdir -p build/{desktop-pets,web-export,mobile-export}
    
    print_success "目录结构创建完成"
}

# 安装MCP SDK
install_mcp_sdk() {
    print_status "安装 MCP SDK..."
    
    npm install -g @modelcontextprotocol/sdk
    npm install @modelcontextprotocol/sdk
    
    print_success "MCP SDK 安装完成"
}

# 克隆官方MCP服务器
clone_mcp_servers() {
    print_status "克隆官方 MCP 服务器..."
    
    if [ ! -d "mcp-servers-official" ]; then
        git clone https://github.com/modelcontextprotocol/servers.git mcp-servers-official
    fi
    
    # 复制需要的服务器
    if [ -d "mcp-servers-official/blender" ]; then
        cp -r mcp-servers-official/blender mcp-servers/
        print_success "Blender MCP 服务器复制完成"
    fi
    
    if [ -d "mcp-servers-official/unity3d" ]; then
        cp -r mcp-servers-official/unity3d mcp-servers/
        print_success "Unity3D MCP 服务器复制完成"
    fi
}

# 安装Blender MCP服务器
install_blender_mcp() {
    print_status "安装 Blender MCP 服务器..."
    
    cd mcp-servers/blender
    npm install
    cd ../..
    
    print_success "Blender MCP 服务器安装完成"
}

# 安装Unity3D MCP服务器
install_unity_mcp() {
    print_status "安装 Unity3D MCP 服务器..."
    
    if [ -d "mcp-servers/unity3d" ]; then
        cd mcp-servers/unity3d
        npm install
        cd ../..
        print_success "Unity3D MCP 服务器安装完成"
    else
        print_warning "Unity3D MCP 服务器不存在，跳过安装"
    fi
}

# 安装自定义Ark-Pets MCP服务器
install_arkpets_mcp() {
    print_status "安装 Ark-Pets 自定义 MCP 服务器..."
    
    cd mcp-servers/ark-pets-3d
    
    # 创建package.json
    cat > package.json << EOF
{
  "name": "ark-pets-3d-mcp",
  "version": "1.0.0",
  "description": "Ark-Pets 3D MCP Server for desktop pet development",
  "main": "index.js",
  "type": "module",
  "scripts": {
    "start": "node index.js",
    "dev": "node --watch index.js"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "^0.5.0"
  },
  "keywords": ["mcp", "3d", "desktop-pet", "ark-pets"],
  "author": "Ark-Pets Team",
  "license": "MIT"
}
EOF
    
    npm install
    cd ../..
    
    print_success "Ark-Pets MCP 服务器安装完成"
}

# 配置Claude Desktop
configure_claude_desktop() {
    print_status "配置 Claude Desktop..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        CLAUDE_CONFIG_DIR="$HOME/Library/Application Support/Claude"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        CLAUDE_CONFIG_DIR="$HOME/.config/claude"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        # Windows
        CLAUDE_CONFIG_DIR="$APPDATA/Claude"
    else
        print_warning "未知操作系统，请手动配置 Claude Desktop"
        return
    fi
    
    # 创建配置目录
    mkdir -p "$CLAUDE_CONFIG_DIR"
    
    # 获取当前工作目录的绝对路径
    CURRENT_DIR=$(pwd)
    
    # 生成配置文件
    cat > "$CLAUDE_CONFIG_DIR/claude_desktop_config.json" << EOF
{
  "mcpServers": {
    "blender": {
      "command": "node",
      "args": ["$CURRENT_DIR/mcp-servers/blender/index.js"],
      "env": {
        "BLENDER_PATH": "/Applications/Blender.app/Contents/MacOS/Blender",
        "PROJECT_PATH": "$CURRENT_DIR/blender-projects",
        "ASSETS_PATH": "$CURRENT_DIR/assets/3d-models"
      }
    },
    "ark-pets-3d": {
      "command": "node",
      "args": ["$CURRENT_DIR/mcp-servers/ark-pets-3d/index.js"],
      "env": {
        "PETS_MODELS_PATH": "$CURRENT_DIR/assets/3d-models",
        "ANIMATIONS_PATH": "$CURRENT_DIR/assets/animations",
        "MATERIALS_PATH": "$CURRENT_DIR/assets/materials",
        "EFFECTS_PATH": "$CURRENT_DIR/assets/effects",
        "AUDIO_PATH": "$CURRENT_DIR/assets/audio"
      }
    }
  }
}
EOF
    
    print_success "Claude Desktop 配置完成"
    print_status "配置文件位置: $CLAUDE_CONFIG_DIR/claude_desktop_config.json"
}

# 下载示例资源
download_sample_assets() {
    print_status "下载示例资源..."
    
    # 创建示例宠物配置
    cat > assets/3d-models/sample_cat.json << EOF
{
  "name": "示例猫咪",
  "type": "cat",
  "style": "cartoon",
  "colors": ["orange", "white"],
  "accessories": ["collar"],
  "animations": ["idle", "walk", "play", "sleep"],
  "personality": {
    "playfulness": 0.8,
    "friendliness": 0.9,
    "energy": 0.7
  }
}
EOF
    
    # 创建示例动画配置
    cat > assets/animations/cat_idle.json << EOF
{
  "name": "cat_idle",
  "duration": 3.0,
  "looping": true,
  "keyframes": [
    {"time": 0.0, "position": [0, 0, 0], "rotation": [0, 0, 0]},
    {"time": 1.5, "position": [0, 0.1, 0], "rotation": [0, 5, 0]},
    {"time": 3.0, "position": [0, 0, 0], "rotation": [0, 0, 0]}
  ]
}
EOF
    
    print_success "示例资源创建完成"
}

# 创建启动脚本
create_launch_scripts() {
    print_status "创建启动脚本..."
    
    # MCP服务器启动脚本
    cat > start-mcp-servers.sh << 'EOF'
#!/bin/bash
echo "🚀 启动 Ark-Pets MCP 服务器..."

# 启动Blender MCP服务器
echo "启动 Blender MCP 服务器..."
cd mcp-servers/blender && npm start &
BLENDER_PID=$!

# 启动Ark-Pets MCP服务器
echo "启动 Ark-Pets MCP 服务器..."
cd ../ark-pets-3d && npm start &
ARKPETS_PID=$!

echo "MCP 服务器已启动"
echo "Blender MCP PID: $BLENDER_PID"
echo "Ark-Pets MCP PID: $ARKPETS_PID"

# 等待用户输入来停止服务器
read -p "按 Enter 键停止所有 MCP 服务器..."

kill $BLENDER_PID $ARKPETS_PID
echo "所有 MCP 服务器已停止"
EOF
    
    chmod +x start-mcp-servers.sh
    
    # 开发环境启动脚本
    cat > start-dev-environment.sh << 'EOF'
#!/bin/bash
echo "🛠️ 启动 Ark-Pets 开发环境..."

# 启动前端开发服务器
echo "启动前端开发服务器..."
cd ark-pets-frontend && npm start &
FRONTEND_PID=$!

# 启动后端服务器
echo "启动后端服务器..."
cd ../ark-pets-backend && ./mvnw spring-boot:run &
BACKEND_PID=$!

# 启动MCP服务器
echo "启动 MCP 服务器..."
./start-mcp-servers.sh &
MCP_PID=$!

echo "开发环境已启动"
echo "前端: http://localhost:3000"
echo "后端: http://localhost:8080"
echo "MCP 服务器已运行"

# 等待用户输入来停止所有服务
read -p "按 Enter 键停止开发环境..."

kill $FRONTEND_PID $BACKEND_PID $MCP_PID
echo "开发环境已停止"
EOF
    
    chmod +x start-dev-environment.sh
    
    print_success "启动脚本创建完成"
}

# 验证安装
verify_installation() {
    print_status "验证安装..."
    
    # 检查MCP服务器文件
    if [ -f "mcp-servers/ark-pets-3d/index.js" ]; then
        print_success "✅ Ark-Pets MCP 服务器"
    else
        print_error "❌ Ark-Pets MCP 服务器"
    fi
    
    # 检查目录结构
    if [ -d "assets/3d-models" ]; then
        print_success "✅ 资源目录结构"
    else
        print_error "❌ 资源目录结构"
    fi
    
    # 检查配置文件
    if [ -f "mcp-config/claude-desktop-config.json" ]; then
        print_success "✅ MCP 配置文件"
    else
        print_error "❌ MCP 配置文件"
    fi
    
    print_success "安装验证完成"
}

# 显示使用说明
show_usage() {
    echo ""
    echo "🎉 Ark-Pets 3D MCP 环境安装完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 重启 Claude Desktop 应用"
    echo "2. 在 Claude 中测试 MCP 连接"
    echo "3. 运行 './start-dev-environment.sh' 启动开发环境"
    echo ""
    echo "🛠️ 可用命令："
    echo "- ./start-mcp-servers.sh      # 启动 MCP 服务器"
    echo "- ./start-dev-environment.sh  # 启动完整开发环境"
    echo ""
    echo "📚 MCP 工具使用示例："
    echo "- create_3d_pet_model: 创建3D宠物模型"
    echo "- create_pet_animation: 制作宠物动画"
    echo "- create_particle_effect: 生成粒子特效"
    echo ""
    echo "🎮 开始你的3D桌宠开发之旅吧！"
}

# 主函数
main() {
    echo "🎮 Ark-Pets 3D MCP 安装程序"
    echo "================================"
    
    check_requirements
    create_directories
    install_mcp_sdk
    clone_mcp_servers
    install_blender_mcp
    install_unity_mcp
    install_arkpets_mcp
    configure_claude_desktop
    download_sample_assets
    create_launch_scripts
    verify_installation
    show_usage
}

# 运行主函数
main "$@"
