const express = require('express');
const cors = require('cors');
const app = express();
const port = 8080;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟宠物数据
const pets = [
  {
    id: 1,
    name: "小橙",
    type: "cat",
    avatar: "🐱",
    level: 5,
    health: 85,
    energy: 70,
    hunger: 30,
    mood: "开心",
    experience: 450,
    status: "active"
  },
  {
    id: 2,
    name: "小白",
    type: "dog",
    avatar: "🐶",
    level: 3,
    health: 90,
    energy: 80,
    hunger: 20,
    mood: "兴奋",
    experience: 280,
    status: "active"
  },
  {
    id: 3,
    name: "小蓝",
    type: "bird",
    avatar: "🐦",
    level: 7,
    health: 75,
    energy: 60,
    hunger: 40,
    mood: "平静",
    experience: 680,
    status: "sleeping"
  }
];

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Ark-Pets Backend is running' });
});

// 获取所有宠物
app.get('/api/pets', (req, res) => {
  res.json({
    success: true,
    data: pets,
    message: '获取宠物列表成功'
  });
});

// 获取单个宠物
app.get('/api/pets/:id', (req, res) => {
  const petId = parseInt(req.params.id);
  const pet = pets.find(p => p.id === petId);
  
  if (!pet) {
    return res.status(404).json({
      success: false,
      message: '宠物不存在'
    });
  }
  
  res.json({
    success: true,
    data: pet,
    message: '获取宠物信息成功'
  });
});

// 启动桌面宠物
app.post('/api/pets/:id/launch', (req, res) => {
  const petId = parseInt(req.params.id);
  const pet = pets.find(p => p.id === petId);
  
  if (!pet) {
    return res.status(404).json({
      success: false,
      message: '宠物不存在'
    });
  }
  
  // 模拟启动过程
  console.log(`🚀 启动桌面宠物: ${pet.name} (${pet.type})`);
  
  res.json({
    success: true,
    data: {
      petId: pet.id,
      processId: Math.floor(Math.random() * 10000),
      status: 'launched',
      message: `桌面宠物 ${pet.name} 启动成功`
    },
    message: '桌面宠物启动成功'
  });
});

// 停止桌面宠物
app.post('/api/pets/:id/stop', (req, res) => {
  const petId = parseInt(req.params.id);
  const pet = pets.find(p => p.id === petId);
  
  if (!pet) {
    return res.status(404).json({
      success: false,
      message: '宠物不存在'
    });
  }
  
  console.log(`⏹️ 停止桌面宠物: ${pet.name}`);
  
  res.json({
    success: true,
    data: {
      petId: pet.id,
      status: 'stopped'
    },
    message: '桌面宠物已停止'
  });
});

// 宠物互动
app.post('/api/pets/:id/interact', (req, res) => {
  const petId = parseInt(req.params.id);
  const { action } = req.body;
  const pet = pets.find(p => p.id === petId);
  
  if (!pet) {
    return res.status(404).json({
      success: false,
      message: '宠物不存在'
    });
  }
  
  // 根据互动类型更新宠物状态
  switch (action) {
    case 'feed':
      pet.hunger = Math.max(0, pet.hunger - 20);
      pet.health = Math.min(100, pet.health + 5);
      pet.mood = "满足";
      break;
    case 'play':
      pet.energy = Math.max(0, pet.energy - 15);
      pet.health = Math.min(100, pet.health + 3);
      pet.mood = "兴奋";
      break;
    case 'pet':
      pet.health = Math.min(100, pet.health + 2);
      pet.mood = "开心";
      break;
    case 'rest':
      pet.energy = Math.min(100, pet.energy + 25);
      pet.mood = "平静";
      break;
    default:
      pet.mood = "困惑";
  }
  
  // 增加经验值
  pet.experience += 10;
  if (pet.experience >= pet.level * 100) {
    pet.level++;
    pet.experience = 0;
  }
  
  console.log(`🎮 宠物互动: ${pet.name} - ${action}`);
  
  res.json({
    success: true,
    data: {
      pet: pet,
      action: action,
      effects: {
        experienceGained: 10,
        levelUp: pet.experience === 0
      }
    },
    message: `与 ${pet.name} 互动成功`
  });
});

// 获取宠物状态
app.get('/api/pets/:id/status', (req, res) => {
  const petId = parseInt(req.params.id);
  const pet = pets.find(p => p.id === petId);
  
  if (!pet) {
    return res.status(404).json({
      success: false,
      message: '宠物不存在'
    });
  }
  
  res.json({
    success: true,
    data: {
      id: pet.id,
      name: pet.name,
      level: pet.level,
      health: pet.health,
      energy: pet.energy,
      hunger: pet.hunger,
      mood: pet.mood,
      experience: pet.experience,
      status: pet.status
    },
    message: '获取宠物状态成功'
  });
});

// 创建新宠物
app.post('/api/pets', (req, res) => {
  const { name, type, avatar } = req.body;
  
  const newPet = {
    id: pets.length + 1,
    name: name || "新宠物",
    type: type || "cat",
    avatar: avatar || "🐱",
    level: 1,
    health: 100,
    energy: 100,
    hunger: 0,
    mood: "开心",
    experience: 0,
    status: "active"
  };
  
  pets.push(newPet);
  
  console.log(`✨ 创建新宠物: ${newPet.name}`);
  
  res.json({
    success: true,
    data: newPet,
    message: '创建宠物成功'
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(port, () => {
  console.log(`🚀 Ark-Pets Backend Server 启动成功!`);
  console.log(`📡 服务地址: http://localhost:${port}`);
  console.log(`🏥 健康检查: http://localhost:${port}/health`);
  console.log(`🐾 宠物API: http://localhost:${port}/api/pets`);
  console.log(`\n🎮 可用的API端点:`);
  console.log(`  GET  /health                    - 健康检查`);
  console.log(`  GET  /api/pets                  - 获取所有宠物`);
  console.log(`  GET  /api/pets/:id              - 获取单个宠物`);
  console.log(`  POST /api/pets/:id/launch       - 启动桌面宠物`);
  console.log(`  POST /api/pets/:id/stop         - 停止桌面宠物`);
  console.log(`  POST /api/pets/:id/interact     - 宠物互动`);
  console.log(`  GET  /api/pets/:id/status       - 获取宠物状态`);
  console.log(`  POST /api/pets                  - 创建新宠物`);
  console.log(`\n🎉 准备好为您的桌面宠物提供服务！`);
});

module.exports = app;
