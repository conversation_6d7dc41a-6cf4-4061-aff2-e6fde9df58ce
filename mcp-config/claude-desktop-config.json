{"mcpServers": {"blender": {"command": "node", "args": ["/path/to/mcp-servers/blender/index.js"], "env": {"BLENDER_PATH": "/Applications/Blender.app/Contents/MacOS/Blender", "BLENDER_VERSION": "4.0", "PROJECT_PATH": "./ark-pets-3d-project", "ASSETS_PATH": "./assets/3d-models", "EXPORT_FORMAT": "FBX"}}, "unity3d": {"command": "node", "args": ["/path/to/mcp-servers/unity3d/index.js"], "env": {"UNITY_PROJECT_PATH": "./ark-pets-unity-project", "UNITY_VERSION": "2023.3", "BUILD_TARGET": "StandaloneWindows64", "QUALITY_LEVEL": "High"}}, "ark-pets-3d": {"command": "node", "args": ["./mcp-servers/ark-pets-3d/index.js"], "env": {"PETS_MODELS_PATH": "./assets/3d-models", "ANIMATIONS_PATH": "./assets/animations", "MATERIALS_PATH": "./assets/materials", "EFFECTS_PATH": "./assets/effects", "AUDIO_PATH": "./assets/audio", "EXPORT_PATH": "./build/desktop-pets", "TEXTURE_QUALITY": "High", "ANIMATION_FPS": "60", "MODEL_LOD_LEVELS": "3"}}, "spine-animation": {"command": "node", "args": ["./mcp-servers/spine-animation/index.js"], "env": {"SPINE_PROJECT_PATH": "./spine-projects", "ATLAS_PATH": "./assets/spine-atlases", "SKELETON_PATH": "./assets/spine-skeletons", "ANIMATION_PATH": "./assets/spine-animations", "EXPORT_FORMAT": "JSON", "TEXTURE_PACKER": "true"}}, "live2d": {"command": "node", "args": ["./mcp-servers/live2d/index.js"], "env": {"LIVE2D_PROJECT_PATH": "./live2d-projects", "MODEL_PATH": "./assets/live2d-models", "MOTION_PATH": "./assets/live2d-motions", "EXPRESSION_PATH": "./assets/live2d-expressions", "PHYSICS_PATH": "./assets/live2d-physics", "EXPORT_VERSION": "4.0"}}, "vfx-particles": {"command": "node", "args": ["./mcp-servers/vfx-particles/index.js"], "env": {"PARTICLE_SYSTEMS_PATH": "./assets/particle-systems", "TEXTURES_PATH": "./assets/particle-textures", "SHADERS_PATH": "./assets/particle-shaders", "PRESETS_PATH": "./assets/particle-presets", "MAX_PARTICLES": "1000", "QUALITY_PRESET": "High"}}, "audio-engine": {"command": "node", "args": ["./mcp-servers/audio-engine/index.js"], "env": {"AUDIO_LIBRARY_PATH": "./assets/audio", "SOUND_EFFECTS_PATH": "./assets/audio/sfx", "MUSIC_PATH": "./assets/audio/music", "VOICE_PATH": "./assets/audio/voice", "AUDIO_FORMAT": "OGG", "SAMPLE_RATE": "44100", "BIT_DEPTH": "16"}}, "ai-behavior": {"command": "node", "args": ["./mcp-servers/ai-behavior/index.js"], "env": {"BEHAVIOR_TREES_PATH": "./assets/behavior-trees", "AI_MODELS_PATH": "./assets/ai-models", "TRAINING_DATA_PATH": "./data/training", "PERSONALITY_CONFIGS_PATH": "./configs/personalities", "LEARNING_RATE": "0.001", "MEMORY_SIZE": "1000"}}, "desktop-integration": {"command": "node", "args": ["./mcp-servers/desktop-integration/index.js"], "env": {"DESKTOP_APP_PATH": "./desktop-app", "JAVAFX_PATH": "./javafx-sdk", "NATIVE_LIBS_PATH": "./native-libs", "CONFIG_PATH": "./configs/desktop", "LOG_PATH": "./logs/desktop", "AUTO_START": "true", "TRANSPARENCY": "true", "ALWAYS_ON_TOP": "true"}}, "asset-pipeline": {"command": "node", "args": ["./mcp-servers/asset-pipeline/index.js"], "env": {"SOURCE_ASSETS_PATH": "./source-assets", "PROCESSED_ASSETS_PATH": "./processed-assets", "CACHE_PATH": "./cache/assets", "OPTIMIZATION_LEVEL": "High", "COMPRESSION_QUALITY": "0.8", "AUTO_OPTIMIZE": "true", "BATCH_SIZE": "10"}}}, "globalSettings": {"logLevel": "info", "timeout": 30000, "retryAttempts": 3, "cacheEnabled": true, "parallelProcessing": true, "maxConcurrentTasks": 4}, "arkPetsConfig": {"projectName": "Ark-<PERSON><PERSON>ced", "version": "4.0.0", "targetPlatforms": ["Windows", "macOS", "Linux"], "renderPipeline": "URP", "qualitySettings": {"textureQuality": "High", "shadowQuality": "High", "antiAliasing": "4x", "anisotropicFiltering": "16x", "particleRaycastBudget": 1024}, "performanceSettings": {"targetFrameRate": 60, "vsyncEnabled": true, "batteryOptimization": true, "thermalThrottling": true}, "featureFlags": {"enablePhysics": true, "enableParticles": true, "enableAudio": true, "enableAI": true, "enableNetworking": false, "enableAnalytics": true}}}