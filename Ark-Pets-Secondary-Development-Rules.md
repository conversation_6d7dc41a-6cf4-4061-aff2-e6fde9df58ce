# Ark-Pets 二次开发项目规则文档

## 项目概述

### 基础信息
- **原项目名称**: Ark-Pets (明日方舟桌宠)
- **原项目地址**: https://github.com/isHarryh/Ark-Pets
- **原项目许可证**: GPL-3.0 License
- **技术栈**: Java + LibGDX + JavaFX + Gradle
- **支持平台**: Windows (主要), macOS/Linux (开发中)
- **项目类型**: 桌面宠物应用程序

### 项目描述
**重要更新**: 本项目现已获取 Ark-Pets 原始源代码，将基于原项目进行**二次开发**。项目将在保持原有核心功能的基础上，进行现代化架构升级，增加AI智能交互、云服务支持等新特性。

### 开发模式说明
- **当前状态**: 二次开发 (Secondary Development) ✅ **已获取源代码**
- **原始项目**: Ark-Pets v3.8.0 (https://github.com/isHarryh/Ark-Pets)
- **源码位置**: `./Ark-Pets/` 目录
- **原始技术栈**: Java + LibGDX + JavaFX + Spine
- **升级方向**: 前后端分离 + 微服务 + AI集成

### 原始项目分析
- **版本**: v3.8.0
- **许可证**: GPL-3.0 License
- **核心技术**:
  - LibGDX 1.11.0 (游戏引擎)
  - Spine ******** (动画系统)
  - JavaFX 17.0.8 (UI界面)
  - JNA 5.12.1 (系统调用)
- **项目结构**:
  - `core/` - 核心逻辑模块
  - `desktop/` - 桌面应用模块
  - `assets/` - 资源文件

## 开发环境要求

### 必需环境
- **Java**: JDK 17 或更高版本
- **构建工具**: Gradle 7.0+
- **IDE**: IntelliJ IDEA (推荐) 或 Eclipse
- **操作系统**: Windows 10+ (主要开发平台)

### 推荐工具
- **版本控制**: Git
- **代码质量**: SonarLint 插件
- **调试工具**: JProfiler 或 VisualVM
- **图像处理**: Spine Editor (用于动画模型)

## 项目结构规范

### 目录结构

#### 前后端分离架构
```
ark-pets-project/
├── frontend/                           # 前端项目 (桌面客户端)
│   ├── core/                          # 核心逻辑模块
│   │   ├── src/main/java/             # 核心Java源码
│   │   │   └── com/arkpets/core/
│   │   │       ├── animation/         # 动画系统
│   │   │       ├── physics/           # 物理引擎
│   │   │       ├── model/             # 数据模型
│   │   │       ├── ai/                # AI交互逻辑
│   │   │       └── utils/             # 工具类
│   │   └── src/main/resources/        # 核心资源文件
│   ├── desktop/                       # 桌面应用模块
│   │   ├── src/main/java/             # 桌面应用源码
│   │   │   └── com/arkpets/desktop/
│   │   │       ├── ui/                # 用户界面
│   │   │       ├── launcher/          # 启动器
│   │   │       ├── tray/              # 系统托盘
│   │   │       ├── config/            # 配置管理
│   │   │       └── client/            # API客户端
│   │   └── src/main/resources/        # 桌面应用资源
│   ├── assets/                        # 静态资源文件
│   │   ├── models/                    # 角色模型文件
│   │   ├── textures/                  # 纹理贴图
│   │   ├── sounds/                    # 音频文件
│   │   └── shaders/                   # 着色器文件
│   ├── gradle/                        # Gradle配置
│   ├── build.gradle                   # 前端构建脚本
│   └── settings.gradle                # 前端项目设置
│
├── backend/                           # 后端项目 (云服务)
│   ├── api-gateway/                   # API网关服务
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/gateway/
│   │   │       ├── config/            # 网关配置
│   │   │       ├── filter/            # 过滤器
│   │   │       └── route/             # 路由配置
│   │   └── src/main/resources/
│   │
│   ├── auth-service/                  # 认证授权服务
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/auth/
│   │   │       ├── controller/        # 控制器
│   │   │       ├── service/           # 业务逻辑
│   │   │       ├── repository/        # 数据访问
│   │   │       ├── entity/            # 实体类
│   │   │       ├── dto/               # 数据传输对象
│   │   │       ├── security/          # 安全配置
│   │   │       └── config/            # 配置类
│   │   └── src/main/resources/
│   │
│   ├── model-service/                 # 模型管理服务
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/model/
│   │   │       ├── controller/        # 模型API控制器
│   │   │       ├── service/           # 模型业务逻辑
│   │   │       ├── repository/        # 模型数据访问
│   │   │       ├── entity/            # 模型实体
│   │   │       ├── dto/               # 模型DTO
│   │   │       ├── storage/           # 文件存储
│   │   │       └── validator/         # 数据验证
│   │   └── src/main/resources/
│   │
│   ├── user-service/                  # 用户数据服务
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/user/
│   │   │       ├── controller/        # 用户API控制器
│   │   │       ├── service/           # 用户业务逻辑
│   │   │       ├── repository/        # 用户数据访问
│   │   │       ├── entity/            # 用户实体
│   │   │       ├── dto/               # 用户DTO
│   │   │       └── sync/              # 数据同步
│   │   └── src/main/resources/
│   │
│   ├── ai-service/                    # AI语言模型服务 (新增)
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/ai/
│   │   │       ├── controller/        # AI API控制器
│   │   │       │   ├── ChatController.java
│   │   │       │   ├── ModelController.java
│   │   │       │   └── ConversationController.java
│   │   │       ├── service/           # AI业务逻辑
│   │   │       │   ├── ChatService.java
│   │   │       │   ├── ModelProviderService.java
│   │   │       │   ├── ConversationService.java
│   │   │       │   ├── PromptService.java
│   │   │       │   └── PersonalityService.java
│   │   │       ├── provider/          # AI模型提供商
│   │   │       │   ├── OpenAIProvider.java
│   │   │       │   ├── ClaudeProvider.java
│   │   │       │   ├── LocalModelProvider.java
│   │   │       │   └── ModelProviderFactory.java
│   │   │       ├── entity/            # AI实体类
│   │   │       │   ├── Conversation.java
│   │   │       │   ├── Message.java
│   │   │       │   ├── ModelConfig.java
│   │   │       │   └── Personality.java
│   │   │       ├── dto/               # AI数据传输对象
│   │   │       │   ├── ChatRequest.java
│   │   │       │   ├── ChatResponse.java
│   │   │       │   ├── ModelInfo.java
│   │   │       │   └── ConversationSummary.java
│   │   │       ├── config/            # AI配置
│   │   │       │   ├── AIConfig.java
│   │   │       │   └── ModelProviderConfig.java
│   │   │       ├── repository/        # AI数据访问
│   │   │       │   ├── ConversationRepository.java
│   │   │       │   ├── MessageRepository.java
│   │   │       │   └── PersonalityRepository.java
│   │   │       ├── utils/             # AI工具类
│   │   │       │   ├── TokenCounter.java
│   │   │       │   ├── PromptTemplate.java
│   │   │       │   └── ResponseParser.java
│   │   │       └── exception/         # AI异常处理
│   │   │           ├── AIServiceException.java
│   │   │           ├── ModelUnavailableException.java
│   │   │           └── TokenLimitExceededException.java
│   │   └── src/main/resources/
│   │       ├── prompts/               # 提示词模板
│   │       │   ├── character/         # 角色提示词
│   │       │   ├── system/            # 系统提示词
│   │       │   └── templates/         # 通用模板
│   │       └── application.yml        # AI服务配置
│   │
│   ├── config-service/                # 配置同步服务
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/config/
│   │   │       ├── controller/        # 配置API控制器
│   │   │       ├── service/           # 配置业务逻辑
│   │   │       ├── repository/        # 配置数据访问
│   │   │       ├── entity/            # 配置实体
│   │   │       ├── dto/               # 配置DTO
│   │   │       └── sync/              # 配置同步
│   │   └── src/main/resources/
│   │
│   ├── notification-service/          # 通知服务
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/notification/
│   │   │       ├── controller/        # 通知API控制器
│   │   │       ├── service/           # 通知业务逻辑
│   │   │       ├── websocket/         # WebSocket处理
│   │   │       ├── entity/            # 通知实体
│   │   │       └── dto/               # 通知DTO
│   │   └── src/main/resources/
│   │
│   ├── shared/                        # 共享模块
│   │   ├── src/main/java/
│   │   │   └── com/arkpets/shared/
│   │   │       ├── dto/               # 共享DTO
│   │   │       ├── entity/            # 共享实体
│   │   │       ├── exception/         # 共享异常
│   │   │       ├── utils/             # 共享工具类
│   │   │       ├── config/            # 共享配置
│   │   │       └── constants/         # 常量定义
│   │   └── src/main/resources/
│   │
│   ├── docker/                        # Docker配置
│   │   ├── docker-compose.yml         # 开发环境
│   │   ├── docker-compose.prod.yml    # 生产环境
│   │   └── Dockerfile.template        # Dockerfile模板
│   │
│   ├── k8s/                          # Kubernetes配置
│   │   ├── namespace.yaml            # 命名空间
│   │   ├── configmap.yaml            # 配置映射
│   │   ├── secret.yaml               # 密钥
│   │   ├── services/                 # 服务配置
│   │   └── deployments/              # 部署配置
│   │
│   ├── scripts/                      # 部署脚本
│   │   ├── build.sh                  # 构建脚本
│   │   ├── deploy.sh                 # 部署脚本
│   │   └── migrate.sh                # 数据库迁移
│   │
│   ├── gradle/                       # Gradle配置
│   ├── build.gradle                  # 后端构建脚本
│   └── settings.gradle               # 后端项目设置
│
├── docs/                             # 项目文档
│   ├── api/                          # API文档
│   ├── architecture/                 # 架构文档
│   ├── deployment/                   # 部署文档
│   └── development/                  # 开发文档
│
├── tools/                            # 开发工具
│   ├── model-converter/              # 模型转换工具
│   ├── asset-optimizer/              # 资源优化工具
│   └── test-data-generator/          # 测试数据生成器
│
└── README.md                         # 项目说明
```

### 模块划分

#### 前端模块 (Frontend)
- **core**: 核心游戏逻辑，平台无关，包含动画、物理、AI交互等
- **desktop**: 桌面平台特定实现，包含UI、启动器、系统托盘等
- **assets**: 所有静态资源文件，包含模型、纹理、音频、着色器等

#### 后端模块 (Backend)
- **api-gateway**: API网关服务，统一入口和路由
- **auth-service**: 认证授权服务，用户身份验证和权限管理
- **model-service**: 模型管理服务，角色模型的存储和分发
- **user-service**: 用户数据服务，用户配置和桌宠状态管理
- **ai-service**: AI语言模型服务，智能对话和角色交互 (新增)
- **config-service**: 配置同步服务，全局配置和更新管理
- **notification-service**: 通知服务，实时消息推送
- **shared**: 共享模块，通用组件和工具类

## 代码规范

### Java 编码规范
1. **命名约定**
   - 类名: PascalCase (如 `PetController`)
   - 方法名: camelCase (如 `updateAnimation`)
   - 常量: UPPER_SNAKE_CASE (如 `MAX_FRAME_RATE`)
   - 包名: 小写字母 + 点分隔 (如 `com.project.pets.core`)

2. **代码格式**
   - 缩进: 4个空格
   - 行长度: 最大120字符
   - 大括号: K&R风格
   - 导入: 按字母顺序排列，分组

3. **注释规范**
   - 类和公共方法必须有JavaDoc注释
   - 复杂逻辑需要行内注释
   - TODO/FIXME标记需要包含负责人和日期

### 示例代码格式
```java
/**
 * 宠物控制器类，负责管理桌宠的行为和状态
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
public class PetController {
    private static final int DEFAULT_SPEED = 100;
    
    /**
     * 更新宠物动画状态
     * 
     * @param deltaTime 时间增量
     * @return 是否更新成功
     */
    public boolean updateAnimation(float deltaTime) {
        // 实现逻辑
        return true;
    }
}
```

## 版本控制规范

### Git 工作流
- **主分支**: `main` - 稳定发布版本
- **开发分支**: `develop` - 开发集成分支
- **功能分支**: `feature/功能名称` - 新功能开发
- **修复分支**: `hotfix/问题描述` - 紧急修复
- **发布分支**: `release/版本号` - 发布准备

### 提交信息规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

**示例**:
```
feat(animation): 添加新的宠物行走动画

- 实现基于Spine的行走动画系统
- 支持多方向移动动画
- 添加动画状态机管理

Closes #123
```

## 依赖管理

### 核心依赖
```gradle
dependencies {
    // LibGDX 核心
    implementation "com.badlogicgames.gdx:gdx:$gdxVersion"
    implementation "com.badlogicgames.gdx:gdx-backend-lwjgl3:$gdxVersion"
    
    // JavaFX (用于UI)
    implementation "org.openjfx:javafx-controls:$javafxVersion"
    implementation "org.openjfx:javafx-fxml:$javafxVersion"
    
    // Spine 动画
    implementation "com.esotericsoftware.spine:spine-libgdx:$spineVersion"
    
    // 日志
    implementation "org.slf4j:slf4j-api:$slf4jVersion"
    implementation "ch.qos.logback:logback-classic:$logbackVersion"
    
    // 测试
    testImplementation "org.junit.jupiter:junit-jupiter:$junitVersion"
    testImplementation "org.mockito:mockito-core:$mockitoVersion"
}
```

### 版本管理
- 使用 `gradle.properties` 统一管理版本号
- 定期更新依赖到稳定版本
- 避免使用快照版本依赖

## 构建和部署

### 构建命令
```bash
# 清理构建
./gradlew clean

# 编译项目
./gradlew build

# 运行桌面版本
./gradlew desktop:run

# 生成发布包
./gradlew desktop:dist

# 运行测试
./gradlew test
```

### 打包规范
- **开发版本**: `项目名-版本号-SNAPSHOT.jar`
- **发布版本**: `项目名-版本号.jar`
- **安装包**: `项目名-Setup-版本号.exe` (Windows)

## 测试规范

### 测试策略
1. **单元测试**: 覆盖率 ≥ 80%
2. **集成测试**: 关键功能模块
3. **性能测试**: 内存使用和帧率
4. **兼容性测试**: 不同操作系统版本

### 测试文件组织
```
src/test/java/
├── unit/          # 单元测试
├── integration/   # 集成测试
└── performance/   # 性能测试
```

## 许可证和法律

### 开源许可证
- **遵循原项目**: GPL-3.0 License
- **衍生项目**: 必须使用相同或兼容许可证
- **商业使用**: 需要开源代码

### 版权声明
```java
/*
 * Copyright (C) 2025 Your Name/Organization
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 */
```

### 第三方资源
- **模型文件**: 确保有合法使用权
- **音频素材**: 注意版权限制
- **图像资源**: 遵循原始许可证

## 文档规范

### 必需文档
- `README.md`: 项目介绍和快速开始
- `CHANGELOG.md`: 版本更新日志
- `CONTRIBUTING.md`: 贡献指南
- `LICENSE`: 许可证文件
- `docs/API.md`: API文档
- `docs/DEPLOYMENT.md`: 部署指南

### 文档格式
- 使用 Markdown 格式
- 包含代码示例
- 提供截图说明
- 支持多语言版本

## 性能优化

### 性能指标
- **内存使用**: < 200MB
- **CPU占用**: < 5% (空闲时)
- **帧率**: ≥ 60 FPS
- **启动时间**: < 3秒

### 优化策略
1. **资源管理**: 及时释放不用的资源
2. **动画优化**: 使用对象池减少GC
3. **渲染优化**: 批量渲染和纹理合并
4. **内存优化**: 避免内存泄漏

## 后端服务接口规范

### 服务架构设计

#### 微服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   桌宠客户端     │    │   API网关       │    │   认证服务       │
│   (Ark-Pets)   │◄──►│   (Gateway)     │◄──►│   (Auth)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
        ┌─────────────────────────────────────────────────────┐
        │                 核心服务集群                         │
        ├─────────────────┬─────────────────┬─────────────────┤
        │   模型管理服务   │   用户数据服务   │   配置同步服务   │
        │   (Model)       │   (User)        │   (Config)      │
        └─────────────────┴─────────────────┴─────────────────┘
```

#### 技术栈选择
- **API框架**: Spring Boot 3.x + Spring WebFlux (响应式)
- **数据库**: PostgreSQL (主) + Redis (缓存)
- **消息队列**: RabbitMQ 或 Apache Kafka
- **服务发现**: Consul 或 Eureka
- **API网关**: Spring Cloud Gateway
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)

### API 设计规范

#### RESTful API 标准
```http
# 资源命名规范
GET    /api/v1/models                    # 获取模型列表
GET    /api/v1/models/{id}               # 获取特定模型
POST   /api/v1/models                    # 创建新模型
PUT    /api/v1/models/{id}               # 更新模型
DELETE /api/v1/models/{id}               # 删除模型

# 查询参数规范
GET /api/v1/models?page=1&size=20&sort=name,asc&filter=character:amiya
```

#### HTTP 状态码规范
```
200 OK              - 请求成功
201 Created         - 资源创建成功
204 No Content      - 删除成功
400 Bad Request     - 请求参数错误
401 Unauthorized    - 未认证
403 Forbidden       - 无权限
404 Not Found       - 资源不存在
409 Conflict        - 资源冲突
422 Unprocessable   - 业务逻辑错误
429 Too Many Req    - 请求频率限制
500 Internal Error  - 服务器内部错误
503 Service Unavail - 服务不可用
```

#### 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "timestamp": "2025-01-01T12:00:00Z",
  "data": {
    // 实际数据内容
  },
  "pagination": {
    "page": 1,
    "size": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### 核心服务接口

#### 1. 模型管理服务 (Model Service)

**模型信息接口**
```java
@RestController
@RequestMapping("/api/v1/models")
public class ModelController {

    /**
     * 获取模型列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ModelInfo>>> getModels(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String character,
            @RequestParam(required = false) String category) {
        // 实现逻辑
    }

    /**
     * 获取模型详情
     */
    @GetMapping("/{modelId}")
    public ResponseEntity<ApiResponse<ModelDetail>> getModel(
            @PathVariable String modelId) {
        // 实现逻辑
    }

    /**
     * 下载模型文件
     */
    @GetMapping("/{modelId}/download")
    public ResponseEntity<Resource> downloadModel(
            @PathVariable String modelId,
            @RequestHeader("Authorization") String token) {
        // 实现逻辑
    }
}
```

**模型数据结构**
```java
public class ModelInfo {
    private String id;
    private String name;
    private String character;
    private String category;
    private String version;
    private long fileSize;
    private String downloadUrl;
    private String thumbnailUrl;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    // getters and setters
}
```

#### 2. 用户数据服务 (User Service)

**用户配置接口**
```java
@RestController
@RequestMapping("/api/v1/users")
public class UserController {

    /**
     * 获取用户配置
     */
    @GetMapping("/{userId}/config")
    public ResponseEntity<ApiResponse<UserConfig>> getUserConfig(
            @PathVariable String userId) {
        // 实现逻辑
    }

    /**
     * 更新用户配置
     */
    @PutMapping("/{userId}/config")
    public ResponseEntity<ApiResponse<Void>> updateUserConfig(
            @PathVariable String userId,
            @RequestBody @Valid UserConfigRequest request) {
        // 实现逻辑
    }

    /**
     * 同步桌宠状态
     */
    @PostMapping("/{userId}/pets/sync")
    public ResponseEntity<ApiResponse<Void>> syncPetStatus(
            @PathVariable String userId,
            @RequestBody @Valid PetStatusRequest request) {
        // 实现逻辑
    }
}
```

#### 3. 配置同步服务 (Config Service)

**配置同步接口**
```java
@RestController
@RequestMapping("/api/v1/config")
public class ConfigController {

    /**
     * 获取全局配置
     */
    @GetMapping("/global")
    public ResponseEntity<ApiResponse<GlobalConfig>> getGlobalConfig() {
        // 实现逻辑
    }

    /**
     * 检查更新
     */
    @GetMapping("/updates")
    public ResponseEntity<ApiResponse<UpdateInfo>> checkUpdates(
            @RequestParam String currentVersion,
            @RequestParam String platform) {
        // 实现逻辑
    }

    /**
     * 获取公告信息
     */
    @GetMapping("/announcements")
    public ResponseEntity<ApiResponse<List<Announcement>>> getAnnouncements(
            @RequestParam(required = false) String locale) {
        // 实现逻辑
    }
}
```

#### 4. AI语言模型服务 (AI Service) - 新增

**AI聊天接口**
```java
@RestController
@RequestMapping("/api/v1/ai")
@Validated
public class ChatController {

    private final ChatService chatService;
    private final ConversationService conversationService;

    /**
     * 发送聊天消息
     */
    @PostMapping("/chat")
    public ResponseEntity<ApiResponse<ChatResponse>> chat(
            @RequestBody @Valid ChatRequest request,
            @RequestHeader("Authorization") String token) {

        String userId = extractUserIdFromToken(token);
        ChatResponse response = chatService.processMessage(userId, request);

        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 流式聊天 (Server-Sent Events)
     */
    @GetMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChat(
            @RequestParam String conversationId,
            @RequestParam String message,
            @RequestHeader("Authorization") String token) {

        String userId = extractUserIdFromToken(token);
        return chatService.streamMessage(userId, conversationId, message);
    }

    /**
     * 获取对话历史
     */
    @GetMapping("/conversations/{conversationId}/messages")
    public ResponseEntity<ApiResponse<List<MessageDto>>> getConversationMessages(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "50") int size,
            @RequestHeader("Authorization") String token) {

        String userId = extractUserIdFromToken(token);
        List<MessageDto> messages = conversationService.getMessages(
            userId, conversationId, page, size);

        return ResponseEntity.ok(ApiResponse.success(messages));
    }

    /**
     * 创建新对话
     */
    @PostMapping("/conversations")
    public ResponseEntity<ApiResponse<ConversationDto>> createConversation(
            @RequestBody @Valid CreateConversationRequest request,
            @RequestHeader("Authorization") String token) {

        String userId = extractUserIdFromToken(token);
        ConversationDto conversation = conversationService.createConversation(userId, request);

        return ResponseEntity.ok(ApiResponse.success(conversation));
    }

    /**
     * 获取用户对话列表
     */
    @GetMapping("/conversations")
    public ResponseEntity<ApiResponse<List<ConversationSummary>>> getUserConversations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader("Authorization") String token) {

        String userId = extractUserIdFromToken(token);
        List<ConversationSummary> conversations = conversationService.getUserConversations(
            userId, page, size);

        return ResponseEntity.ok(ApiResponse.success(conversations));
    }

    /**
     * 删除对话
     */
    @DeleteMapping("/conversations/{conversationId}")
    public ResponseEntity<ApiResponse<Void>> deleteConversation(
            @PathVariable String conversationId,
            @RequestHeader("Authorization") String token) {

        String userId = extractUserIdFromToken(token);
        conversationService.deleteConversation(userId, conversationId);

        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
```

**AI模型管理接口**
```java
@RestController
@RequestMapping("/api/v1/ai/models")
public class ModelController {

    private final ModelProviderService modelProviderService;

    /**
     * 获取可用AI模型列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<AIModelInfo>>> getAvailableModels() {
        List<AIModelInfo> models = modelProviderService.getAvailableModels();
        return ResponseEntity.ok(ApiResponse.success(models));
    }

    /**
     * 获取模型详细信息
     */
    @GetMapping("/{modelId}")
    public ResponseEntity<ApiResponse<AIModelDetail>> getModelDetail(
            @PathVariable String modelId) {
        AIModelDetail detail = modelProviderService.getModelDetail(modelId);
        return ResponseEntity.ok(ApiResponse.success(detail));
    }

    /**
     * 测试模型连接
     */
    @PostMapping("/{modelId}/test")
    public ResponseEntity<ApiResponse<ModelTestResult>> testModel(
            @PathVariable String modelId,
            @RequestBody @Valid ModelTestRequest request) {

        ModelTestResult result = modelProviderService.testModel(modelId, request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 获取模型使用统计
     */
    @GetMapping("/{modelId}/stats")
    public ResponseEntity<ApiResponse<ModelUsageStats>> getModelStats(
            @PathVariable String modelId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        ModelUsageStats stats = modelProviderService.getModelStats(
            modelId, startDate, endDate);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
}
```

### WebSocket 实时通信

#### 连接管理
```java
@Component
@ServerEndpoint("/ws/pets/{userId}")
public class PetWebSocketHandler {

    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        // 建立连接，用户认证
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        // 处理客户端消息
        // 支持：心跳、状态同步、实时配置更新
    }

    @OnClose
    public void onClose(Session session) {
        // 清理连接资源
    }

    @OnError
    public void onError(Session session, Throwable error) {
        // 错误处理
    }
}
```

#### 消息格式
```json
{
  "type": "pet_status_update",
  "userId": "user123",
  "petId": "pet456",
  "data": {
    "position": {"x": 100, "y": 200},
    "animation": "idle",
    "health": 85,
    "mood": "happy"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 数据库设计

#### 核心表结构
```sql
-- 模型信息表
CREATE TABLE models (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    character VARCHAR(50) NOT NULL,
    category VARCHAR(30) NOT NULL,
    version VARCHAR(20) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    thumbnail_url VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户配置表
CREATE TABLE user_configs (
    user_id VARCHAR(36) PRIMARY KEY,
    display_settings JSONB NOT NULL,
    behavior_settings JSONB NOT NULL,
    pet_preferences JSONB NOT NULL,
    auto_start BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 桌宠状态表
CREATE TABLE pet_instances (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    model_id VARCHAR(36) NOT NULL,
    name VARCHAR(50),
    position_x INTEGER DEFAULT 0,
    position_y INTEGER DEFAULT 0,
    current_animation VARCHAR(50),
    health INTEGER DEFAULT 100,
    mood VARCHAR(20) DEFAULT 'neutral',
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (model_id) REFERENCES models(id)
);

-- AI对话表
CREATE TABLE conversations (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    pet_id VARCHAR(36),
    title VARCHAR(200),
    personality_id VARCHAR(36),
    model_provider VARCHAR(50) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    context_summary TEXT,
    total_messages INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (pet_id) REFERENCES pet_instances(id)
);

-- AI消息表
CREATE TABLE messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    role VARCHAR(20) NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    tokens INTEGER DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- AI角色性格表
CREATE TABLE personalities (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    character_name VARCHAR(100), -- 对应的明日方舟角色名
    description TEXT,
    system_prompt TEXT NOT NULL,
    personality_traits JSONB,
    speaking_style JSONB,
    background_story TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI模型配置表
CREATE TABLE ai_model_configs (
    id VARCHAR(36) PRIMARY KEY,
    provider VARCHAR(50) NOT NULL, -- 'openai', 'claude', 'local'
    model_name VARCHAR(100) NOT NULL,
    api_endpoint VARCHAR(255),
    api_key_encrypted VARCHAR(500),
    max_tokens INTEGER DEFAULT 4096,
    temperature DECIMAL(3,2) DEFAULT 0.7,
    top_p DECIMAL(3,2) DEFAULT 1.0,
    frequency_penalty DECIMAL(3,2) DEFAULT 0.0,
    presence_penalty DECIMAL(3,2) DEFAULT 0.0,
    is_enabled BOOLEAN DEFAULT TRUE,
    rate_limit_per_minute INTEGER DEFAULT 60,
    cost_per_1k_tokens DECIMAL(10,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI使用统计表
CREATE TABLE ai_usage_stats (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    model_config_id VARCHAR(36) NOT NULL,
    date DATE NOT NULL,
    total_requests INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (model_config_id) REFERENCES ai_model_configs(id),
    UNIQUE(user_id, model_config_id, date)
);
```

### 安全规范

#### 认证和授权
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/v1/public/**").permitAll()
                .requestMatchers("/api/v1/models/**").hasRole("USER")
                .requestMatchers("/api/v1/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2.jwt())
            .build();
    }
}
```

#### API 限流
```java
@Component
public class RateLimitingFilter implements Filter {

    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                        FilterChain chain) throws IOException, ServletException {

        String clientId = getClientId(request);
        String key = "rate_limit:" + clientId;

        // 使用 Redis 实现滑动窗口限流
        // 每分钟最多 100 次请求
        if (isRateLimited(key, 100, 60)) {
            ((HttpServletResponse) response).setStatus(429);
            return;
        }

        chain.doFilter(request, response);
    }
}
```

### 监控和日志

#### 健康检查
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 检查数据库连接
        // 检查 Redis 连接
        // 检查文件存储
        // 检查外部服务依赖

        return Health.up()
            .withDetail("database", "UP")
            .withDetail("redis", "UP")
            .withDetail("storage", "UP")
            .build();
    }
}
```

#### 性能监控
```java
@RestController
public class MetricsController {

    private final MeterRegistry meterRegistry;

    @GetMapping("/metrics/custom")
    public Map<String, Object> getCustomMetrics() {
        return Map.of(
            "active_users", getUserCount(),
            "active_pets", getPetCount(),
            "model_downloads", getDownloadCount(),
            "api_response_time", getAverageResponseTime()
        );
    }
}
```

### 部署和运维

#### Docker 配置
```dockerfile
# Dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY target/ark-pets-backend.jar app.jar

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### Kubernetes 部署
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ark-pets-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ark-pets-backend
  template:
    metadata:
      labels:
        app: ark-pets-backend
    spec:
      containers:
      - name: backend
        image: ark-pets-backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

### 客户端集成

#### HTTP 客户端配置
```java
@Configuration
public class HttpClientConfig {

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 设置连接超时和读取超时
        HttpComponentsClientHttpRequestFactory factory =
            new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(10000);
        restTemplate.setRequestFactory(factory);

        // 添加拦截器
        restTemplate.getInterceptors().add(new AuthInterceptor());
        restTemplate.getInterceptors().add(new LoggingInterceptor());

        return restTemplate;
    }
}
```

#### API 客户端服务
```java
@Service
public class ArkPetsApiClient {

    private final RestTemplate restTemplate;
    private final String baseUrl;

    public ArkPetsApiClient(RestTemplate restTemplate,
                           @Value("${arkpets.api.base-url}") String baseUrl) {
        this.restTemplate = restTemplate;
        this.baseUrl = baseUrl;
    }

    /**
     * 获取模型列表
     */
    public CompletableFuture<List<ModelInfo>> getModels(ModelQuery query) {
        return CompletableFuture.supplyAsync(() -> {
            String url = baseUrl + "/api/v1/models";
            UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url)
                .queryParam("page", query.getPage())
                .queryParam("size", query.getSize());

            if (query.getCharacter() != null) {
                builder.queryParam("character", query.getCharacter());
            }

            ResponseEntity<ApiResponse<List<ModelInfo>>> response =
                restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<ApiResponse<List<ModelInfo>>>() {}
                );

            return response.getBody().getData();
        });
    }

    /**
     * 下载模型文件
     */
    public CompletableFuture<Void> downloadModel(String modelId, Path targetPath) {
        return CompletableFuture.runAsync(() -> {
            String url = baseUrl + "/api/v1/models/" + modelId + "/download";

            ResponseEntity<Resource> response = restTemplate.exchange(
                url, HttpMethod.GET, null, Resource.class);

            try (InputStream inputStream = response.getBody().getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(targetPath.toFile())) {

                inputStream.transferTo(outputStream);

            } catch (IOException e) {
                throw new RuntimeException("Failed to download model", e);
            }
        });
    }

    /**
     * 同步用户配置
     */
    public CompletableFuture<Void> syncUserConfig(String userId, UserConfig config) {
        return CompletableFuture.runAsync(() -> {
            String url = baseUrl + "/api/v1/users/" + userId + "/config";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<UserConfig> request = new HttpEntity<>(config, headers);

            restTemplate.exchange(url, HttpMethod.PUT, request, Void.class);
        });
    }
}
```

#### WebSocket 客户端
```java
@Component
public class PetWebSocketClient {

    private WebSocketSession session;
    private final ObjectMapper objectMapper;

    @EventListener
    public void handleWebSocketConnectEvent(SessionConnectedEvent event) {
        logger.info("WebSocket connected: {}", event.getMessage());
    }

    @EventListener
    public void handleWebSocketDisconnectEvent(SessionDisconnectEvent event) {
        logger.info("WebSocket disconnected: {}", event.getMessage());
    }

    /**
     * 发送桌宠状态更新
     */
    public void sendPetStatusUpdate(PetStatusUpdate update) {
        if (session != null && session.isOpen()) {
            try {
                String message = objectMapper.writeValueAsString(update);
                session.sendMessage(new TextMessage(message));
            } catch (Exception e) {
                logger.error("Failed to send pet status update", e);
            }
        }
    }

    /**
     * 处理服务器消息
     */
    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {
        try {
            JsonNode jsonNode = objectMapper.readTree(message.getPayload());
            String messageType = jsonNode.get("type").asText();

            switch (messageType) {
                case "config_update":
                    handleConfigUpdate(jsonNode);
                    break;
                case "model_update":
                    handleModelUpdate(jsonNode);
                    break;
                case "announcement":
                    handleAnnouncement(jsonNode);
                    break;
                default:
                    logger.warn("Unknown message type: {}", messageType);
            }
        } catch (Exception e) {
            logger.error("Failed to handle WebSocket message", e);
        }
    }
}
```

#### 离线模式支持
```java
@Service
public class OfflineModeService {

    private final LocalStorageService localStorageService;
    private final NetworkStatusService networkStatusService;

    /**
     * 检查网络状态并决定使用在线或离线模式
     */
    public <T> CompletableFuture<T> executeWithFallback(
            Supplier<CompletableFuture<T>> onlineOperation,
            Supplier<T> offlineOperation) {

        if (networkStatusService.isOnline()) {
            return onlineOperation.get()
                .exceptionally(throwable -> {
                    logger.warn("Online operation failed, falling back to offline mode", throwable);
                    return offlineOperation.get();
                });
        } else {
            return CompletableFuture.completedFuture(offlineOperation.get());
        }
    }

    /**
     * 缓存数据到本地存储
     */
    public void cacheData(String key, Object data) {
        try {
            String jsonData = objectMapper.writeValueAsString(data);
            localStorageService.store(key, jsonData);
        } catch (Exception e) {
            logger.error("Failed to cache data for key: {}", key, e);
        }
    }

    /**
     * 从本地存储读取缓存数据
     */
    public <T> Optional<T> getCachedData(String key, Class<T> type) {
        try {
            String jsonData = localStorageService.retrieve(key);
            if (jsonData != null) {
                return Optional.of(objectMapper.readValue(jsonData, type));
            }
        } catch (Exception e) {
            logger.error("Failed to retrieve cached data for key: {}", key, e);
        }
        return Optional.empty();
    }
}
```

### 数据同步策略

#### 增量同步
```java
@Service
public class DataSyncService {

    private final ArkPetsApiClient apiClient;
    private final LocalDataService localDataService;

    /**
     * 执行增量数据同步
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void performIncrementalSync() {
        try {
            // 获取本地最后同步时间
            LocalDateTime lastSyncTime = localDataService.getLastSyncTime();

            // 从服务器获取增量更新
            CompletableFuture<SyncData> syncDataFuture =
                apiClient.getIncrementalUpdates(lastSyncTime);

            syncDataFuture.thenAccept(syncData -> {
                // 应用模型更新
                if (!syncData.getModelUpdates().isEmpty()) {
                    localDataService.applyModelUpdates(syncData.getModelUpdates());
                }

                // 应用配置更新
                if (!syncData.getConfigUpdates().isEmpty()) {
                    localDataService.applyConfigUpdates(syncData.getConfigUpdates());
                }

                // 更新最后同步时间
                localDataService.updateLastSyncTime(LocalDateTime.now());

                logger.info("Incremental sync completed successfully");
            }).exceptionally(throwable -> {
                logger.error("Incremental sync failed", throwable);
                return null;
            });

        } catch (Exception e) {
            logger.error("Failed to perform incremental sync", e);
        }
    }

    /**
     * 执行完整数据同步
     */
    public CompletableFuture<Void> performFullSync() {
        return CompletableFuture.runAsync(() -> {
            try {
                // 清除本地缓存
                localDataService.clearCache();

                // 重新下载所有数据
                List<ModelInfo> models = apiClient.getModels(new ModelQuery()).get();
                UserConfig userConfig = apiClient.getUserConfig(getCurrentUserId()).get();
                GlobalConfig globalConfig = apiClient.getGlobalConfig().get();

                // 保存到本地
                localDataService.saveModels(models);
                localDataService.saveUserConfig(userConfig);
                localDataService.saveGlobalConfig(globalConfig);

                // 更新同步时间
                localDataService.updateLastSyncTime(LocalDateTime.now());

                logger.info("Full sync completed successfully");

            } catch (Exception e) {
                logger.error("Full sync failed", e);
                throw new RuntimeException(e);
            }
        });
    }
}
```

#### 冲突解决策略
```java
@Component
public class ConflictResolver {

    /**
     * 解决配置冲突
     */
    public UserConfig resolveConfigConflict(UserConfig localConfig,
                                          UserConfig serverConfig) {
        UserConfig resolvedConfig = new UserConfig();

        // 服务器配置优先级更高
        resolvedConfig.setDisplaySettings(
            serverConfig.getDisplaySettings() != null ?
            serverConfig.getDisplaySettings() : localConfig.getDisplaySettings()
        );

        // 本地行为设置优先（用户个人偏好）
        resolvedConfig.setBehaviorSettings(localConfig.getBehaviorSettings());

        // 合并宠物偏好设置
        Map<String, Object> mergedPreferences = new HashMap<>();
        if (localConfig.getPetPreferences() != null) {
            mergedPreferences.putAll(localConfig.getPetPreferences());
        }
        if (serverConfig.getPetPreferences() != null) {
            mergedPreferences.putAll(serverConfig.getPetPreferences());
        }
        resolvedConfig.setPetPreferences(mergedPreferences);

        return resolvedConfig;
    }

    /**
     * 解决模型版本冲突
     */
    public ModelInfo resolveModelConflict(ModelInfo localModel,
                                        ModelInfo serverModel) {
        // 比较版本号，选择更新的版本
        if (compareVersions(serverModel.getVersion(), localModel.getVersion()) > 0) {
            return serverModel;
        }
        return localModel;
    }

    private int compareVersions(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int maxLength = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < maxLength; i++) {
            int v1Part = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2Part = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1Part != v2Part) {
                return Integer.compare(v1Part, v2Part);
            }
        }

        return 0;
    }
}
```

### AI服务核心业务逻辑

#### AI聊天服务实现
```java
@Service
@Transactional
public class ChatService {

    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;
    private final ModelProviderFactory modelProviderFactory;
    private final PersonalityService personalityService;
    private final TokenCounter tokenCounter;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 处理聊天消息
     */
    public ChatResponse processMessage(String userId, ChatRequest request) {
        // 1. 验证和获取对话
        Conversation conversation = getOrCreateConversation(userId, request);

        // 2. 构建消息上下文
        List<Message> contextMessages = buildMessageContext(conversation, request.getMessage());

        // 3. 获取AI模型提供商
        ModelProvider provider = modelProviderFactory.getProvider(conversation.getModelProvider());

        // 4. 调用AI模型
        AIResponse aiResponse = provider.chat(contextMessages, conversation.getModelConfig());

        // 5. 保存用户消息和AI回复
        Message userMessage = saveUserMessage(conversation, request.getMessage());
        Message assistantMessage = saveAssistantMessage(conversation, aiResponse.getContent());

        // 6. 更新对话统计
        updateConversationStats(conversation, aiResponse.getTokens());

        // 7. 更新使用统计
        updateUsageStats(userId, conversation.getModelProvider(), aiResponse.getTokens());

        return ChatResponse.builder()
            .conversationId(conversation.getId())
            .messageId(assistantMessage.getId())
            .content(aiResponse.getContent())
            .tokens(aiResponse.getTokens())
            .model(conversation.getModelName())
            .timestamp(assistantMessage.getCreatedAt())
            .build();
    }

    /**
     * 流式聊天处理
     */
    public SseEmitter streamMessage(String userId, String conversationId, String message) {
        SseEmitter emitter = new SseEmitter(30000L);

        CompletableFuture.runAsync(() -> {
            try {
                Conversation conversation = conversationRepository.findByIdAndUserId(
                    conversationId, userId)
                    .orElseThrow(() -> new ConversationNotFoundException(conversationId));

                List<Message> contextMessages = buildMessageContext(conversation, message);
                ModelProvider provider = modelProviderFactory.getProvider(conversation.getModelProvider());

                // 保存用户消息
                Message userMessage = saveUserMessage(conversation, message);

                // 流式调用AI模型
                StringBuilder fullResponse = new StringBuilder();
                AtomicInteger totalTokens = new AtomicInteger(0);

                provider.streamChat(contextMessages, conversation.getModelConfig(),
                    new StreamCallback() {
                        @Override
                        public void onToken(String token) {
                            try {
                                fullResponse.append(token);
                                emitter.send(SseEmitter.event()
                                    .name("token")
                                    .data(token));
                            } catch (IOException e) {
                                logger.error("Error sending SSE token", e);
                            }
                        }

                        @Override
                        public void onComplete(int tokens) {
                            try {
                                totalTokens.set(tokens);

                                // 保存完整的AI回复
                                Message assistantMessage = saveAssistantMessage(
                                    conversation, fullResponse.toString());

                                // 更新统计
                                updateConversationStats(conversation, tokens);
                                updateUsageStats(userId, conversation.getModelProvider(), tokens);

                                emitter.send(SseEmitter.event()
                                    .name("complete")
                                    .data(Map.of(
                                        "messageId", assistantMessage.getId(),
                                        "tokens", tokens
                                    )));

                                emitter.complete();
                            } catch (Exception e) {
                                logger.error("Error completing SSE stream", e);
                                emitter.completeWithError(e);
                            }
                        }

                        @Override
                        public void onError(Exception error) {
                            logger.error("Error in AI stream", error);
                            emitter.completeWithError(error);
                        }
                    });

            } catch (Exception e) {
                logger.error("Error in stream chat", e);
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 构建消息上下文
     */
    private List<Message> buildMessageContext(Conversation conversation, String newMessage) {
        // 获取最近的消息历史
        List<Message> recentMessages = messageRepository.findRecentMessages(
            conversation.getId(), 20); // 最多20条历史消息

        // 添加系统提示词
        List<Message> contextMessages = new ArrayList<>();
        if (conversation.getPersonalityId() != null) {
            Personality personality = personalityService.getPersonality(conversation.getPersonalityId());
            contextMessages.add(Message.builder()
                .role("system")
                .content(personality.getSystemPrompt())
                .build());
        }

        // 添加历史消息
        contextMessages.addAll(recentMessages);

        // 添加当前用户消息
        contextMessages.add(Message.builder()
            .role("user")
            .content(newMessage)
            .build());

        // 检查token限制
        int totalTokens = tokenCounter.countTokens(contextMessages);
        int maxTokens = conversation.getModelConfig().getMaxTokens();

        // 如果超出限制，裁剪历史消息
        while (totalTokens > maxTokens * 0.8 && contextMessages.size() > 2) {
            contextMessages.remove(1); // 保留系统提示词和当前消息
            totalTokens = tokenCounter.countTokens(contextMessages);
        }

        return contextMessages;
    }

    /**
     * 获取或创建对话
     */
    private Conversation getOrCreateConversation(String userId, ChatRequest request) {
        if (request.getConversationId() != null) {
            return conversationRepository.findByIdAndUserId(request.getConversationId(), userId)
                .orElseThrow(() -> new ConversationNotFoundException(request.getConversationId()));
        }

        // 创建新对话
        Conversation conversation = Conversation.builder()
            .id(UUID.randomUUID().toString())
            .userId(userId)
            .petId(request.getPetId())
            .title(generateConversationTitle(request.getMessage()))
            .personalityId(request.getPersonalityId())
            .modelProvider(request.getModelProvider())
            .modelName(request.getModelName())
            .status("active")
            .totalMessages(0)
            .totalTokens(0)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        return conversationRepository.save(conversation);
    }
}
```

#### AI模型提供商实现
```java
@Component
public class OpenAIProvider implements ModelProvider {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AIConfig aiConfig;

    @Override
    public AIResponse chat(List<Message> messages, ModelConfig config) {
        try {
            OpenAIRequest request = buildOpenAIRequest(messages, config);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(config.getApiKey());

            HttpEntity<OpenAIRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<OpenAIResponse> response = restTemplate.postForEntity(
                config.getApiEndpoint() + "/chat/completions",
                entity,
                OpenAIResponse.class
            );

            OpenAIResponse openAIResponse = response.getBody();
            if (openAIResponse == null || openAIResponse.getChoices().isEmpty()) {
                throw new AIServiceException("Empty response from OpenAI");
            }

            OpenAIChoice choice = openAIResponse.getChoices().get(0);

            return AIResponse.builder()
                .content(choice.getMessage().getContent())
                .tokens(openAIResponse.getUsage().getTotalTokens())
                .model(openAIResponse.getModel())
                .finishReason(choice.getFinishReason())
                .build();

        } catch (Exception e) {
            logger.error("Error calling OpenAI API", e);
            throw new AIServiceException("Failed to get response from OpenAI", e);
        }
    }

    @Override
    public void streamChat(List<Message> messages, ModelConfig config, StreamCallback callback) {
        try {
            OpenAIRequest request = buildOpenAIRequest(messages, config);
            request.setStream(true);

            // 使用WebClient进行流式请求
            WebClient webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + config.getApiKey())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();

            StringBuilder fullContent = new StringBuilder();
            AtomicInteger totalTokens = new AtomicInteger(0);

            webClient.post()
                .uri(config.getApiEndpoint() + "/chat/completions")
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(chunk -> {
                    try {
                        if (chunk.startsWith("data: ")) {
                            String data = chunk.substring(6);
                            if ("[DONE]".equals(data)) {
                                callback.onComplete(totalTokens.get());
                                return;
                            }

                            OpenAIStreamResponse streamResponse = objectMapper.readValue(
                                data, OpenAIStreamResponse.class);

                            if (!streamResponse.getChoices().isEmpty()) {
                                OpenAIStreamChoice choice = streamResponse.getChoices().get(0);
                                if (choice.getDelta() != null && choice.getDelta().getContent() != null) {
                                    String content = choice.getDelta().getContent();
                                    fullContent.append(content);
                                    totalTokens.incrementAndGet();
                                    callback.onToken(content);
                                }
                            }
                        }
                    } catch (Exception e) {
                        callback.onError(e);
                    }
                })
                .doOnError(callback::onError)
                .subscribe();

        } catch (Exception e) {
            callback.onError(e);
        }
    }

    private OpenAIRequest buildOpenAIRequest(List<Message> messages, ModelConfig config) {
        List<OpenAIMessage> openAIMessages = messages.stream()
            .map(msg -> OpenAIMessage.builder()
                .role(msg.getRole())
                .content(msg.getContent())
                .build())
            .collect(Collectors.toList());

        return OpenAIRequest.builder()
            .model(config.getModelName())
            .messages(openAIMessages)
            .maxTokens(config.getMaxTokens())
            .temperature(config.getTemperature())
            .topP(config.getTopP())
            .frequencyPenalty(config.getFrequencyPenalty())
            .presencePenalty(config.getPresencePenalty())
            .build();
    }
}
```

## 安全规范

### 代码安全
- 输入验证和过滤
- 避免硬编码敏感信息
- 使用安全的文件操作
- 定期安全审计
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证

### 数据安全
- 用户配置文件加密存储
- 网络通信使用HTTPS
- 不收集用户隐私数据
- 敏感数据脱敏处理
- 定期数据备份
- 访问日志记录

### API安全
- JWT令牌认证
- API请求签名验证
- 请求频率限制
- IP白名单机制
- 接口权限控制

### AI服务安全
- API密钥加密存储
- 请求内容过滤和审核
- Token使用量限制
- 敏感信息脱敏
- 对话内容加密存储

## AI服务数据结构

### 请求响应DTO
```java
// 聊天请求
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatRequest {
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 4000, message = "消息长度不能超过4000字符")
    private String message;

    private String conversationId;
    private String petId;
    private String personalityId;
    private String modelProvider;
    private String modelName;

    @Valid
    private ModelParameters parameters;
}

// 聊天响应
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponse {
    private String conversationId;
    private String messageId;
    private String content;
    private Integer tokens;
    private String model;
    private LocalDateTime timestamp;
    private String finishReason;
}

// 对话摘要
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationSummary {
    private String id;
    private String title;
    private String petName;
    private String personalityName;
    private Integer totalMessages;
    private LocalDateTime lastMessageTime;
    private String lastMessage;
    private String status;
}

// AI模型信息
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIModelInfo {
    private String id;
    private String name;
    private String provider;
    private String description;
    private Integer maxTokens;
    private BigDecimal costPer1kTokens;
    private Boolean isAvailable;
    private Map<String, Object> capabilities;
}

// 模型参数
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelParameters {
    @DecimalMin(value = "0.0", message = "temperature必须大于等于0")
    @DecimalMax(value = "2.0", message = "temperature必须小于等于2")
    private BigDecimal temperature;

    @DecimalMin(value = "0.0", message = "topP必须大于等于0")
    @DecimalMax(value = "1.0", message = "topP必须小于等于1")
    private BigDecimal topP;

    @Min(value = 1, message = "maxTokens必须大于0")
    @Max(value = 8192, message = "maxTokens不能超过8192")
    private Integer maxTokens;

    @DecimalMin(value = "-2.0", message = "frequencyPenalty必须大于等于-2")
    @DecimalMax(value = "2.0", message = "frequencyPenalty必须小于等于2")
    private BigDecimal frequencyPenalty;

    @DecimalMin(value = "-2.0", message = "presencePenalty必须大于等于-2")
    @DecimalMax(value = "2.0", message = "presencePenalty必须小于等于2")
    private BigDecimal presencePenalty;
}
```

### 实体类定义
```java
// 对话实体
@Entity
@Table(name = "conversations")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Conversation {
    @Id
    private String id;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "pet_id")
    private String petId;

    @Column(name = "title")
    private String title;

    @Column(name = "personality_id")
    private String personalityId;

    @Column(name = "model_provider", nullable = false)
    private String modelProvider;

    @Column(name = "model_name", nullable = false)
    private String modelName;

    @Column(name = "context_summary", columnDefinition = "TEXT")
    private String contextSummary;

    @Column(name = "total_messages")
    private Integer totalMessages;

    @Column(name = "total_tokens")
    private Integer totalTokens;

    @Column(name = "status")
    private String status;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 关联查询
    @OneToMany(mappedBy = "conversationId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Message> messages;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "personality_id", insertable = false, updatable = false)
    private Personality personality;
}

// 消息实体
@Entity
@Table(name = "messages")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Message {
    @Id
    private String id;

    @Column(name = "conversation_id", nullable = false)
    private String conversationId;

    @Column(name = "role", nullable = false)
    private String role; // user, assistant, system

    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(name = "tokens")
    private Integer tokens;

    @Column(name = "metadata", columnDefinition = "JSONB")
    @Convert(converter = JsonConverter.class)
    private Map<String, Object> metadata;

    @Column(name = "created_at")
    private LocalDateTime createdAt;
}

// 性格实体
@Entity
@Table(name = "personalities")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Personality {
    @Id
    private String id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "character_name")
    private String characterName;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "system_prompt", nullable = false, columnDefinition = "TEXT")
    private String systemPrompt;

    @Column(name = "personality_traits", columnDefinition = "JSONB")
    @Convert(converter = JsonConverter.class)
    private Map<String, Object> personalityTraits;

    @Column(name = "speaking_style", columnDefinition = "JSONB")
    @Convert(converter = JsonConverter.class)
    private Map<String, Object> speakingStyle;

    @Column(name = "background_story", columnDefinition = "TEXT")
    private String backgroundStory;

    @Column(name = "is_default")
    private Boolean isDefault;

    @Column(name = "is_active")
    private Boolean isActive;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### AI服务配置
```java
@Configuration
@ConfigurationProperties(prefix = "arkpets.ai")
@Data
public class AIConfig {

    private Map<String, ProviderConfig> providers = new HashMap<>();
    private SecurityConfig security = new SecurityConfig();
    private UsageConfig usage = new UsageConfig();
    private CacheConfig cache = new CacheConfig();

    @Data
    public static class ProviderConfig {
        private String apiEndpoint;
        private String apiKey;
        private Integer timeout = 30000;
        private Integer maxRetries = 3;
        private Integer rateLimitPerMinute = 60;
        private Boolean enabled = true;
        private Map<String, Object> defaultParameters = new HashMap<>();
    }

    @Data
    public static class SecurityConfig {
        private Boolean enableContentFilter = true;
        private Boolean enableAuditLog = true;
        private Boolean encryptConversations = true;
        private List<String> bannedWords = new ArrayList<>();
        private Integer maxMessageLength = 4000;
        private Integer maxConversationLength = 100;
    }

    @Data
    public static class UsageConfig {
        private Integer dailyTokenLimit = 100000;
        private Integer monthlyTokenLimit = 1000000;
        private BigDecimal maxCostPerDay = new BigDecimal("10.00");
        private BigDecimal maxCostPerMonth = new BigDecimal("100.00");
        private Boolean enableUsageAlerts = true;
    }

    @Data
    public static class CacheConfig {
        private Boolean enableResponseCache = true;
        private Integer cacheExpirationMinutes = 60;
        private Integer maxCacheSize = 1000;
        private Boolean enableConversationCache = true;
    }
}
```

### 应用配置文件
```yaml
# application.yml (AI服务)
arkpets:
  ai:
    providers:
      openai:
        api-endpoint: https://api.openai.com/v1
        api-key: ${OPENAI_API_KEY}
        timeout: 30000
        max-retries: 3
        rate-limit-per-minute: 60
        enabled: true
        default-parameters:
          temperature: 0.7
          top-p: 1.0
          max-tokens: 2048
          frequency-penalty: 0.0
          presence-penalty: 0.0

      claude:
        api-endpoint: https://api.anthropic.com/v1
        api-key: ${CLAUDE_API_KEY}
        timeout: 30000
        max-retries: 3
        rate-limit-per-minute: 30
        enabled: true
        default-parameters:
          temperature: 0.7
          max-tokens: 2048

      local:
        api-endpoint: http://localhost:11434/v1
        timeout: 60000
        max-retries: 1
        rate-limit-per-minute: 120
        enabled: true
        default-parameters:
          temperature: 0.7
          max-tokens: 2048

    security:
      enable-content-filter: true
      enable-audit-log: true
      encrypt-conversations: true
      max-message-length: 4000
      max-conversation-length: 100
      banned-words:
        - "违禁词1"
        - "违禁词2"

    usage:
      daily-token-limit: 100000
      monthly-token-limit: 1000000
      max-cost-per-day: 10.00
      max-cost-per-month: 100.00
      enable-usage-alerts: true

    cache:
      enable-response-cache: true
      cache-expiration-minutes: 60
      max-cache-size: 1000
      enable-conversation-cache: true

spring:
  datasource:
    url: *******************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  redis:
    host: localhost
    port: 6379
    password: ${REDIS_PASSWORD}
    database: 1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: ai-service
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer

logging:
  level:
    com.arkpets.ai: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ai-service.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

## AI服务API接口定义

### 接口命名规范

#### 基础路径
- **基础URL**: `https://api.arkpets.com`
- **API版本**: `/api/v1`
- **AI服务前缀**: `/ai`

#### 完整接口路径格式
```
{基础URL}{API版本}{服务前缀}{资源路径}
例: https://api.arkpets.com/api/v1/ai/chat
```

### 1. 聊天对话接口

#### 1.1 发送聊天消息
```http
POST /api/v1/ai/chat
Content-Type: application/json
Authorization: Bearer {token}

{
  "message": "你好，阿米娅",
  "conversationId": "conv_123456789", // 可选，不传则创建新对话
  "petId": "pet_amiya_001",
  "personalityId": "personality_amiya_default",
  "modelProvider": "openai",
  "modelName": "gpt-4",
  "parameters": {
    "temperature": 0.7,
    "maxTokens": 2048
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "timestamp": "2025-01-01T12:00:00Z",
  "data": {
    "conversationId": "conv_123456789",
    "messageId": "msg_987654321",
    "content": "博士，您好！我是阿米娅，很高兴见到您。有什么我可以帮助您的吗？",
    "tokens": 45,
    "model": "gpt-4",
    "timestamp": "2025-01-01T12:00:00Z",
    "finishReason": "stop"
  }
}
```

**业务逻辑**:
1. 验证用户身份和权限
2. 检查用户Token使用限额
3. 验证请求参数完整性
4. 获取或创建对话会话
5. 构建消息上下文（包含历史消息和角色设定）
6. 调用指定的AI模型提供商
7. 保存用户消息和AI回复
8. 更新对话统计信息
9. 记录使用量和成本
10. 返回AI回复结果

#### 1.2 流式聊天
```http
GET /api/v1/ai/chat/stream?conversationId={id}&message={text}
Accept: text/event-stream
Authorization: Bearer {token}
```

**SSE响应流**:
```
data: {"type":"token","content":"博"}

data: {"type":"token","content":"士"}

data: {"type":"token","content":"，"}

data: {"type":"complete","messageId":"msg_123","tokens":45}
```

**业务逻辑**:
1. 建立SSE连接
2. 验证用户身份
3. 获取对话上下文
4. 流式调用AI模型
5. 实时推送Token片段
6. 完成后保存完整消息
7. 关闭SSE连接

#### 1.3 停止生成
```http
POST /api/v1/ai/chat/stop
Content-Type: application/json
Authorization: Bearer {token}

{
  "conversationId": "conv_123456789"
}
```

**业务逻辑**:
1. 验证用户权限
2. 查找正在进行的生成任务
3. 中断AI模型调用
4. 保存已生成的部分内容
5. 返回停止确认

### 2. 对话管理接口

#### 2.1 创建新对话
```http
POST /api/v1/ai/conversations
Content-Type: application/json
Authorization: Bearer {token}

{
  "title": "与阿米娅的对话",
  "petId": "pet_amiya_001",
  "personalityId": "personality_amiya_default",
  "modelProvider": "openai",
  "modelName": "gpt-4"
}
```

**业务逻辑**:
1. 验证用户身份
2. 验证宠物和性格ID有效性
3. 创建新对话记录
4. 初始化对话设置
5. 返回对话信息

#### 2.2 获取对话列表
```http
GET /api/v1/ai/conversations?page=1&size=20&status=active
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "conv_123456789",
      "title": "与阿米娅的对话",
      "petName": "阿米娅",
      "personalityName": "默认性格",
      "totalMessages": 15,
      "lastMessageTime": "2025-01-01T11:30:00Z",
      "lastMessage": "博士，今天的工作辛苦了！",
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "size": 20,
    "total": 5,
    "totalPages": 1
  }
}
```

**业务逻辑**:
1. 验证用户身份
2. 根据用户ID查询对话列表
3. 支持分页和状态过滤
4. 返回对话摘要信息

#### 2.3 获取对话详情
```http
GET /api/v1/ai/conversations/{conversationId}
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户身份和对话所有权
2. 查询对话详细信息
3. 包含关联的宠物和性格信息
4. 返回完整对话数据

#### 2.4 更新对话
```http
PUT /api/v1/ai/conversations/{conversationId}
Content-Type: application/json
Authorization: Bearer {token}

{
  "title": "新的对话标题",
  "personalityId": "personality_amiya_cheerful"
}
```

**业务逻辑**:
1. 验证用户权限
2. 验证更新参数
3. 更新对话信息
4. 记录修改日志
5. 返回更新结果

#### 2.5 删除对话
```http
DELETE /api/v1/ai/conversations/{conversationId}
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户权限
2. 软删除对话记录
3. 清理相关消息数据
4. 更新统计信息
5. 返回删除确认

### 3. 消息管理接口

#### 3.1 获取对话消息
```http
GET /api/v1/ai/conversations/{conversationId}/messages?page=1&size=50&order=desc
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "msg_987654321",
      "role": "assistant",
      "content": "博士，您好！我是阿米娅...",
      "tokens": 45,
      "timestamp": "2025-01-01T12:00:00Z",
      "metadata": {
        "model": "gpt-4",
        "finishReason": "stop"
      }
    },
    {
      "id": "msg_987654320",
      "role": "user",
      "content": "你好，阿米娅",
      "tokens": 8,
      "timestamp": "2025-01-01T11:59:30Z",
      "metadata": {}
    }
  ]
}
```

**业务逻辑**:
1. 验证用户权限
2. 查询指定对话的消息列表
3. 支持分页和排序
4. 返回消息详细信息

#### 3.2 删除消息
```http
DELETE /api/v1/ai/messages/{messageId}
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户权限
2. 检查消息所有权
3. 软删除消息记录
4. 更新对话统计
5. 返回删除确认

#### 3.3 重新生成回复
```http
POST /api/v1/ai/messages/{messageId}/regenerate
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户权限
2. 获取原始用户消息
3. 重新构建上下文
4. 调用AI模型生成新回复
5. 保存新的回复消息
6. 返回新生成的内容

### 4. 模型管理接口

#### 4.1 获取可用模型列表
```http
GET /api/v1/ai/models?provider=openai&available=true
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "gpt-4",
      "name": "GPT-4",
      "provider": "openai",
      "description": "最新的GPT-4模型，具有强大的理解和生成能力",
      "maxTokens": 8192,
      "costPer1kTokens": 0.03,
      "isAvailable": true,
      "capabilities": {
        "supportStream": true,
        "supportFunctions": true,
        "supportImages": false
      }
    }
  ]
}
```

**业务逻辑**:
1. 查询系统配置的AI模型
2. 检查模型可用性状态
3. 返回模型详细信息和能力

#### 4.2 测试模型连接
```http
POST /api/v1/ai/models/{modelId}/test
Content-Type: application/json
Authorization: Bearer {token}

{
  "message": "Hello, this is a test message."
}
```

**业务逻辑**:
1. 验证管理员权限
2. 使用测试消息调用指定模型
3. 检查响应时间和质量
4. 返回测试结果

#### 4.3 获取模型使用统计
```http
GET /api/v1/ai/models/{modelId}/stats?startDate=2025-01-01&endDate=2025-01-31
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户权限
2. 查询指定时间范围的使用统计
3. 计算Token使用量和成本
4. 返回统计数据

### 5. 性格管理接口

#### 5.1 获取性格列表
```http
GET /api/v1/ai/personalities?character=amiya&active=true
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "personality_amiya_default",
      "name": "阿米娅-默认",
      "characterName": "阿米娅",
      "description": "温和、负责任的领导者性格",
      "isDefault": true,
      "isActive": true,
      "personalityTraits": {
        "温和": 0.8,
        "负责": 0.9,
        "关怀": 0.7
      },
      "speakingStyle": {
        "称呼": "博士",
        "语调": "温和礼貌",
        "口癖": "无"
      }
    }
  ]
}
```

**业务逻辑**:
1. 查询可用的性格配置
2. 支持按角色和状态过滤
3. 返回性格详细信息

#### 5.2 创建自定义性格
```http
POST /api/v1/ai/personalities
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "阿米娅-活泼版",
  "characterName": "阿米娅",
  "description": "更加活泼开朗的阿米娅",
  "systemPrompt": "你是阿米娅，一个活泼开朗的兔子女孩...",
  "personalityTraits": {
    "活泼": 0.9,
    "开朗": 0.8,
    "好奇": 0.7
  },
  "speakingStyle": {
    "称呼": "博士",
    "语调": "活泼可爱",
    "口癖": "呢"
  }
}
```

**业务逻辑**:
1. 验证用户权限
2. 验证性格配置参数
3. 创建新的性格记录
4. 返回创建结果

#### 5.3 更新性格配置
```http
PUT /api/v1/ai/personalities/{personalityId}
Content-Type: application/json
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户权限和所有权
2. 更新性格配置
3. 验证系统提示词有效性
4. 返回更新结果

#### 5.4 删除性格配置
```http
DELETE /api/v1/ai/personalities/{personalityId}
Authorization: Bearer {token}
```

**业务逻辑**:
1. 验证用户权限
2. 检查是否为默认性格
3. 检查是否有关联对话
4. 执行删除操作
5. 返回删除确认

### 6. 使用统计接口

#### 6.1 获取用户使用统计
```http
GET /api/v1/ai/usage/stats?period=daily&startDate=2025-01-01&endDate=2025-01-31
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "period": "daily",
    "totalRequests": 150,
    "totalTokens": 45000,
    "totalCost": 1.35,
    "dailyStats": [
      {
        "date": "2025-01-01",
        "requests": 10,
        "tokens": 3000,
        "cost": 0.09
      }
    ],
    "modelBreakdown": {
      "gpt-4": {
        "requests": 100,
        "tokens": 30000,
        "cost": 0.90
      },
      "gpt-3.5-turbo": {
        "requests": 50,
        "tokens": 15000,
        "cost": 0.45
      }
    }
  }
}
```

**业务逻辑**:
1. 验证用户身份
2. 查询指定时间范围的使用数据
3. 按模型和时间维度聚合统计
4. 计算成本信息
5. 返回详细统计报告

#### 6.2 获取使用限额信息
```http
GET /api/v1/ai/usage/limits
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "dailyLimit": {
      "tokens": 10000,
      "used": 3500,
      "remaining": 6500,
      "resetTime": "2025-01-02T00:00:00Z"
    },
    "monthlyLimit": {
      "tokens": 100000,
      "used": 45000,
      "remaining": 55000,
      "resetTime": "2025-02-01T00:00:00Z"
    },
    "costLimit": {
      "dailyMax": 5.00,
      "dailyUsed": 1.05,
      "monthlyMax": 50.00,
      "monthlyUsed": 13.50
    }
  }
}
```

**业务逻辑**:
1. 查询用户的使用限额配置
2. 计算当前使用量
3. 计算剩余额度
4. 返回限额和使用情况

### 7. 系统管理接口

#### 7.1 健康检查
```http
GET /api/v1/ai/health
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "UP",
    "timestamp": "2025-01-01T12:00:00Z",
    "services": {
      "database": "UP",
      "redis": "UP",
      "openai": "UP",
      "claude": "DOWN"
    },
    "metrics": {
      "activeConnections": 25,
      "responseTime": 150,
      "errorRate": 0.01
    }
  }
}
```

**业务逻辑**:
1. 检查数据库连接状态
2. 检查Redis缓存状态
3. 检查各AI模型提供商状态
4. 收集系统性能指标
5. 返回健康状态报告

#### 7.2 获取系统指标
```http
GET /api/v1/ai/metrics
Authorization: Bearer {admin_token}
```

**业务逻辑**:
1. 验证管理员权限
2. 收集系统运行指标
3. 统计API调用情况
4. 返回详细指标数据

## 项目开发进度

### 📊 当前完成情况 (2025-01-01)

#### 项目状态
- **开发模式**: ✅ 二次开发 (Secondary Development) - **已获取源代码**
- **源码分析**: ✅ 完成 (100%) - 已分析原始项目结构和技术栈
- **架构设计**: ✅ 完成 (100%) - 现代化升级方案已确定
- **技术选型**: ✅ 完成 (100%) - 兼容原技术栈并扩展
- **项目规范**: ✅ 完成 (100%) - 适配二次开发模式
- **文档体系**: 🟡 进行中 (16%) - 基于原项目重新规划

#### 文档完成度统计

| 模块分类 | 计划文档数 | 已完成数 | 完成度 | 状态 |
|---------|-----------|---------|--------|------|
| **前端核心模块** | 4 | 2 | 50% | 🟡 进行中 |
| **前端桌面模块** | 5 | 0 | 0% | ⚪ 待开始 |
| **前端资源模块** | 3 | 0 | 0% | ⚪ 待开始 |
| **后端AI服务** | 5 | 2 | 40% | 🟡 进行中 |
| **后端认证服务** | 3 | 1 | 33% | 🟡 进行中 |
| **后端其他服务** | 12 | 0 | 0% | ⚪ 待开始 |
| **架构文档** | 4 | 1 | 25% | 🟡 进行中 |
| **部署文档** | 4 | 0 | 0% | ⚪ 待开始 |
| **开发文档** | 4 | 1 | 25% | 🟡 进行中 |
| **总计** | **44** | **7** | **16%** | 🟡 **进行中** |

#### 已完成的核心文档

##### ✅ 前端模块文档
1. **[动画系统](docs/frontend/core/animation-system.md)**
   - 完整的动画管理器实现
   - 状态机设计模式
   - 事件驱动架构
   - 性能优化策略

2. **[AI交互逻辑](docs/frontend/core/ai-interaction.md)**
   - 对话管理和上下文处理
   - 情感分析和行为决策
   - 语音合成集成
   - 多模态交互支持

##### ✅ 后端模块文档
3. **[聊天服务](docs/backend/ai-service/chat-service.md)**
   - 流式响应处理
   - 上下文压缩算法
   - 消息验证机制
   - 错误恢复策略

4. **[模型提供商](docs/backend/ai-service/model-providers.md)**
   - 统一的提供商接口
   - 多模型支持架构
   - 负载均衡和故障转移
   - 插件化扩展机制

5. **[身份认证](docs/backend/auth-service/authentication.md)**
   - JWT令牌管理
   - 多因素认证(MFA)
   - 会话管理
   - 安全策略配置

##### ✅ 架构文档
6. **[系统架构](docs/architecture/system-architecture.md)**
   - 微服务架构设计
   - Kubernetes部署配置
   - 监控和日志系统
   - 性能优化方案

##### ✅ 开发文档
7. **[编码规范](docs/development/coding-standards.md)**
   - Java/TypeScript编码标准
   - 代码质量检查工具
   - Git提交规范
   - 性能优化指南

#### 技术成果统计
- **文档总行数**: ~2,100行
- **函数定义**: ~150个
- **API接口**: 22个核心接口
- **数据模型**: ~30个
- **流程图**: 15个
- **配置示例**: 20+个

### 🎯 下一阶段开发计划

#### 优先级1 (高优先级) - 预计2周
1. **完成AI服务核心模块**
   - [ ] conversation-manager.md (对话管理器)
   - [ ] personality-system.md (性格系统)
   - [ ] usage-statistics.md (使用统计)

2. **完成前端核心模块**
   - [ ] physics-engine.md (物理引擎)
   - [ ] data-models.md (数据模型)

3. **开始代码实现**
   - [ ] 搭建项目基础架构
   - [ ] 实现核心模块框架

#### 优先级2 (中优先级) - 预计4周
1. **前端桌面模块文档**
   - [ ] ui-components.md (UI组件)
   - [ ] launcher.md (启动器)
   - [ ] system-tray.md (系统托盘)
   - [ ] config-manager.md (配置管理)
   - [ ] api-client.md (API客户端)

2. **后端服务模块**
   - [ ] 用户服务文档
   - [ ] 模型服务文档
   - [ ] 配置服务文档

#### 优先级3 (低优先级) - 预计6周
1. **部署和运维文档**
2. **测试和质量保证**
3. **性能优化和监控**

### 📈 项目里程碑

#### 第一阶段: 架构设计 (已完成)
- ✅ 系统架构设计
- ✅ 技术栈选型
- ✅ 开发规范制定
- ✅ 核心模块文档

#### 第二阶段: 核心开发 (进行中)
- 🟡 完善文档体系 (16% → 60%)
- ⚪ 搭建基础架构
- ⚪ 实现核心功能

#### 第三阶段: 功能完善 (计划中)
- ⚪ AI服务集成
- ⚪ 前端界面开发
- ⚪ 测试和优化

#### 第四阶段: 部署上线 (计划中)
- ⚪ 生产环境部署
- ⚪ 监控和运维
- ⚪ 用户反馈和迭代

### 🔍 项目特色和创新点

#### 原始Ark-Pets项目分析 (v3.8.0)

**✅ 原项目优势**
1. **成熟的桌宠系统**
   - 完整的Spine动画支持
   - 物理引擎实现 (重力、碰撞、排斥)
   - 多显示器支持
   - 系统托盘集成

2. **丰富的交互功能**
   - 鼠标拖拽和点击交互
   - 键盘控制和快捷键
   - 窗口边缘检测和站立
   - 透明模式和高亮描边

3. **完善的模型系统**
   - 支持明日方舟角色模型
   - 多形态角色切换
   - 动态立绘和基建小人
   - 社区模型库支持

4. **用户友好的界面**
   - JavaFX图形界面
   - 模型搜索和筛选
   - 配置管理和设置
   - 开机自启动支持

#### 二次开发升级方案

**🚀 架构现代化**
1. **保留核心优势**
   - 继承原有的动画系统和物理引擎
   - 保持Spine模型兼容性
   - 维持用户熟悉的交互方式

2. **架构升级**
   - 原项目: 单体桌面应用
   - 升级后: 前后端分离 + 微服务架构

3. **AI智能化**
   - 原项目: 预设行为模式
   - 升级后: 完整AI对话系统，支持多模型

4. **云服务化**
   - 原项目: 纯本地运行
   - 升级后: 云端服务 + 本地客户端混合模式

5. **扩展性增强**
   - 原项目: 功能相对固定
   - 升级后: 插件化架构，易于扩展

6. **用户体验提升**
   - 原项目: 基础桌宠功能
   - 升级后: 智能陪伴 + 情感交互 + 语音对话

---

**文档版本**: v1.4
**最后更新**: 2025-01-01
**维护者**: Ark-Pets开发团队
