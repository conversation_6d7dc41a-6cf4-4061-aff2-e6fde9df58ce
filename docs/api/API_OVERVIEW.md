# Ark-Pets Enhanced API 接口总览

**最后更新**: 2025-01-01  
**API版本**: v1  
**基础路径**: `/api/v1`  
**当前状态**: 部分可用

## 📋 API 服务概览

### ✅ 已完成的服务

#### 🔐 认证服务 (auth-service)
**服务端口**: 8081  
**状态**: ✅ 完全可用  
**接口数量**: 8个

| 接口 | 方法 | 路径 | 功能 | 状态 |
|------|------|------|------|------|
| 用户登录 | POST | `/auth/login` | 用户名密码登录 | ✅ |
| 用户注册 | POST | `/auth/register` | 新用户注册 | ✅ |
| 用户登出 | POST | `/auth/logout` | 用户登出 | ✅ |
| 刷新Token | POST | `/auth/refresh` | 刷新JWT Token | ✅ |
| 用户信息 | GET | `/auth/userinfo` | 获取当前用户信息 | ✅ |
| GitHub授权 | GET | `/auth/oauth/github/authorize` | GitHub登录授权 | ✅ |
| GitHub回调 | GET | `/auth/oauth/github/callback` | GitHub登录回调 | ✅ |
| 第三方登录 | GET | `/auth/oauth/{platform}/*` | 其他平台登录 | ✅ |

#### 🤖 AI服务 (ai-service)
**服务端口**: 8083  
**状态**: ✅ 完全可用  
**接口数量**: 7个

| 接口 | 方法 | 路径 | 功能 | 状态 |
|------|------|------|------|------|
| 基础聊天 | POST | `/ai/chat` | AI智能对话 | ✅ |
| 指定模型聊天 | POST | `/ai/chat/{model}` | 使用特定AI模型 | ✅ |
| 创意生成 | GET | `/ai/creative` | 创意回复生成 | ✅ |
| 情感分析 | POST | `/ai/emotion` | 文本情感分析 | ✅ |
| 模型列表 | GET | `/ai/models` | 获取可用模型 | ✅ |
| 模型状态 | GET | `/ai/models/{model}/status` | 检查模型状态 | ✅ |
| 健康检查 | GET | `/ai/health` | 服务健康状态 | ✅ |

### 🟡 开发中的服务

#### 💾 缓存服务 (cache-service)
**服务端口**: 8084  
**状态**: 🟡 开发中  
**预计完成**: 2025-01-02

| 接口 | 方法 | 路径 | 功能 | 状态 |
|------|------|------|------|------|
| 缓存设置 | POST | `/cache/set` | 设置缓存数据 | 🔄 |
| 缓存获取 | GET | `/cache/get/{key}` | 获取缓存数据 | 🔄 |
| 缓存删除 | DELETE | `/cache/delete/{key}` | 删除缓存数据 | 🔄 |
| 缓存清理 | POST | `/cache/clear` | 清理过期缓存 | 🔄 |

### ⏳ 待开发的服务

#### 📊 监控服务 (monitor-service)
**服务端口**: 8085  
**状态**: ⏳ 待开始  
**预计开始**: 2025-01-02

#### 🌍 环境感知服务 (env-service)
**服务端口**: 8086  
**状态**: ⏳ 待开始  
**预计开始**: 2025-01-03

#### 👤 用户服务 (user-service)
**服务端口**: 8082  
**状态**: ⏳ 待开始  
**预计开始**: 2025-01-05

#### 🔧 配置服务 (config-service)
**服务端口**: 8087  
**状态**: ⏳ 待开始  
**预计开始**: 2025-01-06

#### 📁 文件服务 (file-service)
**服务端口**: 8088  
**状态**: ⏳ 待开始  
**预计开始**: 2025-01-07

#### 🚪 网关服务 (gateway-service)
**服务端口**: 8080  
**状态**: ⏳ 待开始  
**预计开始**: 2025-01-08

---

## 🔧 API 使用指南

### 🔑 认证方式

#### JWT Token认证
```http
Authorization: Bearer <jwt-token>
```

#### Sa-Token认证 (推荐)
```http
ark-pets-token: <sa-token>
```

### 📝 请求格式

#### 标准请求头
```http
Content-Type: application/json
Accept: application/json
User-Agent: Ark-Pets-Client/4.0.0
```

#### 标准响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": 1704067200000
}
```

### 🚨 错误处理

#### 标准错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "ERROR_CODE",
  "timestamp": 1704067200000
}
```

#### 常见状态码
| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 请求成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权访问 | 重新登录获取Token |
| 403 | 权限不足 | 检查用户权限 |
| 429 | 请求频率超限 | 降低请求频率 |
| 500 | 服务器内部错误 | 稍后重试或联系支持 |
| 503 | 服务不可用 | 检查服务状态 |

---

## 🧪 API 测试

### 测试环境
- **开发环境**: http://localhost:8080
- **测试环境**: 待配置
- **生产环境**: 待配置

### 快速测试

#### 1. 测试认证服务
```bash
# 用户登录
curl -X POST http://localhost:8081/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password123"}'

# 获取用户信息
curl -X GET http://localhost:8081/api/v1/auth/userinfo \
  -H "ark-pets-token: your-token-here"
```

#### 2. 测试AI服务
```bash
# AI聊天
curl -X POST http://localhost:8083/api/v1/ai/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "你好", "username": "testuser"}'

# 情感分析
curl -X POST "http://localhost:8083/api/v1/ai/emotion?message=今天很开心"
```

### Postman集合
- 📁 [认证服务API集合](./postman/auth-service.json) (待创建)
- 📁 [AI服务API集合](./postman/ai-service.json) (待创建)

---

## 📊 API 性能指标

### 当前性能
| 服务 | 平均响应时间 | 并发支持 | 可用性 |
|------|-------------|----------|--------|
| 认证服务 | < 100ms | 500+ | 99.9% |
| AI服务 | < 3s | 100+ | 99.5% |

### 性能目标
- **响应时间**: 认证API < 100ms, AI API < 3s
- **并发支持**: 1000+ 并发用户
- **可用性**: 99.5%+
- **错误率**: < 0.1%

---

## 🔄 API 版本管理

### 当前版本: v1
- **发布时间**: 2025-01-01
- **主要功能**: 认证授权、AI对话
- **兼容性**: 向后兼容

### 版本规划
- **v1.1** (2025-01-15): 缓存服务、监控服务
- **v1.2** (2025-02-01): 环境感知、用户管理
- **v2.0** (2025-03-01): 完整功能集

---

## 📞 支持与反馈

### 技术支持
- **文档**: [API详细文档](./README.md)
- **问题反馈**: GitHub Issues
- **技术讨论**: GitHub Discussions

### 更新通知
- **更新频率**: 每日更新
- **通知方式**: 文档更新、版本发布
- **订阅方式**: Watch GitHub仓库

---

**🚀 API持续完善中，感谢您的使用和反馈！**
