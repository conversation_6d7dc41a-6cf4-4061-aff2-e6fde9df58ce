# 邮件服务 (Email Service)

## 模块概述

邮件服务模块负责Ark-Pets AI Enhanced项目中邮件的发送、管理和跟踪，包括用户注册验证、密码重置、系统通知、营销邮件等功能。支持多种邮件提供商（SMTP、SendGrid、阿里云邮件推送），提供邮件模板、发送统计、退订管理等功能。

**核心职责**:
- 邮件发送和投递管理
- 邮件模板设计和渲染
- 多邮件提供商支持
- 发送统计和效果跟踪
- 退订管理和合规性

## 核心功能架构

### 1. 邮件服务架构

#### 分层邮件服务架构模型
```
┌─────────────────────────────────────┐
│           邮件服务层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 发送管理  │ 模板渲染  │ 统计跟踪  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           处理中间层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 队列管理  │ 重试机制  │ 限流控制  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           提供商适配层               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ SMTP     │ SendGrid │ 阿里云   │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 邮件发送流程

#### 邮件发送处理流程图
```mermaid
graph TB
    subgraph "邮件发送流程"
        SendRequest[发送请求]
        ValidateRequest[请求验证]
        CheckSubscription[检查订阅状态]
        RenderTemplate[渲染模板]
        QueueEmail[邮件入队]
        SelectProvider[选择提供商]
        SendEmail[发送邮件]
        RecordResult[记录结果]
    end
    
    subgraph "模板处理流程"
        LoadTemplate[加载模板]
        ProcessVariables[处理变量]
        RenderHTML[渲染HTML]
        RenderText[渲染文本]
        ValidateContent[验证内容]
    end
    
    subgraph "统计跟踪流程"
        TrackSent[跟踪发送]
        TrackOpen[跟踪打开]
        TrackClick[跟踪点击]
        TrackUnsubscribe[跟踪退订]
        UpdateStats[更新统计]
    end
    
    SendRequest --> ValidateRequest
    ValidateRequest --> CheckSubscription
    CheckSubscription --> LoadTemplate
    LoadTemplate --> ProcessVariables
    ProcessVariables --> RenderHTML
    RenderHTML --> RenderText
    RenderText --> ValidateContent
    ValidateContent --> QueueEmail
    
    QueueEmail --> SelectProvider
    SelectProvider --> SendEmail
    SendEmail --> RecordResult
    RecordResult --> TrackSent
    
    TrackSent --> TrackOpen
    TrackOpen --> TrackClick
    TrackClick --> TrackUnsubscribe
    TrackUnsubscribe --> UpdateStats
```

## 核心类和接口

### 1. 邮件服务主类

#### EmailService - 邮件服务主类
```java
/**
 * 邮件服务主类
 * 负责邮件的发送、管理和跟踪
 */
@Service
@Slf4j
public class EmailService {
    
    private final EmailTemplateRepository emailTemplateRepository;
    private final EmailLogRepository emailLogRepository;
    private final EmailSubscriptionRepository emailSubscriptionRepository;
    private final EmailProviderFactory emailProviderFactory;
    private final EmailTemplateEngine emailTemplateEngine;
    private final EmailQueueService emailQueueService;
    private final EmailStatisticsService emailStatisticsService;
    private final EmailConfig emailConfig;
    
    /**
     * 发送邮件
     * @param emailRequest 邮件请求
     * @return 发送结果
     */
    public EmailSendResult sendEmail(EmailSendRequest emailRequest) {
        try {
            // 1. 验证邮件请求
            validateEmailRequest(emailRequest);
            
            // 2. 检查订阅状态
            if (!checkSubscriptionStatus(emailRequest.getToEmail(), emailRequest.getEmailType())) {
                return EmailSendResult.unsubscribed();
            }
            
            // 3. 构建邮件内容
            EmailContent emailContent = buildEmailContent(emailRequest);
            
            // 4. 创建邮件日志
            EmailLog emailLog = createEmailLog(emailRequest, emailContent);
            emailLog = emailLogRepository.save(emailLog);
            
            // 5. 异步发送邮件
            emailQueueService.queueEmail(emailLog.getId(), emailContent);
            
            log.info("邮件已加入发送队列: logId={}, to={}, subject={}", 
                emailLog.getId(), emailRequest.getToEmail(), emailContent.getSubject());
            
            return EmailSendResult.builder()
                .emailLogId(emailLog.getId())
                .status(EmailStatus.QUEUED)
                .queuedAt(LocalDateTime.now())
                .build();
                
        } catch (Exception e) {
            log.error("邮件发送失败: {}", emailRequest, e);
            throw new EmailSendException("邮件发送失败", e);
        }
    }
    
    /**
     * 发送模板邮件
     * @param templateRequest 模板邮件请求
     * @return 发送结果
     */
    public EmailSendResult sendTemplateEmail(TemplateEmailRequest templateRequest) {
        try {
            // 1. 获取邮件模板
            EmailTemplate template = emailTemplateRepository.findById(templateRequest.getTemplateId())
                .orElseThrow(() -> new EmailTemplateNotFoundException("邮件模板不存在"));
            
            // 2. 渲染邮件内容
            EmailContent emailContent = emailTemplateEngine.renderTemplate(template, templateRequest.getVariables());
            
            // 3. 构建邮件请求
            EmailSendRequest emailRequest = EmailSendRequest.builder()
                .toEmail(templateRequest.getToEmail())
                .toName(templateRequest.getToName())
                .emailType(template.getEmailType())
                .priority(templateRequest.getPriority())
                .scheduledAt(templateRequest.getScheduledAt())
                .build();
            
            // 4. 发送邮件
            return sendEmailWithContent(emailRequest, emailContent);
            
        } catch (Exception e) {
            log.error("模板邮件发送失败: {}", templateRequest, e);
            throw new EmailSendException("模板邮件发送失败", e);
        }
    }
    
    /**
     * 批量发送邮件
     * @param batchRequest 批量邮件请求
     * @return 批量发送结果
     */
    @Async
    public CompletableFuture<BatchEmailResult> sendBatchEmails(BatchEmailRequest batchRequest) {
        try {
            List<EmailSendResult> results = new ArrayList<>();
            
            for (EmailRecipient recipient : batchRequest.getRecipients()) {
                try {
                    EmailSendRequest emailRequest = EmailSendRequest.builder()
                        .toEmail(recipient.getEmail())
                        .toName(recipient.getName())
                        .emailType(batchRequest.getEmailType())
                        .priority(batchRequest.getPriority())
                        .build();
                    
                    EmailSendResult result = sendEmail(emailRequest);
                    results.add(result);
                    
                } catch (Exception e) {
                    log.error("批量邮件中的单个邮件发送失败: {}", recipient.getEmail(), e);
                    results.add(EmailSendResult.failed(e.getMessage()));
                }
            }
            
            BatchEmailResult batchResult = BatchEmailResult.builder()
                .batchId(batchRequest.getBatchId())
                .totalRecipients(batchRequest.getRecipients().size())
                .results(results)
                .completedAt(LocalDateTime.now())
                .build();
            
            return CompletableFuture.completedFuture(batchResult);
            
        } catch (Exception e) {
            log.error("批量邮件发送失败: {}", batchRequest, e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * 处理邮件队列
     * @param emailLogId 邮件日志ID
     * @param emailContent 邮件内容
     */
    @Async
    public void processEmailQueue(Long emailLogId, EmailContent emailContent) {
        try {
            // 1. 获取邮件日志
            EmailLog emailLog = emailLogRepository.findById(emailLogId)
                .orElseThrow(() -> new EmailLogNotFoundException("邮件日志不存在"));
            
            // 2. 检查是否已发送
            if (emailLog.getStatus() != EmailStatus.QUEUED) {
                return;
            }
            
            // 3. 更新状态为发送中
            emailLog.setStatus(EmailStatus.SENDING);
            emailLog.setSentAt(LocalDateTime.now());
            emailLogRepository.save(emailLog);
            
            // 4. 选择邮件提供商
            EmailProvider emailProvider = emailProviderFactory.getProvider(emailContent.getProviderType());
            
            // 5. 发送邮件
            EmailProviderResult providerResult = emailProvider.sendEmail(emailContent);
            
            // 6. 更新发送结果
            if (providerResult.isSuccess()) {
                emailLog.setStatus(EmailStatus.SENT);
                emailLog.setProviderMessageId(providerResult.getMessageId());
            } else {
                emailLog.setStatus(EmailStatus.FAILED);
                emailLog.setErrorMessage(providerResult.getErrorMessage());
            }
            
            emailLog.setCompletedAt(LocalDateTime.now());
            emailLogRepository.save(emailLog);
            
            // 7. 更新统计
            emailStatisticsService.recordEmailSent(emailLog);
            
            log.info("邮件发送完成: logId={}, status={}", emailLogId, emailLog.getStatus());
            
        } catch (Exception e) {
            log.error("处理邮件队列失败: emailLogId={}", emailLogId, e);
            
            // 更新失败状态
            try {
                EmailLog emailLog = emailLogRepository.findById(emailLogId).orElse(null);
                if (emailLog != null) {
                    emailLog.setStatus(EmailStatus.FAILED);
                    emailLog.setErrorMessage(e.getMessage());
                    emailLog.setCompletedAt(LocalDateTime.now());
                    emailLogRepository.save(emailLog);
                }
            } catch (Exception ex) {
                log.error("更新邮件失败状态失败: emailLogId={}", emailLogId, ex);
            }
        }
    }
    
    /**
     * 跟踪邮件打开
     * @param trackingId 跟踪ID
     * @param userAgent 用户代理
     * @param ipAddress IP地址
     */
    public void trackEmailOpen(String trackingId, String userAgent, String ipAddress) {
        try {
            EmailLog emailLog = emailLogRepository.findByTrackingId(trackingId)
                .orElse(null);
            
            if (emailLog != null && !emailLog.isOpened()) {
                emailLog.setOpened(true);
                emailLog.setOpenedAt(LocalDateTime.now());
                emailLog.setOpenUserAgent(userAgent);
                emailLog.setOpenIpAddress(ipAddress);
                emailLogRepository.save(emailLog);
                
                // 更新统计
                emailStatisticsService.recordEmailOpened(emailLog);
                
                log.debug("邮件打开跟踪: trackingId={}", trackingId);
            }
            
        } catch (Exception e) {
            log.error("邮件打开跟踪失败: trackingId={}", trackingId, e);
        }
    }
    
    /**
     * 跟踪邮件点击
     * @param trackingId 跟踪ID
     * @param linkUrl 链接URL
     * @param userAgent 用户代理
     * @param ipAddress IP地址
     */
    public void trackEmailClick(String trackingId, String linkUrl, String userAgent, String ipAddress) {
        try {
            EmailLog emailLog = emailLogRepository.findByTrackingId(trackingId)
                .orElse(null);
            
            if (emailLog != null) {
                // 记录点击事件
                EmailClickEvent clickEvent = EmailClickEvent.builder()
                    .emailLogId(emailLog.getId())
                    .linkUrl(linkUrl)
                    .userAgent(userAgent)
                    .ipAddress(ipAddress)
                    .clickedAt(LocalDateTime.now())
                    .build();
                
                // 保存点击事件（假设有相应的repository）
                // emailClickEventRepository.save(clickEvent);
                
                // 更新邮件日志
                if (!emailLog.isClicked()) {
                    emailLog.setClicked(true);
                    emailLog.setFirstClickedAt(LocalDateTime.now());
                    emailLogRepository.save(emailLog);
                }
                
                // 更新统计
                emailStatisticsService.recordEmailClicked(emailLog, linkUrl);
                
                log.debug("邮件点击跟踪: trackingId={}, linkUrl={}", trackingId, linkUrl);
            }
            
        } catch (Exception e) {
            log.error("邮件点击跟踪失败: trackingId={}, linkUrl={}", trackingId, linkUrl, e);
        }
    }
    
    /**
     * 处理退订请求
     * @param unsubscribeRequest 退订请求
     * @return 退订结果
     */
    @Transactional
    public UnsubscribeResult processUnsubscribe(UnsubscribeRequest unsubscribeRequest) {
        try {
            // 1. 验证退订请求
            validateUnsubscribeRequest(unsubscribeRequest);
            
            // 2. 查找或创建订阅记录
            EmailSubscription subscription = emailSubscriptionRepository
                .findByEmailAndEmailType(unsubscribeRequest.getEmail(), unsubscribeRequest.getEmailType())
                .orElse(EmailSubscription.builder()
                    .email(unsubscribeRequest.getEmail())
                    .emailType(unsubscribeRequest.getEmailType())
                    .subscribed(true)
                    .createdAt(LocalDateTime.now())
                    .build());
            
            // 3. 更新订阅状态
            subscription.setSubscribed(false);
            subscription.setUnsubscribedAt(LocalDateTime.now());
            subscription.setUnsubscribeReason(unsubscribeRequest.getReason());
            subscription.setUpdatedAt(LocalDateTime.now());
            
            emailSubscriptionRepository.save(subscription);
            
            // 4. 记录退订统计
            emailStatisticsService.recordUnsubscribe(subscription);
            
            log.info("用户退订成功: email={}, emailType={}", 
                unsubscribeRequest.getEmail(), unsubscribeRequest.getEmailType());
            
            return UnsubscribeResult.success();
            
        } catch (Exception e) {
            log.error("处理退订请求失败: {}", unsubscribeRequest, e);
            throw new UnsubscribeException("处理退订请求失败", e);
        }
    }
    
    /**
     * 获取邮件统计
     * @param statisticsRequest 统计请求
     * @return 邮件统计
     */
    public EmailStatistics getEmailStatistics(EmailStatisticsRequest statisticsRequest) {
        try {
            return emailStatisticsService.getEmailStatistics(statisticsRequest);
        } catch (Exception e) {
            log.error("获取邮件统计失败: {}", statisticsRequest, e);
            throw new EmailStatisticsException("获取邮件统计失败", e);
        }
    }

    private void validateEmailRequest(EmailSendRequest request) {
        if (StringUtils.isBlank(request.getToEmail())) {
            throw new IllegalArgumentException("收件人邮箱不能为空");
        }
        if (!isValidEmail(request.getToEmail())) {
            throw new IllegalArgumentException("收件人邮箱格式无效");
        }
        if (request.getEmailType() == null) {
            throw new IllegalArgumentException("邮件类型不能为空");
        }
    }

    private boolean checkSubscriptionStatus(String email, EmailType emailType) {
        Optional<EmailSubscription> subscription = emailSubscriptionRepository
            .findByEmailAndEmailType(email, emailType);

        return subscription.map(EmailSubscription::isSubscribed).orElse(true);
    }

    private EmailContent buildEmailContent(EmailSendRequest request) {
        return EmailContent.builder()
            .toEmail(request.getToEmail())
            .toName(request.getToName())
            .fromEmail(emailConfig.getDefaultFromEmail())
            .fromName(emailConfig.getDefaultFromName())
            .subject(request.getSubject())
            .htmlContent(request.getHtmlContent())
            .textContent(request.getTextContent())
            .attachments(request.getAttachments())
            .priority(request.getPriority())
            .providerType(emailConfig.getDefaultProvider())
            .build();
    }

    private EmailLog createEmailLog(EmailSendRequest request, EmailContent content) {
        return EmailLog.builder()
            .toEmail(request.getToEmail())
            .toName(request.getToName())
            .fromEmail(content.getFromEmail())
            .fromName(content.getFromName())
            .subject(content.getSubject())
            .emailType(request.getEmailType())
            .status(EmailStatus.QUEUED)
            .priority(request.getPriority())
            .trackingId(generateTrackingId())
            .scheduledAt(request.getScheduledAt())
            .createdAt(LocalDateTime.now())
            .build();
    }

    private EmailSendResult sendEmailWithContent(EmailSendRequest request, EmailContent content) {
        EmailLog emailLog = createEmailLog(request, content);
        emailLog = emailLogRepository.save(emailLog);

        emailQueueService.queueEmail(emailLog.getId(), content);

        return EmailSendResult.builder()
            .emailLogId(emailLog.getId())
            .status(EmailStatus.QUEUED)
            .queuedAt(LocalDateTime.now())
            .build();
    }

    private boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

    private String generateTrackingId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private void validateUnsubscribeRequest(UnsubscribeRequest request) {
        if (StringUtils.isBlank(request.getEmail())) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }
        if (!isValidEmail(request.getEmail())) {
            throw new IllegalArgumentException("邮箱地址格式无效");
        }
        if (request.getEmailType() == null) {
            throw new IllegalArgumentException("邮件类型不能为空");
        }
    }
}
```

### 2. 邮件模板引擎

#### EmailTemplateEngine - 邮件模板引擎
```java
/**
 * 邮件模板引擎
 * 负责邮件模板的渲染和处理
 */
@Component
@Slf4j
public class EmailTemplateEngine {

    private final TemplateEngine templateEngine;
    private final EmailConfig emailConfig;

    /**
     * 渲染邮件模板
     * @param template 邮件模板
     * @param variables 模板变量
     * @return 渲染后的邮件内容
     */
    public EmailContent renderTemplate(EmailTemplate template, Map<String, Object> variables) {
        try {
            // 1. 准备模板上下文
            Context context = prepareTemplateContext(variables);

            // 2. 渲染HTML内容
            String htmlContent = null;
            if (StringUtils.isNotBlank(template.getHtmlTemplate())) {
                htmlContent = templateEngine.process(template.getHtmlTemplate(), context);
                // 添加跟踪像素
                htmlContent = addTrackingPixel(htmlContent, variables);
                // 处理链接跟踪
                htmlContent = processLinkTracking(htmlContent, variables);
            }

            // 3. 渲染文本内容
            String textContent = null;
            if (StringUtils.isNotBlank(template.getTextTemplate())) {
                textContent = templateEngine.process(template.getTextTemplate(), context);
            }

            // 4. 渲染主题
            String subject = templateEngine.process(template.getSubjectTemplate(), context);

            return EmailContent.builder()
                .subject(subject)
                .htmlContent(htmlContent)
                .textContent(textContent)
                .fromEmail(template.getFromEmail() != null ? template.getFromEmail() : emailConfig.getDefaultFromEmail())
                .fromName(template.getFromName() != null ? template.getFromName() : emailConfig.getDefaultFromName())
                .providerType(emailConfig.getDefaultProvider())
                .build();

        } catch (Exception e) {
            log.error("渲染邮件模板失败: templateId={}", template.getId(), e);
            throw new EmailTemplateRenderException("渲染邮件模板失败", e);
        }
    }

    private Context prepareTemplateContext(Map<String, Object> variables) {
        Context context = new Context();

        // 添加用户变量
        if (variables != null) {
            variables.forEach(context::setVariable);
        }

        // 添加系统变量
        context.setVariable("currentYear", LocalDate.now().getYear());
        context.setVariable("currentDate", LocalDate.now().toString());
        context.setVariable("baseUrl", emailConfig.getBaseUrl());
        context.setVariable("companyName", emailConfig.getCompanyName());

        return context;
    }

    private String addTrackingPixel(String htmlContent, Map<String, Object> variables) {
        String trackingId = (String) variables.get("trackingId");
        if (StringUtils.isNotBlank(trackingId)) {
            String trackingPixel = String.format(
                "<img src=\"%s/api/email/track/open/%s\" width=\"1\" height=\"1\" style=\"display:none;\">",
                emailConfig.getBaseUrl(), trackingId);

            // 在</body>标签前插入跟踪像素
            return htmlContent.replace("</body>", trackingPixel + "</body>");
        }
        return htmlContent;
    }

    private String processLinkTracking(String htmlContent, Map<String, Object> variables) {
        String trackingId = (String) variables.get("trackingId");
        if (StringUtils.isNotBlank(trackingId)) {
            // 简单的链接跟踪处理
            return htmlContent.replaceAll(
                "href=\"(http[^\"]+)\"",
                String.format("href=\"%s/api/email/track/click/%s?url=$1\"", emailConfig.getBaseUrl(), trackingId)
            );
        }
        return htmlContent;
    }
}
```

## 数据模型定义

### 1. 邮件相关实体

#### EmailLog - 邮件日志实体
```java
/**
 * 邮件日志实体
 * 记录邮件发送的详细信息
 */
@Entity
@Table(name = "email_logs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String toEmail;

    private String toName;

    @Column(nullable = false)
    private String fromEmail;

    private String fromName;

    @Column(nullable = false, length = 500)
    private String subject;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EmailType emailType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EmailStatus status;

    @Enumerated(EnumType.STRING)
    private EmailPriority priority;

    @Column(unique = true)
    private String trackingId;

    private String providerMessageId;

    private String errorMessage;

    private LocalDateTime scheduledAt;
    private LocalDateTime sentAt;
    private LocalDateTime completedAt;

    @Column(nullable = false)
    private Boolean opened = false;

    private LocalDateTime openedAt;
    private String openUserAgent;
    private String openIpAddress;

    @Column(nullable = false)
    private Boolean clicked = false;

    private LocalDateTime firstClickedAt;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
```

#### EmailTemplate - 邮件模板实体
```java
/**
 * 邮件模板实体
 * 存储邮件模板信息
 */
@Entity
@Table(name = "email_templates")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String templateCode;

    @Column(nullable = false)
    private String templateName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EmailType emailType;

    @Column(nullable = false, length = 500)
    private String subjectTemplate;

    @Column(columnDefinition = "TEXT")
    private String htmlTemplate;

    @Column(columnDefinition = "TEXT")
    private String textTemplate;

    private String fromEmail;
    private String fromName;

    @Column(nullable = false)
    private Boolean isActive = true;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本邮件服务操作
```java
// 注入邮件服务
@Autowired
private EmailService emailService;

// 发送简单邮件
EmailSendRequest emailRequest = EmailSendRequest.builder()
    .toEmail("<EMAIL>")
    .toName("用户")
    .subject("欢迎使用Ark-Pets AI Enhanced")
    .htmlContent("<h1>欢迎！</h1><p>感谢您注册我们的服务。</p>")
    .textContent("欢迎！感谢您注册我们的服务。")
    .emailType(EmailType.WELCOME)
    .priority(EmailPriority.NORMAL)
    .build();

EmailSendResult result = emailService.sendEmail(emailRequest);
System.out.println("邮件发送状态: " + result.getStatus());

// 发送模板邮件
TemplateEmailRequest templateRequest = TemplateEmailRequest.builder()
    .templateId(1L)
    .toEmail("<EMAIL>")
    .toName("用户")
    .variables(Map.of(
        "username", "小明",
        "verificationCode", "123456",
        "expiryTime", "10分钟"
    ))
    .priority(EmailPriority.HIGH)
    .build();

EmailSendResult templateResult = emailService.sendTemplateEmail(templateRequest);

// 批量发送邮件
List<EmailRecipient> recipients = Arrays.asList(
    new EmailRecipient("<EMAIL>", "用户1"),
    new EmailRecipient("<EMAIL>", "用户2")
);

BatchEmailRequest batchRequest = BatchEmailRequest.builder()
    .batchId("batch123")
    .recipients(recipients)
    .emailType(EmailType.NEWSLETTER)
    .priority(EmailPriority.LOW)
    .build();

CompletableFuture<BatchEmailResult> batchResult = emailService.sendBatchEmails(batchRequest);

// 处理退订
UnsubscribeRequest unsubscribeRequest = UnsubscribeRequest.builder()
    .email("<EMAIL>")
    .emailType(EmailType.NEWSLETTER)
    .reason("不再需要此类邮件")
    .build();

UnsubscribeResult unsubscribeResult = emailService.processUnsubscribe(unsubscribeRequest);

// 获取邮件统计
EmailStatisticsRequest statsRequest = EmailStatisticsRequest.builder()
    .emailType(EmailType.NEWSLETTER)
    .dateRange(new DateRange(LocalDate.now().minusDays(30), LocalDate.now()))
    .build();

EmailStatistics statistics = emailService.getEmailStatistics(statsRequest);
System.out.println("发送总数: " + statistics.getTotalSent());
System.out.println("打开率: " + statistics.getOpenRate() + "%");
System.out.println("点击率: " + statistics.getClickRate() + "%");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的邮件服务实现，支持模板邮件、批量发送、跟踪统计、退订管理等功能
