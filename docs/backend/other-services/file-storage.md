# 文件存储服务 (File Storage)

## 模块概述

文件存储服务模块负责Ark-Pets AI Enhanced项目中文件的上传、存储、管理和访问，包括用户头像、宠物图片、聊天附件、系统资源等文件的处理。支持多种存储后端（本地存储、云存储），提供文件安全、访问控制、缩略图生成、CDN加速等功能。

**核心职责**:
- 文件上传和下载管理
- 多存储后端支持（本地、云存储）
- 文件安全和访问控制
- 图片处理和缩略图生成
- 文件元数据管理和检索

## 核心功能架构

### 1. 文件存储架构

#### 分层文件存储架构模型
```
┌─────────────────────────────────────┐
│           文件服务层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 上传处理  │ 下载服务  │ 文件管理  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           处理中间层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 格式转换  │ 图片处理  │ 安全检查  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           存储适配层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 本地存储  │ 云存储   │ CDN分发  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 文件处理流程

#### 文件上传处理流程图
```mermaid
graph TB
    subgraph "文件上传流程"
        UploadRequest[上传请求]
        ValidateFile[文件验证]
        SecurityCheck[安全检查]
        ProcessFile[文件处理]
        GenerateThumbnail[生成缩略图]
        SaveMetadata[保存元数据]
        StoreFile[存储文件]
        GenerateURL[生成访问URL]
    end
    
    subgraph "文件下载流程"
        DownloadRequest[下载请求]
        AuthCheck[权限检查]
        GetMetadata[获取元数据]
        RetrieveFile[检索文件]
        ProcessResponse[处理响应]
        ServeFile[提供文件]
    end
    
    subgraph "文件管理流程"
        ListFiles[文件列表]
        SearchFiles[文件搜索]
        DeleteFile[删除文件]
        UpdateMetadata[更新元数据]
        CleanupExpired[清理过期]
    end
    
    UploadRequest --> ValidateFile
    ValidateFile --> SecurityCheck
    SecurityCheck --> ProcessFile
    ProcessFile --> GenerateThumbnail
    GenerateThumbnail --> SaveMetadata
    SaveMetadata --> StoreFile
    StoreFile --> GenerateURL
    
    DownloadRequest --> AuthCheck
    AuthCheck --> GetMetadata
    GetMetadata --> RetrieveFile
    RetrieveFile --> ProcessResponse
    ProcessResponse --> ServeFile
    
    ListFiles --> SearchFiles
    SearchFiles --> DeleteFile
    DeleteFile --> UpdateMetadata
    UpdateMetadata --> CleanupExpired
```

## 核心类和接口

### 1. 文件存储主服务

#### FileStorageService - 文件存储主服务
```java
/**
 * 文件存储主服务
 * 负责文件的上传、下载、管理等操作
 */
@Service
@Slf4j
public class FileStorageService {
    
    private final FileMetadataRepository fileMetadataRepository;
    private final StorageProviderFactory storageProviderFactory;
    private final FileProcessorService fileProcessorService;
    private final FileSecurityService fileSecurityService;
    private final ThumbnailGeneratorService thumbnailGeneratorService;
    private final FileStorageConfig fileStorageConfig;
    
    /**
     * 上传文件
     * @param uploadRequest 上传请求
     * @return 上传结果
     */
    @Transactional
    public FileUploadResult uploadFile(FileUploadRequest uploadRequest) {
        try {
            // 1. 验证上传请求
            validateUploadRequest(uploadRequest);
            
            // 2. 安全检查
            fileSecurityService.validateFile(uploadRequest.getFile());
            
            // 3. 生成文件ID和路径
            String fileId = generateFileId();
            String filePath = generateFilePath(uploadRequest);
            
            // 4. 处理文件
            ProcessedFile processedFile = fileProcessorService.processFile(uploadRequest.getFile());
            
            // 5. 生成缩略图（如果是图片）
            List<ThumbnailInfo> thumbnails = new ArrayList<>();
            if (isImageFile(processedFile)) {
                thumbnails = thumbnailGeneratorService.generateThumbnails(processedFile);
            }
            
            // 6. 获取存储提供商
            StorageProvider storageProvider = storageProviderFactory.getProvider(uploadRequest.getStorageType());
            
            // 7. 存储文件
            StorageResult storageResult = storageProvider.store(filePath, processedFile);
            
            // 8. 存储缩略图
            for (ThumbnailInfo thumbnail : thumbnails) {
                String thumbnailPath = generateThumbnailPath(filePath, thumbnail.getSize());
                storageProvider.store(thumbnailPath, thumbnail.getData());
            }
            
            // 9. 保存文件元数据
            FileMetadata fileMetadata = FileMetadata.builder()
                .fileId(fileId)
                .originalName(uploadRequest.getFile().getOriginalFilename())
                .fileName(processedFile.getFileName())
                .filePath(filePath)
                .fileSize(processedFile.getSize())
                .mimeType(processedFile.getMimeType())
                .fileType(determineFileType(processedFile))
                .storageType(uploadRequest.getStorageType())
                .uploadedBy(uploadRequest.getUserId())
                .category(uploadRequest.getCategory())
                .tags(uploadRequest.getTags())
                .isPublic(uploadRequest.isPublic())
                .expiresAt(uploadRequest.getExpiresAt())
                .thumbnails(thumbnails)
                .checksum(processedFile.getChecksum())
                .createdAt(LocalDateTime.now())
                .build();
            
            fileMetadata = fileMetadataRepository.save(fileMetadata);
            
            // 10. 生成访问URL
            String accessUrl = generateAccessUrl(fileMetadata);
            
            log.info("文件上传成功: fileId={}, originalName={}, size={}", 
                fileId, uploadRequest.getFile().getOriginalFilename(), processedFile.getSize());
            
            return FileUploadResult.builder()
                .fileId(fileId)
                .fileName(processedFile.getFileName())
                .fileSize(processedFile.getSize())
                .mimeType(processedFile.getMimeType())
                .accessUrl(accessUrl)
                .thumbnails(thumbnails.stream()
                    .collect(Collectors.toMap(
                        ThumbnailInfo::getSize,
                        t -> generateThumbnailUrl(fileMetadata, t.getSize())
                    )))
                .uploadedAt(fileMetadata.getCreatedAt())
                .build();
                
        } catch (Exception e) {
            log.error("文件上传失败: {}", uploadRequest, e);
            throw new FileUploadException("文件上传失败", e);
        }
    }
    
    /**
     * 下载文件
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 文件下载响应
     */
    public FileDownloadResponse downloadFile(String fileId, String userId) {
        try {
            // 1. 获取文件元数据
            FileMetadata fileMetadata = fileMetadataRepository.findByFileId(fileId)
                .orElseThrow(() -> new FileNotFoundException("文件不存在: " + fileId));
            
            // 2. 检查访问权限
            if (!fileSecurityService.hasAccessPermission(fileMetadata, userId)) {
                throw new FileAccessDeniedException("无权访问文件: " + fileId);
            }
            
            // 3. 检查文件是否过期
            if (fileMetadata.getExpiresAt() != null && 
                fileMetadata.getExpiresAt().isBefore(LocalDateTime.now())) {
                throw new FileExpiredException("文件已过期: " + fileId);
            }
            
            // 4. 获取存储提供商
            StorageProvider storageProvider = storageProviderFactory.getProvider(fileMetadata.getStorageType());
            
            // 5. 检索文件
            StorageFile storageFile = storageProvider.retrieve(fileMetadata.getFilePath());
            
            // 6. 更新下载统计
            updateDownloadStatistics(fileMetadata, userId);
            
            return FileDownloadResponse.builder()
                .fileId(fileId)
                .fileName(fileMetadata.getOriginalName())
                .mimeType(fileMetadata.getMimeType())
                .fileSize(fileMetadata.getFileSize())
                .inputStream(storageFile.getInputStream())
                .lastModified(fileMetadata.getUpdatedAt())
                .build();
                
        } catch (Exception e) {
            log.error("文件下载失败: fileId={}, userId={}", fileId, userId, e);
            throw new FileDownloadException("文件下载失败", e);
        }
    }
    
    /**
     * 获取文件信息
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 文件信息
     */
    public FileInfoResponse getFileInfo(String fileId, String userId) {
        try {
            FileMetadata fileMetadata = fileMetadataRepository.findByFileId(fileId)
                .orElseThrow(() -> new FileNotFoundException("文件不存在: " + fileId));
            
            // 检查访问权限
            if (!fileSecurityService.hasAccessPermission(fileMetadata, userId)) {
                throw new FileAccessDeniedException("无权访问文件: " + fileId);
            }
            
            return FileInfoResponse.builder()
                .fileId(fileMetadata.getFileId())
                .originalName(fileMetadata.getOriginalName())
                .fileName(fileMetadata.getFileName())
                .fileSize(fileMetadata.getFileSize())
                .mimeType(fileMetadata.getMimeType())
                .fileType(fileMetadata.getFileType())
                .category(fileMetadata.getCategory())
                .tags(fileMetadata.getTags())
                .isPublic(fileMetadata.getIsPublic())
                .uploadedBy(fileMetadata.getUploadedBy())
                .uploadedAt(fileMetadata.getCreatedAt())
                .expiresAt(fileMetadata.getExpiresAt())
                .accessUrl(generateAccessUrl(fileMetadata))
                .thumbnails(fileMetadata.getThumbnails().stream()
                    .collect(Collectors.toMap(
                        ThumbnailInfo::getSize,
                        t -> generateThumbnailUrl(fileMetadata, t.getSize())
                    )))
                .build();
                
        } catch (Exception e) {
            log.error("获取文件信息失败: fileId={}, userId={}", fileId, userId, e);
            throw new FileInfoException("获取文件信息失败", e);
        }
    }
    
    /**
     * 删除文件
     * @param fileId 文件ID
     * @param userId 用户ID
     */
    @Transactional
    public void deleteFile(String fileId, String userId) {
        try {
            FileMetadata fileMetadata = fileMetadataRepository.findByFileId(fileId)
                .orElseThrow(() -> new FileNotFoundException("文件不存在: " + fileId));
            
            // 检查删除权限
            if (!fileSecurityService.hasDeletePermission(fileMetadata, userId)) {
                throw new FileAccessDeniedException("无权删除文件: " + fileId);
            }
            
            // 获取存储提供商
            StorageProvider storageProvider = storageProviderFactory.getProvider(fileMetadata.getStorageType());
            
            // 删除主文件
            storageProvider.delete(fileMetadata.getFilePath());
            
            // 删除缩略图
            for (ThumbnailInfo thumbnail : fileMetadata.getThumbnails()) {
                String thumbnailPath = generateThumbnailPath(fileMetadata.getFilePath(), thumbnail.getSize());
                storageProvider.delete(thumbnailPath);
            }
            
            // 软删除元数据
            fileMetadata.setDeleted(true);
            fileMetadata.setDeletedAt(LocalDateTime.now());
            fileMetadata.setDeletedBy(userId);
            fileMetadataRepository.save(fileMetadata);
            
            log.info("文件删除成功: fileId={}, userId={}", fileId, userId);
            
        } catch (Exception e) {
            log.error("文件删除失败: fileId={}, userId={}", fileId, userId, e);
            throw new FileDeletionException("文件删除失败", e);
        }
    }
    
    /**
     * 搜索文件
     * @param searchRequest 搜索请求
     * @return 搜索结果
     */
    public Page<FileSearchResult> searchFiles(FileSearchRequest searchRequest) {
        try {
            return fileMetadataRepository.searchFiles(
                searchRequest.getKeyword(),
                searchRequest.getFileType(),
                searchRequest.getCategory(),
                searchRequest.getUserId(),
                searchRequest.getDateRange(),
                searchRequest.getPageable()
            );
        } catch (Exception e) {
            log.error("文件搜索失败: {}", searchRequest, e);
            throw new FileSearchException("文件搜索失败", e);
        }
    }
    
    /**
     * 获取用户文件列表
     * @param userId 用户ID
     * @param category 文件分类
     * @param pageable 分页参数
     * @return 文件列表
     */
    public Page<FileListItem> getUserFiles(String userId, String category, Pageable pageable) {
        try {
            return fileMetadataRepository.findUserFiles(userId, category, pageable);
        } catch (Exception e) {
            log.error("获取用户文件列表失败: userId={}, category={}", userId, category, e);
            throw new FileListException("获取用户文件列表失败", e);
        }
    }
    
    /**
     * 清理过期文件
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupExpiredFiles() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<FileMetadata> expiredFiles = fileMetadataRepository.findExpiredFiles(now);
            
            for (FileMetadata fileMetadata : expiredFiles) {
                try {
                    deleteExpiredFile(fileMetadata);
                } catch (Exception e) {
                    log.error("删除过期文件失败: fileId={}", fileMetadata.getFileId(), e);
                }
            }
            
            log.info("清理过期文件完成: count={}", expiredFiles.size());
            
        } catch (Exception e) {
            log.error("清理过期文件失败", e);
        }
    }

    private void validateUploadRequest(FileUploadRequest request) {
        if (request.getFile() == null || request.getFile().isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }
        if (StringUtils.isBlank(request.getUserId())) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (request.getFile().getSize() > fileStorageConfig.getMaxFileSize()) {
            throw new FileSizeExceededException("文件大小超出限制");
        }
    }

    private String generateFileId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private String generateFilePath(FileUploadRequest request) {
        LocalDate now = LocalDate.now();
        return String.format("%s/%d/%02d/%02d/%s",
            request.getCategory(),
            now.getYear(),
            now.getMonthValue(),
            now.getDayOfMonth(),
            generateFileId());
    }

    private boolean isImageFile(ProcessedFile file) {
        return file.getMimeType().startsWith("image/");
    }

    private FileType determineFileType(ProcessedFile file) {
        String mimeType = file.getMimeType();
        if (mimeType.startsWith("image/")) {
            return FileType.IMAGE;
        } else if (mimeType.startsWith("video/")) {
            return FileType.VIDEO;
        } else if (mimeType.startsWith("audio/")) {
            return FileType.AUDIO;
        } else if (mimeType.equals("application/pdf")) {
            return FileType.DOCUMENT;
        } else {
            return FileType.OTHER;
        }
    }

    private String generateAccessUrl(FileMetadata fileMetadata) {
        return String.format("%s/api/files/%s/download",
            fileStorageConfig.getBaseUrl(),
            fileMetadata.getFileId());
    }

    private String generateThumbnailUrl(FileMetadata fileMetadata, String size) {
        return String.format("%s/api/files/%s/thumbnail/%s",
            fileStorageConfig.getBaseUrl(),
            fileMetadata.getFileId(),
            size);
    }

    private String generateThumbnailPath(String originalPath, String size) {
        return originalPath + "_thumb_" + size;
    }

    private void updateDownloadStatistics(FileMetadata fileMetadata, String userId) {
        // 更新下载统计
        fileMetadata.setDownloadCount(fileMetadata.getDownloadCount() + 1);
        fileMetadata.setLastAccessedAt(LocalDateTime.now());
        fileMetadataRepository.save(fileMetadata);
    }

    private void deleteExpiredFile(FileMetadata fileMetadata) {
        StorageProvider storageProvider = storageProviderFactory.getProvider(fileMetadata.getStorageType());

        // 删除主文件
        storageProvider.delete(fileMetadata.getFilePath());

        // 删除缩略图
        for (ThumbnailInfo thumbnail : fileMetadata.getThumbnails()) {
            String thumbnailPath = generateThumbnailPath(fileMetadata.getFilePath(), thumbnail.getSize());
            storageProvider.delete(thumbnailPath);
        }

        // 标记为已删除
        fileMetadata.setDeleted(true);
        fileMetadata.setDeletedAt(LocalDateTime.now());
        fileMetadata.setDeletedBy("SYSTEM");
        fileMetadataRepository.save(fileMetadata);
    }
}
```

### 2. 存储提供商工厂

#### StorageProviderFactory - 存储提供商工厂
```java
/**
 * 存储提供商工厂
 * 负责根据存储类型创建相应的存储提供商
 */
@Component
public class StorageProviderFactory {

    private final Map<StorageType, StorageProvider> providers = new HashMap<>();

    @Autowired
    public StorageProviderFactory(LocalStorageProvider localProvider,
                                 S3StorageProvider s3Provider,
                                 OSSStorageProvider ossProvider) {
        providers.put(StorageType.LOCAL, localProvider);
        providers.put(StorageType.S3, s3Provider);
        providers.put(StorageType.OSS, ossProvider);
    }

    /**
     * 获取存储提供商
     * @param storageType 存储类型
     * @return 存储提供商
     */
    public StorageProvider getProvider(StorageType storageType) {
        StorageProvider provider = providers.get(storageType);
        if (provider == null) {
            throw new UnsupportedStorageTypeException("不支持的存储类型: " + storageType);
        }
        return provider;
    }
}
```

### 3. 本地存储提供商

#### LocalStorageProvider - 本地存储提供商
```java
/**
 * 本地存储提供商
 * 负责本地文件系统的存储操作
 */
@Component
@Slf4j
public class LocalStorageProvider implements StorageProvider {

    private final FileStorageConfig config;

    @Override
    public StorageResult store(String path, ProcessedFile file) {
        try {
            Path fullPath = Paths.get(config.getLocalStoragePath(), path);

            // 创建目录
            Files.createDirectories(fullPath.getParent());

            // 写入文件
            Files.copy(file.getInputStream(), fullPath, StandardCopyOption.REPLACE_EXISTING);

            return StorageResult.builder()
                .success(true)
                .path(path)
                .size(file.getSize())
                .storedAt(LocalDateTime.now())
                .build();

        } catch (Exception e) {
            log.error("本地存储文件失败: path={}", path, e);
            return StorageResult.failed(e.getMessage());
        }
    }

    @Override
    public StorageFile retrieve(String path) {
        try {
            Path fullPath = Paths.get(config.getLocalStoragePath(), path);

            if (!Files.exists(fullPath)) {
                throw new FileNotFoundException("文件不存在: " + path);
            }

            return StorageFile.builder()
                .path(path)
                .inputStream(Files.newInputStream(fullPath))
                .size(Files.size(fullPath))
                .lastModified(Files.getLastModifiedTime(fullPath).toInstant())
                .build();

        } catch (Exception e) {
            log.error("本地检索文件失败: path={}", path, e);
            throw new FileRetrievalException("本地检索文件失败", e);
        }
    }

    @Override
    public boolean delete(String path) {
        try {
            Path fullPath = Paths.get(config.getLocalStoragePath(), path);
            return Files.deleteIfExists(fullPath);
        } catch (Exception e) {
            log.error("本地删除文件失败: path={}", path, e);
            return false;
        }
    }

    @Override
    public boolean exists(String path) {
        try {
            Path fullPath = Paths.get(config.getLocalStoragePath(), path);
            return Files.exists(fullPath);
        } catch (Exception e) {
            log.error("检查本地文件存在性失败: path={}", path, e);
            return false;
        }
    }
}
```

## 数据模型定义

### 1. 文件相关实体

#### FileMetadata - 文件元数据实体
```java
/**
 * 文件元数据实体
 * 存储文件的详细信息
 */
@Entity
@Table(name = "file_metadata")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileMetadata {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String fileId;

    @Column(nullable = false)
    private String originalName;

    @Column(nullable = false)
    private String fileName;

    @Column(nullable = false)
    private String filePath;

    @Column(nullable = false)
    private Long fileSize;

    @Column(nullable = false)
    private String mimeType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FileType fileType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StorageType storageType;

    @Column(nullable = false)
    private String uploadedBy;

    @Column(length = 50)
    private String category;

    @ElementCollection
    @CollectionTable(name = "file_tags")
    private Set<String> tags;

    @Column(nullable = false)
    private Boolean isPublic = false;

    private LocalDateTime expiresAt;

    @Column(columnDefinition = "JSON")
    private List<ThumbnailInfo> thumbnails;

    @Column(length = 64)
    private String checksum;

    @Column(nullable = false)
    private Integer downloadCount = 0;

    private LocalDateTime lastAccessedAt;

    @Column(nullable = false)
    private Boolean deleted = false;

    private LocalDateTime deletedAt;
    private String deletedBy;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本文件存储操作
```java
// 注入文件存储服务
@Autowired
private FileStorageService fileStorageService;

// 上传文件
FileUploadRequest uploadRequest = FileUploadRequest.builder()
    .file(multipartFile)
    .userId("user123")
    .category("avatar")
    .tags(Set.of("profile", "image"))
    .isPublic(true)
    .storageType(StorageType.LOCAL)
    .expiresAt(LocalDateTime.now().plusYears(1))
    .build();

FileUploadResult uploadResult = fileStorageService.uploadFile(uploadRequest);
System.out.println("文件上传成功: " + uploadResult.getFileId());
System.out.println("访问URL: " + uploadResult.getAccessUrl());

// 下载文件
FileDownloadResponse downloadResponse = fileStorageService.downloadFile("file123", "user123");
System.out.println("文件名: " + downloadResponse.getFileName());
System.out.println("文件大小: " + downloadResponse.getFileSize());

// 获取文件信息
FileInfoResponse fileInfo = fileStorageService.getFileInfo("file123", "user123");
System.out.println("文件类型: " + fileInfo.getFileType());
System.out.println("上传时间: " + fileInfo.getUploadedAt());

// 搜索文件
FileSearchRequest searchRequest = FileSearchRequest.builder()
    .keyword("avatar")
    .fileType(FileType.IMAGE)
    .category("profile")
    .userId("user123")
    .pageable(PageRequest.of(0, 10))
    .build();

Page<FileSearchResult> searchResults = fileStorageService.searchFiles(searchRequest);
System.out.println("找到文件数: " + searchResults.getTotalElements());

// 删除文件
fileStorageService.deleteFile("file123", "user123");
System.out.println("文件删除成功");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的文件存储服务实现，支持多存储后端、文件处理、安全控制、缩略图生成等功能
