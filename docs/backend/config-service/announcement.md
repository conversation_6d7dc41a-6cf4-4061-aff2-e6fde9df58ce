# 公告管理 (Announcement)

## 模块概述

公告管理模块负责Ark-Pets AI Enhanced项目中系统公告的发布、管理和分发，包括公告创建、定时发布、目标用户筛选、多渠道推送、阅读状态跟踪等功能。提供灵活的公告管理机制，确保重要信息能够及时准确地传达给目标用户。

**核心职责**:
- 公告内容创建和编辑管理
- 公告发布时间和策略控制
- 目标用户群体筛选和定向推送
- 多渠道公告分发（应用内、邮件、推送）
- 公告阅读状态和效果统计

## 核心功能架构

### 1. 公告管理架构

#### 分层公告管理模型
```
┌─────────────────────────────────────┐
│           公告管理系统               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 内容管理  │ 发布控制  │ 效果统计  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           分发推送层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 应用内   │ 邮件推送  │ 移动推送  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据存储层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 公告数据  │ 用户状态  │ 统计数据  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 公告发布流程

#### 公告发布分发流程图
```mermaid
graph TB
    subgraph "公告创建流程"
        CreateRequest[创建公告请求]
        ValidateContent[内容验证]
        SetTargeting[设置目标用户]
        SchedulePublish[设置发布时间]
        SaveDraft[保存草稿]
        ReviewApproval[审核批准]
        PublishReady[准备发布]
    end
    
    subgraph "公告发布流程"
        TriggerPublish[触发发布]
        FilterUsers[筛选目标用户]
        GenerateContent[生成推送内容]
        MultiChannelPush[多渠道推送]
        TrackDelivery[跟踪投递]
        UpdateStatus[更新状态]
    end
    
    subgraph "用户交互流程"
        UserReceive[用户接收]
        UserRead[用户阅读]
        UserAction[用户操作]
        RecordInteraction[记录交互]
        UpdateStatistics[更新统计]
    end
    
    CreateRequest --> ValidateContent
    ValidateContent --> SetTargeting
    SetTargeting --> SchedulePublish
    SchedulePublish --> SaveDraft
    SaveDraft --> ReviewApproval
    ReviewApproval --> PublishReady
    
    PublishReady --> TriggerPublish
    TriggerPublish --> FilterUsers
    FilterUsers --> GenerateContent
    GenerateContent --> MultiChannelPush
    MultiChannelPush --> TrackDelivery
    TrackDelivery --> UpdateStatus
    
    UpdateStatus --> UserReceive
    UserReceive --> UserRead
    UserRead --> UserAction
    UserAction --> RecordInteraction
    RecordInteraction --> UpdateStatistics
```

## 核心类和接口

### 1. 公告管理服务

#### AnnouncementService - 公告管理主服务
```java
/**
 * 公告管理主服务
 * 负责公告的完整生命周期管理
 */
@Service
@Slf4j
public class AnnouncementService {
    
    private final AnnouncementRepository announcementRepository;
    private final AnnouncementReadRepository announcementReadRepository;
    private final UserRepository userRepository;
    private final AnnouncementTargetingService targetingService;
    private final AnnouncementDeliveryService deliveryService;
    private final AnnouncementStatisticsService statisticsService;
    private final SecurityService securityService;
    private final EventPublisher eventPublisher;
    
    /**
     * 创建公告
     * @param createRequest 创建请求
     * @param creatorId 创建者ID
     * @return 创建的公告
     */
    @Transactional
    public Announcement createAnnouncement(CreateAnnouncementRequest createRequest, String creatorId) {
        try {
            // 1. 验证创建权限
            securityService.validateAnnouncementPermission(creatorId, "ANNOUNCEMENT_CREATE");
            
            // 2. 验证公告内容
            validateAnnouncementContent(createRequest);
            
            // 3. 创建公告实体
            Announcement announcement = Announcement.builder()
                .title(createRequest.getTitle())
                .content(createRequest.getContent())
                .type(createRequest.getType())
                .priority(createRequest.getPriority())
                .category(createRequest.getCategory())
                .targetingRules(createRequest.getTargetingRules())
                .channels(createRequest.getChannels())
                .scheduledAt(createRequest.getScheduledAt())
                .expiresAt(createRequest.getExpiresAt())
                .status(AnnouncementStatus.DRAFT)
                .createdBy(creatorId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
            
            // 4. 保存公告
            announcement = announcementRepository.save(announcement);
            
            // 5. 如果设置了立即发布，则发布公告
            if (createRequest.isPublishImmediately()) {
                publishAnnouncement(announcement.getId(), creatorId);
            }
            
            // 6. 发布创建事件
            eventPublisher.publishAnnouncementCreated(announcement, creatorId);
            
            log.info("公告创建成功: id={}, title={}, creator={}", 
                announcement.getId(), announcement.getTitle(), creatorId);
            
            return announcement;
            
        } catch (Exception e) {
            log.error("创建公告失败: creator={}", creatorId, e);
            throw new AnnouncementCreationException("创建公告失败", e);
        }
    }
    
    /**
     * 更新公告
     * @param announcementId 公告ID
     * @param updateRequest 更新请求
     * @param updaterId 更新者ID
     * @return 更新后的公告
     */
    @Transactional
    public Announcement updateAnnouncement(Long announcementId, UpdateAnnouncementRequest updateRequest, String updaterId) {
        try {
            // 1. 验证更新权限
            securityService.validateAnnouncementPermission(updaterId, "ANNOUNCEMENT_UPDATE");
            
            // 2. 获取公告
            Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new AnnouncementNotFoundException("公告不存在: " + announcementId));
            
            // 3. 检查是否可以更新
            if (!canUpdateAnnouncement(announcement)) {
                throw new AnnouncementUpdateNotAllowedException("当前状态不允许更新");
            }
            
            // 4. 更新公告内容
            updateAnnouncementContent(announcement, updateRequest);
            announcement.setUpdatedBy(updaterId);
            announcement.setUpdatedAt(LocalDateTime.now());
            
            // 5. 保存更新
            announcement = announcementRepository.save(announcement);
            
            // 6. 发布更新事件
            eventPublisher.publishAnnouncementUpdated(announcement, updaterId);
            
            log.info("公告更新成功: id={}, updater={}", announcementId, updaterId);
            
            return announcement;
            
        } catch (Exception e) {
            log.error("更新公告失败: id={}, updater={}", announcementId, updaterId, e);
            throw new AnnouncementUpdateException("更新公告失败", e);
        }
    }
    
    /**
     * 发布公告
     * @param announcementId 公告ID
     * @param publisherId 发布者ID
     * @return 发布结果
     */
    @Transactional
    public AnnouncementPublishResult publishAnnouncement(Long announcementId, String publisherId) {
        try {
            // 1. 验证发布权限
            securityService.validateAnnouncementPermission(publisherId, "ANNOUNCEMENT_PUBLISH");
            
            // 2. 获取公告
            Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new AnnouncementNotFoundException("公告不存在: " + announcementId));
            
            // 3. 检查是否可以发布
            if (!canPublishAnnouncement(announcement)) {
                throw new AnnouncementPublishNotAllowedException("当前状态不允许发布");
            }
            
            // 4. 筛选目标用户
            List<String> targetUserIds = targetingService.getTargetUsers(announcement.getTargetingRules());
            
            // 5. 更新公告状态
            announcement.setStatus(AnnouncementStatus.PUBLISHED);
            announcement.setPublishedBy(publisherId);
            announcement.setPublishedAt(LocalDateTime.now());
            announcement.setTargetUserCount(targetUserIds.size());
            announcementRepository.save(announcement);
            
            // 6. 异步分发公告
            deliveryService.deliverAnnouncement(announcement, targetUserIds);
            
            // 7. 发布发布事件
            eventPublisher.publishAnnouncementPublished(announcement, publisherId);
            
            log.info("公告发布成功: id={}, targetUsers={}, publisher={}", 
                announcementId, targetUserIds.size(), publisherId);
            
            return AnnouncementPublishResult.builder()
                .announcementId(announcementId)
                .targetUserCount(targetUserIds.size())
                .publishedAt(announcement.getPublishedAt())
                .channels(announcement.getChannels())
                .build();
                
        } catch (Exception e) {
            log.error("发布公告失败: id={}, publisher={}", announcementId, publisherId, e);
            throw new AnnouncementPublishException("发布公告失败", e);
        }
    }
    
    /**
     * 撤回公告
     * @param announcementId 公告ID
     * @param withdrawerId 撤回者ID
     * @param reason 撤回原因
     */
    @Transactional
    public void withdrawAnnouncement(Long announcementId, String withdrawerId, String reason) {
        try {
            // 1. 验证撤回权限
            securityService.validateAnnouncementPermission(withdrawerId, "ANNOUNCEMENT_WITHDRAW");
            
            // 2. 获取公告
            Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new AnnouncementNotFoundException("公告不存在: " + announcementId));
            
            // 3. 检查是否可以撤回
            if (!canWithdrawAnnouncement(announcement)) {
                throw new AnnouncementWithdrawNotAllowedException("当前状态不允许撤回");
            }
            
            // 4. 更新公告状态
            announcement.setStatus(AnnouncementStatus.WITHDRAWN);
            announcement.setWithdrawnBy(withdrawerId);
            announcement.setWithdrawnAt(LocalDateTime.now());
            announcement.setWithdrawReason(reason);
            announcementRepository.save(announcement);
            
            // 5. 停止分发
            deliveryService.stopDelivery(announcementId);
            
            // 6. 发布撤回事件
            eventPublisher.publishAnnouncementWithdrawn(announcement, withdrawerId, reason);
            
            log.info("公告撤回成功: id={}, withdrawer={}, reason={}", announcementId, withdrawerId, reason);
            
        } catch (Exception e) {
            log.error("撤回公告失败: id={}, withdrawer={}", announcementId, withdrawerId, e);
            throw new AnnouncementWithdrawException("撤回公告失败", e);
        }
    }
    
    /**
     * 获取用户公告列表
     * @param userId 用户ID
     * @param filter 过滤条件
     * @param pageable 分页参数
     * @return 公告列表
     */
    public Page<AnnouncementResponse> getUserAnnouncements(String userId, AnnouncementFilter filter, Pageable pageable) {
        try {
            // 1. 获取用户可见的公告
            Page<Announcement> announcements = announcementRepository.findUserAnnouncements(userId, filter, pageable);
            
            // 2. 获取用户阅读状态
            Map<Long, AnnouncementRead> readStatusMap = getReadStatusMap(userId, 
                announcements.getContent().stream().map(Announcement::getId).collect(Collectors.toList()));
            
            // 3. 构建响应对象
            return announcements.map(announcement -> {
                AnnouncementRead readStatus = readStatusMap.get(announcement.getId());
                return buildAnnouncementResponse(announcement, readStatus);
            });
            
        } catch (Exception e) {
            log.error("获取用户公告列表失败: userId={}", userId, e);
            throw new AnnouncementAccessException("获取用户公告列表失败", e);
        }
    }
    
    /**
     * 标记公告为已读
     * @param announcementId 公告ID
     * @param userId 用户ID
     */
    @Transactional
    public void markAnnouncementAsRead(Long announcementId, String userId) {
        try {
            // 1. 检查公告是否存在
            Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new AnnouncementNotFoundException("公告不存在: " + announcementId));
            
            // 2. 检查是否已读
            Optional<AnnouncementRead> existingRead = announcementReadRepository
                .findByAnnouncementIdAndUserId(announcementId, userId);
            
            if (existingRead.isPresent()) {
                return; // 已经标记为已读
            }
            
            // 3. 创建阅读记录
            AnnouncementRead announcementRead = AnnouncementRead.builder()
                .announcementId(announcementId)
                .userId(userId)
                .readAt(LocalDateTime.now())
                .build();
            
            announcementReadRepository.save(announcementRead);
            
            // 4. 更新统计信息
            statisticsService.incrementReadCount(announcementId);
            
            // 5. 发布阅读事件
            eventPublisher.publishAnnouncementRead(announcementId, userId);
            
            log.debug("公告标记为已读: id={}, userId={}", announcementId, userId);
            
        } catch (Exception e) {
            log.error("标记公告已读失败: id={}, userId={}", announcementId, userId, e);
            throw new AnnouncementReadException("标记公告已读失败", e);
        }
    }
    
    /**
     * 获取公告统计信息
     * @param announcementId 公告ID
     * @return 统计信息
     */
    public AnnouncementStatistics getAnnouncementStatistics(Long announcementId) {
        try {
            // 1. 验证公告存在
            Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new AnnouncementNotFoundException("公告不存在: " + announcementId));
            
            // 2. 获取统计数据
            return statisticsService.getAnnouncementStatistics(announcementId);
            
        } catch (Exception e) {
            log.error("获取公告统计失败: id={}", announcementId, e);
            throw new AnnouncementStatisticsException("获取公告统计失败", e);
        }
    }
    
    /**
     * 搜索公告
     * @param searchRequest 搜索请求
     * @return 搜索结果
     */
    public Page<Announcement> searchAnnouncements(AnnouncementSearchRequest searchRequest) {
        try {
            return announcementRepository.searchAnnouncements(
                searchRequest.getKeyword(),
                searchRequest.getType(),
                searchRequest.getStatus(),
                searchRequest.getCategory(),
                searchRequest.getDateRange(),
                searchRequest.getPageable()
            );
        } catch (Exception e) {
            log.error("搜索公告失败: {}", searchRequest, e);
            throw new AnnouncementSearchException("搜索公告失败", e);
        }
    }
    
    /**
     * 定时发布公告
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void processScheduledAnnouncements() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<Announcement> scheduledAnnouncements = announcementRepository
                .findScheduledAnnouncements(now);
            
            for (Announcement announcement : scheduledAnnouncements) {
                try {
                    publishAnnouncement(announcement.getId(), "SYSTEM");
                    log.info("定时发布公告成功: id={}", announcement.getId());
                } catch (Exception e) {
                    log.error("定时发布公告失败: id={}", announcement.getId(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("处理定时公告失败", e);
        }
    }
    
    /**
     * 清理过期公告
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredAnnouncements() {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<Announcement> expiredAnnouncements = announcementRepository
                .findExpiredAnnouncements(now);
            
            for (Announcement announcement : expiredAnnouncements) {
                announcement.setStatus(AnnouncementStatus.EXPIRED);
                announcementRepository.save(announcement);
            }
            
            log.info("清理过期公告完成: count={}", expiredAnnouncements.size());
            
        } catch (Exception e) {
            log.error("清理过期公告失败", e);
        }
    }

    private void validateAnnouncementContent(CreateAnnouncementRequest request) {
        if (StringUtils.isBlank(request.getTitle())) {
            throw new IllegalArgumentException("公告标题不能为空");
        }
        if (request.getTitle().length() > 200) {
            throw new IllegalArgumentException("公告标题过长");
        }
        if (StringUtils.isBlank(request.getContent())) {
            throw new IllegalArgumentException("公告内容不能为空");
        }
        if (request.getContent().length() > 10000) {
            throw new IllegalArgumentException("公告内容过长");
        }
    }

    private boolean canUpdateAnnouncement(Announcement announcement) {
        return announcement.getStatus() == AnnouncementStatus.DRAFT ||
               announcement.getStatus() == AnnouncementStatus.SCHEDULED;
    }

    private void updateAnnouncementContent(Announcement announcement, UpdateAnnouncementRequest request) {
        if (StringUtils.isNotBlank(request.getTitle())) {
            announcement.setTitle(request.getTitle());
        }
        if (StringUtils.isNotBlank(request.getContent())) {
            announcement.setContent(request.getContent());
        }
        if (request.getType() != null) {
            announcement.setType(request.getType());
        }
        if (request.getPriority() != null) {
            announcement.setPriority(request.getPriority());
        }
        if (request.getCategory() != null) {
            announcement.setCategory(request.getCategory());
        }
        if (request.getTargetingRules() != null) {
            announcement.setTargetingRules(request.getTargetingRules());
        }
        if (request.getChannels() != null) {
            announcement.setChannels(request.getChannels());
        }
        if (request.getScheduledAt() != null) {
            announcement.setScheduledAt(request.getScheduledAt());
        }
        if (request.getExpiresAt() != null) {
            announcement.setExpiresAt(request.getExpiresAt());
        }
    }

    private boolean canPublishAnnouncement(Announcement announcement) {
        return announcement.getStatus() == AnnouncementStatus.DRAFT ||
               announcement.getStatus() == AnnouncementStatus.SCHEDULED;
    }

    private boolean canWithdrawAnnouncement(Announcement announcement) {
        return announcement.getStatus() == AnnouncementStatus.PUBLISHED;
    }

    private Map<Long, AnnouncementRead> getReadStatusMap(String userId, List<Long> announcementIds) {
        List<AnnouncementRead> readRecords = announcementReadRepository
            .findByUserIdAndAnnouncementIdIn(userId, announcementIds);

        return readRecords.stream()
            .collect(Collectors.toMap(AnnouncementRead::getAnnouncementId, Function.identity()));
    }

    private AnnouncementResponse buildAnnouncementResponse(Announcement announcement, AnnouncementRead readStatus) {
        return AnnouncementResponse.builder()
            .id(announcement.getId())
            .title(announcement.getTitle())
            .content(announcement.getContent())
            .type(announcement.getType())
            .priority(announcement.getPriority())
            .category(announcement.getCategory())
            .status(announcement.getStatus())
            .publishedAt(announcement.getPublishedAt())
            .expiresAt(announcement.getExpiresAt())
            .isRead(readStatus != null)
            .readAt(readStatus != null ? readStatus.getReadAt() : null)
            .build();
    }
}
```

### 2. 公告分发服务

#### AnnouncementDeliveryService - 公告分发服务
```java
/**
 * 公告分发服务
 * 负责公告的多渠道分发和投递
 */
@Service
@Slf4j
public class AnnouncementDeliveryService {

    private final EmailService emailService;
    private final PushNotificationService pushNotificationService;
    private final WebSocketService webSocketService;
    private final AnnouncementDeliveryRepository deliveryRepository;
    private final UserPreferenceService userPreferenceService;

    /**
     * 分发公告
     * @param announcement 公告
     * @param targetUserIds 目标用户ID列表
     */
    @Async
    public void deliverAnnouncement(Announcement announcement, List<String> targetUserIds) {
        try {
            log.info("开始分发公告: id={}, targetUsers={}", announcement.getId(), targetUserIds.size());

            // 按渠道分发
            for (AnnouncementChannel channel : announcement.getChannels()) {
                deliverToChannel(announcement, targetUserIds, channel);
            }

            log.info("公告分发完成: id={}", announcement.getId());

        } catch (Exception e) {
            log.error("公告分发失败: id={}", announcement.getId(), e);
        }
    }

    /**
     * 按渠道分发
     * @param announcement 公告
     * @param targetUserIds 目标用户ID列表
     * @param channel 分发渠道
     */
    private void deliverToChannel(Announcement announcement, List<String> targetUserIds, AnnouncementChannel channel) {
        switch (channel) {
            case IN_APP:
                deliverInApp(announcement, targetUserIds);
                break;
            case EMAIL:
                deliverEmail(announcement, targetUserIds);
                break;
            case PUSH:
                deliverPush(announcement, targetUserIds);
                break;
            case WEBSOCKET:
                deliverWebSocket(announcement, targetUserIds);
                break;
            default:
                log.warn("不支持的分发渠道: {}", channel);
        }
    }

    /**
     * 应用内分发
     */
    private void deliverInApp(Announcement announcement, List<String> targetUserIds) {
        try {
            // 应用内公告通过数据库存储，用户登录时获取
            for (String userId : targetUserIds) {
                AnnouncementDelivery delivery = AnnouncementDelivery.builder()
                    .announcementId(announcement.getId())
                    .userId(userId)
                    .channel(AnnouncementChannel.IN_APP)
                    .status(DeliveryStatus.DELIVERED)
                    .deliveredAt(LocalDateTime.now())
                    .build();

                deliveryRepository.save(delivery);
            }

            log.info("应用内公告分发完成: id={}, users={}", announcement.getId(), targetUserIds.size());

        } catch (Exception e) {
            log.error("应用内公告分发失败: id={}", announcement.getId(), e);
        }
    }

    /**
     * 邮件分发
     */
    private void deliverEmail(Announcement announcement, List<String> targetUserIds) {
        try {
            for (String userId : targetUserIds) {
                // 检查用户邮件偏好
                if (!shouldSendEmail(userId)) {
                    continue;
                }

                try {
                    EmailContent emailContent = buildEmailContent(announcement);
                    emailService.sendAnnouncementEmail(userId, emailContent);

                    // 记录投递状态
                    recordDelivery(announcement.getId(), userId, AnnouncementChannel.EMAIL, DeliveryStatus.DELIVERED);

                } catch (Exception e) {
                    log.warn("邮件发送失败: userId={}, announcementId={}", userId, announcement.getId(), e);
                    recordDelivery(announcement.getId(), userId, AnnouncementChannel.EMAIL, DeliveryStatus.FAILED);
                }
            }

            log.info("邮件公告分发完成: id={}", announcement.getId());

        } catch (Exception e) {
            log.error("邮件公告分发失败: id={}", announcement.getId(), e);
        }
    }

    /**
     * 推送通知分发
     */
    private void deliverPush(Announcement announcement, List<String> targetUserIds) {
        try {
            for (String userId : targetUserIds) {
                // 检查用户推送偏好
                if (!shouldSendPush(userId)) {
                    continue;
                }

                try {
                    PushMessage pushMessage = buildPushMessage(announcement);
                    pushNotificationService.sendAnnouncementPush(userId, pushMessage);

                    // 记录投递状态
                    recordDelivery(announcement.getId(), userId, AnnouncementChannel.PUSH, DeliveryStatus.DELIVERED);

                } catch (Exception e) {
                    log.warn("推送发送失败: userId={}, announcementId={}", userId, announcement.getId(), e);
                    recordDelivery(announcement.getId(), userId, AnnouncementChannel.PUSH, DeliveryStatus.FAILED);
                }
            }

            log.info("推送公告分发完成: id={}", announcement.getId());

        } catch (Exception e) {
            log.error("推送公告分发失败: id={}", announcement.getId(), e);
        }
    }

    /**
     * WebSocket实时分发
     */
    private void deliverWebSocket(Announcement announcement, List<String> targetUserIds) {
        try {
            WebSocketMessage message = buildWebSocketMessage(announcement);

            for (String userId : targetUserIds) {
                try {
                    webSocketService.sendAnnouncementMessage(userId, message);
                    recordDelivery(announcement.getId(), userId, AnnouncementChannel.WEBSOCKET, DeliveryStatus.DELIVERED);
                } catch (Exception e) {
                    log.warn("WebSocket发送失败: userId={}, announcementId={}", userId, announcement.getId(), e);
                    recordDelivery(announcement.getId(), userId, AnnouncementChannel.WEBSOCKET, DeliveryStatus.FAILED);
                }
            }

            log.info("WebSocket公告分发完成: id={}", announcement.getId());

        } catch (Exception e) {
            log.error("WebSocket公告分发失败: id={}", announcement.getId(), e);
        }
    }

    /**
     * 停止分发
     * @param announcementId 公告ID
     */
    public void stopDelivery(Long announcementId) {
        try {
            // 标记所有未投递的记录为已取消
            deliveryRepository.cancelPendingDeliveries(announcementId);

            log.info("公告分发已停止: id={}", announcementId);

        } catch (Exception e) {
            log.error("停止公告分发失败: id={}", announcementId, e);
        }
    }

    private boolean shouldSendEmail(String userId) {
        // 检查用户邮件通知偏好
        return userPreferenceService.isEmailNotificationEnabled(userId);
    }

    private boolean shouldSendPush(String userId) {
        // 检查用户推送通知偏好
        return userPreferenceService.isPushNotificationEnabled(userId);
    }

    private EmailContent buildEmailContent(Announcement announcement) {
        return EmailContent.builder()
            .subject(announcement.getTitle())
            .htmlContent(formatAnnouncementForEmail(announcement))
            .textContent(announcement.getContent())
            .build();
    }

    private PushMessage buildPushMessage(Announcement announcement) {
        return PushMessage.builder()
            .title(announcement.getTitle())
            .body(truncateContent(announcement.getContent(), 100))
            .data(Map.of("announcementId", announcement.getId().toString()))
            .build();
    }

    private WebSocketMessage buildWebSocketMessage(Announcement announcement) {
        return WebSocketMessage.builder()
            .type("ANNOUNCEMENT")
            .data(Map.of(
                "id", announcement.getId(),
                "title", announcement.getTitle(),
                "content", announcement.getContent(),
                "type", announcement.getType(),
                "priority", announcement.getPriority()
            ))
            .build();
    }

    private void recordDelivery(Long announcementId, String userId, AnnouncementChannel channel, DeliveryStatus status) {
        AnnouncementDelivery delivery = AnnouncementDelivery.builder()
            .announcementId(announcementId)
            .userId(userId)
            .channel(channel)
            .status(status)
            .deliveredAt(LocalDateTime.now())
            .build();

        deliveryRepository.save(delivery);
    }

    private String formatAnnouncementForEmail(Announcement announcement) {
        // 格式化公告内容为HTML邮件格式
        return String.format(
            "<h2>%s</h2><div>%s</div><p><small>发布时间: %s</small></p>",
            announcement.getTitle(),
            announcement.getContent().replace("\n", "<br>"),
            announcement.getPublishedAt()
        );
    }

    private String truncateContent(String content, int maxLength) {
        if (content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength - 3) + "...";
    }
}
```

## 数据模型定义

### 1. 公告相关实体

#### Announcement - 公告实体
```java
/**
 * 公告实体
 * 存储公告的基本信息
 */
@Entity
@Table(name = "announcements")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Announcement {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 200)
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AnnouncementType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AnnouncementPriority priority;

    @Column(length = 50)
    private String category;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AnnouncementStatus status;

    @Column(columnDefinition = "JSON")
    private TargetingRules targetingRules;

    @ElementCollection(targetClass = AnnouncementChannel.class)
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "announcement_channels")
    private Set<AnnouncementChannel> channels;

    private LocalDateTime scheduledAt;
    private LocalDateTime expiresAt;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private String createdBy;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    private String updatedBy;

    private LocalDateTime publishedAt;
    private String publishedBy;
    private Integer targetUserCount;

    private LocalDateTime withdrawnAt;
    private String withdrawnBy;
    private String withdrawReason;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本公告管理操作
```java
// 注入公告服务
@Autowired
private AnnouncementService announcementService;

// 创建公告
CreateAnnouncementRequest createRequest = CreateAnnouncementRequest.builder()
    .title("系统维护通知")
    .content("系统将于今晚22:00-24:00进行维护，期间服务可能中断，请提前保存工作。")
    .type(AnnouncementType.SYSTEM)
    .priority(AnnouncementPriority.HIGH)
    .category("maintenance")
    .targetingRules(TargetingRules.builder()
        .allUsers(true)
        .build())
    .channels(Set.of(AnnouncementChannel.IN_APP, AnnouncementChannel.EMAIL))
    .scheduledAt(LocalDateTime.now().plusHours(1))
    .expiresAt(LocalDateTime.now().plusDays(1))
    .publishImmediately(false)
    .build();

Announcement announcement = announcementService.createAnnouncement(createRequest, "admin123");

// 发布公告
AnnouncementPublishResult publishResult = announcementService.publishAnnouncement(announcement.getId(), "admin123");
System.out.println("公告发布成功，目标用户数: " + publishResult.getTargetUserCount());

// 获取用户公告列表
AnnouncementFilter filter = AnnouncementFilter.builder()
    .type(AnnouncementType.SYSTEM)
    .unreadOnly(true)
    .build();

Page<AnnouncementResponse> userAnnouncements = announcementService.getUserAnnouncements(
    "user123", filter, PageRequest.of(0, 10));

// 标记公告为已读
announcementService.markAnnouncementAsRead(announcement.getId(), "user123");

// 获取公告统计
AnnouncementStatistics stats = announcementService.getAnnouncementStatistics(announcement.getId());
System.out.println("阅读率: " + stats.getReadRate() + "%");

// 撤回公告
announcementService.withdrawAnnouncement(announcement.getId(), "admin123", "内容有误，需要修正");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的公告管理实现，支持公告创建发布、多渠道分发、阅读跟踪、统计分析等功能
