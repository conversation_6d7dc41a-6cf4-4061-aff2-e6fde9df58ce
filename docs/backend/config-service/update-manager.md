# 更新管理器 (Update Manager)

## 模块概述

更新管理器模块负责Ark-Pets AI Enhanced项目中应用程序的版本更新管理，包括版本检查、更新包分发、增量更新、回滚机制、更新进度跟踪等功能。提供安全可靠的自动更新机制，确保用户能够及时获得最新功能和安全修复。

**核心职责**:
- 应用版本检查和比较
- 更新包管理和分发
- 增量更新和全量更新
- 更新进度跟踪和通知
- 更新回滚和恢复机制

## 核心功能架构

### 1. 更新管理架构

#### 分层更新管理模型
```
┌─────────────────────────────────────┐
│           更新管理系统               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 版本检查  │ 更新分发  │ 进度跟踪  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           更新处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 包验证   │ 增量计算  │ 安装管理  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           存储分发层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 文件存储  │ CDN分发  │ 缓存管理  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 更新流程

#### 应用更新流程图
```mermaid
graph TB
    subgraph "更新检查流程"
        CheckRequest[检查更新请求]
        GetCurrentVersion[获取当前版本]
        QueryLatestVersion[查询最新版本]
        CompareVersions[版本比较]
        CheckCompatibility[兼容性检查]
        GenerateUpdateInfo[生成更新信息]
        ReturnUpdateInfo[返回更新信息]
    end
    
    subgraph "更新下载流程"
        DownloadRequest[下载请求]
        ValidatePermission[权限验证]
        SelectUpdateType[选择更新类型]
        CalculateDelta[计算增量]
        DownloadPackage[下载更新包]
        VerifyPackage[验证更新包]
        NotifyProgress[通知进度]
    end
    
    subgraph "更新安装流程"
        InstallRequest[安装请求]
        BackupCurrent[备份当前版本]
        ApplyUpdate[应用更新]
        VerifyInstall[验证安装]
        UpdateComplete[更新完成]
        CleanupFiles[清理文件]
    end
    
    CheckRequest --> GetCurrentVersion
    GetCurrentVersion --> QueryLatestVersion
    QueryLatestVersion --> CompareVersions
    CompareVersions --> CheckCompatibility
    CheckCompatibility --> GenerateUpdateInfo
    GenerateUpdateInfo --> ReturnUpdateInfo
    
    DownloadRequest --> ValidatePermission
    ValidatePermission --> SelectUpdateType
    SelectUpdateType --> CalculateDelta
    CalculateDelta --> DownloadPackage
    DownloadPackage --> VerifyPackage
    VerifyPackage --> NotifyProgress
    
    InstallRequest --> BackupCurrent
    BackupCurrent --> ApplyUpdate
    ApplyUpdate --> VerifyInstall
    VerifyInstall --> UpdateComplete
    UpdateComplete --> CleanupFiles
```

## 核心类和接口

### 1. 更新管理服务

#### UpdateManagerService - 更新管理主服务
```java
/**
 * 更新管理主服务
 * 负责应用程序版本更新的完整生命周期管理
 */
@Service
@Slf4j
public class UpdateManagerService {
    
    private final VersionRepository versionRepository;
    private final UpdatePackageRepository updatePackageRepository;
    private final UpdateRecordRepository updateRecordRepository;
    private final FileStorageService fileStorageService;
    private final VersionComparator versionComparator;
    private final UpdatePackageBuilder updatePackageBuilder;
    private final UpdateNotificationService updateNotificationService;
    private final SecurityService securityService;
    private final EventPublisher eventPublisher;
    
    /**
     * 检查更新
     * @param checkRequest 检查请求
     * @return 更新信息
     */
    public UpdateCheckResponse checkForUpdates(UpdateCheckRequest checkRequest) {
        try {
            // 1. 验证客户端信息
            validateClientInfo(checkRequest);
            
            // 2. 获取当前版本信息
            Version currentVersion = parseVersion(checkRequest.getCurrentVersion());
            
            // 3. 查询最新可用版本
            Version latestVersion = getLatestAvailableVersion(
                checkRequest.getChannel(), 
                checkRequest.getPlatform(),
                checkRequest.getArchitecture()
            );
            
            // 4. 比较版本
            if (!versionComparator.isNewerVersion(latestVersion, currentVersion)) {
                return UpdateCheckResponse.noUpdateAvailable(currentVersion);
            }
            
            // 5. 检查兼容性
            CompatibilityResult compatibility = checkCompatibility(currentVersion, latestVersion, checkRequest);
            if (!compatibility.isCompatible()) {
                return UpdateCheckResponse.incompatibleUpdate(latestVersion, compatibility.getReason());
            }
            
            // 6. 获取更新包信息
            UpdatePackage updatePackage = getUpdatePackage(currentVersion, latestVersion, checkRequest);
            
            // 7. 构建更新响应
            return UpdateCheckResponse.builder()
                .updateAvailable(true)
                .currentVersion(currentVersion.getVersionString())
                .latestVersion(latestVersion.getVersionString())
                .updatePackage(updatePackage)
                .releaseNotes(latestVersion.getReleaseNotes())
                .isForced(latestVersion.isForceUpdate())
                .downloadUrl(updatePackage.getDownloadUrl())
                .packageSize(updatePackage.getFileSize())
                .checksum(updatePackage.getChecksum())
                .build();
                
        } catch (Exception e) {
            log.error("检查更新失败: {}", checkRequest, e);
            throw new UpdateCheckException("检查更新失败", e);
        }
    }
    
    /**
     * 下载更新包
     * @param downloadRequest 下载请求
     * @return 下载信息
     */
    public UpdateDownloadResponse downloadUpdate(UpdateDownloadRequest downloadRequest) {
        try {
            // 1. 验证下载权限
            securityService.validateUpdatePermission(downloadRequest.getClientId());
            
            // 2. 获取更新包信息
            UpdatePackage updatePackage = updatePackageRepository
                .findById(downloadRequest.getPackageId())
                .orElseThrow(() -> new UpdatePackageNotFoundException("更新包不存在"));
            
            // 3. 选择更新类型（增量或全量）
            UpdateType updateType = selectUpdateType(downloadRequest, updatePackage);
            
            // 4. 生成下载令牌
            String downloadToken = generateDownloadToken(downloadRequest, updatePackage);
            
            // 5. 记录下载开始
            UpdateRecord updateRecord = createUpdateRecord(downloadRequest, updatePackage, updateType);
            updateRecordRepository.save(updateRecord);
            
            // 6. 构建下载响应
            return UpdateDownloadResponse.builder()
                .downloadToken(downloadToken)
                .downloadUrl(buildDownloadUrl(updatePackage, updateType))
                .updateType(updateType)
                .packageSize(updatePackage.getFileSize())
                .checksum(updatePackage.getChecksum())
                .expiresAt(LocalDateTime.now().plusHours(24))
                .build();
                
        } catch (Exception e) {
            log.error("下载更新包失败: {}", downloadRequest, e);
            throw new UpdateDownloadException("下载更新包失败", e);
        }
    }
    
    /**
     * 验证更新包
     * @param verifyRequest 验证请求
     * @return 验证结果
     */
    public UpdateVerificationResponse verifyUpdatePackage(UpdateVerificationRequest verifyRequest) {
        try {
            // 1. 获取更新包信息
            UpdatePackage updatePackage = updatePackageRepository
                .findById(verifyRequest.getPackageId())
                .orElseThrow(() -> new UpdatePackageNotFoundException("更新包不存在"));
            
            // 2. 验证文件完整性
            boolean checksumValid = verifyChecksum(verifyRequest.getLocalChecksum(), updatePackage.getChecksum());
            
            // 3. 验证数字签名
            boolean signatureValid = verifyDigitalSignature(verifyRequest.getPackageData(), updatePackage.getSignature());
            
            // 4. 验证文件大小
            boolean sizeValid = verifyFileSize(verifyRequest.getFileSize(), updatePackage.getFileSize());
            
            boolean isValid = checksumValid && signatureValid && sizeValid;
            
            return UpdateVerificationResponse.builder()
                .isValid(isValid)
                .checksumValid(checksumValid)
                .signatureValid(signatureValid)
                .sizeValid(sizeValid)
                .packageId(updatePackage.getId())
                .build();
                
        } catch (Exception e) {
            log.error("验证更新包失败: {}", verifyRequest, e);
            throw new UpdateVerificationException("验证更新包失败", e);
        }
    }
    
    /**
     * 应用更新
     * @param applyRequest 应用请求
     * @return 应用结果
     */
    @Transactional
    public UpdateApplyResponse applyUpdate(UpdateApplyRequest applyRequest) {
        try {
            // 1. 验证应用权限
            securityService.validateUpdatePermission(applyRequest.getClientId());
            
            // 2. 获取更新记录
            UpdateRecord updateRecord = updateRecordRepository
                .findById(applyRequest.getUpdateRecordId())
                .orElseThrow(() -> new UpdateRecordNotFoundException("更新记录不存在"));
            
            // 3. 检查更新状态
            if (updateRecord.getStatus() != UpdateStatus.DOWNLOADED) {
                throw new InvalidUpdateStatusException("更新状态无效: " + updateRecord.getStatus());
            }
            
            // 4. 创建备份
            BackupInfo backupInfo = createBackup(updateRecord);
            
            // 5. 应用更新
            updateRecord.setStatus(UpdateStatus.INSTALLING);
            updateRecord.setInstallStartedAt(LocalDateTime.now());
            updateRecordRepository.save(updateRecord);
            
            // 6. 执行更新安装
            InstallationResult installResult = executeInstallation(updateRecord, applyRequest);
            
            // 7. 验证安装结果
            boolean installSuccess = verifyInstallation(installResult);
            
            // 8. 更新记录状态
            if (installSuccess) {
                updateRecord.setStatus(UpdateStatus.COMPLETED);
                updateRecord.setCompletedAt(LocalDateTime.now());
            } else {
                updateRecord.setStatus(UpdateStatus.FAILED);
                updateRecord.setErrorMessage("安装验证失败");
            }
            updateRecordRepository.save(updateRecord);
            
            // 9. 发送通知
            updateNotificationService.notifyUpdateResult(updateRecord, installSuccess);
            
            // 10. 发布事件
            eventPublisher.publishUpdateApplied(updateRecord, installSuccess);
            
            return UpdateApplyResponse.builder()
                .success(installSuccess)
                .updateRecordId(updateRecord.getId())
                .backupInfo(backupInfo)
                .newVersion(updateRecord.getTargetVersion())
                .build();
                
        } catch (Exception e) {
            log.error("应用更新失败: {}", applyRequest, e);
            throw new UpdateApplyException("应用更新失败", e);
        }
    }
    
    /**
     * 回滚更新
     * @param rollbackRequest 回滚请求
     * @return 回滚结果
     */
    @Transactional
    public UpdateRollbackResponse rollbackUpdate(UpdateRollbackRequest rollbackRequest) {
        try {
            // 1. 验证回滚权限
            securityService.validateUpdatePermission(rollbackRequest.getClientId());
            
            // 2. 获取更新记录
            UpdateRecord updateRecord = updateRecordRepository
                .findById(rollbackRequest.getUpdateRecordId())
                .orElseThrow(() -> new UpdateRecordNotFoundException("更新记录不存在"));
            
            // 3. 检查是否可以回滚
            if (!canRollback(updateRecord)) {
                throw new RollbackNotAllowedException("当前状态不允许回滚");
            }
            
            // 4. 获取备份信息
            BackupInfo backupInfo = getBackupInfo(updateRecord);
            if (backupInfo == null) {
                throw new BackupNotFoundException("备份信息不存在");
            }
            
            // 5. 执行回滚
            updateRecord.setStatus(UpdateStatus.ROLLING_BACK);
            updateRecordRepository.save(updateRecord);
            
            RollbackResult rollbackResult = executeRollback(updateRecord, backupInfo);
            
            // 6. 更新记录状态
            if (rollbackResult.isSuccess()) {
                updateRecord.setStatus(UpdateStatus.ROLLED_BACK);
                updateRecord.setRolledBackAt(LocalDateTime.now());
            } else {
                updateRecord.setStatus(UpdateStatus.ROLLBACK_FAILED);
                updateRecord.setErrorMessage(rollbackResult.getErrorMessage());
            }
            updateRecordRepository.save(updateRecord);
            
            // 7. 发送通知
            updateNotificationService.notifyRollbackResult(updateRecord, rollbackResult.isSuccess());
            
            // 8. 发布事件
            eventPublisher.publishUpdateRolledBack(updateRecord, rollbackResult.isSuccess());
            
            return UpdateRollbackResponse.builder()
                .success(rollbackResult.isSuccess())
                .updateRecordId(updateRecord.getId())
                .restoredVersion(updateRecord.getSourceVersion())
                .errorMessage(rollbackResult.getErrorMessage())
                .build();
                
        } catch (Exception e) {
            log.error("回滚更新失败: {}", rollbackRequest, e);
            throw new UpdateRollbackException("回滚更新失败", e);
        }
    }
    
    /**
     * 获取更新历史
     * @param clientId 客户端ID
     * @param pageable 分页参数
     * @return 更新历史
     */
    public Page<UpdateRecord> getUpdateHistory(String clientId, Pageable pageable) {
        try {
            return updateRecordRepository.findByClientId(clientId, pageable);
        } catch (Exception e) {
            log.error("获取更新历史失败: clientId={}", clientId, e);
            throw new UpdateHistoryException("获取更新历史失败", e);
        }
    }
    
    /**
     * 获取更新进度
     * @param updateRecordId 更新记录ID
     * @return 更新进度
     */
    public UpdateProgressResponse getUpdateProgress(Long updateRecordId) {
        try {
            UpdateRecord updateRecord = updateRecordRepository.findById(updateRecordId)
                .orElseThrow(() -> new UpdateRecordNotFoundException("更新记录不存在"));
            
            return UpdateProgressResponse.builder()
                .updateRecordId(updateRecordId)
                .status(updateRecord.getStatus())
                .progress(calculateProgress(updateRecord))
                .currentStep(getCurrentStep(updateRecord))
                .estimatedTimeRemaining(estimateTimeRemaining(updateRecord))
                .build();
                
        } catch (Exception e) {
            log.error("获取更新进度失败: updateRecordId={}", updateRecordId, e);
            throw new UpdateProgressException("获取更新进度失败", e);
        }
    }
    
    /**
     * 发布新版本
     * @param releaseRequest 发布请求
     * @param operatorId 操作者ID
     * @return 发布结果
     */
    @Transactional
    public VersionReleaseResponse releaseVersion(VersionReleaseRequest releaseRequest, String operatorId) {
        try {
            // 1. 验证发布权限
            securityService.validateReleasePermission(operatorId);
            
            // 2. 验证版本信息
            validateVersionRelease(releaseRequest);
            
            // 3. 创建版本记录
            Version version = createVersionRecord(releaseRequest, operatorId);
            version = versionRepository.save(version);
            
            // 4. 上传更新包
            UpdatePackage updatePackage = uploadUpdatePackage(version, releaseRequest);
            updatePackageRepository.save(updatePackage);
            
            // 5. 生成增量包
            generateDeltaPackages(version, updatePackage);
            
            // 6. 发布版本
            publishVersion(version);
            
            // 7. 发送发布通知
            updateNotificationService.notifyVersionReleased(version);
            
            // 8. 发布事件
            eventPublisher.publishVersionReleased(version, operatorId);
            
            log.info("版本发布成功: version={}, operator={}", version.getVersionString(), operatorId);
            
            return VersionReleaseResponse.builder()
                .versionId(version.getId())
                .versionString(version.getVersionString())
                .packageId(updatePackage.getId())
                .releaseTime(version.getReleasedAt())
                .build();
                
        } catch (Exception e) {
            log.error("版本发布失败: {}, operator={}", releaseRequest, operatorId, e);
            throw new VersionReleaseException("版本发布失败", e);
        }
    }

    private void validateClientInfo(UpdateCheckRequest request) {
        if (StringUtils.isBlank(request.getClientId())) {
            throw new IllegalArgumentException("客户端ID不能为空");
        }
        if (StringUtils.isBlank(request.getCurrentVersion())) {
            throw new IllegalArgumentException("当前版本不能为空");
        }
    }

    private Version parseVersion(String versionString) {
        return versionRepository.findByVersionString(versionString)
            .orElseThrow(() -> new VersionNotFoundException("版本不存在: " + versionString));
    }

    private Version getLatestAvailableVersion(String channel, String platform, String architecture) {
        return versionRepository.findLatestVersion(channel, platform, architecture)
            .orElseThrow(() -> new VersionNotFoundException("没有可用的版本"));
    }

    private CompatibilityResult checkCompatibility(Version current, Version latest, UpdateCheckRequest request) {
        // 检查平台兼容性
        if (!latest.getSupportedPlatforms().contains(request.getPlatform())) {
            return CompatibilityResult.incompatible("平台不兼容: " + request.getPlatform());
        }

        // 检查架构兼容性
        if (!latest.getSupportedArchitectures().contains(request.getArchitecture())) {
            return CompatibilityResult.incompatible("架构不兼容: " + request.getArchitecture());
        }

        // 检查最小版本要求
        if (latest.getMinimumVersion() != null &&
            versionComparator.isOlderVersion(current, latest.getMinimumVersion())) {
            return CompatibilityResult.incompatible("当前版本过低，需要先升级到: " + latest.getMinimumVersion().getVersionString());
        }

        return CompatibilityResult.compatible();
    }

    private UpdatePackage getUpdatePackage(Version current, Version latest, UpdateCheckRequest request) {
        return updatePackageRepository.findByVersionAndPlatformAndArchitecture(
            latest.getId(), request.getPlatform(), request.getArchitecture())
            .orElseThrow(() -> new UpdatePackageNotFoundException("更新包不存在"));
    }

    private UpdateType selectUpdateType(UpdateDownloadRequest request, UpdatePackage updatePackage) {
        // 如果客户端支持增量更新且有增量包可用，优先使用增量更新
        if (request.isSupportDelta() && updatePackage.hasDeltaPackage()) {
            return UpdateType.DELTA;
        }
        return UpdateType.FULL;
    }

    private String generateDownloadToken(UpdateDownloadRequest request, UpdatePackage updatePackage) {
        // 生成带有过期时间的下载令牌
        return JWT.create()
            .withSubject(request.getClientId())
            .withClaim("packageId", updatePackage.getId())
            .withExpiresAt(Date.from(Instant.now().plus(24, ChronoUnit.HOURS)))
            .sign(Algorithm.HMAC256("download-secret"));
    }

    private UpdateRecord createUpdateRecord(UpdateDownloadRequest request, UpdatePackage updatePackage, UpdateType updateType) {
        return UpdateRecord.builder()
            .clientId(request.getClientId())
            .packageId(updatePackage.getId())
            .sourceVersion(request.getCurrentVersion())
            .targetVersion(updatePackage.getVersion().getVersionString())
            .updateType(updateType)
            .status(UpdateStatus.DOWNLOADING)
            .createdAt(LocalDateTime.now())
            .build();
    }

    private String buildDownloadUrl(UpdatePackage updatePackage, UpdateType updateType) {
        String baseUrl = "https://updates.arkpets.com/packages/";
        if (updateType == UpdateType.DELTA) {
            return baseUrl + "delta/" + updatePackage.getDeltaFileName();
        } else {
            return baseUrl + "full/" + updatePackage.getFileName();
        }
    }

    private boolean verifyChecksum(String localChecksum, String expectedChecksum) {
        return StringUtils.equals(localChecksum, expectedChecksum);
    }

    private boolean verifyDigitalSignature(byte[] packageData, String signature) {
        // 实现数字签名验证逻辑
        return true; // 简化实现
    }

    private boolean verifyFileSize(long localSize, long expectedSize) {
        return localSize == expectedSize;
    }

    private BackupInfo createBackup(UpdateRecord updateRecord) {
        // 创建当前版本的备份
        String backupPath = "backups/" + updateRecord.getClientId() + "/" + updateRecord.getSourceVersion();

        return BackupInfo.builder()
            .backupId(UUID.randomUUID().toString())
            .backupPath(backupPath)
            .sourceVersion(updateRecord.getSourceVersion())
            .createdAt(LocalDateTime.now())
            .build();
    }

    private InstallationResult executeInstallation(UpdateRecord updateRecord, UpdateApplyRequest request) {
        // 执行实际的安装过程
        try {
            // 1. 停止应用服务
            // 2. 应用更新文件
            // 3. 更新配置文件
            // 4. 重启应用服务

            return InstallationResult.success();
        } catch (Exception e) {
            return InstallationResult.failure(e.getMessage());
        }
    }

    private boolean verifyInstallation(InstallationResult result) {
        return result.isSuccess();
    }

    private boolean canRollback(UpdateRecord updateRecord) {
        return updateRecord.getStatus() == UpdateStatus.COMPLETED ||
               updateRecord.getStatus() == UpdateStatus.FAILED;
    }

    private BackupInfo getBackupInfo(UpdateRecord updateRecord) {
        // 从存储中获取备份信息
        return null; // 简化实现
    }

    private RollbackResult executeRollback(UpdateRecord updateRecord, BackupInfo backupInfo) {
        try {
            // 1. 停止应用服务
            // 2. 恢复备份文件
            // 3. 恢复配置文件
            // 4. 重启应用服务

            return RollbackResult.success();
        } catch (Exception e) {
            return RollbackResult.failure(e.getMessage());
        }
    }

    private int calculateProgress(UpdateRecord updateRecord) {
        switch (updateRecord.getStatus()) {
            case DOWNLOADING:
                return 25;
            case DOWNLOADED:
                return 50;
            case INSTALLING:
                return 75;
            case COMPLETED:
                return 100;
            default:
                return 0;
        }
    }

    private String getCurrentStep(UpdateRecord updateRecord) {
        switch (updateRecord.getStatus()) {
            case DOWNLOADING:
                return "正在下载更新包";
            case DOWNLOADED:
                return "下载完成，准备安装";
            case INSTALLING:
                return "正在安装更新";
            case COMPLETED:
                return "更新完成";
            case FAILED:
                return "更新失败";
            default:
                return "未知状态";
        }
    }

    private Duration estimateTimeRemaining(UpdateRecord updateRecord) {
        // 根据当前状态和历史数据估算剩余时间
        switch (updateRecord.getStatus()) {
            case DOWNLOADING:
                return Duration.ofMinutes(5);
            case INSTALLING:
                return Duration.ofMinutes(2);
            default:
                return Duration.ZERO;
        }
    }
}
```

### 2. 版本比较器

#### VersionComparator - 版本比较器
```java
/**
 * 版本比较器
 * 负责版本号的解析和比较
 */
@Component
public class VersionComparator {

    /**
     * 检查是否为更新版本
     * @param version1 版本1
     * @param version2 版本2
     * @return version1是否比version2新
     */
    public boolean isNewerVersion(Version version1, Version version2) {
        return compareVersions(version1.getVersionString(), version2.getVersionString()) > 0;
    }

    /**
     * 检查是否为旧版本
     * @param version1 版本1
     * @param version2 版本2
     * @return version1是否比version2旧
     */
    public boolean isOlderVersion(Version version1, Version version2) {
        return compareVersions(version1.getVersionString(), version2.getVersionString()) < 0;
    }

    /**
     * 比较版本号
     * @param version1 版本1字符串
     * @param version2 版本2字符串
     * @return 比较结果（正数表示version1更新，负数表示version2更新，0表示相同）
     */
    public int compareVersions(String version1, String version2) {
        if (version1.equals(version2)) {
            return 0;
        }

        String[] parts1 = parseVersionParts(version1);
        String[] parts2 = parseVersionParts(version2);

        int maxLength = Math.max(parts1.length, parts2.length);

        for (int i = 0; i < maxLength; i++) {
            int part1 = i < parts1.length ? parseVersionPart(parts1[i]) : 0;
            int part2 = i < parts2.length ? parseVersionPart(parts2[i]) : 0;

            if (part1 != part2) {
                return Integer.compare(part1, part2);
            }
        }

        return 0;
    }

    private String[] parseVersionParts(String version) {
        // 移除预发布标识符（如 -alpha, -beta, -rc）
        String cleanVersion = version.replaceAll("-.*$", "");
        return cleanVersion.split("\\.");
    }

    private int parseVersionPart(String part) {
        try {
            return Integer.parseInt(part);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
```

## 数据模型定义

### 1. 版本相关实体

#### Version - 版本实体
```java
/**
 * 版本实体
 * 存储应用程序版本信息
 */
@Entity
@Table(name = "versions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Version {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String versionString;

    @Column(nullable = false)
    private Integer majorVersion;

    @Column(nullable = false)
    private Integer minorVersion;

    @Column(nullable = false)
    private Integer patchVersion;

    private String preRelease;

    @Column(nullable = false)
    private String channel; // stable, beta, alpha

    @ElementCollection
    @CollectionTable(name = "version_platforms")
    private Set<String> supportedPlatforms;

    @ElementCollection
    @CollectionTable(name = "version_architectures")
    private Set<String> supportedArchitectures;

    @ManyToOne
    @JoinColumn(name = "minimum_version_id")
    private Version minimumVersion;

    @Column(columnDefinition = "TEXT")
    private String releaseNotes;

    @Column(nullable = false)
    private Boolean forceUpdate = false;

    @Column(nullable = false)
    private Boolean isActive = true;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private String createdBy;

    private LocalDateTime releasedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
```

#### UpdateRecord - 更新记录实体
```java
/**
 * 更新记录实体
 * 记录客户端的更新过程
 */
@Entity
@Table(name = "update_records")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String clientId;

    @Column(nullable = false)
    private Long packageId;

    @Column(nullable = false)
    private String sourceVersion;

    @Column(nullable = false)
    private String targetVersion;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UpdateType updateType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UpdateStatus status;

    private String errorMessage;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    private LocalDateTime downloadStartedAt;
    private LocalDateTime downloadCompletedAt;
    private LocalDateTime installStartedAt;
    private LocalDateTime completedAt;
    private LocalDateTime rolledBackAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本更新管理操作
```java
// 注入更新管理服务
@Autowired
private UpdateManagerService updateManagerService;

// 检查更新
UpdateCheckRequest checkRequest = UpdateCheckRequest.builder()
    .clientId("client-123")
    .currentVersion("1.0.0")
    .platform("windows")
    .architecture("x64")
    .channel("stable")
    .build();

UpdateCheckResponse checkResponse = updateManagerService.checkForUpdates(checkRequest);
if (checkResponse.isUpdateAvailable()) {
    System.out.println("发现新版本: " + checkResponse.getLatestVersion());
    System.out.println("更新包大小: " + checkResponse.getPackageSize() + " bytes");
}

// 下载更新
if (checkResponse.isUpdateAvailable()) {
    UpdateDownloadRequest downloadRequest = UpdateDownloadRequest.builder()
        .clientId("client-123")
        .packageId(checkResponse.getUpdatePackage().getId())
        .currentVersion("1.0.0")
        .supportDelta(true)
        .build();

    UpdateDownloadResponse downloadResponse = updateManagerService.downloadUpdate(downloadRequest);
    System.out.println("下载URL: " + downloadResponse.getDownloadUrl());
    System.out.println("下载令牌: " + downloadResponse.getDownloadToken());
}

// 验证更新包
UpdateVerificationRequest verifyRequest = UpdateVerificationRequest.builder()
    .packageId(checkResponse.getUpdatePackage().getId())
    .localChecksum("abc123...")
    .fileSize(1024000L)
    .packageData(packageBytes)
    .build();

UpdateVerificationResponse verifyResponse = updateManagerService.verifyUpdatePackage(verifyRequest);
if (verifyResponse.isValid()) {
    System.out.println("更新包验证通过");
} else {
    System.out.println("更新包验证失败");
}

// 应用更新
UpdateApplyRequest applyRequest = UpdateApplyRequest.builder()
    .clientId("client-123")
    .updateRecordId(updateRecordId)
    .backupEnabled(true)
    .build();

UpdateApplyResponse applyResponse = updateManagerService.applyUpdate(applyRequest);
if (applyResponse.isSuccess()) {
    System.out.println("更新应用成功，新版本: " + applyResponse.getNewVersion());
} else {
    System.out.println("更新应用失败");
}

// 获取更新进度
UpdateProgressResponse progress = updateManagerService.getUpdateProgress(updateRecordId);
System.out.println("更新进度: " + progress.getProgress() + "%");
System.out.println("当前步骤: " + progress.getCurrentStep());

// 回滚更新（如果需要）
UpdateRollbackRequest rollbackRequest = UpdateRollbackRequest.builder()
    .clientId("client-123")
    .updateRecordId(updateRecordId)
    .build();

UpdateRollbackResponse rollbackResponse = updateManagerService.rollbackUpdate(rollbackRequest);
if (rollbackResponse.isSuccess()) {
    System.out.println("回滚成功，恢复到版本: " + rollbackResponse.getRestoredVersion());
}
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的应用更新管理实现，支持版本检查、增量更新、安装回滚、进度跟踪等功能
