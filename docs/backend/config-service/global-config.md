# 全局配置管理 (Global Config)

## 模块概述

全局配置管理模块负责Ark-Pets AI Enhanced项目中系统级配置的统一管理，包括应用配置、功能开关、系统参数、环境变量等。提供配置的动态更新、版本控制、权限管理、配置验证等功能，确保系统配置的一致性和可维护性。

**核心职责**:
- 系统级配置统一管理
- 配置动态更新和热加载
- 配置版本控制和回滚
- 配置权限管理和审计
- 环境配置隔离和管理

## 核心功能架构

### 1. 配置管理架构

#### 分层配置管理模型
```
┌─────────────────────────────────────┐
│           配置管理系统               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 配置存储  │ 配置分发  │ 配置监控  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           配置处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 配置验证  │ 版本控制  │ 权限控制  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           配置存储层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据库   │ 缓存     │ 文件系统  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 配置更新流程

#### 配置更新分发流程图
```mermaid
graph TB
    subgraph "配置更新流程"
        UpdateRequest[配置更新请求]
        ValidateAuth[权限验证]
        ValidateConfig[配置验证]
        CreateVersion[创建版本]
        UpdateStorage[更新存储]
        NotifyServices[通知服务]
        UpdateCache[更新缓存]
        AuditLog[审计日志]
        UpdateComplete[更新完成]
    end
    
    subgraph "配置分发"
        ServiceA[服务A]
        ServiceB[服务B]
        ServiceC[服务C]
        ConfigSync[配置同步]
    end
    
    subgraph "配置监控"
        HealthCheck[健康检查]
        ConfigDrift[配置漂移检测]
        AlertSystem[告警系统]
    end
    
    UpdateRequest --> ValidateAuth
    ValidateAuth --> ValidateConfig
    ValidateConfig --> CreateVersion
    CreateVersion --> UpdateStorage
    UpdateStorage --> UpdateCache
    UpdateCache --> NotifyServices
    NotifyServices --> ServiceA
    NotifyServices --> ServiceB
    NotifyServices --> ServiceC
    ServiceA --> ConfigSync
    ServiceB --> ConfigSync
    ServiceC --> ConfigSync
    ConfigSync --> AuditLog
    AuditLog --> UpdateComplete
    
    UpdateComplete --> HealthCheck
    HealthCheck --> ConfigDrift
    ConfigDrift --> AlertSystem
```

## 核心类和接口

### 1. 全局配置管理服务

#### GlobalConfigService - 全局配置主服务
```java
/**
 * 全局配置管理主服务
 * 负责系统级配置的统一管理和分发
 */
@Service
@Slf4j
public class GlobalConfigService {
    
    private final ConfigRepository configRepository;
    private final ConfigVersionRepository configVersionRepository;
    private final ConfigCacheService configCacheService;
    private final ConfigValidationService configValidationService;
    private final ConfigNotificationService configNotificationService;
    private final SecurityService securityService;
    private final AuditService auditService;
    private final EventPublisher eventPublisher;
    
    /**
     * 获取配置值
     * @param configKey 配置键
     * @param environment 环境
     * @return 配置值
     */
    public ConfigValue getConfig(String configKey, String environment) {
        try {
            // 1. 从缓存获取
            ConfigValue cachedValue = configCacheService.getConfig(configKey, environment);
            if (cachedValue != null) {
                return cachedValue;
            }
            
            // 2. 从数据库获取
            GlobalConfig config = configRepository.findByKeyAndEnvironment(configKey, environment)
                .orElseThrow(() -> new ConfigNotFoundException("配置不存在: " + configKey));
            
            // 3. 构建配置值
            ConfigValue configValue = buildConfigValue(config);
            
            // 4. 缓存配置
            configCacheService.cacheConfig(configKey, environment, configValue);
            
            return configValue;
            
        } catch (Exception e) {
            log.error("获取配置失败: key={}, env={}", configKey, environment, e);
            throw new ConfigAccessException("获取配置失败", e);
        }
    }
    
    /**
     * 批量获取配置
     * @param configKeys 配置键列表
     * @param environment 环境
     * @return 配置映射
     */
    public Map<String, ConfigValue> getBatchConfigs(List<String> configKeys, String environment) {
        Map<String, ConfigValue> result = new HashMap<>();
        
        for (String key : configKeys) {
            try {
                ConfigValue value = getConfig(key, environment);
                result.put(key, value);
            } catch (ConfigNotFoundException e) {
                log.warn("配置不存在: key={}, env={}", key, environment);
                // 继续处理其他配置
            }
        }
        
        return result;
    }
    
    /**
     * 更新配置
     * @param updateRequest 更新请求
     * @param operatorId 操作者ID
     * @return 更新后的配置
     */
    @Transactional
    public GlobalConfig updateConfig(UpdateConfigRequest updateRequest, String operatorId) {
        try {
            // 1. 验证权限
            securityService.validateConfigPermission(operatorId, "CONFIG_UPDATE", updateRequest.getConfigKey());
            
            // 2. 验证配置
            configValidationService.validateConfigUpdate(updateRequest);
            
            // 3. 获取当前配置
            GlobalConfig currentConfig = configRepository
                .findByKeyAndEnvironment(updateRequest.getConfigKey(), updateRequest.getEnvironment())
                .orElse(null);
            
            // 4. 创建配置版本
            ConfigVersion version = createConfigVersion(currentConfig, updateRequest, operatorId);
            
            // 5. 更新或创建配置
            GlobalConfig updatedConfig;
            if (currentConfig != null) {
                updatedConfig = updateExistingConfig(currentConfig, updateRequest, operatorId);
            } else {
                updatedConfig = createNewConfig(updateRequest, operatorId);
            }
            
            // 6. 保存配置
            updatedConfig = configRepository.save(updatedConfig);
            
            // 7. 清除缓存
            configCacheService.evictConfig(updateRequest.getConfigKey(), updateRequest.getEnvironment());
            
            // 8. 通知相关服务
            configNotificationService.notifyConfigUpdate(updatedConfig);
            
            // 9. 记录审计日志
            auditService.logConfigUpdate(updatedConfig, version, operatorId);
            
            // 10. 发布配置更新事件
            eventPublisher.publishConfigUpdated(updatedConfig, operatorId);
            
            log.info("配置更新成功: key={}, env={}, operator={}", 
                updateRequest.getConfigKey(), updateRequest.getEnvironment(), operatorId);
            
            return updatedConfig;
            
        } catch (Exception e) {
            log.error("配置更新失败: key={}, env={}, operator={}", 
                updateRequest.getConfigKey(), updateRequest.getEnvironment(), operatorId, e);
            throw new ConfigUpdateException("配置更新失败", e);
        }
    }
    
    /**
     * 删除配置
     * @param configKey 配置键
     * @param environment 环境
     * @param operatorId 操作者ID
     */
    @Transactional
    public void deleteConfig(String configKey, String environment, String operatorId) {
        try {
            // 1. 验证权限
            securityService.validateConfigPermission(operatorId, "CONFIG_DELETE", configKey);
            
            // 2. 获取配置
            GlobalConfig config = configRepository.findByKeyAndEnvironment(configKey, environment)
                .orElseThrow(() -> new ConfigNotFoundException("配置不存在: " + configKey));
            
            // 3. 检查配置依赖
            checkConfigDependencies(configKey, environment);
            
            // 4. 创建删除版本记录
            ConfigVersion deleteVersion = createDeleteVersion(config, operatorId);
            configVersionRepository.save(deleteVersion);
            
            // 5. 软删除配置
            config.setDeleted(true);
            config.setDeletedAt(LocalDateTime.now());
            config.setDeletedBy(operatorId);
            configRepository.save(config);
            
            // 6. 清除缓存
            configCacheService.evictConfig(configKey, environment);
            
            // 7. 通知相关服务
            configNotificationService.notifyConfigDeleted(config);
            
            // 8. 记录审计日志
            auditService.logConfigDeletion(config, operatorId);
            
            // 9. 发布配置删除事件
            eventPublisher.publishConfigDeleted(config, operatorId);
            
            log.info("配置删除成功: key={}, env={}, operator={}", configKey, environment, operatorId);
            
        } catch (Exception e) {
            log.error("配置删除失败: key={}, env={}, operator={}", configKey, environment, operatorId, e);
            throw new ConfigDeletionException("配置删除失败", e);
        }
    }
    
    /**
     * 获取配置历史版本
     * @param configKey 配置键
     * @param environment 环境
     * @param pageable 分页参数
     * @return 版本历史
     */
    public Page<ConfigVersion> getConfigHistory(String configKey, String environment, Pageable pageable) {
        try {
            return configVersionRepository.findByConfigKeyAndEnvironment(configKey, environment, pageable);
        } catch (Exception e) {
            log.error("获取配置历史失败: key={}, env={}", configKey, environment, e);
            throw new ConfigAccessException("获取配置历史失败", e);
        }
    }
    
    /**
     * 回滚配置到指定版本
     * @param configKey 配置键
     * @param environment 环境
     * @param versionId 版本ID
     * @param operatorId 操作者ID
     * @return 回滚后的配置
     */
    @Transactional
    public GlobalConfig rollbackConfig(String configKey, String environment, Long versionId, String operatorId) {
        try {
            // 1. 验证权限
            securityService.validateConfigPermission(operatorId, "CONFIG_ROLLBACK", configKey);
            
            // 2. 获取目标版本
            ConfigVersion targetVersion = configVersionRepository.findById(versionId)
                .orElseThrow(() -> new ConfigVersionNotFoundException("配置版本不存在: " + versionId));
            
            // 3. 验证版本匹配
            if (!targetVersion.getConfigKey().equals(configKey) || 
                !targetVersion.getEnvironment().equals(environment)) {
                throw new ConfigVersionMismatchException("配置版本不匹配");
            }
            
            // 4. 获取当前配置
            GlobalConfig currentConfig = configRepository.findByKeyAndEnvironment(configKey, environment)
                .orElseThrow(() -> new ConfigNotFoundException("配置不存在: " + configKey));
            
            // 5. 创建回滚版本记录
            ConfigVersion rollbackVersion = createRollbackVersion(currentConfig, targetVersion, operatorId);
            configVersionRepository.save(rollbackVersion);
            
            // 6. 回滚配置值
            currentConfig.setValue(targetVersion.getValue());
            currentConfig.setDescription(targetVersion.getDescription());
            currentConfig.setUpdatedAt(LocalDateTime.now());
            currentConfig.setUpdatedBy(operatorId);
            
            // 7. 保存配置
            currentConfig = configRepository.save(currentConfig);
            
            // 8. 清除缓存
            configCacheService.evictConfig(configKey, environment);
            
            // 9. 通知相关服务
            configNotificationService.notifyConfigUpdate(currentConfig);
            
            // 10. 记录审计日志
            auditService.logConfigRollback(currentConfig, targetVersion, operatorId);
            
            // 11. 发布配置回滚事件
            eventPublisher.publishConfigRolledBack(currentConfig, targetVersion, operatorId);
            
            log.info("配置回滚成功: key={}, env={}, versionId={}, operator={}", 
                configKey, environment, versionId, operatorId);
            
            return currentConfig;
            
        } catch (Exception e) {
            log.error("配置回滚失败: key={}, env={}, versionId={}, operator={}", 
                configKey, environment, versionId, operatorId, e);
            throw new ConfigRollbackException("配置回滚失败", e);
        }
    }
    
    /**
     * 搜索配置
     * @param searchRequest 搜索请求
     * @return 搜索结果
     */
    public Page<GlobalConfig> searchConfigs(ConfigSearchRequest searchRequest) {
        try {
            return configRepository.searchConfigs(
                searchRequest.getKeyword(),
                searchRequest.getEnvironment(),
                searchRequest.getCategory(),
                searchRequest.getPageable()
            );
        } catch (Exception e) {
            log.error("搜索配置失败: {}", searchRequest, e);
            throw new ConfigSearchException("搜索配置失败", e);
        }
    }
    
    /**
     * 导出配置
     * @param environment 环境
     * @param category 分类（可选）
     * @return 配置导出数据
     */
    public ConfigExportData exportConfigs(String environment, String category) {
        try {
            List<GlobalConfig> configs;
            if (StringUtils.isNotBlank(category)) {
                configs = configRepository.findByEnvironmentAndCategory(environment, category);
            } else {
                configs = configRepository.findByEnvironment(environment);
            }
            
            return ConfigExportData.builder()
                .environment(environment)
                .category(category)
                .configs(configs)
                .exportTime(LocalDateTime.now())
                .version("1.0")
                .build();
                
        } catch (Exception e) {
            log.error("导出配置失败: env={}, category={}", environment, category, e);
            throw new ConfigExportException("导出配置失败", e);
        }
    }
    
    /**
     * 导入配置
     * @param importData 导入数据
     * @param operatorId 操作者ID
     * @return 导入结果
     */
    @Transactional
    public ConfigImportResult importConfigs(ConfigImportData importData, String operatorId) {
        try {
            // 1. 验证权限
            securityService.validateConfigPermission(operatorId, "CONFIG_IMPORT", "*");
            
            // 2. 验证导入数据
            configValidationService.validateImportData(importData);
            
            List<String> successfulImports = new ArrayList<>();
            List<String> failedImports = new ArrayList<>();
            
            // 3. 逐个导入配置
            for (GlobalConfig config : importData.getConfigs()) {
                try {
                    importSingleConfig(config, operatorId);
                    successfulImports.add(config.getConfigKey());
                } catch (Exception e) {
                    log.warn("导入配置失败: key={}", config.getConfigKey(), e);
                    failedImports.add(config.getConfigKey());
                }
            }
            
            // 4. 记录导入审计日志
            auditService.logConfigImport(importData, successfulImports, failedImports, operatorId);
            
            log.info("配置导入完成: successful={}, failed={}, operator={}", 
                successfulImports.size(), failedImports.size(), operatorId);
            
            return ConfigImportResult.builder()
                .totalCount(importData.getConfigs().size())
                .successfulImports(successfulImports)
                .failedImports(failedImports)
                .build();
                
        } catch (Exception e) {
            log.error("配置导入失败: operator={}", operatorId, e);
            throw new ConfigImportException("配置导入失败", e);
        }
    }
    
    /**
     * 刷新配置缓存
     * @param configKey 配置键（可选，为空时刷新所有）
     * @param environment 环境（可选）
     */
    public void refreshConfigCache(String configKey, String environment) {
        try {
            if (StringUtils.isNotBlank(configKey)) {
                configCacheService.evictConfig(configKey, environment);
            } else {
                configCacheService.evictAllConfigs();
            }
            
            log.info("配置缓存刷新成功: key={}, env={}", configKey, environment);
            
        } catch (Exception e) {
            log.error("配置缓存刷新失败: key={}, env={}", configKey, environment, e);
            throw new ConfigCacheException("配置缓存刷新失败", e);
        }
    }

    private ConfigValue buildConfigValue(GlobalConfig config) {
        return ConfigValue.builder()
            .key(config.getConfigKey())
            .value(config.getValue())
            .dataType(config.getDataType())
            .environment(config.getEnvironment())
            .category(config.getCategory())
            .description(config.getDescription())
            .lastUpdated(config.getUpdatedAt())
            .build();
    }

    private ConfigVersion createConfigVersion(GlobalConfig currentConfig, UpdateConfigRequest request, String operatorId) {
        return ConfigVersion.builder()
            .configKey(request.getConfigKey())
            .environment(request.getEnvironment())
            .oldValue(currentConfig != null ? currentConfig.getValue() : null)
            .newValue(request.getValue())
            .operation(currentConfig != null ? ConfigOperation.UPDATE : ConfigOperation.CREATE)
            .operatorId(operatorId)
            .createdAt(LocalDateTime.now())
            .build();
    }

    private GlobalConfig updateExistingConfig(GlobalConfig config, UpdateConfigRequest request, String operatorId) {
        config.setValue(request.getValue());
        config.setDescription(request.getDescription());
        config.setDataType(request.getDataType());
        config.setCategory(request.getCategory());
        config.setUpdatedAt(LocalDateTime.now());
        config.setUpdatedBy(operatorId);
        return config;
    }

    private GlobalConfig createNewConfig(UpdateConfigRequest request, String operatorId) {
        return GlobalConfig.builder()
            .configKey(request.getConfigKey())
            .value(request.getValue())
            .dataType(request.getDataType())
            .environment(request.getEnvironment())
            .category(request.getCategory())
            .description(request.getDescription())
            .createdAt(LocalDateTime.now())
            .createdBy(operatorId)
            .updatedAt(LocalDateTime.now())
            .updatedBy(operatorId)
            .deleted(false)
            .build();
    }

    private void checkConfigDependencies(String configKey, String environment) {
        // 检查是否有其他配置依赖此配置
        // 这里可以实现配置依赖检查逻辑
    }

    private ConfigVersion createDeleteVersion(GlobalConfig config, String operatorId) {
        return ConfigVersion.builder()
            .configKey(config.getConfigKey())
            .environment(config.getEnvironment())
            .oldValue(config.getValue())
            .newValue(null)
            .operation(ConfigOperation.DELETE)
            .operatorId(operatorId)
            .createdAt(LocalDateTime.now())
            .build();
    }

    private ConfigVersion createRollbackVersion(GlobalConfig currentConfig, ConfigVersion targetVersion, String operatorId) {
        return ConfigVersion.builder()
            .configKey(currentConfig.getConfigKey())
            .environment(currentConfig.getEnvironment())
            .oldValue(currentConfig.getValue())
            .newValue(targetVersion.getValue())
            .operation(ConfigOperation.ROLLBACK)
            .operatorId(operatorId)
            .targetVersionId(targetVersion.getId())
            .createdAt(LocalDateTime.now())
            .build();
    }

    private void importSingleConfig(GlobalConfig config, String operatorId) {
        UpdateConfigRequest request = UpdateConfigRequest.builder()
            .configKey(config.getConfigKey())
            .value(config.getValue())
            .dataType(config.getDataType())
            .environment(config.getEnvironment())
            .category(config.getCategory())
            .description(config.getDescription())
            .build();

        updateConfig(request, operatorId);
    }
}
```

### 2. 配置缓存服务

#### ConfigCacheService - 配置缓存服务
```java
/**
 * 配置缓存服务
 * 负责配置的缓存管理和性能优化
 */
@Service
@Slf4j
public class ConfigCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ConfigCacheProperties cacheProperties;

    private static final String CONFIG_CACHE_PREFIX = "config:";
    private static final String CONFIG_HASH_PREFIX = "config_hash:";

    /**
     * 获取缓存的配置
     * @param configKey 配置键
     * @param environment 环境
     * @return 配置值
     */
    public ConfigValue getConfig(String configKey, String environment) {
        try {
            String cacheKey = buildCacheKey(configKey, environment);
            return (ConfigValue) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取配置缓存失败: key={}, env={}", configKey, environment, e);
            return null;
        }
    }

    /**
     * 缓存配置
     * @param configKey 配置键
     * @param environment 环境
     * @param configValue 配置值
     */
    public void cacheConfig(String configKey, String environment, ConfigValue configValue) {
        try {
            String cacheKey = buildCacheKey(configKey, environment);
            Duration ttl = Duration.ofSeconds(cacheProperties.getTtlSeconds());

            redisTemplate.opsForValue().set(cacheKey, configValue, ttl);

            // 同时维护环境配置哈希表
            String hashKey = CONFIG_HASH_PREFIX + environment;
            redisTemplate.opsForHash().put(hashKey, configKey, configValue);
            redisTemplate.expire(hashKey, ttl);

        } catch (Exception e) {
            log.error("缓存配置失败: key={}, env={}", configKey, environment, e);
        }
    }

    /**
     * 移除配置缓存
     * @param configKey 配置键
     * @param environment 环境
     */
    public void evictConfig(String configKey, String environment) {
        try {
            String cacheKey = buildCacheKey(configKey, environment);
            redisTemplate.delete(cacheKey);

            // 从环境哈希表中移除
            String hashKey = CONFIG_HASH_PREFIX + environment;
            redisTemplate.opsForHash().delete(hashKey, configKey);

            log.debug("配置缓存已移除: key={}, env={}", configKey, environment);

        } catch (Exception e) {
            log.error("移除配置缓存失败: key={}, env={}", configKey, environment, e);
        }
    }

    /**
     * 清除所有配置缓存
     */
    public void evictAllConfigs() {
        try {
            Set<String> keys = redisTemplate.keys(CONFIG_CACHE_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }

            Set<String> hashKeys = redisTemplate.keys(CONFIG_HASH_PREFIX + "*");
            if (hashKeys != null && !hashKeys.isEmpty()) {
                redisTemplate.delete(hashKeys);
            }

            log.info("所有配置缓存已清除");

        } catch (Exception e) {
            log.error("清除所有配置缓存失败", e);
        }
    }

    /**
     * 获取环境下的所有配置
     * @param environment 环境
     * @return 配置映射
     */
    public Map<String, ConfigValue> getEnvironmentConfigs(String environment) {
        try {
            String hashKey = CONFIG_HASH_PREFIX + environment;
            Map<Object, Object> rawConfigs = redisTemplate.opsForHash().entries(hashKey);

            Map<String, ConfigValue> configs = new HashMap<>();
            for (Map.Entry<Object, Object> entry : rawConfigs.entrySet()) {
                String key = (String) entry.getKey();
                ConfigValue value = (ConfigValue) entry.getValue();
                configs.put(key, value);
            }

            return configs;

        } catch (Exception e) {
            log.error("获取环境配置缓存失败: env={}", environment, e);
            return new HashMap<>();
        }
    }

    /**
     * 预热配置缓存
     * @param environment 环境
     */
    public void warmupCache(String environment) {
        try {
            // 这里可以从数据库加载所有配置并缓存
            log.info("配置缓存预热完成: env={}", environment);
        } catch (Exception e) {
            log.error("配置缓存预热失败: env={}", environment, e);
        }
    }

    private String buildCacheKey(String configKey, String environment) {
        return CONFIG_CACHE_PREFIX + environment + ":" + configKey;
    }
}
```

## 数据模型定义

### 1. 配置相关实体

#### GlobalConfig - 全局配置实体
```java
/**
 * 全局配置实体
 * 存储系统级配置信息
 */
@Entity
@Table(name = "global_configs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GlobalConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String configKey;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String value;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ConfigDataType dataType;

    @Column(nullable = false, length = 50)
    private String environment;

    @Column(length = 50)
    private String category;

    @Column(length = 500)
    private String description;

    @Column(nullable = false)
    private Boolean deleted = false;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private String createdBy;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    @Column(nullable = false)
    private String updatedBy;

    private LocalDateTime deletedAt;
    private String deletedBy;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * 获取类型化的配置值
     * @param targetType 目标类型
     * @return 类型化的值
     */
    @SuppressWarnings("unchecked")
    public <T> T getTypedValue(Class<T> targetType) {
        if (value == null) {
            return null;
        }

        try {
            switch (dataType) {
                case STRING:
                    return (T) value;
                case INTEGER:
                    return (T) Integer.valueOf(value);
                case LONG:
                    return (T) Long.valueOf(value);
                case DOUBLE:
                    return (T) Double.valueOf(value);
                case BOOLEAN:
                    return (T) Boolean.valueOf(value);
                case JSON:
                    ObjectMapper mapper = new ObjectMapper();
                    return mapper.readValue(value, targetType);
                default:
                    return (T) value;
            }
        } catch (Exception e) {
            throw new ConfigValueConversionException("配置值类型转换失败: " + configKey, e);
        }
    }
}
```

#### ConfigVersion - 配置版本实体
```java
/**
 * 配置版本实体
 * 记录配置的变更历史
 */
@Entity
@Table(name = "config_versions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String configKey;

    @Column(nullable = false, length = 50)
    private String environment;

    @Column(columnDefinition = "TEXT")
    private String oldValue;

    @Column(columnDefinition = "TEXT")
    private String newValue;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ConfigOperation operation;

    @Column(nullable = false)
    private String operatorId;

    private Long targetVersionId; // 用于回滚操作

    @Column(length = 500)
    private String changeReason;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本配置管理操作
```java
// 注入配置服务
@Autowired
private GlobalConfigService globalConfigService;

// 获取配置
ConfigValue dbUrl = globalConfigService.getConfig("database.url", "production");
System.out.println("数据库URL: " + dbUrl.getValue());

// 获取类型化配置值
GlobalConfig maxConnections = configRepository.findByKeyAndEnvironment("database.max_connections", "production").orElse(null);
Integer maxConn = maxConnections.getTypedValue(Integer.class);

// 批量获取配置
List<String> configKeys = Arrays.asList("database.url", "database.username", "redis.host");
Map<String, ConfigValue> configs = globalConfigService.getBatchConfigs(configKeys, "production");

// 更新配置
UpdateConfigRequest updateRequest = UpdateConfigRequest.builder()
    .configKey("feature.ai_chat.enabled")
    .value("true")
    .dataType(ConfigDataType.BOOLEAN)
    .environment("production")
    .category("feature_flags")
    .description("启用AI聊天功能")
    .build();

GlobalConfig updatedConfig = globalConfigService.updateConfig(updateRequest, "admin123");

// 获取配置历史
Page<ConfigVersion> history = globalConfigService.getConfigHistory(
    "feature.ai_chat.enabled", "production", PageRequest.of(0, 10));

// 回滚配置
GlobalConfig rolledBackConfig = globalConfigService.rollbackConfig(
    "feature.ai_chat.enabled", "production", history.getContent().get(1).getId(), "admin123");

// 搜索配置
ConfigSearchRequest searchRequest = ConfigSearchRequest.builder()
    .keyword("database")
    .environment("production")
    .category("database")
    .pageable(PageRequest.of(0, 20))
    .build();

Page<GlobalConfig> searchResults = globalConfigService.searchConfigs(searchRequest);

// 导出配置
ConfigExportData exportData = globalConfigService.exportConfigs("production", "feature_flags");

// 刷新缓存
globalConfigService.refreshConfigCache("feature.ai_chat.enabled", "production");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的全局配置管理实现，支持配置CRUD、版本控制、缓存管理、权限控制等功能
