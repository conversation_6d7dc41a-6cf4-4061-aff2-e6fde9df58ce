# 实时同步 (Real-time Sync)

## 模块概述

实时同步模块负责Ark-Pets AI Enhanced项目中多设备间的数据实时同步，包括对话状态同步、用户偏好同步、宠物状态同步、配置同步等功能。通过WebSocket、Server-Sent Events等技术实现低延迟的实时数据同步，确保用户在不同设备间的一致体验。

**核心职责**:
- 多设备数据实时同步
- 对话状态和历史同步
- 用户偏好和配置同步
- 宠物状态和行为同步
- 冲突检测和解决机制

## 核心功能架构

### 1. 实时同步架构

#### 分层实时同步架构模型
```
┌─────────────────────────────────────┐
│           同步协调层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 冲突检测  │ 版本控制  │ 合并策略  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           同步传输层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ WebSocket │ SSE推送  │ 消息队列  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据管理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 变更检测  │ 增量同步  │ 状态缓存  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 实时同步流程

#### 数据同步处理流程图
```mermaid
graph TB
    subgraph "数据变更流程"
        DataChange[数据变更]
        ChangeDetection[变更检测]
        VersionCheck[版本检查]
        ConflictDetection[冲突检测]
        ConflictResolution[冲突解决]
        SyncPrepare[同步准备]
    end
    
    subgraph "同步分发流程"
        GetDevices[获取设备列表]
        FilterDevices[过滤目标设备]
        BuildSyncMessage[构建同步消息]
        SendSync[发送同步]
        ConfirmSync[确认同步]
        UpdateStatus[更新状态]
    end
    
    subgraph "接收处理流程"
        ReceiveSync[接收同步]
        ValidateSync[验证同步]
        ApplyChanges[应用变更]
        SendAck[发送确认]
        NotifyUI[通知界面]
    end
    
    DataChange --> ChangeDetection
    ChangeDetection --> VersionCheck
    VersionCheck --> ConflictDetection
    ConflictDetection --> ConflictResolution
    ConflictResolution --> SyncPrepare
    
    SyncPrepare --> GetDevices
    GetDevices --> FilterDevices
    FilterDevices --> BuildSyncMessage
    BuildSyncMessage --> SendSync
    SendSync --> ConfirmSync
    ConfirmSync --> UpdateStatus
    
    SendSync --> ReceiveSync
    ReceiveSync --> ValidateSync
    ValidateSync --> ApplyChanges
    ApplyChanges --> SendAck
    SendAck --> NotifyUI
```

## 核心类和接口

### 1. 实时同步主服务

#### RealTimeSyncService - 实时同步主服务
```java
/**
 * 实时同步主服务
 * 负责多设备间的数据实时同步
 */
@Service
@Slf4j
public class RealTimeSyncService {
    
    private final SyncStateRepository syncStateRepository;
    private final DeviceSessionRepository deviceSessionRepository;
    private final WebSocketSessionManager webSocketSessionManager;
    private final SyncConflictResolver syncConflictResolver;
    private final SyncMessageBuilder syncMessageBuilder;
    private final EventPublisher eventPublisher;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 同步数据变更
     * @param syncRequest 同步请求
     * @return 同步结果
     */
    @Transactional
    public SyncResult syncDataChange(SyncRequest syncRequest) {
        try {
            // 1. 验证同步请求
            validateSyncRequest(syncRequest);
            
            // 2. 获取当前同步状态
            SyncState currentState = getCurrentSyncState(syncRequest.getUserId(), syncRequest.getDataType());
            
            // 3. 检测版本冲突
            ConflictDetectionResult conflictResult = detectVersionConflict(syncRequest, currentState);
            
            // 4. 解决冲突（如果存在）
            if (conflictResult.hasConflict()) {
                SyncData resolvedData = syncConflictResolver.resolveConflict(
                    syncRequest.getData(), currentState.getData(), conflictResult);
                syncRequest.setData(resolvedData);
            }
            
            // 5. 更新同步状态
            SyncState newState = updateSyncState(syncRequest, currentState);
            
            // 6. 获取需要同步的设备
            List<DeviceSession> targetDevices = getTargetDevices(syncRequest.getUserId(), syncRequest.getSourceDeviceId());
            
            // 7. 构建同步消息
            SyncMessage syncMessage = syncMessageBuilder.buildSyncMessage(syncRequest, newState);
            
            // 8. 发送同步消息
            List<SyncDeliveryResult> deliveryResults = sendSyncToDevices(targetDevices, syncMessage);
            
            // 9. 记录同步结果
            SyncResult result = SyncResult.builder()
                .syncId(syncMessage.getSyncId())
                .dataType(syncRequest.getDataType())
                .version(newState.getVersion())
                .targetDeviceCount(targetDevices.size())
                .deliveryResults(deliveryResults)
                .syncedAt(LocalDateTime.now())
                .build();
            
            // 10. 发布同步事件
            eventPublisher.publishDataSynced(result);
            
            log.info("数据同步完成: userId={}, dataType={}, version={}, devices={}", 
                syncRequest.getUserId(), syncRequest.getDataType(), newState.getVersion(), targetDevices.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("数据同步失败: {}", syncRequest, e);
            throw new SyncException("数据同步失败", e);
        }
    }
    
    /**
     * 处理同步消息
     * @param deviceId 设备ID
     * @param syncMessage 同步消息
     * @return 处理结果
     */
    @Transactional
    public SyncProcessResult processSyncMessage(String deviceId, SyncMessage syncMessage) {
        try {
            // 1. 验证同步消息
            if (!validateSyncMessage(syncMessage)) {
                return SyncProcessResult.invalid("同步消息验证失败");
            }
            
            // 2. 获取当前状态
            SyncState currentState = getCurrentSyncState(syncMessage.getUserId(), syncMessage.getDataType());
            
            // 3. 检查版本
            if (currentState != null && currentState.getVersion() >= syncMessage.getVersion()) {
                return SyncProcessResult.ignored("版本过期");
            }
            
            // 4. 应用同步数据
            applySyncData(syncMessage);
            
            // 5. 更新同步状态
            updateSyncStateFromMessage(syncMessage);
            
            // 6. 发送确认
            sendSyncAcknowledgment(deviceId, syncMessage);
            
            // 7. 通知UI更新
            notifyUIUpdate(syncMessage);
            
            log.debug("同步消息处理成功: deviceId={}, syncId={}, dataType={}", 
                deviceId, syncMessage.getSyncId(), syncMessage.getDataType());
            
            return SyncProcessResult.success();
            
        } catch (Exception e) {
            log.error("处理同步消息失败: deviceId={}, syncId={}", deviceId, syncMessage.getSyncId(), e);
            return SyncProcessResult.failed(e.getMessage());
        }
    }
    
    /**
     * 获取同步状态
     * @param userId 用户ID
     * @param dataType 数据类型
     * @return 同步状态
     */
    public SyncStatusResponse getSyncStatus(String userId, SyncDataType dataType) {
        try {
            SyncState syncState = getCurrentSyncState(userId, dataType);
            List<DeviceSession> userDevices = deviceSessionRepository.findActiveDevicesByUserId(userId);
            
            return SyncStatusResponse.builder()
                .userId(userId)
                .dataType(dataType)
                .currentVersion(syncState != null ? syncState.getVersion() : 0L)
                .lastSyncTime(syncState != null ? syncState.getUpdatedAt() : null)
                .deviceCount(userDevices.size())
                .devices(userDevices.stream()
                    .map(this::buildDeviceInfo)
                    .collect(Collectors.toList()))
                .build();
                
        } catch (Exception e) {
            log.error("获取同步状态失败: userId={}, dataType={}", userId, dataType, e);
            throw new SyncStatusException("获取同步状态失败", e);
        }
    }
    
    /**
     * 强制同步
     * @param userId 用户ID
     * @param dataType 数据类型
     * @param sourceDeviceId 源设备ID
     * @return 同步结果
     */
    public SyncResult forceSyncData(String userId, SyncDataType dataType, String sourceDeviceId) {
        try {
            // 1. 获取最新数据
            SyncData latestData = getLatestSyncData(userId, dataType);
            
            // 2. 构建强制同步请求
            SyncRequest forceSyncRequest = SyncRequest.builder()
                .userId(userId)
                .dataType(dataType)
                .sourceDeviceId(sourceDeviceId)
                .data(latestData)
                .forceSync(true)
                .timestamp(LocalDateTime.now())
                .build();
            
            // 3. 执行同步
            return syncDataChange(forceSyncRequest);
            
        } catch (Exception e) {
            log.error("强制同步失败: userId={}, dataType={}", userId, dataType, e);
            throw new ForceSyncException("强制同步失败", e);
        }
    }
    
    /**
     * 注册设备会话
     * @param deviceRegistration 设备注册信息
     * @return 注册结果
     */
    @Transactional
    public DeviceRegistrationResult registerDeviceSession(DeviceSessionRegistration deviceRegistration) {
        try {
            // 1. 验证设备信息
            validateDeviceRegistration(deviceRegistration);
            
            // 2. 检查是否已存在
            Optional<DeviceSession> existingSession = deviceSessionRepository
                .findByUserIdAndDeviceId(deviceRegistration.getUserId(), deviceRegistration.getDeviceId());
            
            DeviceSession deviceSession;
            if (existingSession.isPresent()) {
                // 更新现有会话
                deviceSession = existingSession.get();
                updateDeviceSession(deviceSession, deviceRegistration);
            } else {
                // 创建新会话
                deviceSession = createDeviceSession(deviceRegistration);
            }
            
            // 3. 保存设备会话
            deviceSession = deviceSessionRepository.save(deviceSession);
            
            // 4. 初始化同步状态
            initializeSyncStates(deviceSession);
            
            log.info("设备会话注册成功: userId={}, deviceId={}, platform={}", 
                deviceRegistration.getUserId(), deviceRegistration.getDeviceId(), deviceRegistration.getPlatform());
            
            return DeviceRegistrationResult.success(deviceSession.getId());
            
        } catch (Exception e) {
            log.error("注册设备会话失败: {}", deviceRegistration, e);
            throw new DeviceRegistrationException("注册设备会话失败", e);
        }
    }
    
    /**
     * 获取同步历史
     * @param userId 用户ID
     * @param dataType 数据类型
     * @param pageable 分页参数
     * @return 同步历史
     */
    public Page<SyncHistoryRecord> getSyncHistory(String userId, SyncDataType dataType, Pageable pageable) {
        try {
            return syncStateRepository.findSyncHistory(userId, dataType, pageable);
        } catch (Exception e) {
            log.error("获取同步历史失败: userId={}, dataType={}", userId, dataType, e);
            throw new SyncHistoryException("获取同步历史失败", e);
        }
    }
    
    /**
     * 清理过期同步状态
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupExpiredSyncStates() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(30);
            int cleanedCount = syncStateRepository.deleteExpiredStates(expireTime);
            
            log.info("清理过期同步状态完成: count={}", cleanedCount);
            
        } catch (Exception e) {
            log.error("清理过期同步状态失败", e);
        }
    }
    
    private void validateSyncRequest(SyncRequest request) {
        if (StringUtils.isBlank(request.getUserId())) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (request.getDataType() == null) {
            throw new IllegalArgumentException("数据类型不能为空");
        }
        if (request.getData() == null) {
            throw new IllegalArgumentException("同步数据不能为空");
        }
    }
    
    private SyncState getCurrentSyncState(String userId, SyncDataType dataType) {
        return syncStateRepository.findByUserIdAndDataType(userId, dataType).orElse(null);
    }
    
    private ConflictDetectionResult detectVersionConflict(SyncRequest request, SyncState currentState) {
        if (currentState == null) {
            return ConflictDetectionResult.noConflict();
        }
        
        // 检查版本冲突
        if (request.getVersion() != null && request.getVersion() < currentState.getVersion()) {
            return ConflictDetectionResult.versionConflict(currentState.getVersion(), request.getVersion());
        }
        
        // 检查时间戳冲突
        if (request.getTimestamp().isBefore(currentState.getUpdatedAt())) {
            return ConflictDetectionResult.timestampConflict(currentState.getUpdatedAt(), request.getTimestamp());
        }
        
        return ConflictDetectionResult.noConflict();
    }
    
    private SyncState updateSyncState(SyncRequest request, SyncState currentState) {
        if (currentState == null) {
            currentState = SyncState.builder()
                .userId(request.getUserId())
                .dataType(request.getDataType())
                .version(1L)
                .data(request.getData())
                .sourceDeviceId(request.getSourceDeviceId())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        } else {
            currentState.setVersion(currentState.getVersion() + 1);
            currentState.setData(request.getData());
            currentState.setSourceDeviceId(request.getSourceDeviceId());
            currentState.setUpdatedAt(LocalDateTime.now());
        }
        
        return syncStateRepository.save(currentState);
    }
    
    private List<DeviceSession> getTargetDevices(String userId, String sourceDeviceId) {
        return deviceSessionRepository.findActiveDevicesByUserId(userId)
            .stream()
            .filter(device -> !device.getDeviceId().equals(sourceDeviceId))
            .collect(Collectors.toList());
    }
    
    private List<SyncDeliveryResult> sendSyncToDevices(List<DeviceSession> devices, SyncMessage syncMessage) {
        List<SyncDeliveryResult> results = new ArrayList<>();
        
        for (DeviceSession device : devices) {
            try {
                boolean sent = sendSyncToDevice(device, syncMessage);
                results.add(SyncDeliveryResult.builder()
                    .deviceId(device.getDeviceId())
                    .success(sent)
                    .sentAt(LocalDateTime.now())
                    .build());
            } catch (Exception e) {
                log.error("发送同步消息到设备失败: deviceId={}", device.getDeviceId(), e);
                results.add(SyncDeliveryResult.builder()
                    .deviceId(device.getDeviceId())
                    .success(false)
                    .errorMessage(e.getMessage())
                    .sentAt(LocalDateTime.now())
                    .build());
            }
        }
        
        return results;
    }
    
    private boolean sendSyncToDevice(DeviceSession device, SyncMessage syncMessage) {
        // 通过WebSocket发送同步消息
        return webSocketSessionManager.sendMessageToUser(device.getUserId(), 
            WebSocketMessage.builder()
                .type(MessageType.SYNC)
                .data(Map.of("syncMessage", syncMessage))
                .timestamp(LocalDateTime.now())
                .build());
    }

    private void applySyncData(SyncMessage syncMessage) {
        // 根据数据类型应用同步数据
        switch (syncMessage.getDataType()) {
            case CONVERSATION:
                applyConversationSync(syncMessage);
                break;
            case USER_PREFERENCES:
                applyUserPreferencesSync(syncMessage);
                break;
            case PET_STATUS:
                applyPetStatusSync(syncMessage);
                break;
            case CONFIGURATION:
                applyConfigurationSync(syncMessage);
                break;
            default:
                log.warn("未知的同步数据类型: {}", syncMessage.getDataType());
        }
    }

    private void updateSyncStateFromMessage(SyncMessage syncMessage) {
        SyncState syncState = SyncState.builder()
            .userId(syncMessage.getUserId())
            .dataType(syncMessage.getDataType())
            .version(syncMessage.getVersion())
            .data(syncMessage.getData())
            .sourceDeviceId(syncMessage.getSourceDeviceId())
            .updatedAt(LocalDateTime.now())
            .build();

        syncStateRepository.save(syncState);
    }

    private void sendSyncAcknowledgment(String deviceId, SyncMessage syncMessage) {
        SyncAcknowledgment ack = SyncAcknowledgment.builder()
            .syncId(syncMessage.getSyncId())
            .deviceId(deviceId)
            .status(SyncStatus.SUCCESS)
            .timestamp(LocalDateTime.now())
            .build();

        // 发送确认消息
        webSocketSessionManager.sendMessage(deviceId,
            WebSocketMessage.builder()
                .type(MessageType.SYNC_ACK)
                .data(Map.of("acknowledgment", ack))
                .timestamp(LocalDateTime.now())
                .build());
    }

    private void notifyUIUpdate(SyncMessage syncMessage) {
        // 通知UI更新
        eventPublisher.publishUIUpdateRequired(syncMessage.getUserId(), syncMessage.getDataType());
    }

    private boolean validateSyncMessage(SyncMessage syncMessage) {
        return syncMessage != null &&
               StringUtils.isNotBlank(syncMessage.getSyncId()) &&
               StringUtils.isNotBlank(syncMessage.getUserId()) &&
               syncMessage.getDataType() != null &&
               syncMessage.getData() != null;
    }

    private DeviceInfo buildDeviceInfo(DeviceSession deviceSession) {
        return DeviceInfo.builder()
            .deviceId(deviceSession.getDeviceId())
            .deviceName(deviceSession.getDeviceName())
            .platform(deviceSession.getPlatform())
            .lastActiveTime(deviceSession.getLastActiveTime())
            .isOnline(isDeviceOnline(deviceSession.getDeviceId()))
            .build();
    }

    private boolean isDeviceOnline(String deviceId) {
        return webSocketSessionManager.getSession(deviceId) != null;
    }

    private SyncData getLatestSyncData(String userId, SyncDataType dataType) {
        SyncState syncState = getCurrentSyncState(userId, dataType);
        return syncState != null ? syncState.getData() : null;
    }

    private void validateDeviceRegistration(DeviceSessionRegistration registration) {
        if (StringUtils.isBlank(registration.getUserId())) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(registration.getDeviceId())) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        if (registration.getPlatform() == null) {
            throw new IllegalArgumentException("设备平台不能为空");
        }
    }

    private DeviceSession createDeviceSession(DeviceSessionRegistration registration) {
        return DeviceSession.builder()
            .userId(registration.getUserId())
            .deviceId(registration.getDeviceId())
            .deviceName(registration.getDeviceName())
            .platform(registration.getPlatform())
            .osVersion(registration.getOsVersion())
            .appVersion(registration.getAppVersion())
            .status(DeviceStatus.ACTIVE)
            .registeredAt(LocalDateTime.now())
            .lastActiveTime(LocalDateTime.now())
            .build();
    }

    private void updateDeviceSession(DeviceSession deviceSession, DeviceSessionRegistration registration) {
        deviceSession.setDeviceName(registration.getDeviceName());
        deviceSession.setOsVersion(registration.getOsVersion());
        deviceSession.setAppVersion(registration.getAppVersion());
        deviceSession.setStatus(DeviceStatus.ACTIVE);
        deviceSession.setLastActiveTime(LocalDateTime.now());
    }

    private void initializeSyncStates(DeviceSession deviceSession) {
        // 为新设备初始化各种数据类型的同步状态
        for (SyncDataType dataType : SyncDataType.values()) {
            SyncState existingState = getCurrentSyncState(deviceSession.getUserId(), dataType);
            if (existingState == null) {
                SyncState initialState = SyncState.builder()
                    .userId(deviceSession.getUserId())
                    .dataType(dataType)
                    .version(0L)
                    .data(getDefaultSyncData(dataType))
                    .sourceDeviceId(deviceSession.getDeviceId())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

                syncStateRepository.save(initialState);
            }
        }
    }

    private SyncData getDefaultSyncData(SyncDataType dataType) {
        // 返回各种数据类型的默认同步数据
        return SyncData.empty(dataType);
    }

    private void applyConversationSync(SyncMessage syncMessage) {
        // 应用对话同步数据
        log.debug("应用对话同步: userId={}, version={}", syncMessage.getUserId(), syncMessage.getVersion());
    }

    private void applyUserPreferencesSync(SyncMessage syncMessage) {
        // 应用用户偏好同步数据
        log.debug("应用用户偏好同步: userId={}, version={}", syncMessage.getUserId(), syncMessage.getVersion());
    }

    private void applyPetStatusSync(SyncMessage syncMessage) {
        // 应用宠物状态同步数据
        log.debug("应用宠物状态同步: userId={}, version={}", syncMessage.getUserId(), syncMessage.getVersion());
    }

    private void applyConfigurationSync(SyncMessage syncMessage) {
        // 应用配置同步数据
        log.debug("应用配置同步: userId={}, version={}", syncMessage.getUserId(), syncMessage.getVersion());
    }
}
```

## 数据模型定义

### 1. 同步相关实体

#### SyncState - 同步状态实体
```java
/**
 * 同步状态实体
 * 存储数据的同步状态信息
 */
@Entity
@Table(name = "sync_states")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncState {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String userId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private SyncDataType dataType;

    @Column(nullable = false)
    private Long version;

    @Column(columnDefinition = "JSON")
    private SyncData data;

    @Column(nullable = false)
    private String sourceDeviceId;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

#### DeviceSession - 设备会话实体
```java
/**
 * 设备会话实体
 * 存储用户设备的会话信息
 */
@Entity
@Table(name = "device_sessions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceSession {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String userId;

    @Column(nullable = false)
    private String deviceId;

    @Column(length = 100)
    private String deviceName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DevicePlatform platform;

    @Column(length = 50)
    private String osVersion;

    @Column(length = 50)
    private String appVersion;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DeviceStatus status;

    @Column(nullable = false)
    private LocalDateTime registeredAt;

    @Column(nullable = false)
    private LocalDateTime lastActiveTime;

    @PrePersist
    protected void onCreate() {
        registeredAt = LocalDateTime.now();
        lastActiveTime = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本实时同步操作
```java
// 注入实时同步服务
@Autowired
private RealTimeSyncService realTimeSyncService;

// 同步对话数据
SyncRequest conversationSync = SyncRequest.builder()
    .userId("user123")
    .dataType(SyncDataType.CONVERSATION)
    .sourceDeviceId("device456")
    .data(SyncData.builder()
        .type(SyncDataType.CONVERSATION)
        .content(Map.of(
            "conversationId", "conv789",
            "messages", conversationMessages,
            "lastMessageTime", LocalDateTime.now()
        ))
        .build())
    .timestamp(LocalDateTime.now())
    .build();

SyncResult result = realTimeSyncService.syncDataChange(conversationSync);
System.out.println("同步结果: 设备数=" + result.getTargetDeviceCount());

// 注册设备会话
DeviceSessionRegistration deviceReg = DeviceSessionRegistration.builder()
    .userId("user123")
    .deviceId("device789")
    .deviceName("iPhone 13")
    .platform(DevicePlatform.IOS)
    .osVersion("15.0")
    .appVersion("1.0.0")
    .build();

DeviceRegistrationResult regResult = realTimeSyncService.registerDeviceSession(deviceReg);
if (regResult.isSuccess()) {
    System.out.println("设备会话注册成功");
}

// 获取同步状态
SyncStatusResponse status = realTimeSyncService.getSyncStatus("user123", SyncDataType.USER_PREFERENCES);
System.out.println("当前版本: " + status.getCurrentVersion());
System.out.println("设备数量: " + status.getDeviceCount());

// 强制同步
SyncResult forceResult = realTimeSyncService.forceSyncData("user123", SyncDataType.PET_STATUS, "device456");
System.out.println("强制同步完成");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的实时同步服务实现，支持多设备数据同步、冲突检测、版本控制、状态管理等功能
