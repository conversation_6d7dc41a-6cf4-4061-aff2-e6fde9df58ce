# WebSocket处理器 (WebSocket Handler)

## 模块概述

WebSocket处理器模块负责Ark-Pets AI Enhanced项目中实时通信的WebSocket连接管理，包括连接建立、消息路由、会话管理、心跳检测、断线重连等功能。提供高性能、可扩展的实时通信基础设施，支持AI对话、系统通知、状态同步等实时交互场景。

**核心职责**:
- WebSocket连接生命周期管理
- 实时消息路由和分发
- 会话状态管理和持久化
- 心跳检测和连接保活
- 断线重连和故障恢复

## 核心功能架构

### 1. WebSocket架构

#### 分层WebSocket架构模型
```
┌─────────────────────────────────────┐
│           WebSocket网关层           │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 连接管理  │ 认证授权  │ 负载均衡  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           消息处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 消息路由  │ 协议转换  │ 消息队列  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           会话管理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 会话存储  │ 状态同步  │ 心跳检测  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. WebSocket通信流程

#### WebSocket连接处理流程图
```mermaid
graph TB
    subgraph "连接建立流程"
        ConnectRequest[连接请求]
        AuthCheck[身份认证]
        SessionCreate[创建会话]
        HandshakeComplete[握手完成]
        RegisterConnection[注册连接]
        SendWelcome[发送欢迎消息]
    end
    
    subgraph "消息处理流程"
        ReceiveMessage[接收消息]
        ValidateMessage[消息验证]
        RouteMessage[消息路由]
        ProcessMessage[消息处理]
        SendResponse[发送响应]
        UpdateSession[更新会话]
    end
    
    subgraph "连接维护流程"
        HeartbeatCheck[心跳检测]
        ConnectionStatus[连接状态]
        ReconnectHandle[重连处理]
        SessionCleanup[会话清理]
    end
    
    ConnectRequest --> AuthCheck
    AuthCheck --> SessionCreate
    SessionCreate --> HandshakeComplete
    HandshakeComplete --> RegisterConnection
    RegisterConnection --> SendWelcome
    
    SendWelcome --> ReceiveMessage
    ReceiveMessage --> ValidateMessage
    ValidateMessage --> RouteMessage
    RouteMessage --> ProcessMessage
    ProcessMessage --> SendResponse
    SendResponse --> UpdateSession
    UpdateSession --> ReceiveMessage
    
    RegisterConnection --> HeartbeatCheck
    HeartbeatCheck --> ConnectionStatus
    ConnectionStatus --> ReconnectHandle
    ReconnectHandle --> SessionCleanup
```

## 核心类和接口

### 1. WebSocket处理器主服务

#### WebSocketHandler - WebSocket处理器主类
```java
/**
 * WebSocket处理器主类
 * 负责WebSocket连接的完整生命周期管理
 */
@Component
@Slf4j
public class WebSocketHandler extends TextWebSocketHandler {
    
    private final WebSocketSessionManager sessionManager;
    private final WebSocketMessageRouter messageRouter;
    private final WebSocketAuthService authService;
    private final WebSocketHeartbeatService heartbeatService;
    private final EventPublisher eventPublisher;
    
    /**
     * 连接建立后的处理
     * @param session WebSocket会话
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        try {
            log.info("WebSocket连接建立: sessionId={}, remoteAddress={}", 
                session.getId(), session.getRemoteAddress());
            
            // 1. 身份认证
            String token = extractTokenFromSession(session);
            UserInfo userInfo = authService.authenticateToken(token);
            
            if (userInfo == null) {
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("认证失败"));
                return;
            }
            
            // 2. 创建会话信息
            WebSocketSessionInfo sessionInfo = WebSocketSessionInfo.builder()
                .sessionId(session.getId())
                .userId(userInfo.getUserId())
                .username(userInfo.getUsername())
                .connectTime(LocalDateTime.now())
                .lastActiveTime(LocalDateTime.now())
                .status(SessionStatus.ACTIVE)
                .build();
            
            // 3. 注册会话
            sessionManager.registerSession(session, sessionInfo);
            
            // 4. 启动心跳检测
            heartbeatService.startHeartbeat(session.getId());
            
            // 5. 发送欢迎消息
            sendWelcomeMessage(session, userInfo);
            
            // 6. 发布连接建立事件
            eventPublisher.publishWebSocketConnected(sessionInfo);
            
        } catch (Exception e) {
            log.error("WebSocket连接建立失败: sessionId={}", session.getId(), e);
            session.close(CloseStatus.SERVER_ERROR.withReason("连接建立失败"));
        }
    }
    
    /**
     * 接收到消息时的处理
     * @param session WebSocket会话
     * @param message 文本消息
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            String sessionId = session.getId();
            String payload = message.getPayload();
            
            log.debug("收到WebSocket消息: sessionId={}, payload={}", sessionId, payload);
            
            // 1. 更新会话活跃时间
            sessionManager.updateLastActiveTime(sessionId);
            
            // 2. 解析消息
            WebSocketMessage wsMessage = parseMessage(payload);
            if (wsMessage == null) {
                sendErrorMessage(session, "消息格式错误");
                return;
            }
            
            // 3. 验证消息
            if (!validateMessage(wsMessage)) {
                sendErrorMessage(session, "消息验证失败");
                return;
            }
            
            // 4. 路由消息
            messageRouter.routeMessage(session, wsMessage);
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: sessionId={}", session.getId(), e);
            sendErrorMessage(session, "消息处理失败");
        }
    }
    
    /**
     * 连接关闭后的处理
     * @param session WebSocket会话
     * @param status 关闭状态
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        try {
            String sessionId = session.getId();
            log.info("WebSocket连接关闭: sessionId={}, status={}", sessionId, status);
            
            // 1. 获取会话信息
            WebSocketSessionInfo sessionInfo = sessionManager.getSessionInfo(sessionId);
            
            // 2. 停止心跳检测
            heartbeatService.stopHeartbeat(sessionId);
            
            // 3. 注销会话
            sessionManager.unregisterSession(sessionId);
            
            // 4. 发布连接关闭事件
            if (sessionInfo != null) {
                eventPublisher.publishWebSocketDisconnected(sessionInfo, status);
            }
            
        } catch (Exception e) {
            log.error("WebSocket连接关闭处理失败: sessionId={}", session.getId(), e);
        }
    }
    
    /**
     * 传输错误处理
     * @param session WebSocket会话
     * @param exception 异常
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        try {
            String sessionId = session.getId();
            log.error("WebSocket传输错误: sessionId={}", sessionId, exception);
            
            // 1. 获取会话信息
            WebSocketSessionInfo sessionInfo = sessionManager.getSessionInfo(sessionId);
            
            // 2. 更新会话状态
            if (sessionInfo != null) {
                sessionInfo.setStatus(SessionStatus.ERROR);
                sessionInfo.setErrorMessage(exception.getMessage());
            }
            
            // 3. 发布错误事件
            eventPublisher.publishWebSocketError(sessionInfo, exception);
            
            // 4. 关闭连接
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR);
            }
            
        } catch (Exception e) {
            log.error("WebSocket错误处理失败: sessionId={}", session.getId(), e);
        }
    }
    
    /**
     * 发送消息到指定会话
     * @param sessionId 会话ID
     * @param message 消息内容
     */
    public void sendMessage(String sessionId, WebSocketMessage message) {
        try {
            WebSocketSession session = sessionManager.getSession(sessionId);
            if (session != null && session.isOpen()) {
                String payload = serializeMessage(message);
                session.sendMessage(new TextMessage(payload));
                
                log.debug("发送WebSocket消息: sessionId={}, type={}", sessionId, message.getType());
            }
        } catch (Exception e) {
            log.error("发送WebSocket消息失败: sessionId={}", sessionId, e);
        }
    }
    
    /**
     * 广播消息到所有会话
     * @param message 消息内容
     */
    public void broadcastMessage(WebSocketMessage message) {
        sessionManager.getAllSessions().forEach(session -> {
            try {
                if (session.isOpen()) {
                    String payload = serializeMessage(message);
                    session.sendMessage(new TextMessage(payload));
                }
            } catch (Exception e) {
                log.error("广播消息失败: sessionId={}", session.getId(), e);
            }
        });
    }
    
    /**
     * 发送消息到指定用户的所有会话
     * @param userId 用户ID
     * @param message 消息内容
     */
    public void sendMessageToUser(String userId, WebSocketMessage message) {
        List<WebSocketSession> userSessions = sessionManager.getUserSessions(userId);
        userSessions.forEach(session -> {
            try {
                if (session.isOpen()) {
                    String payload = serializeMessage(message);
                    session.sendMessage(new TextMessage(payload));
                }
            } catch (Exception e) {
                log.error("发送用户消息失败: sessionId={}, userId={}", session.getId(), userId, e);
            }
        });
    }
    
    private String extractTokenFromSession(WebSocketSession session) {
        // 从查询参数或头部提取认证令牌
        String token = (String) session.getAttributes().get("token");
        if (StringUtils.isBlank(token)) {
            URI uri = session.getUri();
            if (uri != null) {
                String query = uri.getQuery();
                if (StringUtils.isNotBlank(query)) {
                    // 解析查询参数中的token
                    String[] params = query.split("&");
                    for (String param : params) {
                        String[] kv = param.split("=");
                        if (kv.length == 2 && "token".equals(kv[0])) {
                            token = kv[1];
                            break;
                        }
                    }
                }
            }
        }
        return token;
    }
    
    private void sendWelcomeMessage(WebSocketSession session, UserInfo userInfo) {
        WebSocketMessage welcomeMessage = WebSocketMessage.builder()
            .type(MessageType.WELCOME)
            .data(Map.of(
                "userId", userInfo.getUserId(),
                "username", userInfo.getUsername(),
                "connectTime", LocalDateTime.now().toString(),
                "message", "欢迎连接到Ark-Pets AI Enhanced"
            ))
            .timestamp(LocalDateTime.now())
            .build();
        
        try {
            String payload = serializeMessage(welcomeMessage);
            session.sendMessage(new TextMessage(payload));
        } catch (Exception e) {
            log.error("发送欢迎消息失败: sessionId={}", session.getId(), e);
        }
    }
    
    private void sendErrorMessage(WebSocketSession session, String errorMessage) {
        WebSocketMessage errorMsg = WebSocketMessage.builder()
            .type(MessageType.ERROR)
            .data(Map.of("error", errorMessage))
            .timestamp(LocalDateTime.now())
            .build();
        
        try {
            String payload = serializeMessage(errorMsg);
            session.sendMessage(new TextMessage(payload));
        } catch (Exception e) {
            log.error("发送错误消息失败: sessionId={}", session.getId(), e);
        }
    }
    
    private WebSocketMessage parseMessage(String payload) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(payload, WebSocketMessage.class);
        } catch (Exception e) {
            log.warn("解析WebSocket消息失败: payload={}", payload, e);
            return null;
        }
    }
    
    private boolean validateMessage(WebSocketMessage message) {
        return message != null && 
               message.getType() != null && 
               message.getData() != null;
    }
    
    private String serializeMessage(WebSocketMessage message) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(message);
        } catch (Exception e) {
            log.error("序列化WebSocket消息失败: message={}", message, e);
            return "{}";
        }
    }
}
```

### 2. WebSocket会话管理器

#### WebSocketSessionManager - 会话管理器
```java
/**
 * WebSocket会话管理器
 * 负责WebSocket会话的注册、管理和查询
 */
@Component
@Slf4j
public class WebSocketSessionManager {

    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final Map<String, WebSocketSessionInfo> sessionInfos = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> userSessions = new ConcurrentHashMap<>();
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 注册WebSocket会话
     * @param session WebSocket会话
     * @param sessionInfo 会话信息
     */
    public void registerSession(WebSocketSession session, WebSocketSessionInfo sessionInfo) {
        String sessionId = session.getId();
        String userId = sessionInfo.getUserId();

        // 1. 存储会话
        sessions.put(sessionId, session);
        sessionInfos.put(sessionId, sessionInfo);

        // 2. 建立用户会话映射
        userSessions.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(sessionId);

        // 3. 存储到Redis（用于集群环境）
        storeSessionToRedis(sessionInfo);

        log.info("WebSocket会话注册成功: sessionId={}, userId={}", sessionId, userId);
    }

    /**
     * 注销WebSocket会话
     * @param sessionId 会话ID
     */
    public void unregisterSession(String sessionId) {
        WebSocketSessionInfo sessionInfo = sessionInfos.remove(sessionId);
        sessions.remove(sessionId);

        if (sessionInfo != null) {
            String userId = sessionInfo.getUserId();
            Set<String> userSessionSet = userSessions.get(userId);
            if (userSessionSet != null) {
                userSessionSet.remove(sessionId);
                if (userSessionSet.isEmpty()) {
                    userSessions.remove(userId);
                }
            }

            // 从Redis中移除
            removeSessionFromRedis(sessionId);

            log.info("WebSocket会话注销成功: sessionId={}, userId={}", sessionId, userId);
        }
    }

    /**
     * 获取WebSocket会话
     * @param sessionId 会话ID
     * @return WebSocket会话
     */
    public WebSocketSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }

    /**
     * 获取会话信息
     * @param sessionId 会话ID
     * @return 会话信息
     */
    public WebSocketSessionInfo getSessionInfo(String sessionId) {
        return sessionInfos.get(sessionId);
    }

    /**
     * 获取用户的所有会话
     * @param userId 用户ID
     * @return 用户会话列表
     */
    public List<WebSocketSession> getUserSessions(String userId) {
        Set<String> sessionIds = userSessions.get(userId);
        if (sessionIds == null || sessionIds.isEmpty()) {
            return Collections.emptyList();
        }

        return sessionIds.stream()
            .map(sessions::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 获取所有活跃会话
     * @return 所有会话列表
     */
    public Collection<WebSocketSession> getAllSessions() {
        return sessions.values();
    }

    /**
     * 更新会话最后活跃时间
     * @param sessionId 会话ID
     */
    public void updateLastActiveTime(String sessionId) {
        WebSocketSessionInfo sessionInfo = sessionInfos.get(sessionId);
        if (sessionInfo != null) {
            sessionInfo.setLastActiveTime(LocalDateTime.now());
            // 更新Redis中的信息
            storeSessionToRedis(sessionInfo);
        }
    }

    /**
     * 获取在线用户数量
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }

    /**
     * 获取总会话数量
     * @return 总会话数量
     */
    public int getTotalSessionCount() {
        return sessions.size();
    }

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isUserOnline(String userId) {
        Set<String> sessionIds = userSessions.get(userId);
        return sessionIds != null && !sessionIds.isEmpty();
    }

    private void storeSessionToRedis(WebSocketSessionInfo sessionInfo) {
        try {
            String key = "websocket:session:" + sessionInfo.getSessionId();
            redisTemplate.opsForValue().set(key, sessionInfo, Duration.ofHours(24));
        } catch (Exception e) {
            log.warn("存储会话信息到Redis失败: sessionId={}", sessionInfo.getSessionId(), e);
        }
    }

    private void removeSessionFromRedis(String sessionId) {
        try {
            String key = "websocket:session:" + sessionId;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.warn("从Redis删除会话信息失败: sessionId={}", sessionId, e);
        }
    }
}
```

### 3. WebSocket消息路由器

#### WebSocketMessageRouter - 消息路由器
```java
/**
 * WebSocket消息路由器
 * 负责根据消息类型路由到相应的处理器
 */
@Component
@Slf4j
public class WebSocketMessageRouter {

    private final Map<MessageType, WebSocketMessageHandler> handlers = new HashMap<>();
    private final ApplicationContext applicationContext;

    @PostConstruct
    public void initializeHandlers() {
        // 注册消息处理器
        handlers.put(MessageType.CHAT, applicationContext.getBean(ChatMessageHandler.class));
        handlers.put(MessageType.HEARTBEAT, applicationContext.getBean(HeartbeatMessageHandler.class));
        handlers.put(MessageType.NOTIFICATION, applicationContext.getBean(NotificationMessageHandler.class));
        handlers.put(MessageType.SYSTEM, applicationContext.getBean(SystemMessageHandler.class));
        handlers.put(MessageType.USER_STATUS, applicationContext.getBean(UserStatusMessageHandler.class));
    }

    /**
     * 路由消息到相应的处理器
     * @param session WebSocket会话
     * @param message WebSocket消息
     */
    public void routeMessage(WebSocketSession session, WebSocketMessage message) {
        try {
            MessageType messageType = message.getType();
            WebSocketMessageHandler handler = handlers.get(messageType);

            if (handler != null) {
                handler.handleMessage(session, message);
            } else {
                log.warn("未找到消息处理器: type={}, sessionId={}", messageType, session.getId());
                sendErrorResponse(session, "不支持的消息类型: " + messageType);
            }

        } catch (Exception e) {
            log.error("消息路由失败: sessionId={}, messageType={}",
                session.getId(), message.getType(), e);
            sendErrorResponse(session, "消息处理失败");
        }
    }

    private void sendErrorResponse(WebSocketSession session, String errorMessage) {
        WebSocketMessage errorMsg = WebSocketMessage.builder()
            .type(MessageType.ERROR)
            .data(Map.of("error", errorMessage))
            .timestamp(LocalDateTime.now())
            .build();

        try {
            ObjectMapper mapper = new ObjectMapper();
            String payload = mapper.writeValueAsString(errorMsg);
            session.sendMessage(new TextMessage(payload));
        } catch (Exception e) {
            log.error("发送错误响应失败: sessionId={}", session.getId(), e);
        }
    }
}
```

## 数据模型定义

### 1. WebSocket相关实体

#### WebSocketMessage - WebSocket消息实体
```java
/**
 * WebSocket消息实体
 * 定义WebSocket通信的消息格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {

    /**
     * 消息类型
     */
    private MessageType type;

    /**
     * 消息数据
     */
    private Map<String, Object> data;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 发送者ID
     */
    private String senderId;

    /**
     * 接收者ID
     */
    private String receiverId;

    /**
     * 消息优先级
     */
    private MessagePriority priority;

    /**
     * 是否需要确认
     */
    private boolean requireAck;

    /**
     * 消息过期时间
     */
    private LocalDateTime expiresAt;
}
```

#### WebSocketSessionInfo - WebSocket会话信息
```java
/**
 * WebSocket会话信息
 * 存储WebSocket会话的详细信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketSessionInfo {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 连接时间
     */
    private LocalDateTime connectTime;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 会话状态
     */
    private SessionStatus status;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 会话属性
     */
    private Map<String, Object> attributes;
}
```

## 使用示例

### 基本WebSocket操作
```java
// 注入WebSocket处理器
@Autowired
private WebSocketHandler webSocketHandler;

// 发送消息到指定用户
WebSocketMessage message = WebSocketMessage.builder()
    .type(MessageType.NOTIFICATION)
    .data(Map.of(
        "title", "新消息",
        "content", "您有一条新的AI对话消息",
        "timestamp", LocalDateTime.now()
    ))
    .messageId(UUID.randomUUID().toString())
    .timestamp(LocalDateTime.now())
    .priority(MessagePriority.NORMAL)
    .build();

webSocketHandler.sendMessageToUser("user123", message);

// 广播系统通知
WebSocketMessage systemMessage = WebSocketMessage.builder()
    .type(MessageType.SYSTEM)
    .data(Map.of(
        "type", "maintenance",
        "message", "系统将于今晚进行维护",
        "startTime", LocalDateTime.now().plusHours(2)
    ))
    .messageId(UUID.randomUUID().toString())
    .timestamp(LocalDateTime.now())
    .priority(MessagePriority.HIGH)
    .build();

webSocketHandler.broadcastMessage(systemMessage);

// 检查用户在线状态
@Autowired
private WebSocketSessionManager sessionManager;

boolean isOnline = sessionManager.isUserOnline("user123");
int onlineCount = sessionManager.getOnlineUserCount();
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的WebSocket处理器实现，支持连接管理、消息路由、会话管理、心跳检测等功能
