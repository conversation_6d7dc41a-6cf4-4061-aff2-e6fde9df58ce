# 推送通知 (Push Notification)

## 模块概述

推送通知模块负责Ark-Pets AI Enhanced项目中移动端和桌面端的推送通知服务，包括消息推送、通知管理、设备注册、推送统计等功能。支持多平台推送（iOS、Android、Web、桌面），提供统一的推送接口和灵活的推送策略。

**核心职责**:
- 多平台推送通知服务
- 设备令牌管理和注册
- 推送消息模板和个性化
- 推送统计和效果分析
- 推送策略和时机控制

## 核心功能架构

### 1. 推送通知架构

#### 分层推送通知架构模型
```
┌─────────────────────────────────────┐
│           推送网关层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 消息队列  │ 路由分发  │ 限流控制  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           推送服务层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ iOS推送  │ Android推送│ Web推送 │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           管理存储层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 设备管理  │ 模板管理  │ 统计分析  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 推送通知流程

#### 推送通知处理流程图
```mermaid
graph TB
    subgraph "推送请求流程"
        PushRequest[推送请求]
        ValidateRequest[请求验证]
        GetDevices[获取设备列表]
        BuildMessage[构建推送消息]
        QueueMessage[消息入队]
        SendPush[发送推送]
        RecordResult[记录结果]
    end
    
    subgraph "设备管理流程"
        DeviceRegister[设备注册]
        TokenValidation[令牌验证]
        DeviceUpdate[设备信息更新]
        TokenRefresh[令牌刷新]
    end
    
    subgraph "推送分发流程"
        MessageQueue[消息队列]
        PlatformRouter[平台路由]
        APNSPush[APNS推送]
        FCMPush[FCM推送]
        WebPush[Web推送]
        DesktopPush[桌面推送]
    end
    
    PushRequest --> ValidateRequest
    ValidateRequest --> GetDevices
    GetDevices --> BuildMessage
    BuildMessage --> QueueMessage
    QueueMessage --> MessageQueue
    
    MessageQueue --> PlatformRouter
    PlatformRouter --> APNSPush
    PlatformRouter --> FCMPush
    PlatformRouter --> WebPush
    PlatformRouter --> DesktopPush
    
    APNSPush --> RecordResult
    FCMPush --> RecordResult
    WebPush --> RecordResult
    DesktopPush --> RecordResult
    
    DeviceRegister --> TokenValidation
    TokenValidation --> DeviceUpdate
    DeviceUpdate --> TokenRefresh
```

## 核心类和接口

### 1. 推送通知主服务

#### PushNotificationService - 推送通知主服务
```java
/**
 * 推送通知主服务
 * 负责推送通知的统一管理和分发
 */
@Service
@Slf4j
public class PushNotificationService {
    
    private final DeviceTokenRepository deviceTokenRepository;
    private final PushMessageRepository pushMessageRepository;
    private final PushTemplateRepository pushTemplateRepository;
    private final PushProviderFactory pushProviderFactory;
    private final PushMessageQueue pushMessageQueue;
    private final PushStatisticsService pushStatisticsService;
    private final UserPreferenceService userPreferenceService;
    
    /**
     * 发送推送通知
     * @param pushRequest 推送请求
     * @return 推送结果
     */
    public PushResult sendPushNotification(PushRequest pushRequest) {
        try {
            // 1. 验证推送请求
            validatePushRequest(pushRequest);
            
            // 2. 获取目标设备列表
            List<DeviceToken> targetDevices = getTargetDevices(pushRequest);
            
            if (targetDevices.isEmpty()) {
                return PushResult.noDevices();
            }
            
            // 3. 构建推送消息
            PushMessage pushMessage = buildPushMessage(pushRequest);
            
            // 4. 保存推送记录
            pushMessage = pushMessageRepository.save(pushMessage);
            
            // 5. 按平台分组设备
            Map<PushPlatform, List<DeviceToken>> devicesByPlatform = 
                targetDevices.stream().collect(Collectors.groupingBy(DeviceToken::getPlatform));
            
            // 6. 异步发送推送
            List<PlatformPushResult> platformResults = new ArrayList<>();
            for (Map.Entry<PushPlatform, List<DeviceToken>> entry : devicesByPlatform.entrySet()) {
                PlatformPushResult result = sendToPlatform(entry.getKey(), entry.getValue(), pushMessage);
                platformResults.add(result);
            }
            
            // 7. 汇总结果
            PushResult result = aggregatePushResults(pushMessage.getId(), platformResults);
            
            // 8. 更新统计
            pushStatisticsService.recordPushResult(result);
            
            log.info("推送通知发送完成: messageId={}, totalDevices={}, successCount={}", 
                pushMessage.getId(), targetDevices.size(), result.getSuccessCount());
            
            return result;
            
        } catch (Exception e) {
            log.error("发送推送通知失败: {}", pushRequest, e);
            throw new PushNotificationException("发送推送通知失败", e);
        }
    }
    
    /**
     * 发送模板推送
     * @param templateRequest 模板推送请求
     * @return 推送结果
     */
    public PushResult sendTemplatedPush(TemplatedPushRequest templateRequest) {
        try {
            // 1. 获取推送模板
            PushTemplate template = pushTemplateRepository.findById(templateRequest.getTemplateId())
                .orElseThrow(() -> new PushTemplateNotFoundException("推送模板不存在"));
            
            // 2. 渲染模板内容
            String title = renderTemplate(template.getTitleTemplate(), templateRequest.getVariables());
            String body = renderTemplate(template.getBodyTemplate(), templateRequest.getVariables());
            
            // 3. 构建推送请求
            PushRequest pushRequest = PushRequest.builder()
                .userIds(templateRequest.getUserIds())
                .title(title)
                .body(body)
                .data(templateRequest.getData())
                .priority(template.getPriority())
                .sound(template.getSound())
                .badge(templateRequest.getBadge())
                .build();
            
            // 4. 发送推送
            return sendPushNotification(pushRequest);
            
        } catch (Exception e) {
            log.error("发送模板推送失败: {}", templateRequest, e);
            throw new PushNotificationException("发送模板推送失败", e);
        }
    }
    
    /**
     * 注册设备令牌
     * @param deviceRegistration 设备注册信息
     * @return 注册结果
     */
    @Transactional
    public DeviceRegistrationResult registerDevice(DeviceRegistration deviceRegistration) {
        try {
            // 1. 验证设备信息
            validateDeviceRegistration(deviceRegistration);
            
            // 2. 检查是否已存在
            Optional<DeviceToken> existingDevice = deviceTokenRepository
                .findByUserIdAndDeviceId(deviceRegistration.getUserId(), deviceRegistration.getDeviceId());
            
            DeviceToken deviceToken;
            if (existingDevice.isPresent()) {
                // 更新现有设备
                deviceToken = existingDevice.get();
                updateDeviceToken(deviceToken, deviceRegistration);
            } else {
                // 创建新设备
                deviceToken = createDeviceToken(deviceRegistration);
            }
            
            // 3. 保存设备令牌
            deviceToken = deviceTokenRepository.save(deviceToken);
            
            // 4. 验证令牌有效性
            boolean isValid = validateTokenWithProvider(deviceToken);
            if (!isValid) {
                deviceToken.setStatus(TokenStatus.INVALID);
                deviceTokenRepository.save(deviceToken);
                return DeviceRegistrationResult.invalidToken();
            }
            
            log.info("设备令牌注册成功: userId={}, deviceId={}, platform={}", 
                deviceRegistration.getUserId(), deviceRegistration.getDeviceId(), deviceRegistration.getPlatform());
            
            return DeviceRegistrationResult.success(deviceToken.getId());
            
        } catch (Exception e) {
            log.error("注册设备令牌失败: {}", deviceRegistration, e);
            throw new DeviceRegistrationException("注册设备令牌失败", e);
        }
    }
    
    /**
     * 注销设备令牌
     * @param userId 用户ID
     * @param deviceId 设备ID
     */
    @Transactional
    public void unregisterDevice(String userId, String deviceId) {
        try {
            Optional<DeviceToken> deviceToken = deviceTokenRepository
                .findByUserIdAndDeviceId(userId, deviceId);
            
            if (deviceToken.isPresent()) {
                DeviceToken token = deviceToken.get();
                token.setStatus(TokenStatus.UNREGISTERED);
                token.setUnregisteredAt(LocalDateTime.now());
                deviceTokenRepository.save(token);
                
                log.info("设备令牌注销成功: userId={}, deviceId={}", userId, deviceId);
            }
            
        } catch (Exception e) {
            log.error("注销设备令牌失败: userId={}, deviceId={}", userId, deviceId, e);
            throw new DeviceUnregistrationException("注销设备令牌失败", e);
        }
    }
    
    /**
     * 获取用户推送统计
     * @param userId 用户ID
     * @param timeRange 时间范围
     * @return 推送统计
     */
    public UserPushStatistics getUserPushStatistics(String userId, TimeRange timeRange) {
        try {
            return pushStatisticsService.getUserPushStatistics(userId, timeRange);
        } catch (Exception e) {
            log.error("获取用户推送统计失败: userId={}", userId, e);
            throw new PushStatisticsException("获取用户推送统计失败", e);
        }
    }
    
    /**
     * 批量发送推送通知
     * @param batchRequest 批量推送请求
     * @return 批量推送结果
     */
    @Async
    public CompletableFuture<BatchPushResult> sendBatchPushNotification(BatchPushRequest batchRequest) {
        try {
            List<PushResult> results = new ArrayList<>();
            
            for (PushRequest pushRequest : batchRequest.getPushRequests()) {
                try {
                    PushResult result = sendPushNotification(pushRequest);
                    results.add(result);
                } catch (Exception e) {
                    log.error("批量推送中的单个推送失败: {}", pushRequest, e);
                    results.add(PushResult.failed(e.getMessage()));
                }
            }
            
            BatchPushResult batchResult = BatchPushResult.builder()
                .batchId(batchRequest.getBatchId())
                .totalRequests(batchRequest.getPushRequests().size())
                .results(results)
                .completedAt(LocalDateTime.now())
                .build();
            
            return CompletableFuture.completedFuture(batchResult);
            
        } catch (Exception e) {
            log.error("批量推送失败: {}", batchRequest, e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    private void validatePushRequest(PushRequest request) {
        if (request.getUserIds() == null || request.getUserIds().isEmpty()) {
            throw new IllegalArgumentException("目标用户列表不能为空");
        }
        if (StringUtils.isBlank(request.getTitle()) && StringUtils.isBlank(request.getBody())) {
            throw new IllegalArgumentException("推送标题和内容不能同时为空");
        }
    }
    
    private List<DeviceToken> getTargetDevices(PushRequest request) {
        List<DeviceToken> allDevices = new ArrayList<>();
        
        for (String userId : request.getUserIds()) {
            // 检查用户推送偏好
            if (!userPreferenceService.isPushNotificationEnabled(userId)) {
                continue;
            }
            
            List<DeviceToken> userDevices = deviceTokenRepository
                .findActiveDevicesByUserId(userId);
            allDevices.addAll(userDevices);
        }
        
        return allDevices;
    }
    
    private PushMessage buildPushMessage(PushRequest request) {
        return PushMessage.builder()
            .title(request.getTitle())
            .body(request.getBody())
            .data(request.getData())
            .priority(request.getPriority())
            .sound(request.getSound())
            .badge(request.getBadge())
            .createdAt(LocalDateTime.now())
            .status(PushStatus.PENDING)
            .build();
    }
    
    private PlatformPushResult sendToPlatform(PushPlatform platform, 
                                            List<DeviceToken> devices, 
                                            PushMessage message) {
        try {
            PushProvider provider = pushProviderFactory.getProvider(platform);
            return provider.sendPush(devices, message);
        } catch (Exception e) {
            log.error("平台推送失败: platform={}", platform, e);
            return PlatformPushResult.failed(platform, e.getMessage());
        }
    }
    
    private PushResult aggregatePushResults(Long messageId, List<PlatformPushResult> platformResults) {
        int totalDevices = platformResults.stream()
            .mapToInt(PlatformPushResult::getTotalDevices)
            .sum();
        
        int successCount = platformResults.stream()
            .mapToInt(PlatformPushResult::getSuccessCount)
            .sum();
        
        int failureCount = totalDevices - successCount;
        
        return PushResult.builder()
            .messageId(messageId)
            .totalDevices(totalDevices)
            .successCount(successCount)
            .failureCount(failureCount)
            .platformResults(platformResults)
            .completedAt(LocalDateTime.now())
            .build();
    }

    private String renderTemplate(String template, Map<String, Object> variables) {
        // 简单的模板渲染实现
        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            result = result.replace(placeholder, String.valueOf(entry.getValue()));
        }
        return result;
    }

    private void validateDeviceRegistration(DeviceRegistration registration) {
        if (StringUtils.isBlank(registration.getUserId())) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(registration.getDeviceId())) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        if (StringUtils.isBlank(registration.getToken())) {
            throw new IllegalArgumentException("设备令牌不能为空");
        }
        if (registration.getPlatform() == null) {
            throw new IllegalArgumentException("设备平台不能为空");
        }
    }

    private DeviceToken createDeviceToken(DeviceRegistration registration) {
        return DeviceToken.builder()
            .userId(registration.getUserId())
            .deviceId(registration.getDeviceId())
            .token(registration.getToken())
            .platform(registration.getPlatform())
            .deviceModel(registration.getDeviceModel())
            .osVersion(registration.getOsVersion())
            .appVersion(registration.getAppVersion())
            .status(TokenStatus.ACTIVE)
            .registeredAt(LocalDateTime.now())
            .lastUsedAt(LocalDateTime.now())
            .build();
    }

    private void updateDeviceToken(DeviceToken deviceToken, DeviceRegistration registration) {
        deviceToken.setToken(registration.getToken());
        deviceToken.setDeviceModel(registration.getDeviceModel());
        deviceToken.setOsVersion(registration.getOsVersion());
        deviceToken.setAppVersion(registration.getAppVersion());
        deviceToken.setStatus(TokenStatus.ACTIVE);
        deviceToken.setLastUsedAt(LocalDateTime.now());
    }

    private boolean validateTokenWithProvider(DeviceToken deviceToken) {
        try {
            PushProvider provider = pushProviderFactory.getProvider(deviceToken.getPlatform());
            return provider.validateToken(deviceToken.getToken());
        } catch (Exception e) {
            log.warn("验证设备令牌失败: deviceId={}", deviceToken.getDeviceId(), e);
            return false;
        }
    }
}
```

### 2. 推送提供商工厂

#### PushProviderFactory - 推送提供商工厂
```java
/**
 * 推送提供商工厂
 * 负责根据平台创建相应的推送提供商
 */
@Component
public class PushProviderFactory {

    private final Map<PushPlatform, PushProvider> providers = new HashMap<>();

    @Autowired
    public PushProviderFactory(APNSPushProvider apnsProvider,
                              FCMPushProvider fcmProvider,
                              WebPushProvider webProvider,
                              DesktopPushProvider desktopProvider) {
        providers.put(PushPlatform.IOS, apnsProvider);
        providers.put(PushPlatform.ANDROID, fcmProvider);
        providers.put(PushPlatform.WEB, webProvider);
        providers.put(PushPlatform.DESKTOP, desktopProvider);
    }

    /**
     * 获取推送提供商
     * @param platform 推送平台
     * @return 推送提供商
     */
    public PushProvider getProvider(PushPlatform platform) {
        PushProvider provider = providers.get(platform);
        if (provider == null) {
            throw new UnsupportedPlatformException("不支持的推送平台: " + platform);
        }
        return provider;
    }
}
```

### 3. APNS推送提供商

#### APNSPushProvider - Apple推送服务提供商
```java
/**
 * Apple推送服务提供商
 * 负责iOS设备的推送通知
 */
@Component
@Slf4j
public class APNSPushProvider implements PushProvider {

    private final ApnsClient apnsClient;
    private final APNSConfig apnsConfig;

    @Override
    public PlatformPushResult sendPush(List<DeviceToken> devices, PushMessage message) {
        List<PushDeliveryResult> deliveryResults = new ArrayList<>();

        for (DeviceToken device : devices) {
            try {
                PushDeliveryResult result = sendSinglePush(device, message);
                deliveryResults.add(result);
            } catch (Exception e) {
                log.error("APNS推送失败: deviceId={}", device.getDeviceId(), e);
                deliveryResults.add(PushDeliveryResult.failed(device.getId(), e.getMessage()));
            }
        }

        return PlatformPushResult.builder()
            .platform(PushPlatform.IOS)
            .totalDevices(devices.size())
            .successCount((int) deliveryResults.stream().filter(PushDeliveryResult::isSuccess).count())
            .failureCount((int) deliveryResults.stream().filter(r -> !r.isSuccess()).count())
            .deliveryResults(deliveryResults)
            .build();
    }

    @Override
    public boolean validateToken(String token) {
        try {
            // 简单的令牌格式验证
            return token != null && token.length() == 64 && token.matches("[0-9a-fA-F]+");
        } catch (Exception e) {
            return false;
        }
    }

    private PushDeliveryResult sendSinglePush(DeviceToken device, PushMessage message) {
        try {
            // 构建APNS推送负载
            ApnsPayloadBuilder payloadBuilder = new ApnsPayloadBuilder();
            payloadBuilder.setAlertTitle(message.getTitle());
            payloadBuilder.setAlertBody(message.getBody());
            payloadBuilder.setBadge(message.getBadge());
            payloadBuilder.setSound(message.getSound());

            // 添加自定义数据
            if (message.getData() != null) {
                for (Map.Entry<String, Object> entry : message.getData().entrySet()) {
                    payloadBuilder.addCustomProperty(entry.getKey(), entry.getValue());
                }
            }

            String payload = payloadBuilder.build();

            // 发送推送
            SimpleApnsPushNotification pushNotification =
                new SimpleApnsPushNotification(device.getToken(), apnsConfig.getTopic(), payload);

            PushNotificationFuture<SimpleApnsPushNotification, PushNotificationResponse<SimpleApnsPushNotification>>
                sendNotificationFuture = apnsClient.sendNotification(pushNotification);

            PushNotificationResponse<SimpleApnsPushNotification> response = sendNotificationFuture.get();

            if (response.isAccepted()) {
                return PushDeliveryResult.success(device.getId());
            } else {
                return PushDeliveryResult.failed(device.getId(), response.getRejectionReason());
            }

        } catch (Exception e) {
            log.error("APNS单个推送失败: deviceId={}", device.getDeviceId(), e);
            return PushDeliveryResult.failed(device.getId(), e.getMessage());
        }
    }
}
```

## 数据模型定义

### 1. 推送相关实体

#### DeviceToken - 设备令牌实体
```java
/**
 * 设备令牌实体
 * 存储用户设备的推送令牌信息
 */
@Entity
@Table(name = "device_tokens")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String userId;

    @Column(nullable = false)
    private String deviceId;

    @Column(nullable = false, length = 500)
    private String token;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PushPlatform platform;

    @Column(length = 100)
    private String deviceModel;

    @Column(length = 50)
    private String osVersion;

    @Column(length = 50)
    private String appVersion;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TokenStatus status;

    @Column(nullable = false)
    private LocalDateTime registeredAt;

    @Column(nullable = false)
    private LocalDateTime lastUsedAt;

    private LocalDateTime unregisteredAt;

    @PrePersist
    protected void onCreate() {
        registeredAt = LocalDateTime.now();
        lastUsedAt = LocalDateTime.now();
    }
}
```

#### PushMessage - 推送消息实体
```java
/**
 * 推送消息实体
 * 存储推送消息的详细信息
 */
@Entity
@Table(name = "push_messages")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 200)
    private String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String body;

    @Column(columnDefinition = "JSON")
    private Map<String, Object> data;

    @Enumerated(EnumType.STRING)
    private PushPriority priority;

    @Column(length = 50)
    private String sound;

    private Integer badge;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PushStatus status;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    private LocalDateTime sentAt;

    private Integer totalDevices;
    private Integer successCount;
    private Integer failureCount;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本推送通知操作
```java
// 注入推送通知服务
@Autowired
private PushNotificationService pushNotificationService;

// 发送简单推送通知
PushRequest pushRequest = PushRequest.builder()
    .userIds(Arrays.asList("user123", "user456"))
    .title("新消息")
    .body("您有一条新的AI对话消息")
    .data(Map.of(
        "type", "chat_message",
        "conversationId", "conv789",
        "messageId", "msg123"
    ))
    .priority(PushPriority.NORMAL)
    .sound("default")
    .badge(1)
    .build();

PushResult result = pushNotificationService.sendPushNotification(pushRequest);
System.out.println("推送结果: 成功=" + result.getSuccessCount() + ", 失败=" + result.getFailureCount());

// 注册设备令牌
DeviceRegistration registration = DeviceRegistration.builder()
    .userId("user123")
    .deviceId("device456")
    .token("abcd1234efgh5678...")
    .platform(PushPlatform.IOS)
    .deviceModel("iPhone 13")
    .osVersion("15.0")
    .appVersion("1.0.0")
    .build();

DeviceRegistrationResult regResult = pushNotificationService.registerDevice(registration);
if (regResult.isSuccess()) {
    System.out.println("设备注册成功: " + regResult.getDeviceTokenId());
}

// 发送模板推送
TemplatedPushRequest templateRequest = TemplatedPushRequest.builder()
    .templateId(1L)
    .userIds(Arrays.asList("user123"))
    .variables(Map.of(
        "username", "小明",
        "petName", "阿米娅",
        "action", "想和你聊天"
    ))
    .data(Map.of("type", "pet_interaction"))
    .badge(1)
    .build();

PushResult templateResult = pushNotificationService.sendTemplatedPush(templateRequest);
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的推送通知服务实现，支持多平台推送、设备管理、模板推送、统计分析等功能
