# 社交功能 (Social Features)

## 模块概述

社交功能模块负责Ark-Pets AI Enhanced项目中用户间的社交互动，包括好友关系管理、群组功能、消息系统、动态分享、社区互动等功能。提供完整的社交网络体验，支持隐私控制、内容审核、实时通信等特性。

**核心职责**:
- 好友关系管理和维护
- 群组创建和成员管理
- 私信和群聊功能
- 动态发布和互动
- 社区内容管理

## 核心功能架构

### 1. 社交功能管理架构

#### 分层社交管理模型
```
┌─────────────────────────────────────┐
│           社交功能管理               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 好友管理  │ 群组管理  │ 消息系统  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           互动处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 动态管理  │ 内容审核  │ 通知推送  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据存储层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 关系数据  │ 消息数据  │ 内容数据  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 社交互动流程

#### 好友关系管理流程图
```mermaid
graph TB
    subgraph "好友关系流程"
        FriendRequest[好友请求]
        ValidateUser[验证用户]
        CheckRelation[检查关系]
        SendRequest[发送请求]
        NotifyTarget[通知目标用户]
        AcceptReject[接受/拒绝]
        UpdateRelation[更新关系]
        NotifyResult[通知结果]
    end
    
    subgraph "群组管理流程"
        CreateGroup[创建群组]
        InviteMembers[邀请成员]
        ManagePermissions[权限管理]
        GroupChat[群组聊天]
    end
    
    subgraph "动态分享流程"
        CreatePost[创建动态]
        ContentReview[内容审核]
        PublishPost[发布动态]
        NotifyFollowers[通知关注者]
        HandleInteraction[处理互动]
    end
    
    FriendRequest --> ValidateUser
    ValidateUser --> CheckRelation
    CheckRelation --> SendRequest
    SendRequest --> NotifyTarget
    NotifyTarget --> AcceptReject
    AcceptReject --> UpdateRelation
    UpdateRelation --> NotifyResult
    
    CreateGroup --> InviteMembers
    InviteMembers --> ManagePermissions
    ManagePermissions --> GroupChat
    
    CreatePost --> ContentReview
    ContentReview --> PublishPost
    PublishPost --> NotifyFollowers
    NotifyFollowers --> HandleInteraction
```

## 核心类和接口

### 1. 好友关系管理服务

#### FriendshipService - 好友关系主服务
```java
/**
 * 好友关系管理主服务
 * 负责用户间好友关系的完整生命周期管理
 */
@Service
@Slf4j
@Transactional
public class FriendshipService {
    
    private final FriendshipRepository friendshipRepository;
    private final FriendRequestRepository friendRequestRepository;
    private final UserRepository userRepository;
    private final NotificationService notificationService;
    private final SecurityService securityService;
    private final EventPublisher eventPublisher;
    
    /**
     * 发送好友请求
     * @param fromUserId 发送者ID
     * @param toUserId 接收者ID
     * @param message 请求消息
     * @return 好友请求信息
     */
    public FriendRequestResponse sendFriendRequest(String fromUserId, String toUserId, String message) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(fromUserId);
            
            // 2. 验证目标用户存在
            User targetUser = userRepository.findById(toUserId)
                .orElseThrow(() -> new UserNotFoundException("目标用户不存在: " + toUserId));
            
            // 3. 检查是否已经是好友
            if (areFriends(fromUserId, toUserId)) {
                throw new AlreadyFriendsException("用户已经是好友关系");
            }
            
            // 4. 检查是否已有待处理的请求
            Optional<FriendRequest> existingRequest = friendRequestRepository
                .findPendingRequest(fromUserId, toUserId);
            if (existingRequest.isPresent()) {
                throw new DuplicateFriendRequestException("已存在待处理的好友请求");
            }
            
            // 5. 检查目标用户的隐私设置
            if (!canSendFriendRequest(fromUserId, toUserId)) {
                throw new FriendRequestBlockedException("无法向该用户发送好友请求");
            }
            
            // 6. 创建好友请求
            FriendRequest friendRequest = FriendRequest.builder()
                .fromUserId(fromUserId)
                .toUserId(toUserId)
                .message(message)
                .status(FriendRequestStatus.PENDING)
                .createdAt(LocalDateTime.now())
                .build();
            
            friendRequest = friendRequestRepository.save(friendRequest);
            
            // 7. 发送通知
            notificationService.sendFriendRequestNotification(toUserId, fromUserId, message);
            
            // 8. 发布事件
            eventPublisher.publishFriendRequestSent(fromUserId, toUserId, friendRequest.getId());
            
            log.info("好友请求发送成功: from={}, to={}", fromUserId, toUserId);
            
            return buildFriendRequestResponse(friendRequest);
            
        } catch (Exception e) {
            log.error("发送好友请求失败: from={}, to={}", fromUserId, toUserId, e);
            throw new FriendRequestException("发送好友请求失败", e);
        }
    }
    
    /**
     * 处理好友请求
     * @param requestId 请求ID
     * @param userId 处理者ID
     * @param action 处理动作（接受/拒绝）
     * @return 处理结果
     */
    public FriendRequestResponse handleFriendRequest(Long requestId, String userId, FriendRequestAction action) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取好友请求
            FriendRequest friendRequest = friendRequestRepository.findById(requestId)
                .orElseThrow(() -> new FriendRequestNotFoundException("好友请求不存在: " + requestId));
            
            // 3. 验证处理权限
            if (!friendRequest.getToUserId().equals(userId)) {
                throw new UnauthorizedFriendRequestException("无权限处理该好友请求");
            }
            
            // 4. 检查请求状态
            if (friendRequest.getStatus() != FriendRequestStatus.PENDING) {
                throw new InvalidFriendRequestStatusException("请求已被处理: " + friendRequest.getStatus());
            }
            
            // 5. 处理请求
            if (action == FriendRequestAction.ACCEPT) {
                acceptFriendRequest(friendRequest);
            } else if (action == FriendRequestAction.REJECT) {
                rejectFriendRequest(friendRequest);
            }
            
            // 6. 更新请求状态
            friendRequest.setStatus(action == FriendRequestAction.ACCEPT ? 
                FriendRequestStatus.ACCEPTED : FriendRequestStatus.REJECTED);
            friendRequest.setHandledAt(LocalDateTime.now());
            friendRequest = friendRequestRepository.save(friendRequest);
            
            // 7. 发送通知
            notificationService.sendFriendRequestResultNotification(
                friendRequest.getFromUserId(), userId, action);
            
            // 8. 发布事件
            eventPublisher.publishFriendRequestHandled(
                friendRequest.getFromUserId(), userId, requestId, action);
            
            log.info("好友请求处理成功: requestId={}, action={}", requestId, action);
            
            return buildFriendRequestResponse(friendRequest);
            
        } catch (Exception e) {
            log.error("处理好友请求失败: requestId={}, action={}", requestId, action, e);
            throw new FriendRequestException("处理好友请求失败", e);
        }
    }
    
    /**
     * 删除好友关系
     * @param userId 用户ID
     * @param friendId 好友ID
     */
    public void removeFriend(String userId, String friendId) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 检查好友关系
            if (!areFriends(userId, friendId)) {
                throw new NotFriendsException("用户不是好友关系");
            }
            
            // 3. 删除好友关系
            friendshipRepository.deleteFriendship(userId, friendId);
            
            // 4. 发送通知
            notificationService.sendFriendRemovedNotification(friendId, userId);
            
            // 5. 发布事件
            eventPublisher.publishFriendRemoved(userId, friendId);
            
            log.info("好友关系删除成功: userId={}, friendId={}", userId, friendId);
            
        } catch (Exception e) {
            log.error("删除好友关系失败: userId={}, friendId={}", userId, friendId, e);
            throw new FriendshipException("删除好友关系失败", e);
        }
    }
    
    /**
     * 获取好友列表
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 好友列表
     */
    @Transactional(readOnly = true)
    public Page<FriendInfo> getFriendsList(String userId, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取好友关系
            Page<Friendship> friendships = friendshipRepository.findFriendshipsByUserId(userId, pageable);
            
            // 3. 构建好友信息
            return friendships.map(friendship -> {
                String friendId = friendship.getFriendId(userId);
                User friend = userRepository.findById(friendId).orElse(null);
                return buildFriendInfo(friendship, friend);
            });
            
        } catch (Exception e) {
            log.error("获取好友列表失败: userId={}", userId, e);
            throw new FriendshipException("获取好友列表失败", e);
        }
    }
    
    /**
     * 获取待处理的好友请求
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 好友请求列表
     */
    @Transactional(readOnly = true)
    public Page<FriendRequestResponse> getPendingFriendRequests(String userId, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取待处理请求
            Page<FriendRequest> requests = friendRequestRepository
                .findPendingRequestsByToUserId(userId, pageable);
            
            // 3. 构建响应对象
            return requests.map(this::buildFriendRequestResponse);
            
        } catch (Exception e) {
            log.error("获取待处理好友请求失败: userId={}", userId, e);
            throw new FriendRequestException("获取待处理好友请求失败", e);
        }
    }
    
    /**
     * 搜索用户
     * @param userId 搜索者ID
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 用户搜索结果
     */
    @Transactional(readOnly = true)
    public Page<UserSearchResult> searchUsers(String userId, String keyword, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 搜索用户
            Page<User> users = userRepository.searchUsers(keyword, pageable);
            
            // 3. 构建搜索结果
            return users.map(user -> {
                FriendshipStatus status = getFriendshipStatus(userId, user.getId());
                return buildUserSearchResult(user, status);
            });
            
        } catch (Exception e) {
            log.error("搜索用户失败: userId={}, keyword={}", userId, keyword, e);
            throw new UserSearchException("搜索用户失败", e);
        }
    }
    
    /**
     * 检查是否为好友关系
     * @param userId1 用户1 ID
     * @param userId2 用户2 ID
     * @return 是否为好友
     */
    @Transactional(readOnly = true)
    public boolean areFriends(String userId1, String userId2) {
        return friendshipRepository.existsFriendship(userId1, userId2);
    }
    
    /**
     * 获取好友关系状态
     * @param userId 用户ID
     * @param targetUserId 目标用户ID
     * @return 好友关系状态
     */
    @Transactional(readOnly = true)
    public FriendshipStatus getFriendshipStatus(String userId, String targetUserId) {
        if (userId.equals(targetUserId)) {
            return FriendshipStatus.SELF;
        }
        
        if (areFriends(userId, targetUserId)) {
            return FriendshipStatus.FRIENDS;
        }
        
        Optional<FriendRequest> pendingRequest = friendRequestRepository
            .findPendingRequest(userId, targetUserId);
        if (pendingRequest.isPresent()) {
            return FriendshipStatus.REQUEST_SENT;
        }
        
        Optional<FriendRequest> receivedRequest = friendRequestRepository
            .findPendingRequest(targetUserId, userId);
        if (receivedRequest.isPresent()) {
            return FriendshipStatus.REQUEST_RECEIVED;
        }
        
        return FriendshipStatus.NOT_FRIENDS;
    }
    
    /**
     * 获取共同好友
     * @param userId 用户ID
     * @param targetUserId 目标用户ID
     * @param pageable 分页参数
     * @return 共同好友列表
     */
    @Transactional(readOnly = true)
    public Page<FriendInfo> getMutualFriends(String userId, String targetUserId, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取共同好友
            Page<User> mutualFriends = friendshipRepository
                .findMutualFriends(userId, targetUserId, pageable);
            
            // 3. 构建好友信息
            return mutualFriends.map(friend -> {
                Friendship friendship = friendshipRepository
                    .findFriendship(userId, friend.getId()).orElse(null);
                return buildFriendInfo(friendship, friend);
            });
            
        } catch (Exception e) {
            log.error("获取共同好友失败: userId={}, targetUserId={}", userId, targetUserId, e);
            throw new FriendshipException("获取共同好友失败", e);
        }
    }
    
    private void acceptFriendRequest(FriendRequest friendRequest) {
        // 创建双向好友关系
        Friendship friendship1 = Friendship.builder()
            .userId(friendRequest.getFromUserId())
            .friendId(friendRequest.getToUserId())
            .createdAt(LocalDateTime.now())
            .build();
            
        Friendship friendship2 = Friendship.builder()
            .userId(friendRequest.getToUserId())
            .friendId(friendRequest.getFromUserId())
            .createdAt(LocalDateTime.now())
            .build();
            
        friendshipRepository.save(friendship1);
        friendshipRepository.save(friendship2);
    }
    
    private void rejectFriendRequest(FriendRequest friendRequest) {
        // 拒绝请求，不需要额外操作
        log.info("好友请求被拒绝: from={}, to={}", 
            friendRequest.getFromUserId(), friendRequest.getToUserId());
    }
    
    private boolean canSendFriendRequest(String fromUserId, String toUserId) {
        // 检查目标用户的隐私设置
        // 这里可以添加更复杂的隐私检查逻辑
        return true;
    }
    
    private FriendRequestResponse buildFriendRequestResponse(FriendRequest request) {
        User fromUser = userRepository.findById(request.getFromUserId()).orElse(null);
        User toUser = userRepository.findById(request.getToUserId()).orElse(null);
        
        return FriendRequestResponse.builder()
            .id(request.getId())
            .fromUser(fromUser != null ? buildUserSummary(fromUser) : null)
            .toUser(toUser != null ? buildUserSummary(toUser) : null)
            .message(request.getMessage())
            .status(request.getStatus())
            .createdAt(request.getCreatedAt())
            .handledAt(request.getHandledAt())
            .build();
    }
    
    private FriendInfo buildFriendInfo(Friendship friendship, User friend) {
        if (friend == null) {
            return null;
        }
        
        return FriendInfo.builder()
            .userId(friend.getId())
            .username(friend.getUsername())
            .nickname(friend.getNickname())
            .avatarUrl(friend.getAvatarUrl())
            .friendsSince(friendship != null ? friendship.getCreatedAt() : null)
            .isOnline(friend.isOnline())
            .lastActiveAt(friend.getLastActiveAt())
            .build();
    }
    
    private UserSearchResult buildUserSearchResult(User user, FriendshipStatus status) {
        return UserSearchResult.builder()
            .userId(user.getId())
            .username(user.getUsername())
            .nickname(user.getNickname())
            .avatarUrl(user.getAvatarUrl())
            .friendshipStatus(status)
            .build();
    }
    
    private UserSummary buildUserSummary(User user) {
        return UserSummary.builder()
            .userId(user.getId())
            .username(user.getUsername())
            .nickname(user.getNickname())
            .avatarUrl(user.getAvatarUrl())
            .build();
    }
}
```

### 2. 群组管理服务

#### GroupService - 群组管理服务
```java
/**
 * 群组管理服务
 * 负责群组的创建、管理和成员维护
 */
@Service
@Slf4j
@Transactional
public class GroupService {

    private final GroupRepository groupRepository;
    private final GroupMemberRepository groupMemberRepository;
    private final UserRepository userRepository;
    private final NotificationService notificationService;
    private final SecurityService securityService;
    private final EventPublisher eventPublisher;

    /**
     * 创建群组
     * @param creatorId 创建者ID
     * @param createRequest 创建请求
     * @return 群组信息
     */
    public GroupResponse createGroup(String creatorId, CreateGroupRequest createRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(creatorId);

            // 2. 验证创建请求
            validateCreateGroupRequest(createRequest);

            // 3. 创建群组
            Group group = Group.builder()
                .name(createRequest.getName())
                .description(createRequest.getDescription())
                .type(createRequest.getType())
                .privacy(createRequest.getPrivacy())
                .maxMembers(createRequest.getMaxMembers())
                .creatorId(creatorId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

            group = groupRepository.save(group);

            // 4. 添加创建者为管理员
            GroupMember creatorMember = GroupMember.builder()
                .groupId(group.getId())
                .userId(creatorId)
                .role(GroupRole.ADMIN)
                .joinedAt(LocalDateTime.now())
                .build();

            groupMemberRepository.save(creatorMember);

            // 5. 邀请初始成员
            if (createRequest.getInitialMembers() != null && !createRequest.getInitialMembers().isEmpty()) {
                inviteInitialMembers(group.getId(), creatorId, createRequest.getInitialMembers());
            }

            // 6. 发布事件
            eventPublisher.publishGroupCreated(group.getId(), creatorId);

            log.info("群组创建成功: groupId={}, creatorId={}", group.getId(), creatorId);

            return buildGroupResponse(group);

        } catch (Exception e) {
            log.error("创建群组失败: creatorId={}", creatorId, e);
            throw new GroupCreationException("创建群组失败", e);
        }
    }

    /**
     * 邀请用户加入群组
     * @param groupId 群组ID
     * @param inviterId 邀请者ID
     * @param userIds 被邀请用户ID列表
     * @return 邀请结果
     */
    public GroupInvitationResult inviteMembers(Long groupId, String inviterId, List<String> userIds) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(inviterId);

            // 2. 验证群组存在
            Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> new GroupNotFoundException("群组不存在: " + groupId));

            // 3. 验证邀请权限
            if (!canInviteMembers(groupId, inviterId)) {
                throw new InsufficientGroupPermissionException("无权限邀请成员");
            }

            // 4. 检查群组成员限制
            long currentMemberCount = groupMemberRepository.countByGroupId(groupId);
            if (currentMemberCount + userIds.size() > group.getMaxMembers()) {
                throw new GroupMemberLimitExceededException("超出群组成员限制");
            }

            // 5. 处理邀请
            List<String> successfulInvites = new ArrayList<>();
            List<String> failedInvites = new ArrayList<>();

            for (String userId : userIds) {
                try {
                    inviteSingleMember(groupId, inviterId, userId);
                    successfulInvites.add(userId);
                } catch (Exception e) {
                    log.warn("邀请用户失败: groupId={}, userId={}", groupId, userId, e);
                    failedInvites.add(userId);
                }
            }

            // 6. 发布事件
            eventPublisher.publishMembersInvited(groupId, inviterId, successfulInvites);

            log.info("群组邀请完成: groupId={}, successful={}, failed={}",
                groupId, successfulInvites.size(), failedInvites.size());

            return GroupInvitationResult.builder()
                .groupId(groupId)
                .successfulInvites(successfulInvites)
                .failedInvites(failedInvites)
                .build();

        } catch (Exception e) {
            log.error("邀请群组成员失败: groupId={}, inviterId={}", groupId, inviterId, e);
            throw new GroupInvitationException("邀请群组成员失败", e);
        }
    }

    /**
     * 移除群组成员
     * @param groupId 群组ID
     * @param operatorId 操作者ID
     * @param userId 被移除用户ID
     */
    public void removeMember(Long groupId, String operatorId, String userId) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(operatorId);

            // 2. 验证操作权限
            if (!canRemoveMembers(groupId, operatorId)) {
                throw new InsufficientGroupPermissionException("无权限移除成员");
            }

            // 3. 检查被移除用户是否为群组成员
            GroupMember member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId)
                .orElseThrow(() -> new GroupMemberNotFoundException("用户不是群组成员"));

            // 4. 检查是否可以移除（不能移除创建者）
            Group group = groupRepository.findById(groupId).orElseThrow();
            if (group.getCreatorId().equals(userId)) {
                throw new CannotRemoveGroupCreatorException("不能移除群组创建者");
            }

            // 5. 移除成员
            groupMemberRepository.delete(member);

            // 6. 发送通知
            notificationService.sendGroupMemberRemovedNotification(userId, groupId, operatorId);

            // 7. 发布事件
            eventPublisher.publishMemberRemoved(groupId, operatorId, userId);

            log.info("群组成员移除成功: groupId={}, userId={}, operatorId={}", groupId, userId, operatorId);

        } catch (Exception e) {
            log.error("移除群组成员失败: groupId={}, userId={}, operatorId={}", groupId, userId, operatorId, e);
            throw new GroupMemberRemovalException("移除群组成员失败", e);
        }
    }

    /**
     * 退出群组
     * @param groupId 群组ID
     * @param userId 用户ID
     */
    public void leaveGroup(Long groupId, String userId) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);

            // 2. 检查用户是否为群组成员
            GroupMember member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId)
                .orElseThrow(() -> new GroupMemberNotFoundException("用户不是群组成员"));

            // 3. 检查是否为群组创建者
            Group group = groupRepository.findById(groupId).orElseThrow();
            if (group.getCreatorId().equals(userId)) {
                // 创建者退出需要转移群组所有权或解散群组
                handleCreatorLeaving(group, userId);
            } else {
                // 普通成员直接退出
                groupMemberRepository.delete(member);
            }

            // 4. 发送通知
            notificationService.sendGroupMemberLeftNotification(groupId, userId);

            // 5. 发布事件
            eventPublisher.publishMemberLeft(groupId, userId);

            log.info("用户退出群组成功: groupId={}, userId={}", groupId, userId);

        } catch (Exception e) {
            log.error("用户退出群组失败: groupId={}, userId={}", groupId, userId, e);
            throw new GroupLeaveException("用户退出群组失败", e);
        }
    }

    /**
     * 获取群组信息
     * @param groupId 群组ID
     * @param userId 请求用户ID
     * @return 群组信息
     */
    @Transactional(readOnly = true)
    public GroupResponse getGroupInfo(Long groupId, String userId) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);

            // 2. 获取群组信息
            Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> new GroupNotFoundException("群组不存在: " + groupId));

            // 3. 检查访问权限
            if (!canAccessGroup(groupId, userId)) {
                throw new GroupAccessDeniedException("无权限访问群组信息");
            }

            // 4. 构建响应
            return buildGroupResponse(group);

        } catch (Exception e) {
            log.error("获取群组信息失败: groupId={}, userId={}", groupId, userId, e);
            throw new GroupAccessException("获取群组信息失败", e);
        }
    }

    /**
     * 获取群组成员列表
     * @param groupId 群组ID
     * @param userId 请求用户ID
     * @param pageable 分页参数
     * @return 成员列表
     */
    @Transactional(readOnly = true)
    public Page<GroupMemberInfo> getGroupMembers(Long groupId, String userId, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);

            // 2. 检查访问权限
            if (!canAccessGroup(groupId, userId)) {
                throw new GroupAccessDeniedException("无权限访问群组成员");
            }

            // 3. 获取成员列表
            Page<GroupMember> members = groupMemberRepository.findByGroupId(groupId, pageable);

            // 4. 构建成员信息
            return members.map(member -> {
                User user = userRepository.findById(member.getUserId()).orElse(null);
                return buildGroupMemberInfo(member, user);
            });

        } catch (Exception e) {
            log.error("获取群组成员列表失败: groupId={}, userId={}", groupId, userId, e);
            throw new GroupAccessException("获取群组成员列表失败", e);
        }
    }

    /**
     * 搜索群组
     * @param userId 搜索者ID
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 群组搜索结果
     */
    @Transactional(readOnly = true)
    public Page<GroupSearchResult> searchGroups(String userId, String keyword, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);

            // 2. 搜索公开群组
            Page<Group> groups = groupRepository.searchPublicGroups(keyword, pageable);

            // 3. 构建搜索结果
            return groups.map(group -> {
                boolean isMember = groupMemberRepository.existsByGroupIdAndUserId(group.getId(), userId);
                long memberCount = groupMemberRepository.countByGroupId(group.getId());
                return buildGroupSearchResult(group, isMember, memberCount);
            });

        } catch (Exception e) {
            log.error("搜索群组失败: userId={}, keyword={}", userId, keyword, e);
            throw new GroupSearchException("搜索群组失败", e);
        }
    }

    private void inviteInitialMembers(Long groupId, String inviterId, List<String> memberIds) {
        for (String memberId : memberIds) {
            try {
                inviteSingleMember(groupId, inviterId, memberId);
            } catch (Exception e) {
                log.warn("邀请初始成员失败: groupId={}, memberId={}", groupId, memberId, e);
            }
        }
    }

    private void inviteSingleMember(Long groupId, String inviterId, String userId) {
        // 检查用户是否已经是成员
        if (groupMemberRepository.existsByGroupIdAndUserId(groupId, userId)) {
            throw new AlreadyGroupMemberException("用户已经是群组成员");
        }

        // 创建群组成员记录
        GroupMember member = GroupMember.builder()
            .groupId(groupId)
            .userId(userId)
            .role(GroupRole.MEMBER)
            .joinedAt(LocalDateTime.now())
            .invitedBy(inviterId)
            .build();

        groupMemberRepository.save(member);

        // 发送通知
        notificationService.sendGroupInvitationNotification(userId, groupId, inviterId);
    }

    private void handleCreatorLeaving(Group group, String creatorId) {
        // 查找其他管理员
        List<GroupMember> admins = groupMemberRepository
            .findByGroupIdAndRole(group.getId(), GroupRole.ADMIN);

        if (admins.size() > 1) {
            // 转移群组所有权给另一个管理员
            GroupMember newOwner = admins.stream()
                .filter(admin -> !admin.getUserId().equals(creatorId))
                .findFirst()
                .orElseThrow();

            group.setCreatorId(newOwner.getUserId());
            groupRepository.save(group);

            // 移除原创建者
            GroupMember creatorMember = groupMemberRepository
                .findByGroupIdAndUserId(group.getId(), creatorId).orElseThrow();
            groupMemberRepository.delete(creatorMember);

        } else {
            // 没有其他管理员，解散群组
            dissolveGroup(group.getId());
        }
    }

    private void dissolveGroup(Long groupId) {
        // 删除所有成员
        groupMemberRepository.deleteByGroupId(groupId);

        // 删除群组
        groupRepository.deleteById(groupId);

        // 发布群组解散事件
        eventPublisher.publishGroupDissolved(groupId);
    }

    private boolean canInviteMembers(Long groupId, String userId) {
        GroupMember member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId).orElse(null);
        return member != null && (member.getRole() == GroupRole.ADMIN || member.getRole() == GroupRole.MODERATOR);
    }

    private boolean canRemoveMembers(Long groupId, String userId) {
        GroupMember member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId).orElse(null);
        return member != null && (member.getRole() == GroupRole.ADMIN || member.getRole() == GroupRole.MODERATOR);
    }

    private boolean canAccessGroup(Long groupId, String userId) {
        Group group = groupRepository.findById(groupId).orElse(null);
        if (group == null) {
            return false;
        }

        // 公开群组任何人都可以访问
        if (group.getPrivacy() == GroupPrivacy.PUBLIC) {
            return true;
        }

        // 私有群组只有成员可以访问
        return groupMemberRepository.existsByGroupIdAndUserId(groupId, userId);
    }

    private void validateCreateGroupRequest(CreateGroupRequest request) {
        if (StringUtils.isBlank(request.getName())) {
            throw new InvalidGroupRequestException("群组名称不能为空");
        }
        if (request.getName().length() > 50) {
            throw new InvalidGroupRequestException("群组名称过长");
        }
        if (request.getMaxMembers() < 2 || request.getMaxMembers() > 1000) {
            throw new InvalidGroupRequestException("群组成员数量限制无效");
        }
    }

    private GroupResponse buildGroupResponse(Group group) {
        long memberCount = groupMemberRepository.countByGroupId(group.getId());

        return GroupResponse.builder()
            .id(group.getId())
            .name(group.getName())
            .description(group.getDescription())
            .type(group.getType())
            .privacy(group.getPrivacy())
            .maxMembers(group.getMaxMembers())
            .memberCount(memberCount)
            .creatorId(group.getCreatorId())
            .createdAt(group.getCreatedAt())
            .updatedAt(group.getUpdatedAt())
            .build();
    }

    private GroupMemberInfo buildGroupMemberInfo(GroupMember member, User user) {
        if (user == null) {
            return null;
        }

        return GroupMemberInfo.builder()
            .userId(user.getId())
            .username(user.getUsername())
            .nickname(user.getNickname())
            .avatarUrl(user.getAvatarUrl())
            .role(member.getRole())
            .joinedAt(member.getJoinedAt())
            .invitedBy(member.getInvitedBy())
            .isOnline(user.isOnline())
            .build();
    }

    private GroupSearchResult buildGroupSearchResult(Group group, boolean isMember, long memberCount) {
        return GroupSearchResult.builder()
            .id(group.getId())
            .name(group.getName())
            .description(group.getDescription())
            .type(group.getType())
            .privacy(group.getPrivacy())
            .memberCount(memberCount)
            .maxMembers(group.getMaxMembers())
            .isMember(isMember)
            .createdAt(group.getCreatedAt())
            .build();
    }
}
```

## 数据模型定义

### 1. 社交关系实体

#### Friendship - 好友关系实体
```java
/**
 * 好友关系实体
 * 存储用户间的好友关系
 */
@Entity
@Table(name = "friendships")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Friendship {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String userId;

    @Column(nullable = false)
    private String friendId;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    /**
     * 获取好友ID（相对于指定用户）
     * @param currentUserId 当前用户ID
     * @return 好友ID
     */
    public String getFriendId(String currentUserId) {
        return userId.equals(currentUserId) ? friendId : userId;
    }
}
```

#### FriendRequest - 好友请求实体
```java
/**
 * 好友请求实体
 * 存储好友请求信息
 */
@Entity
@Table(name = "friend_requests")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FriendRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String fromUserId;

    @Column(nullable = false)
    private String toUserId;

    @Column(length = 200)
    private String message;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FriendRequestStatus status;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    private LocalDateTime handledAt;
}
```

## 使用示例

### 基本社交功能操作
```java
// 注入社交服务
@Autowired
private FriendshipService friendshipService;

@Autowired
private GroupService groupService;

// 发送好友请求
FriendRequestResponse request = friendshipService.sendFriendRequest(
    "user123", "user456", "你好，我想加你为好友！");

// 处理好友请求
friendshipService.handleFriendRequest(request.getId(), "user456", FriendRequestAction.ACCEPT);

// 获取好友列表
Page<FriendInfo> friends = friendshipService.getFriendsList("user123", PageRequest.of(0, 20));

// 创建群组
CreateGroupRequest groupRequest = CreateGroupRequest.builder()
    .name("Ark-Pets爱好者群")
    .description("讨论Ark-Pets相关话题")
    .type(GroupType.INTEREST)
    .privacy(GroupPrivacy.PUBLIC)
    .maxMembers(100)
    .initialMembers(Arrays.asList("user456", "user789"))
    .build();

GroupResponse group = groupService.createGroup("user123", groupRequest);

// 邀请群组成员
groupService.inviteMembers(group.getId(), "user123", Arrays.asList("user999"));

// 搜索用户
Page<UserSearchResult> searchResults = friendshipService.searchUsers(
    "user123", "张三", PageRequest.of(0, 10));
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的社交功能实现，支持好友关系、群组管理、用户搜索等功能
