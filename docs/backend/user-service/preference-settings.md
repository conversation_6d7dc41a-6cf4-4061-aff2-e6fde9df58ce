# 偏好设置 (Preference Settings)

## 模块概述

偏好设置模块负责Ark-Pets AI Enhanced项目中用户个性化配置的管理，包括界面主题、语言设置、通知偏好、AI交互偏好、桌宠行为设置等功能。提供灵活的配置管理机制，支持实时更新、配置同步、默认值管理等特性。

**核心职责**:
- 用户个性化配置管理
- 界面主题和语言设置
- 通知和提醒偏好控制
- AI交互行为配置
- 桌宠外观和行为设置

## 核心功能架构

### 1. 偏好设置管理架构

#### 分层配置管理模型
```
┌─────────────────────────────────────┐
│           偏好设置管理               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 界面设置  │ 交互设置  │ 通知设置  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           配置处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 配置验证  │ 默认值   │ 同步机制  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           存储管理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据库   │ 缓存     │ 配置文件  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 配置更新流程

#### 偏好设置更新流程图
```mermaid
graph TB
    subgraph "配置更新流程"
        UpdateRequest[更新请求]
        ValidateAuth[身份验证]
        ValidateConfig[配置验证]
        MergeDefaults[合并默认值]
        ProcessConfig[配置处理]
        UpdateDatabase[更新数据库]
        UpdateCache[更新缓存]
        SyncDevices[同步设备]
        NotifyChange[通知变更]
        UpdateComplete[更新完成]
    end
    
    subgraph "配置验证"
        SchemaValidate[模式验证]
        RangeCheck[范围检查]
        DependencyCheck[依赖检查]
    end
    
    subgraph "实时同步"
        WebSocketSync[WebSocket同步]
        PushNotification[推送通知]
        EventBroadcast[事件广播]
    end
    
    UpdateRequest --> ValidateAuth
    ValidateAuth --> ValidateConfig
    ValidateConfig --> SchemaValidate
    SchemaValidate --> RangeCheck
    RangeCheck --> DependencyCheck
    DependencyCheck --> MergeDefaults
    MergeDefaults --> ProcessConfig
    ProcessConfig --> UpdateDatabase
    UpdateDatabase --> UpdateCache
    UpdateCache --> SyncDevices
    SyncDevices --> WebSocketSync
    WebSocketSync --> PushNotification
    PushNotification --> EventBroadcast
    EventBroadcast --> NotifyChange
    NotifyChange --> UpdateComplete
```

## 核心类和接口

### 1. 偏好设置管理服务

#### UserPreferenceService - 偏好设置主服务
```java
/**
 * 用户偏好设置管理主服务
 * 负责用户个性化配置的完整生命周期管理
 */
@Service
@Slf4j
@Transactional
public class UserPreferenceService {
    
    private final UserPreferenceRepository userPreferenceRepository;
    private final PreferenceValidationService preferenceValidationService;
    private final PreferenceDefaultService preferenceDefaultService;
    private final PreferenceCacheService preferenceCacheService;
    private final PreferenceSyncService preferenceSyncService;
    private final SecurityService securityService;
    private final EventPublisher eventPublisher;
    
    /**
     * 获取用户偏好设置
     * @param userId 用户ID
     * @return 偏好设置
     */
    @Transactional(readOnly = true)
    public UserPreferenceResponse getUserPreferences(String userId) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 从缓存获取
            UserPreferenceResponse cachedPreferences = preferenceCacheService.getPreferences(userId);
            if (cachedPreferences != null) {
                return cachedPreferences;
            }
            
            // 3. 从数据库获取
            UserPreference userPreference = userPreferenceRepository.findByUserId(userId)
                .orElse(null);
            
            // 4. 如果不存在，创建默认配置
            if (userPreference == null) {
                userPreference = createDefaultPreferences(userId);
            }
            
            // 5. 合并默认值
            UserPreference mergedPreference = preferenceDefaultService.mergeWithDefaults(userPreference);
            
            // 6. 构建响应对象
            UserPreferenceResponse response = buildPreferenceResponse(mergedPreference);
            
            // 7. 缓存结果
            preferenceCacheService.cachePreferences(userId, response);
            
            return response;
            
        } catch (Exception e) {
            log.error("获取用户偏好设置失败: userId={}", userId, e);
            throw new PreferenceAccessException("获取用户偏好设置失败", e);
        }
    }
    
    /**
     * 更新界面设置
     * @param userId 用户ID
     * @param uiRequest 界面设置请求
     * @return 更新后的偏好设置
     */
    public UserPreferenceResponse updateUISettings(String userId, UpdateUISettingsRequest uiRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证配置数据
            preferenceValidationService.validateUISettings(uiRequest);
            
            // 3. 获取当前偏好设置
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            
            // 4. 更新界面设置
            updateUIPreferences(currentPreference, uiRequest);
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncUISettings(userId, uiRequest);
            
            // 8. 发布更新事件
            eventPublisher.publishPreferenceUpdated(userId, "UI_SETTINGS", uiRequest);
            
            log.info("界面设置更新成功: userId={}", userId);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("更新界面设置失败: userId={}", userId, e);
            throw new PreferenceUpdateException("更新界面设置失败", e);
        }
    }
    
    /**
     * 更新通知设置
     * @param userId 用户ID
     * @param notificationRequest 通知设置请求
     * @return 更新后的偏好设置
     */
    public UserPreferenceResponse updateNotificationSettings(String userId, 
                                                            UpdateNotificationSettingsRequest notificationRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证配置数据
            preferenceValidationService.validateNotificationSettings(notificationRequest);
            
            // 3. 获取当前偏好设置
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            
            // 4. 更新通知设置
            updateNotificationPreferences(currentPreference, notificationRequest);
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncNotificationSettings(userId, notificationRequest);
            
            // 8. 发布更新事件
            eventPublisher.publishPreferenceUpdated(userId, "NOTIFICATION_SETTINGS", notificationRequest);
            
            log.info("通知设置更新成功: userId={}", userId);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("更新通知设置失败: userId={}", userId, e);
            throw new PreferenceUpdateException("更新通知设置失败", e);
        }
    }
    
    /**
     * 更新AI交互设置
     * @param userId 用户ID
     * @param aiRequest AI交互设置请求
     * @return 更新后的偏好设置
     */
    public UserPreferenceResponse updateAISettings(String userId, UpdateAISettingsRequest aiRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证配置数据
            preferenceValidationService.validateAISettings(aiRequest);
            
            // 3. 获取当前偏好设置
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            
            // 4. 更新AI设置
            updateAIPreferences(currentPreference, aiRequest);
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncAISettings(userId, aiRequest);
            
            // 8. 发布更新事件
            eventPublisher.publishPreferenceUpdated(userId, "AI_SETTINGS", aiRequest);
            
            log.info("AI交互设置更新成功: userId={}", userId);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("更新AI交互设置失败: userId={}", userId, e);
            throw new PreferenceUpdateException("更新AI交互设置失败", e);
        }
    }
    
    /**
     * 更新桌宠设置
     * @param userId 用户ID
     * @param petRequest 桌宠设置请求
     * @return 更新后的偏好设置
     */
    public UserPreferenceResponse updatePetSettings(String userId, UpdatePetSettingsRequest petRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证配置数据
            preferenceValidationService.validatePetSettings(petRequest);
            
            // 3. 获取当前偏好设置
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            
            // 4. 更新桌宠设置
            updatePetPreferences(currentPreference, petRequest);
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncPetSettings(userId, petRequest);
            
            // 8. 发布更新事件
            eventPublisher.publishPreferenceUpdated(userId, "PET_SETTINGS", petRequest);
            
            log.info("桌宠设置更新成功: userId={}", userId);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("更新桌宠设置失败: userId={}", userId, e);
            throw new PreferenceUpdateException("更新桌宠设置失败", e);
        }
    }
    
    /**
     * 批量更新偏好设置
     * @param userId 用户ID
     * @param batchRequest 批量更新请求
     * @return 更新后的偏好设置
     */
    public UserPreferenceResponse updatePreferencesBatch(String userId, BatchUpdatePreferencesRequest batchRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证批量配置数据
            preferenceValidationService.validateBatchUpdate(batchRequest);
            
            // 3. 获取当前偏好设置
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            
            // 4. 批量更新设置
            if (batchRequest.getUiSettings() != null) {
                updateUIPreferences(currentPreference, batchRequest.getUiSettings());
            }
            if (batchRequest.getNotificationSettings() != null) {
                updateNotificationPreferences(currentPreference, batchRequest.getNotificationSettings());
            }
            if (batchRequest.getAiSettings() != null) {
                updateAIPreferences(currentPreference, batchRequest.getAiSettings());
            }
            if (batchRequest.getPetSettings() != null) {
                updatePetPreferences(currentPreference, batchRequest.getPetSettings());
            }
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncBatchSettings(userId, batchRequest);
            
            // 8. 发布更新事件
            eventPublisher.publishPreferenceUpdated(userId, "BATCH_UPDATE", batchRequest);
            
            log.info("批量偏好设置更新成功: userId={}", userId);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("批量更新偏好设置失败: userId={}", userId, e);
            throw new PreferenceUpdateException("批量更新偏好设置失败", e);
        }
    }
    
    /**
     * 重置偏好设置为默认值
     * @param userId 用户ID
     * @param resetRequest 重置请求
     * @return 重置后的偏好设置
     */
    public UserPreferenceResponse resetPreferences(String userId, ResetPreferencesRequest resetRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取当前偏好设置
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            
            // 3. 创建备份
            PreferenceBackup backup = createPreferenceBackup(currentPreference);
            
            // 4. 重置指定的设置类型
            if (resetRequest.isResetUI()) {
                currentPreference.setUiSettings(preferenceDefaultService.getDefaultUISettings());
            }
            if (resetRequest.isResetNotification()) {
                currentPreference.setNotificationSettings(preferenceDefaultService.getDefaultNotificationSettings());
            }
            if (resetRequest.isResetAI()) {
                currentPreference.setAiSettings(preferenceDefaultService.getDefaultAISettings());
            }
            if (resetRequest.isResetPet()) {
                currentPreference.setPetSettings(preferenceDefaultService.getDefaultPetSettings());
            }
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncResetSettings(userId, resetRequest);
            
            // 8. 发布重置事件
            eventPublisher.publishPreferenceReset(userId, resetRequest, backup);
            
            log.info("偏好设置重置成功: userId={}, resetTypes={}", userId, resetRequest);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("重置偏好设置失败: userId={}", userId, e);
            throw new PreferenceUpdateException("重置偏好设置失败", e);
        }
    }
    
    /**
     * 导出用户偏好设置
     * @param userId 用户ID
     * @return 偏好设置导出数据
     */
    @Transactional(readOnly = true)
    public PreferenceExportData exportPreferences(String userId) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取偏好设置
            UserPreferenceResponse preferences = getUserPreferences(userId);
            
            // 3. 构建导出数据
            return PreferenceExportData.builder()
                .userId(userId)
                .preferences(preferences)
                .exportTime(LocalDateTime.now())
                .version("1.0")
                .build();
                
        } catch (Exception e) {
            log.error("导出偏好设置失败: userId={}", userId, e);
            throw new PreferenceExportException("导出偏好设置失败", e);
        }
    }
    
    /**
     * 导入用户偏好设置
     * @param userId 用户ID
     * @param importData 导入数据
     * @return 导入后的偏好设置
     */
    public UserPreferenceResponse importPreferences(String userId, PreferenceImportData importData) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证导入数据
            preferenceValidationService.validateImportData(importData);
            
            // 3. 创建当前设置备份
            UserPreference currentPreference = getUserPreferenceEntity(userId);
            PreferenceBackup backup = createPreferenceBackup(currentPreference);
            
            // 4. 应用导入的设置
            applyImportedPreferences(currentPreference, importData);
            
            // 5. 保存更新
            UserPreference updatedPreference = userPreferenceRepository.save(currentPreference);
            
            // 6. 清除缓存
            preferenceCacheService.evictPreferences(userId);
            
            // 7. 同步到其他设备
            preferenceSyncService.syncImportedSettings(userId, importData);
            
            // 8. 发布导入事件
            eventPublisher.publishPreferenceImported(userId, importData, backup);
            
            log.info("偏好设置导入成功: userId={}", userId);
            
            return buildPreferenceResponse(updatedPreference);
            
        } catch (Exception e) {
            log.error("导入偏好设置失败: userId={}", userId, e);
            throw new PreferenceImportException("导入偏好设置失败", e);
        }
    }

    private UserPreference getUserPreferenceEntity(String userId) {
        return userPreferenceRepository.findByUserId(userId)
            .orElseGet(() -> createDefaultPreferences(userId));
    }

    private UserPreference createDefaultPreferences(String userId) {
        UserPreference defaultPreference = UserPreference.builder()
            .userId(userId)
            .uiSettings(preferenceDefaultService.getDefaultUISettings())
            .notificationSettings(preferenceDefaultService.getDefaultNotificationSettings())
            .aiSettings(preferenceDefaultService.getDefaultAISettings())
            .petSettings(preferenceDefaultService.getDefaultPetSettings())
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        return userPreferenceRepository.save(defaultPreference);
    }

    private void updateUIPreferences(UserPreference preference, UpdateUISettingsRequest request) {
        UISettings current = preference.getUiSettings();
        if (current == null) {
            current = preferenceDefaultService.getDefaultUISettings();
        }

        if (request.getTheme() != null) {
            current.setTheme(request.getTheme());
        }
        if (request.getLanguage() != null) {
            current.setLanguage(request.getLanguage());
        }
        if (request.getFontSize() != null) {
            current.setFontSize(request.getFontSize());
        }
        if (request.getAnimationEnabled() != null) {
            current.setAnimationEnabled(request.getAnimationEnabled());
        }
        if (request.getSoundEnabled() != null) {
            current.setSoundEnabled(request.getSoundEnabled());
        }

        preference.setUiSettings(current);
        preference.setUpdatedAt(LocalDateTime.now());
    }

    private void updateNotificationPreferences(UserPreference preference, UpdateNotificationSettingsRequest request) {
        NotificationSettings current = preference.getNotificationSettings();
        if (current == null) {
            current = preferenceDefaultService.getDefaultNotificationSettings();
        }

        if (request.getEmailNotifications() != null) {
            current.setEmailNotifications(request.getEmailNotifications());
        }
        if (request.getPushNotifications() != null) {
            current.setPushNotifications(request.getPushNotifications());
        }
        if (request.getDesktopNotifications() != null) {
            current.setDesktopNotifications(request.getDesktopNotifications());
        }
        if (request.getQuietHours() != null) {
            current.setQuietHours(request.getQuietHours());
        }

        preference.setNotificationSettings(current);
        preference.setUpdatedAt(LocalDateTime.now());
    }

    private void updateAIPreferences(UserPreference preference, UpdateAISettingsRequest request) {
        AISettings current = preference.getAiSettings();
        if (current == null) {
            current = preferenceDefaultService.getDefaultAISettings();
        }

        if (request.getPreferredModel() != null) {
            current.setPreferredModel(request.getPreferredModel());
        }
        if (request.getResponseStyle() != null) {
            current.setResponseStyle(request.getResponseStyle());
        }
        if (request.getPersonalityType() != null) {
            current.setPersonalityType(request.getPersonalityType());
        }
        if (request.getContextMemory() != null) {
            current.setContextMemory(request.getContextMemory());
        }
        if (request.getAutoResponse() != null) {
            current.setAutoResponse(request.getAutoResponse());
        }

        preference.setAiSettings(current);
        preference.setUpdatedAt(LocalDateTime.now());
    }

    private void updatePetPreferences(UserPreference preference, UpdatePetSettingsRequest request) {
        PetSettings current = preference.getPetSettings();
        if (current == null) {
            current = preferenceDefaultService.getDefaultPetSettings();
        }

        if (request.getSelectedCharacter() != null) {
            current.setSelectedCharacter(request.getSelectedCharacter());
        }
        if (request.getSize() != null) {
            current.setSize(request.getSize());
        }
        if (request.getPosition() != null) {
            current.setPosition(request.getPosition());
        }
        if (request.getBehaviorMode() != null) {
            current.setBehaviorMode(request.getBehaviorMode());
        }
        if (request.getInteractionFrequency() != null) {
            current.setInteractionFrequency(request.getInteractionFrequency());
        }
        if (request.getAlwaysOnTop() != null) {
            current.setAlwaysOnTop(request.getAlwaysOnTop());
        }

        preference.setPetSettings(current);
        preference.setUpdatedAt(LocalDateTime.now());
    }

    private UserPreferenceResponse buildPreferenceResponse(UserPreference preference) {
        return UserPreferenceResponse.builder()
            .userId(preference.getUserId())
            .uiSettings(preference.getUiSettings())
            .notificationSettings(preference.getNotificationSettings())
            .aiSettings(preference.getAiSettings())
            .petSettings(preference.getPetSettings())
            .createdAt(preference.getCreatedAt())
            .updatedAt(preference.getUpdatedAt())
            .build();
    }

    private PreferenceBackup createPreferenceBackup(UserPreference preference) {
        return PreferenceBackup.builder()
            .userId(preference.getUserId())
            .uiSettings(preference.getUiSettings())
            .notificationSettings(preference.getNotificationSettings())
            .aiSettings(preference.getAiSettings())
            .petSettings(preference.getPetSettings())
            .backupTime(LocalDateTime.now())
            .build();
    }

    private void applyImportedPreferences(UserPreference preference, PreferenceImportData importData) {
        UserPreferenceResponse imported = importData.getPreferences();

        if (imported.getUiSettings() != null) {
            preference.setUiSettings(imported.getUiSettings());
        }
        if (imported.getNotificationSettings() != null) {
            preference.setNotificationSettings(imported.getNotificationSettings());
        }
        if (imported.getAiSettings() != null) {
            preference.setAiSettings(imported.getAiSettings());
        }
        if (imported.getPetSettings() != null) {
            preference.setPetSettings(imported.getPetSettings());
        }

        preference.setUpdatedAt(LocalDateTime.now());
    }
}
```

### 2. 偏好设置验证服务

#### PreferenceValidationService - 偏好设置验证服务
```java
/**
 * 偏好设置验证服务
 * 负责用户偏好设置数据的验证和安全检查
 */
@Service
@Slf4j
public class PreferenceValidationService {

    private final PreferenceValidationProperties validationProperties;

    /**
     * 验证界面设置
     * @param request 界面设置请求
     */
    public void validateUISettings(UpdateUISettingsRequest request) {
        List<String> errors = new ArrayList<>();

        // 验证主题
        if (request.getTheme() != null) {
            validateTheme(request.getTheme(), errors);
        }

        // 验证语言
        if (request.getLanguage() != null) {
            validateLanguage(request.getLanguage(), errors);
        }

        // 验证字体大小
        if (request.getFontSize() != null) {
            validateFontSize(request.getFontSize(), errors);
        }

        if (!errors.isEmpty()) {
            throw new PreferenceValidationException("界面设置验证失败", errors);
        }
    }

    /**
     * 验证通知设置
     * @param request 通知设置请求
     */
    public void validateNotificationSettings(UpdateNotificationSettingsRequest request) {
        List<String> errors = new ArrayList<>();

        // 验证安静时间设置
        if (request.getQuietHours() != null) {
            validateQuietHours(request.getQuietHours(), errors);
        }

        // 验证通知类型组合的合理性
        validateNotificationCombination(request, errors);

        if (!errors.isEmpty()) {
            throw new PreferenceValidationException("通知设置验证失败", errors);
        }
    }

    /**
     * 验证AI设置
     * @param request AI设置请求
     */
    public void validateAISettings(UpdateAISettingsRequest request) {
        List<String> errors = new ArrayList<>();

        // 验证AI模型
        if (request.getPreferredModel() != null) {
            validateAIModel(request.getPreferredModel(), errors);
        }

        // 验证响应风格
        if (request.getResponseStyle() != null) {
            validateResponseStyle(request.getResponseStyle(), errors);
        }

        // 验证个性类型
        if (request.getPersonalityType() != null) {
            validatePersonalityType(request.getPersonalityType(), errors);
        }

        // 验证上下文记忆设置
        if (request.getContextMemory() != null) {
            validateContextMemory(request.getContextMemory(), errors);
        }

        if (!errors.isEmpty()) {
            throw new PreferenceValidationException("AI设置验证失败", errors);
        }
    }

    /**
     * 验证桌宠设置
     * @param request 桌宠设置请求
     */
    public void validatePetSettings(UpdatePetSettingsRequest request) {
        List<String> errors = new ArrayList<>();

        // 验证角色选择
        if (request.getSelectedCharacter() != null) {
            validateCharacter(request.getSelectedCharacter(), errors);
        }

        // 验证尺寸设置
        if (request.getSize() != null) {
            validatePetSize(request.getSize(), errors);
        }

        // 验证位置设置
        if (request.getPosition() != null) {
            validatePosition(request.getPosition(), errors);
        }

        // 验证行为模式
        if (request.getBehaviorMode() != null) {
            validateBehaviorMode(request.getBehaviorMode(), errors);
        }

        // 验证交互频率
        if (request.getInteractionFrequency() != null) {
            validateInteractionFrequency(request.getInteractionFrequency(), errors);
        }

        if (!errors.isEmpty()) {
            throw new PreferenceValidationException("桌宠设置验证失败", errors);
        }
    }

    private void validateTheme(String theme, List<String> errors) {
        if (!validationProperties.getAllowedThemes().contains(theme)) {
            errors.add("不支持的主题: " + theme);
        }
    }

    private void validateLanguage(String language, List<String> errors) {
        if (!validationProperties.getSupportedLanguages().contains(language)) {
            errors.add("不支持的语言: " + language);
        }
    }

    private void validateFontSize(Integer fontSize, List<String> errors) {
        if (fontSize < validationProperties.getMinFontSize() ||
            fontSize > validationProperties.getMaxFontSize()) {
            errors.add("字体大小超出范围: " + fontSize +
                " (允许范围: " + validationProperties.getMinFontSize() +
                "-" + validationProperties.getMaxFontSize() + ")");
        }
    }

    private void validateQuietHours(QuietHours quietHours, List<String> errors) {
        if (quietHours.getStartTime() == null || quietHours.getEndTime() == null) {
            errors.add("安静时间的开始和结束时间不能为空");
            return;
        }

        // 验证时间格式
        if (!isValidTimeFormat(quietHours.getStartTime()) ||
            !isValidTimeFormat(quietHours.getEndTime())) {
            errors.add("时间格式不正确，应为 HH:mm 格式");
        }
    }

    private void validateNotificationCombination(UpdateNotificationSettingsRequest request, List<String> errors) {
        // 如果关闭了所有通知类型，给出警告
        if (Boolean.FALSE.equals(request.getEmailNotifications()) &&
            Boolean.FALSE.equals(request.getPushNotifications()) &&
            Boolean.FALSE.equals(request.getDesktopNotifications())) {
            errors.add("至少需要启用一种通知方式");
        }
    }

    private void validateAIModel(String model, List<String> errors) {
        if (!validationProperties.getAvailableAIModels().contains(model)) {
            errors.add("不支持的AI模型: " + model);
        }
    }

    private void validateResponseStyle(String style, List<String> errors) {
        if (!validationProperties.getAllowedResponseStyles().contains(style)) {
            errors.add("不支持的响应风格: " + style);
        }
    }

    private void validatePersonalityType(String personality, List<String> errors) {
        if (!validationProperties.getAvailablePersonalities().contains(personality)) {
            errors.add("不支持的个性类型: " + personality);
        }
    }

    private void validateContextMemory(Integer contextMemory, List<String> errors) {
        if (contextMemory < validationProperties.getMinContextMemory() ||
            contextMemory > validationProperties.getMaxContextMemory()) {
            errors.add("上下文记忆长度超出范围: " + contextMemory);
        }
    }

    private void validateCharacter(String character, List<String> errors) {
        if (!validationProperties.getAvailableCharacters().contains(character)) {
            errors.add("不支持的角色: " + character);
        }
    }

    private void validatePetSize(Double size, List<String> errors) {
        if (size < validationProperties.getMinPetSize() ||
            size > validationProperties.getMaxPetSize()) {
            errors.add("桌宠尺寸超出范围: " + size);
        }
    }

    private void validatePosition(PetPosition position, List<String> errors) {
        if (position.getX() < 0 || position.getY() < 0) {
            errors.add("桌宠位置坐标不能为负数");
        }

        // 可以添加更多位置验证逻辑，如屏幕边界检查
    }

    private void validateBehaviorMode(String behaviorMode, List<String> errors) {
        if (!validationProperties.getAllowedBehaviorModes().contains(behaviorMode)) {
            errors.add("不支持的行为模式: " + behaviorMode);
        }
    }

    private void validateInteractionFrequency(String frequency, List<String> errors) {
        if (!validationProperties.getAllowedInteractionFrequencies().contains(frequency)) {
            errors.add("不支持的交互频率: " + frequency);
        }
    }

    private boolean isValidTimeFormat(String time) {
        try {
            LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm"));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}
```

## 数据模型定义

### 1. 偏好设置实体

#### UserPreference - 用户偏好设置实体
```java
/**
 * 用户偏好设置实体
 * 存储用户的个性化配置信息
 */
@Entity
@Table(name = "user_preferences")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPreference {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String userId;

    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "theme", column = @Column(name = "ui_theme")),
        @AttributeOverride(name = "language", column = @Column(name = "ui_language")),
        @AttributeOverride(name = "fontSize", column = @Column(name = "ui_font_size")),
        @AttributeOverride(name = "animationEnabled", column = @Column(name = "ui_animation_enabled")),
        @AttributeOverride(name = "soundEnabled", column = @Column(name = "ui_sound_enabled"))
    })
    private UISettings uiSettings;

    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "emailNotifications", column = @Column(name = "notif_email_enabled")),
        @AttributeOverride(name = "pushNotifications", column = @Column(name = "notif_push_enabled")),
        @AttributeOverride(name = "desktopNotifications", column = @Column(name = "notif_desktop_enabled"))
    })
    private NotificationSettings notificationSettings;

    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "preferredModel", column = @Column(name = "ai_preferred_model")),
        @AttributeOverride(name = "responseStyle", column = @Column(name = "ai_response_style")),
        @AttributeOverride(name = "personalityType", column = @Column(name = "ai_personality_type")),
        @AttributeOverride(name = "contextMemory", column = @Column(name = "ai_context_memory")),
        @AttributeOverride(name = "autoResponse", column = @Column(name = "ai_auto_response"))
    })
    private AISettings aiSettings;

    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "selectedCharacter", column = @Column(name = "pet_character")),
        @AttributeOverride(name = "size", column = @Column(name = "pet_size")),
        @AttributeOverride(name = "behaviorMode", column = @Column(name = "pet_behavior_mode")),
        @AttributeOverride(name = "interactionFrequency", column = @Column(name = "pet_interaction_freq")),
        @AttributeOverride(name = "alwaysOnTop", column = @Column(name = "pet_always_on_top"))
    })
    private PetSettings petSettings;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

## 使用示例

### 基本偏好设置操作
```java
// 注入偏好设置服务
@Autowired
private UserPreferenceService userPreferenceService;

// 获取用户偏好设置
UserPreferenceResponse preferences = userPreferenceService.getUserPreferences("user123");
System.out.println("主题: " + preferences.getUiSettings().getTheme());
System.out.println("语言: " + preferences.getUiSettings().getLanguage());

// 更新界面设置
UpdateUISettingsRequest uiRequest = UpdateUISettingsRequest.builder()
    .theme("dark")
    .language("zh-CN")
    .fontSize(14)
    .animationEnabled(true)
    .soundEnabled(false)
    .build();

userPreferenceService.updateUISettings("user123", uiRequest);

// 更新通知设置
UpdateNotificationSettingsRequest notifRequest = UpdateNotificationSettingsRequest.builder()
    .emailNotifications(true)
    .pushNotifications(false)
    .desktopNotifications(true)
    .quietHours(QuietHours.builder()
        .enabled(true)
        .startTime("22:00")
        .endTime("08:00")
        .build())
    .build();

userPreferenceService.updateNotificationSettings("user123", notifRequest);

// 更新AI设置
UpdateAISettingsRequest aiRequest = UpdateAISettingsRequest.builder()
    .preferredModel("gpt-4")
    .responseStyle("friendly")
    .personalityType("cheerful")
    .contextMemory(10)
    .autoResponse(false)
    .build();

userPreferenceService.updateAISettings("user123", aiRequest);

// 批量更新
BatchUpdatePreferencesRequest batchRequest = BatchUpdatePreferencesRequest.builder()
    .uiSettings(uiRequest)
    .notificationSettings(notifRequest)
    .aiSettings(aiRequest)
    .build();

userPreferenceService.updatePreferencesBatch("user123", batchRequest);
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的用户偏好设置管理实现，支持界面、通知、AI、桌宠等多种配置管理
