# 用户档案管理 (Profile Management)

## 模块概述

用户档案管理模块负责Ark-Pets AI Enhanced项目中用户个人信息的管理，包括基本信息维护、头像管理、个性化设置、隐私控制等功能。提供完整的用户档案生命周期管理，支持数据验证、安全更新、版本控制等特性。

**核心职责**:
- 用户基本信息管理和维护
- 头像上传和图片处理
- 个人资料验证和安全更新
- 隐私设置和数据保护
- 档案变更历史和审计

## 核心功能架构

### 1. 用户档案管理架构

#### 分层档案管理模型
```
┌─────────────────────────────────────┐
│           用户档案管理               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 基本信息  │ 扩展信息  │ 隐私设置  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据验证  │ 图片处理  │ 安全检查  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           存储管理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据库   │ 文件存储  │ 缓存管理  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 档案更新流程

#### 用户档案更新流程图
```mermaid
graph TB
    subgraph "档案更新流程"
        UpdateRequest[更新请求]
        ValidateAuth[身份验证]
        ValidateData[数据验证]
        SecurityCheck[安全检查]
        ProcessData[数据处理]
        UpdateDatabase[更新数据库]
        ClearCache[清除缓存]
        AuditLog[审计日志]
        UpdateComplete[更新完成]
    end
    
    subgraph "图片处理流程"
        ImageUpload[图片上传]
        ImageValidate[图片验证]
        ImageResize[图片缩放]
        ImageOptimize[图片优化]
        ImageStore[图片存储]
    end
    
    subgraph "隐私控制"
        PrivacyCheck[隐私检查]
        DataMask[数据脱敏]
        AccessControl[访问控制]
    end
    
    UpdateRequest --> ValidateAuth
    ValidateAuth --> ValidateData
    ValidateData --> SecurityCheck
    SecurityCheck --> ProcessData
    ProcessData --> UpdateDatabase
    UpdateDatabase --> ClearCache
    ClearCache --> AuditLog
    AuditLog --> UpdateComplete
    
    ImageUpload --> ImageValidate
    ImageValidate --> ImageResize
    ImageResize --> ImageOptimize
    ImageOptimize --> ImageStore
    ImageStore --> ProcessData
    
    ValidateData --> PrivacyCheck
    PrivacyCheck --> DataMask
    DataMask --> AccessControl
    AccessControl --> ProcessData
```

## 核心类和接口

### 1. 用户档案管理服务

#### UserProfileService - 用户档案主服务
```java
/**
 * 用户档案管理主服务
 * 负责用户个人信息的完整生命周期管理
 */
@Service
@Slf4j
@Transactional
public class UserProfileService {
    
    private final UserProfileRepository userProfileRepository;
    private final UserRepository userRepository;
    private final ProfileImageService profileImageService;
    private final ProfileValidationService profileValidationService;
    private final ProfileAuditService profileAuditService;
    private final ProfileCacheService profileCacheService;
    private final SecurityService securityService;
    private final EventPublisher eventPublisher;
    
    /**
     * 获取用户档案信息
     * @param userId 用户ID
     * @param requesterId 请求者ID
     * @return 用户档案信息
     */
    @Transactional(readOnly = true)
    public UserProfileResponse getUserProfile(String userId, String requesterId) {
        try {
            // 1. 验证访问权限
            validateProfileAccess(userId, requesterId);
            
            // 2. 从缓存获取
            UserProfileResponse cachedProfile = profileCacheService.getProfile(userId);
            if (cachedProfile != null) {
                return applyPrivacyFilter(cachedProfile, userId, requesterId);
            }
            
            // 3. 从数据库获取
            UserProfile profile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> new ProfileNotFoundException("用户档案不存在: " + userId));
            
            // 4. 构建响应对象
            UserProfileResponse response = buildProfileResponse(profile);
            
            // 5. 缓存结果
            profileCacheService.cacheProfile(userId, response);
            
            // 6. 应用隐私过滤
            return applyPrivacyFilter(response, userId, requesterId);
            
        } catch (Exception e) {
            log.error("获取用户档案失败: userId={}, requesterId={}", userId, requesterId, e);
            throw new ProfileAccessException("获取用户档案失败", e);
        }
    }
    
    /**
     * 更新用户基本信息
     * @param userId 用户ID
     * @param updateRequest 更新请求
     * @return 更新后的档案信息
     */
    public UserProfileResponse updateBasicInfo(String userId, UpdateBasicInfoRequest updateRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取当前档案
            UserProfile currentProfile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> new ProfileNotFoundException("用户档案不存在: " + userId));
            
            // 3. 验证更新数据
            profileValidationService.validateBasicInfo(updateRequest);
            
            // 4. 检查敏感信息变更
            boolean hasSensitiveChanges = checkSensitiveChanges(currentProfile, updateRequest);
            if (hasSensitiveChanges) {
                securityService.validateSensitiveOperation(userId);
            }
            
            // 5. 创建档案快照（用于回滚）
            ProfileSnapshot snapshot = createProfileSnapshot(currentProfile);
            
            // 6. 更新基本信息
            updateProfileBasicInfo(currentProfile, updateRequest);
            
            // 7. 保存更新
            UserProfile updatedProfile = userProfileRepository.save(currentProfile);
            
            // 8. 清除缓存
            profileCacheService.evictProfile(userId);
            
            // 9. 记录审计日志
            profileAuditService.logProfileUpdate(userId, "BASIC_INFO", snapshot, updatedProfile);
            
            // 10. 发布更新事件
            eventPublisher.publishProfileUpdated(userId, "BASIC_INFO", updatedProfile);
            
            log.info("用户基本信息更新成功: userId={}", userId);
            
            return buildProfileResponse(updatedProfile);
            
        } catch (Exception e) {
            log.error("更新用户基本信息失败: userId={}", userId, e);
            throw new ProfileUpdateException("更新用户基本信息失败", e);
        }
    }
    
    /**
     * 更新用户头像
     * @param userId 用户ID
     * @param avatarFile 头像文件
     * @return 更新后的档案信息
     */
    public UserProfileResponse updateAvatar(String userId, MultipartFile avatarFile) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证图片文件
            profileValidationService.validateAvatarFile(avatarFile);
            
            // 3. 获取当前档案
            UserProfile currentProfile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> new ProfileNotFoundException("用户档案不存在: " + userId));
            
            // 4. 处理头像图片
            ProfileImageResult imageResult = profileImageService.processAvatar(userId, avatarFile);
            
            // 5. 删除旧头像
            if (StringUtils.isNotBlank(currentProfile.getAvatarUrl())) {
                profileImageService.deleteAvatar(currentProfile.getAvatarUrl());
            }
            
            // 6. 更新头像信息
            currentProfile.setAvatarUrl(imageResult.getImageUrl());
            currentProfile.setAvatarThumbnailUrl(imageResult.getThumbnailUrl());
            currentProfile.setUpdatedAt(LocalDateTime.now());
            
            // 7. 保存更新
            UserProfile updatedProfile = userProfileRepository.save(currentProfile);
            
            // 8. 清除缓存
            profileCacheService.evictProfile(userId);
            
            // 9. 记录审计日志
            profileAuditService.logAvatarUpdate(userId, imageResult.getImageUrl());
            
            // 10. 发布更新事件
            eventPublisher.publishAvatarUpdated(userId, imageResult.getImageUrl());
            
            log.info("用户头像更新成功: userId={}, avatarUrl={}", userId, imageResult.getImageUrl());
            
            return buildProfileResponse(updatedProfile);
            
        } catch (Exception e) {
            log.error("更新用户头像失败: userId={}", userId, e);
            throw new ProfileUpdateException("更新用户头像失败", e);
        }
    }
    
    /**
     * 更新隐私设置
     * @param userId 用户ID
     * @param privacyRequest 隐私设置请求
     * @return 更新后的档案信息
     */
    public UserProfileResponse updatePrivacySettings(String userId, UpdatePrivacyRequest privacyRequest) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 验证隐私设置
            profileValidationService.validatePrivacySettings(privacyRequest);
            
            // 3. 获取当前档案
            UserProfile currentProfile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> new ProfileNotFoundException("用户档案不存在: " + userId));
            
            // 4. 更新隐私设置
            updateProfilePrivacySettings(currentProfile, privacyRequest);
            
            // 5. 保存更新
            UserProfile updatedProfile = userProfileRepository.save(currentProfile);
            
            // 6. 清除缓存
            profileCacheService.evictProfile(userId);
            
            // 7. 记录审计日志
            profileAuditService.logPrivacyUpdate(userId, privacyRequest);
            
            // 8. 发布更新事件
            eventPublisher.publishPrivacyUpdated(userId, privacyRequest);
            
            log.info("用户隐私设置更新成功: userId={}", userId);
            
            return buildProfileResponse(updatedProfile);
            
        } catch (Exception e) {
            log.error("更新用户隐私设置失败: userId={}", userId, e);
            throw new ProfileUpdateException("更新用户隐私设置失败", e);
        }
    }
    
    /**
     * 删除用户档案
     * @param userId 用户ID
     * @param deleteReason 删除原因
     */
    public void deleteUserProfile(String userId, String deleteReason) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取当前档案
            UserProfile currentProfile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> new ProfileNotFoundException("用户档案不存在: " + userId));
            
            // 3. 创建删除快照
            ProfileSnapshot deleteSnapshot = createProfileSnapshot(currentProfile);
            
            // 4. 删除头像文件
            if (StringUtils.isNotBlank(currentProfile.getAvatarUrl())) {
                profileImageService.deleteAvatar(currentProfile.getAvatarUrl());
            }
            
            // 5. 软删除档案
            currentProfile.setDeleted(true);
            currentProfile.setDeletedAt(LocalDateTime.now());
            currentProfile.setDeleteReason(deleteReason);
            userProfileRepository.save(currentProfile);
            
            // 6. 清除缓存
            profileCacheService.evictProfile(userId);
            
            // 7. 记录审计日志
            profileAuditService.logProfileDeletion(userId, deleteReason, deleteSnapshot);
            
            // 8. 发布删除事件
            eventPublisher.publishProfileDeleted(userId, deleteReason);
            
            log.info("用户档案删除成功: userId={}, reason={}", userId, deleteReason);
            
        } catch (Exception e) {
            log.error("删除用户档案失败: userId={}", userId, e);
            throw new ProfileDeletionException("删除用户档案失败", e);
        }
    }
    
    /**
     * 获取档案更新历史
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 更新历史列表
     */
    @Transactional(readOnly = true)
    public Page<ProfileAuditRecord> getProfileHistory(String userId, Pageable pageable) {
        try {
            // 1. 验证用户权限
            securityService.validateUserAccess(userId);
            
            // 2. 获取审计记录
            return profileAuditService.getProfileAuditHistory(userId, pageable);
            
        } catch (Exception e) {
            log.error("获取档案更新历史失败: userId={}", userId, e);
            throw new ProfileAccessException("获取档案更新历史失败", e);
        }
    }
    
    /**
     * 批量获取用户档案
     * @param userIds 用户ID列表
     * @param requesterId 请求者ID
     * @return 用户档案映射
     */
    @Transactional(readOnly = true)
    public Map<String, UserProfileSummary> getBatchUserProfiles(List<String> userIds, String requesterId) {
        try {
            Map<String, UserProfileSummary> result = new HashMap<>();
            
            for (String userId : userIds) {
                try {
                    // 验证访问权限
                    if (canAccessProfile(userId, requesterId)) {
                        UserProfileResponse profile = getUserProfile(userId, requesterId);
                        result.put(userId, convertToSummary(profile));
                    }
                } catch (Exception e) {
                    log.warn("获取用户档案失败: userId={}, requesterId={}", userId, requesterId, e);
                    // 继续处理其他用户
                }
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("批量获取用户档案失败: requesterId={}", requesterId, e);
            throw new ProfileAccessException("批量获取用户档案失败", e);
        }
    }
    
    private void validateProfileAccess(String userId, String requesterId) {
        if (!canAccessProfile(userId, requesterId)) {
            throw new ProfileAccessDeniedException("无权限访问用户档案: " + userId);
        }
    }
    
    private boolean canAccessProfile(String userId, String requesterId) {
        // 1. 用户可以访问自己的档案
        if (userId.equals(requesterId)) {
            return true;
        }
        
        // 2. 检查是否为好友关系
        if (securityService.areFriends(userId, requesterId)) {
            return true;
        }
        
        // 3. 检查档案隐私设置
        UserProfile profile = userProfileRepository.findByUserId(userId).orElse(null);
        if (profile != null && profile.getPrivacySettings() != null) {
            return profile.getPrivacySettings().isPublicProfile();
        }
        
        return false;
    }
    
    private UserProfileResponse applyPrivacyFilter(UserProfileResponse profile, String userId, String requesterId) {
        if (userId.equals(requesterId)) {
            return profile; // 用户可以看到自己的完整信息
        }
        
        // 根据隐私设置过滤信息
        UserProfileResponse filteredProfile = profile.copy();
        PrivacySettings privacy = profile.getPrivacySettings();
        
        if (privacy != null) {
            if (!privacy.isShowEmail()) {
                filteredProfile.setEmail(null);
            }
            if (!privacy.isShowPhone()) {
                filteredProfile.setPhone(null);
            }
            if (!privacy.isShowBirthday()) {
                filteredProfile.setBirthday(null);
            }
            if (!privacy.isShowLocation()) {
                filteredProfile.setLocation(null);
            }
        }
        
        return filteredProfile;
    }
}
```

### 2. 档案图片处理服务

#### ProfileImageService - 档案图片处理服务
```java
/**
 * 档案图片处理服务
 * 负责用户头像的上传、处理和存储
 */
@Service
@Slf4j
public class ProfileImageService {

    private final FileStorageService fileStorageService;
    private final ImageProcessingService imageProcessingService;
    private final ProfileImageProperties imageProperties;

    /**
     * 处理用户头像
     * @param userId 用户ID
     * @param avatarFile 头像文件
     * @return 图片处理结果
     */
    public ProfileImageResult processAvatar(String userId, MultipartFile avatarFile) {
        try {
            // 1. 生成文件名
            String originalFilename = avatarFile.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String fileName = generateAvatarFileName(userId, fileExtension);

            // 2. 读取图片数据
            byte[] imageData = avatarFile.getBytes();

            // 3. 验证图片格式
            validateImageFormat(imageData, fileExtension);

            // 4. 处理原始图片
            byte[] processedImage = imageProcessingService.processAvatar(
                imageData,
                imageProperties.getAvatarSize(),
                imageProperties.getAvatarQuality()
            );

            // 5. 生成缩略图
            byte[] thumbnailImage = imageProcessingService.generateThumbnail(
                processedImage,
                imageProperties.getThumbnailSize(),
                imageProperties.getThumbnailQuality()
            );

            // 6. 上传原始头像
            String avatarPath = "avatars/" + fileName;
            String avatarUrl = fileStorageService.uploadFile(avatarPath, processedImage, "image/" + fileExtension);

            // 7. 上传缩略图
            String thumbnailFileName = generateThumbnailFileName(userId, fileExtension);
            String thumbnailPath = "avatars/thumbnails/" + thumbnailFileName;
            String thumbnailUrl = fileStorageService.uploadFile(thumbnailPath, thumbnailImage, "image/" + fileExtension);

            log.info("头像处理成功: userId={}, avatarUrl={}", userId, avatarUrl);

            return ProfileImageResult.builder()
                .imageUrl(avatarUrl)
                .thumbnailUrl(thumbnailUrl)
                .fileName(fileName)
                .fileSize(processedImage.length)
                .thumbnailSize(thumbnailImage.length)
                .build();

        } catch (Exception e) {
            log.error("头像处理失败: userId={}", userId, e);
            throw new ImageProcessingException("头像处理失败", e);
        }
    }

    /**
     * 删除用户头像
     * @param avatarUrl 头像URL
     */
    public void deleteAvatar(String avatarUrl) {
        try {
            if (StringUtils.isNotBlank(avatarUrl)) {
                fileStorageService.deleteFile(avatarUrl);

                // 同时删除缩略图
                String thumbnailUrl = convertToThumbnailUrl(avatarUrl);
                if (StringUtils.isNotBlank(thumbnailUrl)) {
                    fileStorageService.deleteFile(thumbnailUrl);
                }

                log.info("头像删除成功: avatarUrl={}", avatarUrl);
            }
        } catch (Exception e) {
            log.error("头像删除失败: avatarUrl={}", avatarUrl, e);
            // 不抛出异常，避免影响主流程
        }
    }

    private void validateImageFormat(byte[] imageData, String fileExtension) {
        // 验证文件扩展名
        if (!imageProperties.getAllowedFormats().contains(fileExtension.toLowerCase())) {
            throw new InvalidImageFormatException("不支持的图片格式: " + fileExtension);
        }

        // 验证文件大小
        if (imageData.length > imageProperties.getMaxFileSize()) {
            throw new ImageTooLargeException("图片文件过大: " + imageData.length + " bytes");
        }

        // 验证图片内容
        if (!imageProcessingService.isValidImage(imageData)) {
            throw new InvalidImageFormatException("无效的图片文件");
        }
    }

    private String generateAvatarFileName(String userId, String extension) {
        return String.format("avatar_%s_%d.%s", userId, System.currentTimeMillis(), extension);
    }

    private String generateThumbnailFileName(String userId, String extension) {
        return String.format("thumb_%s_%d.%s", userId, System.currentTimeMillis(), extension);
    }

    private String getFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "jpg";
        }
        int lastDot = filename.lastIndexOf('.');
        return lastDot > 0 ? filename.substring(lastDot + 1) : "jpg";
    }

    private String convertToThumbnailUrl(String avatarUrl) {
        // 将头像URL转换为缩略图URL
        return avatarUrl.replace("/avatars/", "/avatars/thumbnails/")
                       .replace("avatar_", "thumb_");
    }
}
```

### 3. 档案验证服务

#### ProfileValidationService - 档案验证服务
```java
/**
 * 档案验证服务
 * 负责用户档案数据的验证和安全检查
 */
@Service
@Slf4j
public class ProfileValidationService {

    private final ProfileValidationProperties validationProperties;
    private final SecurityService securityService;

    /**
     * 验证基本信息更新请求
     * @param request 更新请求
     */
    public void validateBasicInfo(UpdateBasicInfoRequest request) {
        List<String> errors = new ArrayList<>();

        // 验证昵称
        if (StringUtils.isNotBlank(request.getNickname())) {
            validateNickname(request.getNickname(), errors);
        }

        // 验证邮箱
        if (StringUtils.isNotBlank(request.getEmail())) {
            validateEmail(request.getEmail(), errors);
        }

        // 验证手机号
        if (StringUtils.isNotBlank(request.getPhone())) {
            validatePhone(request.getPhone(), errors);
        }

        // 验证生日
        if (request.getBirthday() != null) {
            validateBirthday(request.getBirthday(), errors);
        }

        // 验证性别
        if (request.getGender() != null) {
            validateGender(request.getGender(), errors);
        }

        // 验证个人简介
        if (StringUtils.isNotBlank(request.getBio())) {
            validateBio(request.getBio(), errors);
        }

        // 验证位置信息
        if (StringUtils.isNotBlank(request.getLocation())) {
            validateLocation(request.getLocation(), errors);
        }

        if (!errors.isEmpty()) {
            throw new ProfileValidationException("档案信息验证失败", errors);
        }
    }

    /**
     * 验证头像文件
     * @param avatarFile 头像文件
     */
    public void validateAvatarFile(MultipartFile avatarFile) {
        List<String> errors = new ArrayList<>();

        if (avatarFile == null || avatarFile.isEmpty()) {
            errors.add("头像文件不能为空");
        } else {
            // 验证文件大小
            if (avatarFile.getSize() > validationProperties.getMaxAvatarSize()) {
                errors.add("头像文件过大，最大允许: " +
                    formatFileSize(validationProperties.getMaxAvatarSize()));
            }

            // 验证文件类型
            String contentType = avatarFile.getContentType();
            if (!validationProperties.getAllowedImageTypes().contains(contentType)) {
                errors.add("不支持的图片格式: " + contentType);
            }

            // 验证文件名
            String filename = avatarFile.getOriginalFilename();
            if (StringUtils.isBlank(filename) || !isValidFilename(filename)) {
                errors.add("无效的文件名");
            }
        }

        if (!errors.isEmpty()) {
            throw new ProfileValidationException("头像文件验证失败", errors);
        }
    }

    /**
     * 验证隐私设置
     * @param request 隐私设置请求
     */
    public void validatePrivacySettings(UpdatePrivacyRequest request) {
        List<String> errors = new ArrayList<>();

        // 验证隐私设置的合理性
        if (request.getPrivacySettings() != null) {
            PrivacySettings settings = request.getPrivacySettings();

            // 如果设置为私有档案，某些信息必须隐藏
            if (!settings.isPublicProfile()) {
                if (settings.isShowEmail() || settings.isShowPhone()) {
                    errors.add("私有档案不能公开邮箱或手机号");
                }
            }

            // 验证年龄限制设置
            if (settings.getMinAge() != null && settings.getMaxAge() != null) {
                if (settings.getMinAge() > settings.getMaxAge()) {
                    errors.add("最小年龄不能大于最大年龄");
                }
                if (settings.getMinAge() < 13) {
                    errors.add("最小年龄不能小于13岁");
                }
                if (settings.getMaxAge() > 120) {
                    errors.add("最大年龄不能大于120岁");
                }
            }
        }

        if (!errors.isEmpty()) {
            throw new ProfileValidationException("隐私设置验证失败", errors);
        }
    }

    private void validateNickname(String nickname, List<String> errors) {
        if (nickname.length() < validationProperties.getMinNicknameLength()) {
            errors.add("昵称长度不能少于" + validationProperties.getMinNicknameLength() + "个字符");
        }
        if (nickname.length() > validationProperties.getMaxNicknameLength()) {
            errors.add("昵称长度不能超过" + validationProperties.getMaxNicknameLength() + "个字符");
        }
        if (!nickname.matches(validationProperties.getNicknamePattern())) {
            errors.add("昵称包含非法字符");
        }
        if (securityService.containsProfanity(nickname)) {
            errors.add("昵称包含敏感词汇");
        }
    }

    private void validateEmail(String email, List<String> errors) {
        if (!email.matches(validationProperties.getEmailPattern())) {
            errors.add("邮箱格式不正确");
        }
        if (email.length() > validationProperties.getMaxEmailLength()) {
            errors.add("邮箱长度不能超过" + validationProperties.getMaxEmailLength() + "个字符");
        }
    }

    private void validatePhone(String phone, List<String> errors) {
        if (!phone.matches(validationProperties.getPhonePattern())) {
            errors.add("手机号格式不正确");
        }
    }

    private void validateBirthday(LocalDate birthday, List<String> errors) {
        LocalDate now = LocalDate.now();
        LocalDate minDate = now.minusYears(validationProperties.getMaxAge());
        LocalDate maxDate = now.minusYears(validationProperties.getMinAge());

        if (birthday.isBefore(minDate)) {
            errors.add("年龄不能超过" + validationProperties.getMaxAge() + "岁");
        }
        if (birthday.isAfter(maxDate)) {
            errors.add("年龄不能小于" + validationProperties.getMinAge() + "岁");
        }
    }

    private void validateGender(Gender gender, List<String> errors) {
        if (!validationProperties.getAllowedGenders().contains(gender)) {
            errors.add("不支持的性别选项: " + gender);
        }
    }

    private void validateBio(String bio, List<String> errors) {
        if (bio.length() > validationProperties.getMaxBioLength()) {
            errors.add("个人简介长度不能超过" + validationProperties.getMaxBioLength() + "个字符");
        }
        if (securityService.containsProfanity(bio)) {
            errors.add("个人简介包含敏感词汇");
        }
    }

    private void validateLocation(String location, List<String> errors) {
        if (location.length() > validationProperties.getMaxLocationLength()) {
            errors.add("位置信息长度不能超过" + validationProperties.getMaxLocationLength() + "个字符");
        }
    }

    private boolean isValidFilename(String filename) {
        return filename.matches("^[a-zA-Z0-9._-]+$") && !filename.contains("..");
    }

    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return (size / 1024) + " KB";
        return (size / (1024 * 1024)) + " MB";
    }
}
```

## 数据模型定义

### 1. 用户档案实体

#### UserProfile - 用户档案实体
```java
/**
 * 用户档案实体
 * 存储用户的详细个人信息
 */
@Entity
@Table(name = "user_profiles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String userId;

    @Column(length = 50)
    private String nickname;

    @Column(length = 100)
    private String email;

    @Column(length = 20)
    private String phone;

    private LocalDate birthday;

    @Enumerated(EnumType.STRING)
    private Gender gender;

    @Column(length = 500)
    private String bio;

    @Column(length = 100)
    private String location;

    @Column(length = 500)
    private String avatarUrl;

    @Column(length = 500)
    private String avatarThumbnailUrl;

    @Embedded
    private PrivacySettings privacySettings;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime updatedAt;

    private boolean deleted = false;
    private LocalDateTime deletedAt;
    private String deleteReason;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * 计算用户年龄
     * @return 年龄
     */
    public Integer getAge() {
        if (birthday == null) {
            return null;
        }
        return Period.between(birthday, LocalDate.now()).getYears();
    }

    /**
     * 检查档案是否完整
     * @return 是否完整
     */
    public boolean isProfileComplete() {
        return StringUtils.isNotBlank(nickname) &&
               StringUtils.isNotBlank(email) &&
               birthday != null &&
               gender != null;
    }
}
```

## 使用示例

### 基本档案操作
```java
// 注入档案服务
@Autowired
private UserProfileService userProfileService;

// 获取用户档案
UserProfileResponse profile = userProfileService.getUserProfile("user123", "user123");
System.out.println("昵称: " + profile.getNickname());
System.out.println("邮箱: " + profile.getEmail());

// 更新基本信息
UpdateBasicInfoRequest updateRequest = UpdateBasicInfoRequest.builder()
    .nickname("新昵称")
    .bio("这是我的新个人简介")
    .location("北京市")
    .build();

UserProfileResponse updatedProfile = userProfileService.updateBasicInfo("user123", updateRequest);

// 更新头像
MockMultipartFile avatarFile = new MockMultipartFile(
    "avatar", "avatar.jpg", "image/jpeg", avatarBytes);
UserProfileResponse profileWithAvatar = userProfileService.updateAvatar("user123", avatarFile);

// 更新隐私设置
UpdatePrivacyRequest privacyRequest = UpdatePrivacyRequest.builder()
    .privacySettings(PrivacySettings.builder()
        .publicProfile(false)
        .showEmail(false)
        .showPhone(false)
        .showBirthday(true)
        .build())
    .build();

userProfileService.updatePrivacySettings("user123", privacyRequest);
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的用户档案管理实现，支持基本信息管理、头像处理、隐私控制等功能
