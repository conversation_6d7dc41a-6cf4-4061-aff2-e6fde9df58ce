# A2C交互 (Agent-to-Computer Interaction)

## 模块概述

A2C交互模块负责Ark-Pets AI Enhanced项目中AI代理与计算机系统的直接交互，采用**开源组件集成**的现代化架构，基于SikuliX、TestFX和AWT Robot等成熟开源库，提供稳定可靠的屏幕控制、键盘输入、鼠标操作、应用程序控制等功能。

**技术架构**: SikuliX + TestFX + AWT Robot 混合组件架构

**核心职责**:
- 基于SikuliX的图像识别和视觉自动化
- 基于TestFX的JavaFX应用程序控制
- 基于AWT Robot的基础系统级操作
- 统一的安全权限控制和操作审计
- 智能的操作路由和组件选择

## 核心功能架构

### 1. 开源组件混合架构

#### SikuliX + TestFX + AWT Robot 架构模型
```
┌─────────────────────────────────────────────────────────┐
│                A2C统一接口层                             │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ 操作路由器   │ 权限控制     │ 安全审计                │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                开源组件适配层                            │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ SikuliX     │ TestFX      │ AWT Robot               │ │
│  │ 适配器      │ 适配器      │ 适配器                  │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                开源组件层                               │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ SikuliX API │ TestFX API  │ Java AWT Robot          │ │
│  │ 图像识别     │ JavaFX控制  │ 基础输入控制             │ │
│  │ OCR文字识别  │ UI元素查找  │ 系统级操作              │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2. 智能组件路由流程

#### 开源组件智能路由流程图
```mermaid
graph TB
    subgraph "A2C请求处理"
        A2CRequest[A2C操作请求]
        PermissionCheck[权限验证]
        ComponentRouter[组件路由器]
        SecurityAudit[安全审计]
    end

    subgraph "SikuliX处理流程"
        ImageRecognition[图像识别]
        OCRProcessing[OCR文字识别]
        VisualAutomation[视觉自动化]
        ImageBasedClick[基于图像点击]
    end

    subgraph "TestFX处理流程"
        JavaFXDiscovery[JavaFX元素发现]
        UIElementControl[UI元素控制]
        ApplicationState[应用状态验证]
        JavaFXInteraction[JavaFX交互]
    end

    subgraph "AWT Robot处理流程"
        CoordinateClick[坐标点击]
        KeyboardInput[键盘输入]
        MouseMovement[鼠标移动]
        SystemLevelOps[系统级操作]
    end

    A2CRequest --> PermissionCheck
    PermissionCheck --> ComponentRouter
    ComponentRouter --> SecurityAudit

    SecurityAudit --> ImageRecognition
    ImageRecognition --> OCRProcessing
    OCRProcessing --> VisualAutomation
    VisualAutomation --> ImageBasedClick

    SecurityAudit --> JavaFXDiscovery
    JavaFXDiscovery --> UIElementControl
    UIElementControl --> ApplicationState
    ApplicationState --> JavaFXInteraction

    SecurityAudit --> CoordinateClick
    CoordinateClick --> KeyboardInput
    KeyboardInput --> MouseMovement
    MouseMovement --> SystemLevelOps
```

## 核心类和接口

### 1. 开源组件集成主服务

#### OpenSourceA2CInteractionService - 基于开源组件的A2C交互服务
```java
/**
 * 基于开源组件的A2C交互服务
 * 集成SikuliX、TestFX和AWT Robot提供统一的A2C交互接口
 */
@Service
@Slf4j
public class OpenSourceA2CInteractionService {

    // 开源组件适配器
    private final SikuliXAdapter sikuliXAdapter;
    private final TestFXAdapter testFXAdapter;
    private final AWTRobotAdapter awtRobotAdapter;

    // 核心服务组件
    private final A2COperationRouter operationRouter;
    private final A2CPermissionService permissionService;
    private final A2CSecurityService securityService;
    private final A2COperationLogRepository operationLogRepository;
    private final A2CStatisticsService statisticsService;
    
    /**
     * 执行A2C操作 (智能路由到最适合的开源组件)
     * @param operationRequest A2C操作请求
     * @return 操作结果
     */
    public A2COperationResult executeOperation(A2COperationRequest operationRequest) {
        String operationId = generateOperationId();

        try {
            // 1. 验证权限
            if (!permissionService.hasPermission(operationRequest.getUserId(), operationRequest.getOperationType())) {
                return A2COperationResult.permissionDenied();
            }

            // 2. 安全检查
            SecurityCheckResult securityCheck = securityService.checkOperation(operationRequest);
            if (!securityCheck.isAllowed()) {
                return A2COperationResult.securityBlocked(securityCheck.getReason());
            }

            // 3. 智能路由到最适合的开源组件
            A2CAdapter selectedAdapter = operationRouter.selectAdapter(operationRequest);

            // 4. 创建操作日志
            A2COperationLog operationLog = createOperationLog(operationId, operationRequest, selectedAdapter.getComponentName());
            operationLog = operationLogRepository.save(operationLog);

            // 5. 执行操作
            A2COperationResult result = selectedAdapter.executeOperation(operationRequest);

            // 6. 更新操作日志
            updateOperationLog(operationLog, result);

            // 7. 更新统计
            statisticsService.recordOperation(operationLog, result);

            log.info("A2C操作完成: operationId={}, type={}, component={}, success={}",
                operationId, operationRequest.getOperationType(), selectedAdapter.getComponentName(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("A2C操作失败: operationId={}", operationId, e);
            return A2COperationResult.executionError(e.getMessage());
        }
    }
    
    /**
     * 获取A2C操作统计
     * @param statisticsRequest 统计请求
     * @return 操作统计
     */
    public A2COperationStatistics getOperationStatistics(A2CStatisticsRequest statisticsRequest) {
        try {
            return statisticsService.getOperationStatistics(statisticsRequest);
        } catch (Exception e) {
            log.error("获取A2C操作统计失败: {}", statisticsRequest, e);
            throw new A2CStatisticsException("获取A2C操作统计失败", e);
        }
    }
    
    /**
     * 批量执行A2C操作
     * @param batchRequest 批量请求
     * @return 批量结果
     */
    public A2CBatchOperationResult executeBatchOperations(A2CBatchOperationRequest batchRequest) {
        try {
            List<A2COperationResult> results = new ArrayList<>();
            
            for (A2COperationRequest operation : batchRequest.getOperations()) {
                try {
                    A2COperationResult result = executeOperation(operation);
                    results.add(result);
                    
                    // 如果有操作失败且设置了停止标志，则停止执行
                    if (!result.isSuccess() && batchRequest.isStopOnFailure()) {
                        break;
                    }
                    
                    // 操作间延迟
                    if (batchRequest.getDelayBetweenOperations() > 0) {
                        Thread.sleep(batchRequest.getDelayBetweenOperations());
                    }
                    
                } catch (Exception e) {
                    log.error("批量操作中的单个操作失败: {}", operation, e);
                    results.add(A2COperationResult.executionError(e.getMessage()));
                    
                    if (batchRequest.isStopOnFailure()) {
                        break;
                    }
                }
            }
            
            return A2CBatchOperationResult.builder()
                .batchId(batchRequest.getBatchId())
                .results(results)
                .totalOperations(batchRequest.getOperations().size())
                .successCount((int) results.stream().filter(A2COperationResult::isSuccess).count())
                .completedAt(LocalDateTime.now())
                .build();
                
        } catch (Exception e) {
            log.error("批量A2C操作失败: {}", batchRequest, e);
            throw new A2CBatchOperationException("批量A2C操作失败", e);
        }
    }
    
    /**
     * 获取A2C操作历史
     * @param userId 用户ID
     * @param operationType 操作类型（可选）
     * @param pageable 分页参数
     * @return 操作历史
     */
    public Page<A2COperationHistory> getOperationHistory(String userId, A2COperationType operationType, Pageable pageable) {
        try {
            return operationLogRepository.findOperationHistory(userId, operationType, pageable);
        } catch (Exception e) {
            log.error("获取A2C操作历史失败: userId={}, operationType={}", userId, operationType, e);
            throw new A2COperationHistoryException("获取A2C操作历史失败", e);
        }
    }
    
    /**
     * 获取A2C操作统计
     * @param statisticsRequest 统计请求
     * @return 操作统计
     */
    public A2COperationStatistics getOperationStatistics(A2CStatisticsRequest statisticsRequest) {
        try {
            return statisticsService.getOperationStatistics(statisticsRequest);
        } catch (Exception e) {
            log.error("获取A2C操作统计失败: {}", statisticsRequest, e);
            throw new A2CStatisticsException("获取A2C操作统计失败", e);
        }
    }
    
    private String generateOperationId() {
        return "a2c_" + UUID.randomUUID().toString().replace("-", "");
    }

    private A2COperationLog createOperationLog(String operationId, A2COperationRequest request, String componentName) {
        return A2COperationLog.builder()
            .operationId(operationId)
            .userId(request.getUserId())
            .operationType(request.getOperationType())
            .parameters(request.getParameters())
            .componentName(componentName)
            .status(A2COperationStatus.EXECUTING)
            .startedAt(LocalDateTime.now())
            .build();
    }

    private void updateOperationLog(A2COperationLog operationLog, A2COperationResult result) {
        operationLog.setStatus(result.isSuccess() ? A2COperationStatus.COMPLETED : A2COperationStatus.FAILED);
        operationLog.setResult(result.getResult());
        operationLog.setErrorMessage(result.getErrorMessage());
        operationLog.setCompletedAt(LocalDateTime.now());
        operationLog.setExecutionTimeMs(Duration.between(operationLog.getStartedAt(), operationLog.getCompletedAt()).toMillis());

        operationLogRepository.save(operationLog);
    }
}
```

### 2. SikuliX适配器

#### SikuliXAdapter - SikuliX组件适配器
```java
/**
 * SikuliX适配器 - 图像识别和视觉自动化
 * 基于SikuliX开源库实现图像识别、OCR和视觉自动化功能
 */
@Component
@Slf4j
public class SikuliXAdapter implements A2CAdapter {

    private final Screen screen;
    private final A2CImageConfig imageConfig;
    private final ImageStorageService imageStorageService;

    public SikuliXAdapter(A2CImageConfig imageConfig, ImageStorageService imageStorageService) {
        this.screen = new Screen();
        this.imageConfig = imageConfig;
        this.imageStorageService = imageStorageService;
    }

    @Override
    public String getComponentName() {
        return "SikuliX";
    }

    @Override
    public boolean supports(A2COperationType operationType) {
        return operationType == A2COperationType.SCREENSHOT ||
               operationType == A2COperationType.CLICK_BY_IMAGE ||
               operationType == A2COperationType.FIND_TEXT ||
               operationType == A2COperationType.WAIT_FOR_IMAGE ||
               operationType == A2COperationType.OCR_RECOGNITION;
    }

    @Override
    public A2COperationResult executeOperation(A2COperationRequest request) {
        switch (request.getOperationType()) {
            case SCREENSHOT:
                return takeScreenshot(request);
            case CLICK_BY_IMAGE:
                return clickByImage(request);
            case FIND_TEXT:
                return findTextByOCR(request);
            case WAIT_FOR_IMAGE:
                return waitForImage(request);
            case OCR_RECOGNITION:
                return performOCR(request);
            default:
                return A2COperationResult.unsupportedOperation(request.getOperationType());
        }
    }

    /**
     * 使用SikuliX截取屏幕
     */
    private A2COperationResult takeScreenshot(A2COperationRequest request) {
        try {
            ScreenImage screenshot = screen.capture();
            String imagePath = imageStorageService.saveScreenshot(screenshot, request.getUserId());

            return A2COperationResult.success(Map.of(
                "imagePath", imagePath,
                "width", screenshot.getWidth(),
                "height", screenshot.getHeight(),
                "component", "SikuliX"
            ));
        } catch (Exception e) {
            log.error("SikuliX截图失败", e);
            return A2COperationResult.executionError("SikuliX截图失败: " + e.getMessage());
        }
    }

    /**
     * 基于图像识别点击
     */
    private A2COperationResult clickByImage(A2COperationRequest request) {
        try {
            String imagePath = request.getParameter("imagePath");
            double similarity = Double.parseDouble(request.getParameter("similarity", "0.8"));

            Pattern pattern = new Pattern(imagePath).similar(similarity);
            Match match = screen.find(pattern);
            match.click();

            return A2COperationResult.success(Map.of(
                "clickedAt", Map.of("x", match.getX(), "y", match.getY()),
                "similarity", match.getScore(),
                "component", "SikuliX"
            ));
        } catch (FindFailed e) {
            log.warn("SikuliX未找到图像: {}", request.getParameter("imagePath"));
            return A2COperationResult.executionError("未找到匹配的图像");
        } catch (Exception e) {
            log.error("SikuliX图像点击失败", e);
            return A2COperationResult.executionError("图像点击失败: " + e.getMessage());
        }
    }

    /**
     * OCR文字识别
     */
    private A2COperationResult findTextByOCR(A2COperationRequest request) {
        try {
            String targetText = request.getParameter("text");

            // 使用SikuliX的OCR功能
            String screenText = screen.text();
            boolean found = screenText.contains(targetText);

            return A2COperationResult.success(Map.of(
                "found", found,
                "targetText", targetText,
                "fullText", screenText,
                "component", "SikuliX"
            ));
        } catch (Exception e) {
            log.error("SikuliX OCR识别失败", e);
            return A2COperationResult.executionError("OCR识别失败: " + e.getMessage());
        }
    }
}
```

### 3. TestFX适配器

#### TestFXAdapter - TestFX组件适配器
```java
/**
 * TestFX适配器 - JavaFX应用程序控制
 * 基于TestFX开源库实现JavaFX应用程序的UI控制和测试
 */
@Component
@Slf4j
public class TestFXAdapter implements A2CAdapter {

    private final FxRobot robot;
    private final JavaFXApplicationService javaFXApplicationService;

    public TestFXAdapter(JavaFXApplicationService javaFXApplicationService) {
        this.robot = new FxRobot();
        this.javaFXApplicationService = javaFXApplicationService;
    }

    @Override
    public String getComponentName() {
        return "TestFX";
    }

    @Override
    public boolean supports(A2COperationType operationType) {
        return operationType == A2COperationType.CLICK_JAVAFX_NODE ||
               operationType == A2COperationType.TYPE_IN_JAVAFX ||
               operationType == A2COperationType.VERIFY_JAVAFX_STATE ||
               operationType == A2COperationType.FIND_JAVAFX_ELEMENT;
    }

    @Override
    public A2COperationResult executeOperation(A2COperationRequest request) {
        switch (request.getOperationType()) {
            case CLICK_JAVAFX_NODE:
                return clickJavaFXNode(request);
            case TYPE_IN_JAVAFX:
                return typeInJavaFX(request);
            case VERIFY_JAVAFX_STATE:
                return verifyJavaFXState(request);
            case FIND_JAVAFX_ELEMENT:
                return findJavaFXElement(request);
            default:
                return A2COperationResult.unsupportedOperation(request.getOperationType());
        }
    }

    /**
     * 点击JavaFX节点
     */
    private A2COperationResult clickJavaFXNode(A2COperationRequest request) {
        try {
            String selector = request.getParameter("selector");
            int waitTimeout = Integer.parseInt(request.getParameter("waitTimeout", "5000"));

            robot.clickOn(selector);

            return A2COperationResult.success(Map.of(
                "clicked", selector,
                "component", "TestFX"
            ));
        } catch (Exception e) {
            log.error("TestFX JavaFX点击失败", e);
            return A2COperationResult.executionError("JavaFX点击失败: " + e.getMessage());
        }
    }

    /**
     * 在JavaFX中输入文本
     */
    private A2COperationResult typeInJavaFX(A2COperationRequest request) {
        try {
            String text = request.getParameter("text");
            robot.write(text);

            return A2COperationResult.success(Map.of(
                "typed", text,
                "component", "TestFX"
            ));
        } catch (Exception e) {
            log.error("TestFX JavaFX输入失败", e);
            return A2COperationResult.executionError("JavaFX输入失败: " + e.getMessage());
        }
    }

    /**
     * 验证JavaFX应用状态
     */
    private A2COperationResult verifyJavaFXState(A2COperationRequest request) {
        try {
            String selector = request.getParameter("selector");
            String expectedState = request.getParameter("expectedState");

            // 使用TestFX验证元素状态
            boolean stateMatches = javaFXApplicationService.verifyElementState(selector, expectedState);

            return A2COperationResult.success(Map.of(
                "verified", stateMatches,
                "selector", selector,
                "expectedState", expectedState,
                "component", "TestFX"
            ));
        } catch (Exception e) {
            log.error("TestFX状态验证失败", e);
            return A2COperationResult.executionError("JavaFX状态验证失败: " + e.getMessage());
        }
    }

    /**
     * 查找JavaFX元素
     */
    private A2COperationResult findJavaFXElement(A2COperationRequest request) {
        try {
            String selector = request.getParameter("selector");

            // 使用TestFX查找元素
            boolean elementExists = javaFXApplicationService.elementExists(selector);

            return A2COperationResult.success(Map.of(
                "found", elementExists,
                "selector", selector,
                "component", "TestFX"
            ));
        } catch (Exception e) {
            log.error("TestFX元素查找失败", e);
            return A2COperationResult.executionError("JavaFX元素查找失败: " + e.getMessage());
        }
    }
}
```

### 4. AWT Robot适配器

#### AWTRobotAdapter - AWT Robot组件适配器
```java
/**
 * AWT Robot适配器 - 基础系统级操作
 * 基于Java AWT Robot实现基础的鼠标键盘操作
 */
@Component
@Slf4j
public class AWTRobotAdapter implements A2CAdapter {

    private final Robot robot;
    private final A2CRobotConfig robotConfig;

    public AWTRobotAdapter(A2CRobotConfig robotConfig) throws AWTException {
        this.robot = new Robot();
        this.robotConfig = robotConfig;
        this.robot.setAutoDelay(robotConfig.getAutoDelay());
    }

    @Override
    public String getComponentName() {
        return "AWT Robot";
    }

    @Override
    public boolean supports(A2COperationType operationType) {
        return operationType == A2COperationType.CLICK_COORDINATE ||
               operationType == A2COperationType.TYPE_TEXT ||
               operationType == A2COperationType.KEY_PRESS ||
               operationType == A2COperationType.MOUSE_MOVE ||
               operationType == A2COperationType.SCROLL;
    }

    @Override
    public A2COperationResult executeOperation(A2COperationRequest request) {
        switch (request.getOperationType()) {
            case CLICK_COORDINATE:
                return clickCoordinate(request);
            case TYPE_TEXT:
                return typeText(request);
            case KEY_PRESS:
                return pressKey(request);
            case MOUSE_MOVE:
                return moveMouse(request);
            case SCROLL:
                return scroll(request);
            default:
                return A2COperationResult.unsupportedOperation(request.getOperationType());
        }
    }

    /**
     * 坐标点击
     */
    private A2COperationResult clickCoordinate(A2COperationRequest request) {
        try {
            int x = Integer.parseInt(request.getParameter("x"));
            int y = Integer.parseInt(request.getParameter("y"));
            String button = request.getParameter("button", "LEFT");

            robot.mouseMove(x, y);

            int mouseButton = getMouseButton(button);
            robot.mousePress(mouseButton);
            robot.mouseRelease(mouseButton);

            return A2COperationResult.success(Map.of(
                "clickedAt", Map.of("x", x, "y", y),
                "button", button,
                "component", "AWT Robot"
            ));
        } catch (Exception e) {
            log.error("AWT Robot坐标点击失败", e);
            return A2COperationResult.executionError("坐标点击失败: " + e.getMessage());
        }
    }

    /**
     * 文本输入
     */
    private A2COperationResult typeText(A2COperationRequest request) {
        try {
            String text = request.getParameter("text");

            for (char c : text.toCharArray()) {
                int keyCode = KeyEvent.getExtendedKeyCodeForChar(c);
                if (keyCode != KeyEvent.VK_UNDEFINED) {
                    robot.keyPress(keyCode);
                    robot.keyRelease(keyCode);
                }
            }

            return A2COperationResult.success(Map.of(
                "typed", text,
                "component", "AWT Robot"
            ));
        } catch (Exception e) {
            log.error("AWT Robot文本输入失败", e);
            return A2COperationResult.executionError("文本输入失败: " + e.getMessage());
        }
    }

    /**
     * 按键操作
     */
    private A2COperationResult pressKey(A2COperationRequest request) {
        try {
            String keyName = request.getParameter("key");
            boolean hold = Boolean.parseBoolean(request.getParameter("hold", "false"));

            int keyCode = getKeyCode(keyName);

            if (hold) {
                robot.keyPress(keyCode);
            } else {
                robot.keyPress(keyCode);
                robot.keyRelease(keyCode);
            }

            return A2COperationResult.success(Map.of(
                "key", keyName,
                "hold", hold,
                "component", "AWT Robot"
            ));
        } catch (Exception e) {
            log.error("AWT Robot按键操作失败", e);
            return A2COperationResult.executionError("按键操作失败: " + e.getMessage());
        }
    }

    /**
     * 鼠标移动
     */
    private A2COperationResult moveMouse(A2COperationRequest request) {
        try {
            int x = Integer.parseInt(request.getParameter("x"));
            int y = Integer.parseInt(request.getParameter("y"));

            robot.mouseMove(x, y);

            return A2COperationResult.success(Map.of(
                "movedTo", Map.of("x", x, "y", y),
                "component", "AWT Robot"
            ));
        } catch (Exception e) {
            log.error("AWT Robot鼠标移动失败", e);
            return A2COperationResult.executionError("鼠标移动失败: " + e.getMessage());
        }
    }

    /**
     * 滚动操作
     */
    private A2COperationResult scroll(A2COperationRequest request) {
        try {
            int wheelRotation = Integer.parseInt(request.getParameter("wheelRotation", "1"));

            robot.mouseWheel(wheelRotation);

            return A2COperationResult.success(Map.of(
                "wheelRotation", wheelRotation,
                "component", "AWT Robot"
            ));
        } catch (Exception e) {
            log.error("AWT Robot滚动操作失败", e);
            return A2COperationResult.executionError("滚动操作失败: " + e.getMessage());
        }
    }

    private int getMouseButton(String button) {
        switch (button.toUpperCase()) {
            case "LEFT":
                return InputEvent.BUTTON1_DOWN_MASK;
            case "RIGHT":
                return InputEvent.BUTTON3_DOWN_MASK;
            case "MIDDLE":
                return InputEvent.BUTTON2_DOWN_MASK;
            default:
                return InputEvent.BUTTON1_DOWN_MASK;
        }
    }

    private int getKeyCode(String keyName) {
        // 简化的键码映射，实际应用中需要更完整的映射
        switch (keyName.toUpperCase()) {
            case "ENTER":
                return KeyEvent.VK_ENTER;
            case "SPACE":
                return KeyEvent.VK_SPACE;
            case "TAB":
                return KeyEvent.VK_TAB;
            case "ESC":
                return KeyEvent.VK_ESCAPE;
            case "CTRL":
                return KeyEvent.VK_CONTROL;
            case "ALT":
                return KeyEvent.VK_ALT;
            case "SHIFT":
                return KeyEvent.VK_SHIFT;
            default:
                return KeyEvent.getExtendedKeyCodeForChar(keyName.charAt(0));
        }
    }
}
```

### 5. 操作路由器

#### A2COperationRouter - A2C操作路由器
```java
/**
 * A2C操作路由器
 * 根据操作类型智能选择最适合的开源组件
 */
@Component
@Slf4j
public class A2COperationRouter {

    private final List<A2CAdapter> adapters;
    private final Map<A2COperationType, A2CAdapter> routingCache;

    public A2COperationRouter(List<A2CAdapter> adapters) {
        this.adapters = adapters;
        this.routingCache = new ConcurrentHashMap<>();
        initializeRouting();
    }

    /**
     * 选择最适合的适配器
     */
    public A2CAdapter selectAdapter(A2COperationRequest request) {
        A2COperationType operationType = request.getOperationType();

        // 优先从缓存获取
        A2CAdapter cachedAdapter = routingCache.get(operationType);
        if (cachedAdapter != null && cachedAdapter.supports(operationType)) {
            return cachedAdapter;
        }

        // 动态选择适配器
        A2CAdapter selectedAdapter = adapters.stream()
            .filter(adapter -> adapter.supports(operationType))
            .findFirst()
            .orElseThrow(() -> new UnsupportedOperationException("不支持的操作类型: " + operationType));

        // 更新缓存
        routingCache.put(operationType, selectedAdapter);

        log.debug("为操作类型 {} 选择了适配器: {}", operationType, selectedAdapter.getComponentName());

        return selectedAdapter;
    }

    /**
     * 初始化路由映射
     */
    private void initializeRouting() {
        for (A2CAdapter adapter : adapters) {
            log.info("注册A2C适配器: {} - {}", adapter.getComponentName(), adapter.getClass().getSimpleName());
        }
    }

    /**
     * 获取所有可用的适配器
     */
    public List<A2CAdapter> getAvailableAdapters() {
        return new ArrayList<>(adapters);
    }

    /**
     * 获取支持指定操作类型的适配器
     */
    public List<A2CAdapter> getAdaptersForOperation(A2COperationType operationType) {
        return adapters.stream()
            .filter(adapter -> adapter.supports(operationType))
            .collect(Collectors.toList());
    }
}
```

### 6. 适配器接口

#### A2CAdapter - A2C适配器接口
```java
/**
 * A2C适配器接口
 * 定义所有A2C组件适配器的统一接口
 */
public interface A2CAdapter {

    /**
     * 获取组件名称
     * @return 组件名称
     */
    String getComponentName();

    /**
     * 检查是否支持指定的操作类型
     * @param operationType 操作类型
     * @return 是否支持
     */
    boolean supports(A2COperationType operationType);

    /**
     * 执行A2C操作
     * @param request 操作请求
     * @return 操作结果
     */
    A2COperationResult executeOperation(A2COperationRequest request);

    /**
     * 获取适配器状态
     * @return 适配器状态
     */
    default A2CAdapterStatus getStatus() {
        return A2CAdapterStatus.ACTIVE;
    }

    /**
     * 获取支持的操作类型列表
     * @return 支持的操作类型
     */
    default List<A2COperationType> getSupportedOperations() {
        return Arrays.stream(A2COperationType.values())
            .filter(this::supports)
            .collect(Collectors.toList());
    }
}
```

#### A2COperationType - A2C操作类型枚举
```java
/**
 * A2C操作类型枚举
 */
public enum A2COperationType {
    // SikuliX支持的操作
    SCREENSHOT("截图"),
    CLICK_BY_IMAGE("基于图像点击"),
    FIND_TEXT("OCR文字查找"),
    WAIT_FOR_IMAGE("等待图像出现"),
    OCR_RECOGNITION("OCR识别"),

    // TestFX支持的操作
    CLICK_JAVAFX_NODE("JavaFX节点点击"),
    TYPE_IN_JAVAFX("JavaFX文本输入"),
    VERIFY_JAVAFX_STATE("JavaFX状态验证"),
    FIND_JAVAFX_ELEMENT("JavaFX元素查找"),

    // AWT Robot支持的操作
    CLICK_COORDINATE("坐标点击"),
    TYPE_TEXT("文本输入"),
    KEY_PRESS("按键操作"),
    MOUSE_MOVE("鼠标移动"),
    SCROLL("滚动操作"),

    // 通用操作
    BATCH_OPERATION("批量操作"),
    CUSTOM_OPERATION("自定义操作");

    private final String description;

    A2COperationType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
```

## 数据模型定义

### 1. A2C相关实体

#### A2COperationLog - A2C操作日志实体
```java
/**
 * A2C操作日志实体
 * 记录A2C操作的详细信息
 */
@Entity
@Table(name = "a2c_operation_logs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class A2COperationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String operationId;

    @Column(nullable = false)
    private String userId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private A2COperationType operationType;

    @Column(columnDefinition = "JSON")
    private Map<String, Object> parameters;

    @Column(nullable = false)
    private String componentName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private A2COperationStatus status;

    @Column(columnDefinition = "JSON")
    private Object result;

    private String errorMessage;

    @Column(nullable = false)
    private LocalDateTime startedAt;

    private LocalDateTime completedAt;

    private Long executionTimeMs;

    @PrePersist
    protected void onCreate() {
        startedAt = LocalDateTime.now();
    }
}
```

## 技术集成配置

### 1. 开源组件依赖配置

#### Maven依赖
```xml
<!-- SikuliX API -->
<dependency>
    <groupId>com.sikulix</groupId>
    <artifactId>sikulixapi</artifactId>
    <version>2.0.5</version>
</dependency>

<!-- TestFX Core -->
<dependency>
    <groupId>org.testfx</groupId>
    <artifactId>testfx-core</artifactId>
    <version>4.0.18</version>
</dependency>

<!-- TestFX JUnit5 -->
<dependency>
    <groupId>org.testfx</groupId>
    <artifactId>testfx-junit5</artifactId>
    <version>4.0.18</version>
</dependency>

<!-- Java AWT Robot (JDK内置) -->
<!-- 无需额外依赖 -->
```

#### 配置文件
```yaml
# application.yml
a2c:
  # 开源组件配置
  opensource:
    enabled: true

    # SikuliX配置
    sikulix:
      enabled: true
      image_similarity: 0.8
      ocr_enabled: true
      screenshot_path: "./data/screenshots"

    # TestFX配置
    testfx:
      enabled: true
      headless: false
      timeout_seconds: 30

    # AWT Robot配置
    awt_robot:
      enabled: true
      auto_delay: 50

  # 安全配置
  security:
    permission_required: true
    safe_coordinates_only: true
    allowed_applications: ["notepad.exe", "calculator.exe"]
    blocked_commands: ["rm", "del", "format"]
```

### 2. 组件配置类

```java
@Configuration
@EnableConfigurationProperties(A2CProperties.class)
public class A2COpenSourceConfig {

    @Bean
    @ConditionalOnProperty(name = "a2c.opensource.sikulix.enabled", havingValue = "true")
    public SikuliXAdapter sikuliXAdapter(A2CProperties properties) {
        return new SikuliXAdapter(
            properties.getOpensource().getSikulix(),
            imageStorageService()
        );
    }

    @Bean
    @ConditionalOnProperty(name = "a2c.opensource.testfx.enabled", havingValue = "true")
    public TestFXAdapter testFXAdapter(A2CProperties properties) {
        return new TestFXAdapter(
            properties.getOpensource().getTestfx(),
            javaFXApplicationService()
        );
    }

    @Bean
    @ConditionalOnProperty(name = "a2c.opensource.awt_robot.enabled", havingValue = "true")
    public AWTRobotAdapter awtRobotAdapter(A2CProperties properties) throws AWTException {
        return new AWTRobotAdapter(properties.getOpensource().getAwtRobot());
    }

    @Bean
    public A2COperationRouter operationRouter(List<A2CAdapter> adapters) {
        return new A2COperationRouter(adapters);
    }
}
```

## 使用示例

### 基于开源组件的A2C交互操作
```java
// 注入开源组件A2C交互服务
@Autowired
private OpenSourceA2CInteractionService a2cInteractionService;

// 1. SikuliX - 基于图像识别的截图和点击
A2COperationRequest screenshotRequest = A2COperationRequest.builder()
    .userId("user123")
    .operationType(A2COperationType.SCREENSHOT)
    .parameters(Map.of(
        "captureArea", new Rectangle(0, 0, 1920, 1080),
        "processImage", true
    ))
    .build();

A2COperationResult screenshotResult = a2cInteractionService.executeOperation(screenshotRequest);
if (screenshotResult.isSuccess()) {
    System.out.println("SikuliX截图成功: " + screenshotResult.getResult().get("imagePath"));
    System.out.println("使用组件: " + screenshotResult.getResult().get("component"));
}

// 2. SikuliX - 基于图像识别的点击
A2COperationRequest imageClickRequest = A2COperationRequest.builder()
    .userId("user123")
    .operationType(A2COperationType.CLICK_BY_IMAGE)
    .parameters(Map.of(
        "imagePath", "./images/button.png",
        "similarity", 0.8
    ))
    .build();

A2COperationResult imageClickResult = a2cInteractionService.executeOperation(imageClickRequest);
if (imageClickResult.isSuccess()) {
    System.out.println("SikuliX图像点击成功: " + imageClickResult.getResult().get("clickedAt"));
}

// 3. TestFX - JavaFX应用控制
A2COperationRequest javafxClickRequest = A2COperationRequest.builder()
    .userId("user123")
    .operationType(A2COperationType.CLICK_JAVAFX_NODE)
    .parameters(Map.of(
        "selector", "#submitButton",
        "waitTimeout", 5000
    ))
    .build();

A2COperationResult javafxClickResult = a2cInteractionService.executeOperation(javafxClickRequest);
if (javafxClickResult.isSuccess()) {
    System.out.println("TestFX JavaFX点击成功");
}

// 4. AWT Robot - 基础坐标点击
A2COperationRequest coordinateClickRequest = A2COperationRequest.builder()
    .userId("user123")
    .operationType(A2COperationType.CLICK_COORDINATE)
    .parameters(Map.of(
        "x", 500,
        "y", 300,
        "button", "LEFT"
    ))
    .build();

A2COperationResult coordinateClickResult = a2cInteractionService.executeOperation(coordinateClickRequest);
if (coordinateClickResult.isSuccess()) {
    System.out.println("AWT Robot坐标点击成功");
}

// 5. SikuliX - OCR文字识别
A2COperationRequest ocrRequest = A2COperationRequest.builder()
    .userId("user123")
    .operationType(A2COperationType.FIND_TEXT)
    .parameters(Map.of("text", "确认"))
    .build();

A2COperationResult ocrResult = a2cInteractionService.executeOperation(ocrRequest);
if (ocrResult.isSuccess()) {
    Boolean found = (Boolean) ocrResult.getResult().get("found");
    System.out.println("SikuliX OCR识别结果: " + (found ? "找到文字" : "未找到文字"));
}
```

## 技术优势

### 开源组件方案 vs 自研方案对比

| 特性 | 自研方案 | 开源组件方案 |
|------|----------|-------------|
| **开发成本** | 高 (需要从零开发) | 低 (集成现有库) |
| **维护成本** | 高 (需要持续维护) | 低 (社区维护) |
| **功能丰富度** | 有限 (基础功能) | 丰富 (成熟功能) |
| **跨平台兼容性** | 需要大量测试 | 已验证 |
| **稳定性** | 需要长期验证 | 经过社区验证 |
| **更新频率** | 依赖团队资源 | 社区驱动 |
| **文档质量** | 需要自写 | 完善的官方文档 |
| **社区支持** | 无 | 活跃的社区 |
| **学习曲线** | 陡峭 (需要深入理解) | 平缓 (有现成示例) |

### 组件选择优势

#### SikuliX (3k+ stars)
- ✅ **图像识别**: 基于OpenCV的强大图像识别
- ✅ **OCR功能**: 内置文字识别能力
- ✅ **跨平台**: 支持Windows/macOS/Linux
- ✅ **成熟稳定**: 多年开发和社区验证

#### TestFX (942+ stars)
- ✅ **JavaFX专用**: 专为JavaFX应用设计
- ✅ **简洁API**: 易于使用的流畅API
- ✅ **测试集成**: 与JUnit/TestNG完美集成
- ✅ **元素查找**: 强大的UI元素定位能力

#### AWT Robot (JDK内置)
- ✅ **零依赖**: JDK内置，无需额外依赖
- ✅ **基础可靠**: 经过长期验证的基础API
- ✅ **轻量级**: 最小的资源占用
- ✅ **兼容性**: 与所有Java版本兼容

## 迁移建议

### 渐进式迁移策略

1. **第一阶段**: 集成SikuliX进行图像识别功能
2. **第二阶段**: 添加TestFX支持JavaFX应用控制
3. **第三阶段**: 使用AWT Robot处理基础操作
4. **第四阶段**: 移除自研代码，完全基于开源组件

### 配置兼容性

```yaml
# 支持新旧方案切换
a2c:
  implementation: "opensource"  # "custom" or "opensource"

  # 开源组件配置
  opensource:
    enabled: true
    primary_component: "sikulix"  # 主要使用的组件

  # 自研方案配置 (向后兼容)
  custom:
    enabled: false
```

---

**文档版本**: v2.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**技术架构**: SikuliX + TestFX + AWT Robot 开源组件集成架构
**文档说明**: 基于开源组件的A2C交互实现，提供图像识别、JavaFX控制、系统级操作等功能，大幅降低开发和维护成本
