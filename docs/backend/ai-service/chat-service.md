# 聊天服务 (Chat Service)

## 模块概述

聊天服务是AI服务的核心模块，采用**Spring AI + Reactor + WebFlux**的现代化集成架构，基于成熟的开源组件提供企业级的聊天处理、上下文管理、流式响应等功能。

**技术架构**: Spring AI + Project Reactor (45.8k+ stars) + Spring WebFlux

**基于Ark-Pets二次开发的特殊需求**:
- 基于Spring AI的统一AI模型抽象和多提供商支持
- 基于Reactor的高性能异步流式处理
- Spring WebFlux的原生SSE和WebSocket支持
- 明日方舟角色的个性化对话和背景故事集成
- 与桌宠动画和行为系统的响应式联动

## 核心功能

### 1. 基于开源组件的聊天处理器 (OpenSourceChatProcessor)

#### 功能描述
基于Spring AI + Reactor的聊天请求处理，集成企业级AI模型抽象、高性能异步处理、响应式编程等功能。

#### 核心函数

```java
@Service
@Slf4j
public class OpenSourceChatProcessor {

    // Spring AI ChatClient
    private final ChatClient chatClient;

    // 业务适配组件
    private final ChatBusinessAdapter chatAdapter;
    private final ContextManager contextManager;
    private final MessageValidator messageValidator;

    /**
     * 处理聊天消息 (使用Spring AI + Reactor)
     * @param userId 用户ID
     * @param request 聊天请求 (包含角色ID和性格设定)
     * @return 聊天响应 (包含动画提示和情感标签)
     */
    public Mono<ChatResponse> processChatMessage(String userId, ChatRequest request) {
        return Mono.fromCallable(() -> {
            // 1. 验证请求
            MessageValidationResult validation = messageValidator.validateMessage(request.getMessage(), userId);
            if (!validation.isValid()) {
                throw new ChatValidationException(validation.getErrorMessage());
            }

            // 2. 获取或创建对话
            return chatAdapter.getOrCreateConversation(userId, request);
        })
        .flatMap(conversation -> {
            // 3. 构建上下文
            return contextManager.buildContext(conversation, request.getMessage())
                .flatMap(context -> {
                    // 4. 使用Spring AI处理聊天
                    String prompt = chatAdapter.buildPrompt(context, request);

                    return Mono.fromCallable(() -> chatClient.prompt()
                        .user(prompt)
                        .call()
                        .content())
                    .map(response -> {
                        // 5. 分析响应并构建结果
                        ResponseAnalysis analysis = chatAdapter.analyzeResponse(response, conversation);

                        // 6. 保存消息
                        chatAdapter.saveMessages(conversation, request.getMessage(), response);

                        return ChatResponse.builder()
                            .conversationId(conversation.getId())
                            .content(response)
                            .characterName(conversation.getCharacterName())
                            .emotionType(analysis.getEmotionType())
                            .animationHint(analysis.getAnimationHint())
                            .behaviorHint(analysis.getBehaviorHint())
                            .timestamp(LocalDateTime.now())
                            .build();
                    });
                });
        })
        .doOnError(error -> log.error("聊天处理失败: userId={}", userId, error));
    }
    
    /**
     * 处理流式聊天 (使用Reactor + Spring AI)
     * @param userId 用户ID
     * @param request 聊天请求
     * @return 流式响应
     */
    public Flux<ServerSentEvent<String>> processStreamChat(String userId, ChatRequest request) {
        return Mono.fromCallable(() -> {
            // 1. 验证和准备
            MessageValidationResult validation = messageValidator.validateMessage(request.getMessage(), userId);
            if (!validation.isValid()) {
                throw new ChatValidationException(validation.getErrorMessage());
            }

            return chatAdapter.getOrCreateConversation(userId, request);
        })
        .flatMapMany(conversation -> {
            // 2. 构建上下文
            return contextManager.buildContext(conversation, request.getMessage())
                .flatMapMany(context -> {
                    String prompt = chatAdapter.buildPrompt(context, request);

                    // 3. 使用Spring AI流式处理
                    return chatClient.prompt()
                        .user(prompt)
                        .stream()
                        .content()
                        .map(token -> ServerSentEvent.<String>builder()
                            .data(token)
                            .event("token")
                            .build())
                        .concatWith(Mono.just(ServerSentEvent.<String>builder()
                            .data("[DONE]")
                            .event("complete")
                            .build()));
                });
        })
        .doOnError(error -> log.error("流式聊天处理失败: userId={}", userId, error))
        .onErrorResume(error -> Flux.just(ServerSentEvent.<String>builder()
            .data("处理失败: " + error.getMessage())
            .event("error")
            .build()));
    }
    
}
```

## 技术集成配置

### 1. 开源组件依赖配置

#### Maven依赖
```xml
<!-- Spring AI -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-core</artifactId>
</dependency>

<!-- Spring AI OpenAI -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
</dependency>

<!-- Spring AI Anthropic -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-anthropic-spring-boot-starter</artifactId>
</dependency>

<!-- Spring WebFlux -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>

<!-- Reactor Core -->
<dependency>
    <groupId>io.projectreactor</groupId>
    <artifactId>reactor-core</artifactId>
</dependency>

<!-- Reactor Netty -->
<dependency>
    <groupId>io.projectreactor.netty</groupId>
    <artifactId>reactor-netty-http</artifactId>
</dependency>
```

#### 配置文件
```yaml
# application.yml
spring:
  ai:
    # OpenAI配置
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: gpt-4
          temperature: 0.7
          max-tokens: 2048

    # Anthropic配置
    anthropic:
      api-key: ${ANTHROPIC_API_KEY}
      base-url: ${ANTHROPIC_BASE_URL:https://api.anthropic.com}
      chat:
        options:
          model: claude-3-sonnet-20240229
          max-tokens: 2048

  # WebFlux配置
  webflux:
    multipart:
      max-in-memory-size: 10MB
      max-disk-usage-per-part: 100MB

# 聊天服务配置
chat:
  # 流式响应配置
  streaming:
    enabled: true
    timeout: 30s
    buffer-size: 1024

  # 上下文管理
  context:
    max-tokens: 8192
    compression-threshold: 6144
    history-limit: 50

  # 消息验证
  validation:
    max-length: 4000
    rate-limit: 60
    content-filter: true
```

### 2. 上下文管理器 (ContextManager)

#### 功能描述
管理对话上下文，包括历史消息、角色设定、上下文压缩等。

#### 核心函数

```java
@Component
public class ContextManager {
    
    /**
     * 构建对话上下文
     * @param conversationId 对话ID
     * @param newMessage 新消息
     * @param maxTokens 最大Token数
     * @return 上下文消息列表
     */
    public List<ContextMessage> buildConversationContext(String conversationId, 
                                                        String newMessage, int maxTokens);
    
    /**
     * 压缩对话历史
     * @param messages 历史消息列表
     * @param targetTokens 目标Token数
     * @return 压缩后的消息列表
     */
    public List<ContextMessage> compressConversationHistory(List<Message> messages, 
                                                           int targetTokens);
    
    /**
     * 添加系统提示词
     * @param personalityId 性格ID
     * @param contextMessages 上下文消息列表
     * @return 添加系统提示词后的消息列表
     */
    public List<ContextMessage> addSystemPrompt(String personalityId, 
                                              List<ContextMessage> contextMessages);
    
    /**
     * 计算上下文Token数量
     * @param messages 消息列表
     * @return Token数量
     */
    public int calculateContextTokens(List<ContextMessage> messages);
    
    /**
     * 获取上下文摘要
     * @param conversationId 对话ID
     * @return 上下文摘要
     */
    public String getContextSummary(String conversationId);
    
    /**
     * 更新上下文摘要
     * @param conversationId 对话ID
     * @param newMessages 新消息列表
     */
    public void updateContextSummary(String conversationId, List<Message> newMessages);
    
    /**
     * 清理过期上下文
     * @param conversationId 对话ID
     * @param retentionDays 保留天数
     */
    public void cleanupExpiredContext(String conversationId, int retentionDays);
}
```

### 3. 流式响应处理器 (StreamResponseHandler)

#### 功能描述
处理AI模型的流式响应，实现实时Token推送。

#### 核心函数

```java
@Component
public class StreamResponseHandler {
    
    /**
     * 创建流式响应处理器
     * @param userId 用户ID
     * @param conversationId 对话ID
     * @param emitter SSE发射器
     * @return 流式回调处理器
     */
    public StreamCallback createStreamCallback(String userId, String conversationId, 
                                             SseEmitter emitter);
    
    /**
     * 处理Token流
     * @param token 接收到的Token
     * @param emitter SSE发射器
     * @param responseBuilder 响应构建器
     */
    public void handleTokenStream(String token, SseEmitter emitter, 
                                StringBuilder responseBuilder);
    
    /**
     * 处理流式完成
     * @param fullResponse 完整响应
     * @param tokens Token数量
     * @param conversation 对话信息
     * @param emitter SSE发射器
     */
    public void handleStreamComplete(String fullResponse, int tokens, 
                                   Conversation conversation, SseEmitter emitter);
    
    /**
     * 处理流式错误
     * @param error 错误信息
     * @param emitter SSE发射器
     */
    public void handleStreamError(Exception error, SseEmitter emitter);
    
    /**
     * 管理活跃的流式连接
     * @param userId 用户ID
     * @param conversationId 对话ID
     * @param emitter SSE发射器
     */
    public void manageActiveStream(String userId, String conversationId, SseEmitter emitter);
    
    /**
     * 停止流式响应
     * @param userId 用户ID
     * @param conversationId 对话ID
     * @return 停止结果
     */
    public boolean stopStreamResponse(String userId, String conversationId);
}
```

### 4. 消息验证器 (MessageValidator)

#### 功能描述
验证用户输入消息的合法性，包括内容过滤、长度检查等。

#### 核心函数

```java
@Component
public class MessageValidator {
    
    /**
     * 验证消息内容
     * @param message 消息内容
     * @param userId 用户ID
     * @return 验证结果
     */
    public MessageValidationResult validateMessage(String message, String userId);
    
    /**
     * 检查消息长度
     * @param message 消息内容
     * @param maxLength 最大长度
     * @return 长度检查结果
     */
    public boolean checkMessageLength(String message, int maxLength);
    
    /**
     * 内容安全过滤
     * @param message 消息内容
     * @return 过滤结果
     */
    public ContentFilterResult filterMessageContent(String message);
    
    /**
     * 检查用户发送频率
     * @param userId 用户ID
     * @return 频率检查结果
     */
    public RateLimitResult checkUserSendRate(String userId);
    
    /**
     * 验证对话权限
     * @param userId 用户ID
     * @param conversationId 对话ID
     * @return 权限验证结果
     */
    public boolean validateConversationAccess(String userId, String conversationId);
    
    /**
     * 清理和标准化消息
     * @param message 原始消息
     * @return 清理后的消息
     */
    public String sanitizeMessage(String message);
}
```

## API接口实现

### 1. 聊天控制器 (ChatController)

```java
@RestController
@RequestMapping("/api/v1/ai")
@Validated
@Slf4j
public class ChatController {
    
    private final ChatProcessor chatProcessor;
    private final ConversationService conversationService;
    private final MessageValidator messageValidator;
    
    /**
     * 发送聊天消息
     * POST /api/v1/ai/chat
     */
    @PostMapping("/chat")
    public ResponseEntity<ApiResponse<ChatResponse>> sendChatMessage(
            @RequestBody @Valid ChatRequest request,
            @RequestHeader("Authorization") String token) {
        
        try {
            String userId = extractUserIdFromToken(token);
            
            // 验证消息
            MessageValidationResult validation = messageValidator.validateMessage(
                request.getMessage(), userId);
            if (!validation.isValid()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(validation.getErrorMessage()));
            }
            
            // 处理聊天
            ChatResponse response = chatProcessor.processChatMessage(userId, request);
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (ChatProcessingException e) {
            log.error("Chat processing failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("聊天处理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 流式聊天
     * GET /api/v1/ai/chat/stream
     */
    @GetMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChatMessage(
            @RequestParam String conversationId,
            @RequestParam String message,
            @RequestHeader("Authorization") String token) {
        
        try {
            String userId = extractUserIdFromToken(token);
            
            // 验证消息
            MessageValidationResult validation = messageValidator.validateMessage(message, userId);
            if (!validation.isValid()) {
                SseEmitter emitter = new SseEmitter();
                emitter.completeWithError(new IllegalArgumentException(validation.getErrorMessage()));
                return emitter;
            }
            
            // 处理流式聊天
            return chatProcessor.processStreamChatMessage(userId, conversationId, message);
            
        } catch (Exception e) {
            log.error("Stream chat failed", e);
            SseEmitter emitter = new SseEmitter();
            emitter.completeWithError(e);
            return emitter;
        }
    }
    
    /**
     * 停止生成
     * POST /api/v1/ai/chat/stop
     */
    @PostMapping("/chat/stop")
    public ResponseEntity<ApiResponse<Void>> stopChatGeneration(
            @RequestBody @Valid StopGenerationRequest request,
            @RequestHeader("Authorization") String token) {
        
        try {
            String userId = extractUserIdFromToken(token);
            
            StopGenerationResult result = chatProcessor.stopMessageGeneration(
                userId, request.getConversationId());
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(ApiResponse.success(null));
            } else {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(result.getErrorMessage()));
            }
            
        } catch (Exception e) {
            log.error("Stop generation failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("停止生成失败"));
        }
    }
}
```

## 连接流程

### 1. 普通聊天处理流程

```mermaid
graph TD
    A[接收聊天请求] --> B[验证用户身份]
    B --> C[验证消息内容]
    C --> D[检查使用限额]
    D --> E[获取或创建对话]
    E --> F[构建上下文]
    F --> G[调用AI模型]
    G --> H[处理AI响应]
    H --> I[保存消息记录]
    I --> J[更新统计信息]
    J --> K[返回响应]
```

### 2. 流式聊天处理流程

```mermaid
graph TD
    A[建立SSE连接] --> B[验证请求参数]
    B --> C[创建流式回调]
    C --> D[发送消息到AI]
    D --> E[接收Token流]
    E --> F[推送Token到客户端]
    F --> G[检查是否完成]
    G -->|未完成| E
    G -->|完成| H[保存完整消息]
    H --> I[关闭SSE连接]
```

### 3. 上下文管理流程

```mermaid
graph TD
    A[获取历史消息] --> B[计算Token数量]
    B --> C[检查是否超限]
    C -->|超限| D[压缩历史消息]
    C -->|未超限| E[添加系统提示词]
    D --> E
    E --> F[构建最终上下文]
    F --> G[返回上下文消息]
```

## 数据模型

### 聊天请求模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatRequest {
    
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 4000, message = "消息长度不能超过4000字符")
    private String message;
    
    private String conversationId;
    private String petId;
    private String personalityId;
    private String modelProvider;
    private String modelName;
    
    @Valid
    private ModelParameters parameters;
}
```

### 聊天响应模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponse {
    private String conversationId;
    private String messageId;
    private String content;
    private Integer tokens;
    private String model;
    private LocalDateTime timestamp;
    private String finishReason;
    private Map<String, Object> metadata;
}
```

### 上下文消息模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContextMessage {
    private String role; // system, user, assistant
    private String content;
    private Integer tokens;
    private LocalDateTime timestamp;
    private Map<String, Object> metadata;
}
```

## 错误处理

### 异常类型定义

```java
public class ChatProcessingException extends Exception {
    
    public static class InvalidMessageException extends ChatProcessingException {
        public InvalidMessageException(String message) {
            super("无效消息: " + message);
        }
    }
    
    public static class ConversationNotFoundException extends ChatProcessingException {
        public ConversationNotFoundException(String conversationId) {
            super("对话不存在: " + conversationId);
        }
    }
    
    public static class AIModelException extends ChatProcessingException {
        public AIModelException(String message, Throwable cause) {
            super("AI模型调用失败: " + message, cause);
        }
    }
    
    public static class TokenLimitExceededException extends ChatProcessingException {
        public TokenLimitExceededException(int used, int limit) {
            super(String.format("Token使用量超限: %d/%d", used, limit));
        }
    }
    
    public static class RateLimitExceededException extends ChatProcessingException {
        public RateLimitExceededException(String message) {
            super("请求频率超限: " + message);
        }
    }
}
```

### 错误处理策略

1. **消息验证失败**: 返回具体的验证错误信息
2. **AI模型调用失败**: 重试机制，最终失败时返回友好错误信息
3. **Token超限**: 提示用户升级套餐或等待重置
4. **频率限制**: 提示用户稍后重试
5. **系统异常**: 记录详细日志，返回通用错误信息

## 性能优化

### 1. 缓存策略
- Redis缓存对话上下文
- 本地缓存系统提示词
- 缓存用户使用限额信息

### 2. 异步处理
- 异步保存消息记录
- 异步更新统计信息
- 异步发送通知

### 3. 连接池管理
- AI模型API连接池
- 数据库连接池优化
- Redis连接池配置

---

**模块负责人**: AI服务开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
