# 情感分析服务 (Emotion Analysis Service)

## 模块概述

情感分析服务负责分析用户消息和AI回复中的情感状态，为桌宠提供情感感知能力，实现更自然的情感交互和动画表现。通过多维度情感分析，让AI角色能够理解和回应用户的情感需求。

**服务特点**:
- 多语言情感识别支持
- 实时情感状态分析
- 情感强度量化评估
- 与动画系统的深度集成

## 核心功能

### 1. 情感分析控制器 (EmotionAnalysisController)

#### 功能描述
处理情感分析相关的HTTP请求，包括文本情感分析、情感历史查询、情感模型管理等操作。

#### 核心接口

```java
@RestController
@RequestMapping("/api/emotion")
@Validated
public class EmotionAnalysisController {
    
    private final EmotionAnalysisService emotionService;
    private final EmotionHistoryService historyService;
    private final EmotionModelService modelService;
    
    /**
     * 分析文本情感
     * @param request 情感分析请求
     * @return 情感分析响应
     */
    @PostMapping("/analyze")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<EmotionAnalysisResponse> analyzeEmotion(
            @Valid @RequestBody EmotionAnalysisRequest request) {
        
        try {
            // 1. 验证输入文本
            if (request.getText() == null || request.getText().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(EmotionAnalysisResponse.error("文本内容不能为空"));
            }
            
            if (request.getText().length() > 2000) {
                return ResponseEntity.badRequest()
                    .body(EmotionAnalysisResponse.error("文本长度不能超过2000字符"));
            }
            
            // 2. 执行情感分析
            EmotionResult result = emotionService.analyzeText(
                request.getText(), 
                request.getLanguage(), 
                request.getContext()
            );
            
            // 3. 记录分析历史
            if (request.getConversationId() != null) {
                historyService.recordEmotionAnalysis(
                    request.getConversationId(), 
                    request.getText(), 
                    result
                );
            }
            
            return ResponseEntity.ok(EmotionAnalysisResponse.success(result));
            
        } catch (Exception e) {
            logger.error("情感分析失败", e);
            return ResponseEntity.internalServerError()
                .body(EmotionAnalysisResponse.error("情感分析服务暂时不可用"));
        }
    }
    
    /**
     * 批量分析对话情感
     * @param request 批量分析请求
     * @return 批量分析响应
     */
    @PostMapping("/analyze/batch")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<BatchEmotionAnalysisResponse> analyzeBatchEmotion(
            @Valid @RequestBody BatchEmotionAnalysisRequest request) {
        
        try {
            // 验证批量请求
            if (request.getTexts() == null || request.getTexts().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(BatchEmotionAnalysisResponse.error("文本列表不能为空"));
            }
            
            if (request.getTexts().size() > 50) {
                return ResponseEntity.badRequest()
                    .body(BatchEmotionAnalysisResponse.error("批量分析最多支持50条文本"));
            }
            
            // 执行批量分析
            List<EmotionResult> results = emotionService.analyzeBatchTexts(
                request.getTexts(), 
                request.getLanguage()
            );
            
            return ResponseEntity.ok(BatchEmotionAnalysisResponse.success(results));
            
        } catch (Exception e) {
            logger.error("批量情感分析失败", e);
            return ResponseEntity.internalServerError()
                .body(BatchEmotionAnalysisResponse.error("批量情感分析失败"));
        }
    }
    
    /**
     * 获取对话情感历史
     * @param conversationId 对话ID
     * @param pageSize 页面大小
     * @param pageToken 页面令牌
     * @return 情感历史响应
     */
    @GetMapping("/history/{conversationId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<EmotionHistoryResponse> getEmotionHistory(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String pageToken) {
        
        try {
            EmotionHistoryResult result = historyService.getConversationEmotionHistory(
                conversationId, pageSize, pageToken);
            
            return ResponseEntity.ok(EmotionHistoryResponse.success(result));
            
        } catch (Exception e) {
            logger.error("获取情感历史失败", e);
            return ResponseEntity.internalServerError()
                .body(EmotionHistoryResponse.error("获取情感历史失败"));
        }
    }
    
    /**
     * 获取情感统计
     * @param conversationId 对话ID
     * @param timeRange 时间范围
     * @return 情感统计响应
     */
    @GetMapping("/stats/{conversationId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<EmotionStatsResponse> getEmotionStats(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "7d") String timeRange) {
        
        try {
            EmotionStats stats = emotionService.getConversationEmotionStats(
                conversationId, timeRange);
            
            return ResponseEntity.ok(EmotionStatsResponse.success(stats));
            
        } catch (Exception e) {
            logger.error("获取情感统计失败", e);
            return ResponseEntity.internalServerError()
                .body(EmotionStatsResponse.error("获取情感统计失败"));
        }
    }
    
    /**
     * 获取支持的情感类型
     * @return 情感类型列表响应
     */
    @GetMapping("/types")
    public ResponseEntity<EmotionTypesResponse> getSupportedEmotionTypes() {
        try {
            List<EmotionType> types = emotionService.getSupportedEmotionTypes();
            return ResponseEntity.ok(EmotionTypesResponse.success(types));
        } catch (Exception e) {
            logger.error("获取情感类型失败", e);
            return ResponseEntity.internalServerError()
                .body(EmotionTypesResponse.error("获取情感类型失败"));
        }
    }
    
    /**
     * 训练自定义情感模型
     * @param userId 用户ID
     * @param request 训练请求
     * @return 训练响应
     */
    @PostMapping("/model/train")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ModelTrainingResponse> trainCustomModel(
            @AuthenticationPrincipal String userId,
            @Valid @RequestBody ModelTrainingRequest request) {
        
        try {
            // 验证训练权限
            if (!modelService.canTrainCustomModel(userId)) {
                return ResponseEntity.badRequest()
                    .body(ModelTrainingResponse.error("无权限训练自定义模型"));
            }
            
            // 验证训练数据
            ValidationResult validation = modelService.validateTrainingData(request);
            if (!validation.isValid()) {
                return ResponseEntity.badRequest()
                    .body(ModelTrainingResponse.error(validation.getErrors()));
            }
            
            // 启动异步训练
            String trainingJobId = modelService.startTraining(userId, request);
            
            return ResponseEntity.accepted()
                .body(ModelTrainingResponse.accepted(trainingJobId));
            
        } catch (Exception e) {
            logger.error("启动模型训练失败", e);
            return ResponseEntity.internalServerError()
                .body(ModelTrainingResponse.error("启动模型训练失败"));
        }
    }
    
    /**
     * 获取模型训练状态
     * @param userId 用户ID
     * @param trainingJobId 训练任务ID
     * @return 训练状态响应
     */
    @GetMapping("/model/training/{trainingJobId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<TrainingStatusResponse> getTrainingStatus(
            @AuthenticationPrincipal String userId,
            @PathVariable String trainingJobId) {
        
        try {
            TrainingStatus status = modelService.getTrainingStatus(userId, trainingJobId);
            if (status == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(TrainingStatusResponse.success(status));
            
        } catch (Exception e) {
            logger.error("获取训练状态失败", e);
            return ResponseEntity.internalServerError()
                .body(TrainingStatusResponse.error("获取训练状态失败"));
        }
    }
}
```

### 2. 情感分析服务实现 (EmotionAnalysisService)

#### 功能描述
情感分析的核心业务逻辑实现，包括多种情感分析算法、模型管理、结果处理等。

#### 核心函数

```java
@Service
@Transactional
public class EmotionAnalysisServiceImpl implements EmotionAnalysisService {
    
    private final EmotionModelRepository modelRepository;
    private final EmotionAnalyzer primaryAnalyzer;
    private final EmotionAnalyzer backupAnalyzer;
    private final EmotionCache emotionCache;
    private final EmotionConfigProperties config;
    
    /**
     * 分析文本情感
     * @param text 待分析文本
     * @param language 语言代码
     * @param context 上下文信息
     * @return 情感分析结果
     */
    @Override
    public EmotionResult analyzeText(String text, String language, EmotionContext context) {
        try {
            // 1. 检查缓存
            String cacheKey = generateCacheKey(text, language);
            EmotionResult cachedResult = emotionCache.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }
            
            // 2. 预处理文本
            String processedText = preprocessText(text, language);
            
            // 3. 执行情感分析
            EmotionResult result = performEmotionAnalysis(processedText, language, context);
            
            // 4. 后处理结果
            result = postprocessResult(result, context);
            
            // 5. 缓存结果
            emotionCache.put(cacheKey, result, config.getCacheTtl());
            
            return result;
            
        } catch (Exception e) {
            logger.error("情感分析失败: text={}, language={}", text, language, e);
            return createFallbackResult();
        }
    }
    
    /**
     * 执行情感分析
     * @param text 预处理后的文本
     * @param language 语言代码
     * @param context 上下文信息
     * @return 情感分析结果
     */
    private EmotionResult performEmotionAnalysis(String text, String language, EmotionContext context) {
        try {
            // 使用主要分析器
            EmotionResult primaryResult = primaryAnalyzer.analyze(text, language, context);
            
            // 如果主要分析器结果置信度较低，使用备用分析器
            if (primaryResult.getConfidence() < config.getMinConfidenceThreshold()) {
                EmotionResult backupResult = backupAnalyzer.analyze(text, language, context);
                
                // 选择置信度更高的结果
                if (backupResult.getConfidence() > primaryResult.getConfidence()) {
                    return backupResult;
                }
            }
            
            return primaryResult;
            
        } catch (Exception e) {
            logger.warn("主要情感分析器失败，尝试备用分析器", e);
            return backupAnalyzer.analyze(text, language, context);
        }
    }
    
    /**
     * 预处理文本
     * @param text 原始文本
     * @param language 语言代码
     * @return 预处理后的文本
     */
    private String preprocessText(String text, String language) {
        // 1. 清理HTML标签
        text = text.replaceAll("<[^>]+>", "");
        
        // 2. 标准化空白字符
        text = text.replaceAll("\\s+", " ").trim();
        
        // 3. 处理表情符号
        text = normalizeEmojis(text);
        
        // 4. 语言特定的预处理
        if ("zh".equals(language)) {
            text = preprocessChinese(text);
        } else if ("en".equals(language)) {
            text = preprocessEnglish(text);
        }
        
        return text;
    }
    
    /**
     * 后处理分析结果
     * @param result 原始分析结果
     * @param context 上下文信息
     * @return 后处理后的结果
     */
    private EmotionResult postprocessResult(EmotionResult result, EmotionContext context) {
        // 1. 应用上下文调整
        if (context != null) {
            result = applyContextualAdjustment(result, context);
        }
        
        // 2. 平滑情感变化
        if (context != null && context.getPreviousEmotion() != null) {
            result = smoothEmotionTransition(result, context.getPreviousEmotion());
        }
        
        // 3. 生成动画提示
        String animationHint = generateAnimationHint(result);
        result.setAnimationHint(animationHint);
        
        // 4. 生成行为提示
        String behaviorHint = generateBehaviorHint(result);
        result.setBehaviorHint(behaviorHint);
        
        return result;
    }
    
    /**
     * 生成动画提示
     * @param result 情感分析结果
     * @return 动画提示
     */
    private String generateAnimationHint(EmotionResult result) {
        EmotionType primaryEmotion = result.getPrimaryEmotion();
        float intensity = result.getIntensity();
        
        switch (primaryEmotion) {
            case HAPPY:
                return intensity > 0.7f ? "smile_big" : "smile";
            case SAD:
                return intensity > 0.7f ? "cry" : "sad";
            case ANGRY:
                return intensity > 0.7f ? "angry_intense" : "angry";
            case SURPRISED:
                return "surprised";
            case FEAR:
                return "scared";
            case DISGUST:
                return "disgusted";
            case NEUTRAL:
            default:
                return "idle";
        }
    }
    
    /**
     * 生成行为提示
     * @param result 情感分析结果
     * @return 行为提示
     */
    private String generateBehaviorHint(EmotionResult result) {
        EmotionType primaryEmotion = result.getPrimaryEmotion();
        float intensity = result.getIntensity();
        
        switch (primaryEmotion) {
            case HAPPY:
                return intensity > 0.8f ? "celebrate" : "cheerful";
            case SAD:
                return intensity > 0.6f ? "comfort_seeking" : "melancholy";
            case ANGRY:
                return intensity > 0.7f ? "aggressive" : "irritated";
            case SURPRISED:
                return "curious";
            case FEAR:
                return "cautious";
            case DISGUST:
                return "avoidant";
            case NEUTRAL:
            default:
                return "normal";
        }
    }
    
    /**
     * 批量分析文本情感
     * @param texts 文本列表
     * @param language 语言代码
     * @return 情感分析结果列表
     */
    @Override
    public List<EmotionResult> analyzeBatchTexts(List<String> texts, String language) {
        return texts.parallelStream()
            .map(text -> analyzeText(text, language, null))
            .collect(Collectors.toList());
    }
    
    /**
     * 获取支持的情感类型
     * @return 情感类型列表
     */
    @Override
    public List<EmotionType> getSupportedEmotionTypes() {
        return Arrays.asList(EmotionType.values());
    }
    
    /**
     * 创建回退结果
     * @return 默认的情感分析结果
     */
    private EmotionResult createFallbackResult() {
        EmotionResult result = new EmotionResult();
        result.setPrimaryEmotion(EmotionType.NEUTRAL);
        result.setIntensity(0.5f);
        result.setConfidence(0.3f);
        result.setAnimationHint("idle");
        result.setBehaviorHint("normal");
        return result;
    }
}
```

## 数据模型

### EmotionResult - 情感分析结果
```java
public class EmotionResult {
    private EmotionType primaryEmotion;      // 主要情感
    private Map<EmotionType, Float> emotions; // 所有情感及其强度
    private float intensity;                  // 情感强度 (0.0-1.0)
    private float confidence;                 // 置信度 (0.0-1.0)
    private String animationHint;            // 动画提示
    private String behaviorHint;             // 行为提示
    private LocalDateTime analyzedAt;        // 分析时间
    
    // 构造函数、getter、setter等
}
```

### EmotionType - 情感类型枚举
```java
public enum EmotionType {
    HAPPY("开心", "#FFD700", "smile"),
    SAD("悲伤", "#4169E1", "sad"),
    ANGRY("愤怒", "#FF4500", "angry"),
    SURPRISED("惊讶", "#FF69B4", "surprised"),
    FEAR("恐惧", "#8A2BE2", "scared"),
    DISGUST("厌恶", "#228B22", "disgusted"),
    NEUTRAL("中性", "#808080", "idle");
    
    private final String displayName;
    private final String color;
    private final String defaultAnimation;
    
    EmotionType(String displayName, String color, String defaultAnimation) {
        this.displayName = displayName;
        this.color = color;
        this.defaultAnimation = defaultAnimation;
    }
    
    // getter方法...
}
```

### EmotionContext - 情感上下文
```java
public class EmotionContext {
    private String conversationId;           // 对话ID
    private String characterName;            // 角色名称
    private EmotionResult previousEmotion;   // 前一个情感状态
    private List<String> recentMessages;     // 最近的消息
    private Map<String, Object> metadata;   // 额外的上下文信息
    
    // 构造函数、getter、setter等
}
```

## 情感分析算法

### 1. 基于规则的分析器

#### RuleBasedEmotionAnalyzer
```java
@Component
public class RuleBasedEmotionAnalyzer implements EmotionAnalyzer {
    
    private final EmotionLexicon lexicon;
    private final EmotionRules rules;
    
    @Override
    public EmotionResult analyze(String text, String language, EmotionContext context) {
        // 1. 词汇匹配
        Map<EmotionType, Float> lexiconScores = lexicon.analyzeText(text, language);
        
        // 2. 规则应用
        Map<EmotionType, Float> ruleScores = rules.applyRules(text, language, context);
        
        // 3. 分数融合
        Map<EmotionType, Float> finalScores = combineScores(lexiconScores, ruleScores);
        
        // 4. 生成结果
        return createResult(finalScores);
    }
}
```

### 2. 基于机器学习的分析器

#### MLEmotionAnalyzer
```java
@Component
public class MLEmotionAnalyzer implements EmotionAnalyzer {
    
    private final EmotionModel model;
    private final TextVectorizer vectorizer;
    
    @Override
    public EmotionResult analyze(String text, String language, EmotionContext context) {
        // 1. 文本向量化
        float[] features = vectorizer.vectorize(text, language);
        
        // 2. 模型预测
        float[] predictions = model.predict(features);
        
        // 3. 转换为情感分数
        Map<EmotionType, Float> scores = convertPredictionsToScores(predictions);
        
        // 4. 生成结果
        return createResult(scores);
    }
}
```

## API接口文档

### 分析文本情感
```http
POST /api/emotion/analyze
Content-Type: application/json
Authorization: Bearer {token}

{
  "text": "今天心情很好，阿米娅真的很可爱！",
  "language": "zh",
  "conversationId": "conv_123",
  "context": {
    "characterName": "amiya",
    "previousEmotion": "neutral"
  }
}
```

### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "primaryEmotion": "HAPPY",
    "emotions": {
      "HAPPY": 0.85,
      "SURPRISED": 0.12,
      "NEUTRAL": 0.03
    },
    "intensity": 0.85,
    "confidence": 0.92,
    "animationHint": "smile_big",
    "behaviorHint": "cheerful",
    "analyzedAt": "2025-01-01T12:00:00Z"
  }
}
```

## 连接流程

### 情感分析流程

```mermaid
graph TD
    A[接收文本] --> B[预处理文本]
    B --> C[检查缓存]
    C --> D{缓存命中?}
    D -->|是| E[返回缓存结果]
    D -->|否| F[执行情感分析]
    F --> G[主分析器分析]
    G --> H{置信度足够?}
    H -->|是| I[后处理结果]
    H -->|否| J[备用分析器分析]
    J --> K[选择最佳结果]
    K --> I
    I --> L[生成动画提示]
    L --> M[缓存结果]
    M --> N[返回结果]
```

---

**模块负责人**: 情感分析开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
