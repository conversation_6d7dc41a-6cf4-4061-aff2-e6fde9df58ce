# JWT令牌管理 (Token Management)

## 模块概述

JWT令牌管理模块负责Ark-Pets AI Enhanced项目中的用户身份令牌生成、验证、刷新和撤销等功能。基于JWT（JSON Web Token）标准，提供安全可靠的无状态身份认证机制，支持访问令牌和刷新令牌的完整生命周期管理。

**核心职责**:
- JWT访问令牌生成和验证
- 刷新令牌管理和轮换
- 令牌黑名单和撤销机制
- 令牌安全策略和过期管理
- 多设备登录和会话管理

## 核心功能架构

### 1. 令牌管理架构

#### 双令牌架构模型
```
┌─────────────────────────────────────┐
│           令牌管理系统               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 访问令牌  │ 刷新令牌  │ 令牌验证  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           安全控制层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 令牌黑名单│ 会话管理  │ 安全策略  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           存储持久层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ Redis缓存│ 数据库   │ 密钥管理  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 令牌生命周期流程

#### 令牌管理流程图
```mermaid
graph TB
    subgraph "令牌生成流程"
        Login[用户登录]
        Validate[凭证验证]
        GenerateTokens[生成令牌对]
        StoreRefresh[存储刷新令牌]
        ReturnTokens[返回令牌]
    end
    
    subgraph "令牌验证流程"
        Request[API请求]
        ExtractToken[提取令牌]
        ValidateToken[验证令牌]
        CheckBlacklist[检查黑名单]
        GrantAccess[授权访问]
        DenyAccess[拒绝访问]
    end
    
    subgraph "令牌刷新流程"
        RefreshRequest[刷新请求]
        ValidateRefresh[验证刷新令牌]
        GenerateNew[生成新令牌对]
        RevokeOld[撤销旧令牌]
        UpdateSession[更新会话]
    end
    
    subgraph "令牌撤销流程"
        LogoutRequest[登出请求]
        AddToBlacklist[加入黑名单]
        InvalidateSession[失效会话]
        CleanupTokens[清理令牌]
    end
    
    Login --> Validate
    Validate --> GenerateTokens
    GenerateTokens --> StoreRefresh
    StoreRefresh --> ReturnTokens
    
    Request --> ExtractToken
    ExtractToken --> ValidateToken
    ValidateToken --> CheckBlacklist
    CheckBlacklist -->|有效| GrantAccess
    CheckBlacklist -->|无效| DenyAccess
    
    RefreshRequest --> ValidateRefresh
    ValidateRefresh --> GenerateNew
    GenerateNew --> RevokeOld
    RevokeOld --> UpdateSession
    
    LogoutRequest --> AddToBlacklist
    AddToBlacklist --> InvalidateSession
    InvalidateSession --> CleanupTokens
```

## 核心类和接口

### 1. JWT令牌管理服务

#### JwtTokenService - JWT令牌主服务
```java
/**
 * JWT令牌管理主服务
 * 负责JWT令牌的生成、验证、刷新和撤销
 */
@Service
@Slf4j
public class JwtTokenService {
    
    private final JwtProperties jwtProperties;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RefreshTokenRepository refreshTokenRepository;
    private final TokenBlacklistService tokenBlacklistService;
    private final UserSessionService userSessionService;
    private final SecurityEventPublisher securityEventPublisher;
    
    private final Algorithm jwtAlgorithm;
    private final JWTVerifier jwtVerifier;
    
    public JwtTokenService(JwtProperties jwtProperties, 
                          RedisTemplate<String, Object> redisTemplate,
                          RefreshTokenRepository refreshTokenRepository,
                          TokenBlacklistService tokenBlacklistService,
                          UserSessionService userSessionService,
                          SecurityEventPublisher securityEventPublisher) {
        this.jwtProperties = jwtProperties;
        this.redisTemplate = redisTemplate;
        this.refreshTokenRepository = refreshTokenRepository;
        this.tokenBlacklistService = tokenBlacklistService;
        this.userSessionService = userSessionService;
        this.securityEventPublisher = securityEventPublisher;
        
        // 初始化JWT算法和验证器
        this.jwtAlgorithm = Algorithm.HMAC256(jwtProperties.getSecret());
        this.jwtVerifier = JWT.require(jwtAlgorithm)
            .withIssuer(jwtProperties.getIssuer())
            .build();
    }
    
    /**
     * 生成令牌对（访问令牌 + 刷新令牌）
     * @param user 用户信息
     * @param deviceInfo 设备信息
     * @return 令牌对
     */
    public TokenPair generateTokenPair(User user, DeviceInfo deviceInfo) {
        try {
            String sessionId = UUID.randomUUID().toString();
            
            // 1. 生成访问令牌
            String accessToken = generateAccessToken(user, sessionId, deviceInfo);
            
            // 2. 生成刷新令牌
            String refreshToken = generateRefreshToken(user, sessionId, deviceInfo);
            
            // 3. 存储刷新令牌
            storeRefreshToken(refreshToken, user.getId(), sessionId, deviceInfo);
            
            // 4. 创建用户会话
            userSessionService.createSession(sessionId, user.getId(), deviceInfo, accessToken, refreshToken);
            
            // 5. 发布令牌生成事件
            securityEventPublisher.publishTokenGenerated(user.getId(), sessionId, deviceInfo);
            
            log.info("令牌对生成成功: userId={}, sessionId={}, device={}", 
                user.getId(), sessionId, deviceInfo.getDeviceType());
            
            return TokenPair.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .accessTokenExpiresIn(jwtProperties.getAccessTokenExpiration())
                .refreshTokenExpiresIn(jwtProperties.getRefreshTokenExpiration())
                .tokenType("Bearer")
                .build();
                
        } catch (Exception e) {
            log.error("令牌对生成失败: userId={}", user.getId(), e);
            throw new TokenGenerationException("令牌生成失败", e);
        }
    }
    
    /**
     * 验证访问令牌
     * @param accessToken 访问令牌
     * @return 令牌声明信息
     */
    public TokenClaims validateAccessToken(String accessToken) {
        try {
            // 1. 检查令牌黑名单
            if (tokenBlacklistService.isBlacklisted(accessToken)) {
                throw new TokenBlacklistedException("令牌已被撤销");
            }
            
            // 2. 验证JWT签名和过期时间
            DecodedJWT decodedJWT = jwtVerifier.verify(accessToken);
            
            // 3. 提取令牌声明
            TokenClaims claims = extractClaims(decodedJWT);
            
            // 4. 验证会话状态
            if (!userSessionService.isSessionActive(claims.getSessionId())) {
                throw new SessionExpiredException("会话已过期");
            }
            
            // 5. 更新最后访问时间
            userSessionService.updateLastAccessTime(claims.getSessionId());
            
            return claims;
            
        } catch (JWTVerificationException e) {
            log.warn("访问令牌验证失败: {}", e.getMessage());
            throw new InvalidTokenException("无效的访问令牌", e);
        } catch (Exception e) {
            log.error("令牌验证异常", e);
            throw new TokenValidationException("令牌验证失败", e);
        }
    }
    
    /**
     * 刷新访问令牌
     * @param refreshToken 刷新令牌
     * @param deviceInfo 设备信息
     * @return 新的令牌对
     */
    public TokenPair refreshAccessToken(String refreshToken, DeviceInfo deviceInfo) {
        try {
            // 1. 验证刷新令牌
            RefreshTokenEntity refreshTokenEntity = validateRefreshToken(refreshToken);
            
            // 2. 获取用户信息
            User user = getUserById(refreshTokenEntity.getUserId());
            
            // 3. 检查设备信息匹配
            if (!deviceInfo.matches(refreshTokenEntity.getDeviceInfo())) {
                throw new DeviceMismatchException("设备信息不匹配");
            }
            
            // 4. 撤销旧的令牌对
            revokeTokenPair(refreshTokenEntity.getSessionId());
            
            // 5. 生成新的令牌对
            TokenPair newTokenPair = generateTokenPair(user, deviceInfo);
            
            // 6. 记录令牌刷新事件
            securityEventPublisher.publishTokenRefreshed(user.getId(), refreshTokenEntity.getSessionId(), deviceInfo);
            
            log.info("令牌刷新成功: userId={}, oldSessionId={}", user.getId(), refreshTokenEntity.getSessionId());
            
            return newTokenPair;
            
        } catch (Exception e) {
            log.error("令牌刷新失败: refreshToken={}", refreshToken, e);
            throw new TokenRefreshException("令牌刷新失败", e);
        }
    }
    
    /**
     * 撤销令牌对
     * @param sessionId 会话ID
     */
    public void revokeTokenPair(String sessionId) {
        try {
            // 1. 获取会话信息
            UserSession session = userSessionService.getSession(sessionId);
            if (session == null) {
                log.warn("会话不存在: sessionId={}", sessionId);
                return;
            }
            
            // 2. 将访问令牌加入黑名单
            if (StringUtils.isNotBlank(session.getAccessToken())) {
                tokenBlacklistService.addToBlacklist(session.getAccessToken(), 
                    Duration.ofSeconds(jwtProperties.getAccessTokenExpiration()));
            }
            
            // 3. 删除刷新令牌
            refreshTokenRepository.deleteBySessionId(sessionId);
            
            // 4. 失效用户会话
            userSessionService.invalidateSession(sessionId);
            
            // 5. 发布令牌撤销事件
            securityEventPublisher.publishTokenRevoked(session.getUserId(), sessionId);
            
            log.info("令牌对撤销成功: userId={}, sessionId={}", session.getUserId(), sessionId);
            
        } catch (Exception e) {
            log.error("令牌对撤销失败: sessionId={}", sessionId, e);
            throw new TokenRevocationException("令牌撤销失败", e);
        }
    }
    
    /**
     * 撤销用户所有令牌
     * @param userId 用户ID
     */
    public void revokeAllUserTokens(String userId) {
        try {
            // 1. 获取用户所有活跃会话
            List<UserSession> activeSessions = userSessionService.getActiveSessionsByUserId(userId);
            
            // 2. 逐个撤销令牌对
            for (UserSession session : activeSessions) {
                revokeTokenPair(session.getSessionId());
            }
            
            log.info("用户所有令牌撤销成功: userId={}, sessionCount={}", userId, activeSessions.size());
            
        } catch (Exception e) {
            log.error("撤销用户所有令牌失败: userId={}", userId, e);
            throw new TokenRevocationException("撤销用户所有令牌失败", e);
        }
    }
    
    /**
     * 获取用户活跃会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    public List<UserSessionInfo> getUserActiveSessions(String userId) {
        List<UserSession> sessions = userSessionService.getActiveSessionsByUserId(userId);
        return sessions.stream()
            .map(this::convertToSessionInfo)
            .collect(Collectors.toList());
    }
    
    /**
     * 清理过期令牌
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupExpiredTokens() {
        try {
            // 1. 清理过期的刷新令牌
            int deletedRefreshTokens = refreshTokenRepository.deleteExpiredTokens();
            
            // 2. 清理过期的会话
            int deletedSessions = userSessionService.cleanupExpiredSessions();
            
            // 3. 清理过期的黑名单令牌
            int deletedBlacklistTokens = tokenBlacklistService.cleanupExpiredTokens();
            
            log.info("过期令牌清理完成: refreshTokens={}, sessions={}, blacklistTokens={}", 
                deletedRefreshTokens, deletedSessions, deletedBlacklistTokens);
                
        } catch (Exception e) {
            log.error("过期令牌清理失败", e);
        }
    }
    
    private String generateAccessToken(User user, String sessionId, DeviceInfo deviceInfo) {
        Date now = new Date();
        Date expiresAt = new Date(now.getTime() + jwtProperties.getAccessTokenExpiration() * 1000);
        
        return JWT.create()
            .withIssuer(jwtProperties.getIssuer())
            .withSubject(user.getId())
            .withAudience(jwtProperties.getAudience())
            .withIssuedAt(now)
            .withExpiresAt(expiresAt)
            .withClaim("sessionId", sessionId)
            .withClaim("username", user.getUsername())
            .withClaim("email", user.getEmail())
            .withClaim("roles", user.getRoles().stream()
                .map(Role::getName)
                .collect(Collectors.toList()))
            .withClaim("deviceType", deviceInfo.getDeviceType())
            .withClaim("deviceId", deviceInfo.getDeviceId())
            .sign(jwtAlgorithm);
    }
    
    private String generateRefreshToken(User user, String sessionId, DeviceInfo deviceInfo) {
        return UUID.randomUUID().toString().replace("-", "") + 
               UUID.randomUUID().toString().replace("-", "");
    }
    
    private void storeRefreshToken(String refreshToken, String userId, String sessionId, DeviceInfo deviceInfo) {
        RefreshTokenEntity entity = RefreshTokenEntity.builder()
            .token(refreshToken)
            .userId(userId)
            .sessionId(sessionId)
            .deviceInfo(deviceInfo)
            .createdAt(LocalDateTime.now())
            .expiresAt(LocalDateTime.now().plusSeconds(jwtProperties.getRefreshTokenExpiration()))
            .build();
            
        refreshTokenRepository.save(entity);
    }
    
    private RefreshTokenEntity validateRefreshToken(String refreshToken) {
        RefreshTokenEntity entity = refreshTokenRepository.findByToken(refreshToken)
            .orElseThrow(() -> new InvalidTokenException("无效的刷新令牌"));
            
        if (entity.isExpired()) {
            refreshTokenRepository.delete(entity);
            throw new TokenExpiredException("刷新令牌已过期");
        }
        
        return entity;
    }
    
    private TokenClaims extractClaims(DecodedJWT decodedJWT) {
        return TokenClaims.builder()
            .userId(decodedJWT.getSubject())
            .sessionId(decodedJWT.getClaim("sessionId").asString())
            .username(decodedJWT.getClaim("username").asString())
            .email(decodedJWT.getClaim("email").asString())
            .roles(decodedJWT.getClaim("roles").asList(String.class))
            .deviceType(decodedJWT.getClaim("deviceType").asString())
            .deviceId(decodedJWT.getClaim("deviceId").asString())
            .issuedAt(decodedJWT.getIssuedAt())
            .expiresAt(decodedJWT.getExpiresAt())
            .build();
    }
    
    private UserSessionInfo convertToSessionInfo(UserSession session) {
        return UserSessionInfo.builder()
            .sessionId(session.getSessionId())
            .deviceType(session.getDeviceInfo().getDeviceType())
            .deviceName(session.getDeviceInfo().getDeviceName())
            .ipAddress(session.getDeviceInfo().getIpAddress())
            .location(session.getDeviceInfo().getLocation())
            .createdAt(session.getCreatedAt())
            .lastAccessTime(session.getLastAccessTime())
            .isCurrentSession(session.isCurrent())
            .build();
    }
    
    private User getUserById(String userId) {
        // 从用户服务获取用户信息
        return userService.findById(userId)
            .orElseThrow(() -> new UserNotFoundException("用户不存在: " + userId));
    }
}
```

### 2. 令牌黑名单服务

#### TokenBlacklistService - 令牌黑名单服务
```java
/**
 * 令牌黑名单服务
 * 负责管理被撤销的令牌黑名单
 */
@Service
@Slf4j
public class TokenBlacklistService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final BlacklistTokenRepository blacklistTokenRepository;

    private static final String BLACKLIST_KEY_PREFIX = "token:blacklist:";

    /**
     * 将令牌加入黑名单
     * @param token 令牌
     * @param ttl 存活时间
     */
    public void addToBlacklist(String token, Duration ttl) {
        try {
            String tokenHash = hashToken(token);
            String redisKey = BLACKLIST_KEY_PREFIX + tokenHash;

            // 1. 存储到Redis（快速查询）
            redisTemplate.opsForValue().set(redisKey, "blacklisted", ttl);

            // 2. 存储到数据库（持久化）
            BlacklistToken blacklistToken = BlacklistToken.builder()
                .tokenHash(tokenHash)
                .reason("USER_LOGOUT")
                .createdAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plus(ttl))
                .build();
            blacklistTokenRepository.save(blacklistToken);

            log.debug("令牌已加入黑名单: tokenHash={}", tokenHash);

        } catch (Exception e) {
            log.error("加入黑名单失败: token={}", token, e);
            throw new BlacklistOperationException("加入黑名单失败", e);
        }
    }

    /**
     * 检查令牌是否在黑名单中
     * @param token 令牌
     * @return 是否在黑名单中
     */
    public boolean isBlacklisted(String token) {
        try {
            String tokenHash = hashToken(token);
            String redisKey = BLACKLIST_KEY_PREFIX + tokenHash;

            // 1. 先检查Redis缓存
            Boolean exists = redisTemplate.hasKey(redisKey);
            if (Boolean.TRUE.equals(exists)) {
                return true;
            }

            // 2. 检查数据库
            boolean existsInDb = blacklistTokenRepository.existsByTokenHashAndExpiresAtAfter(
                tokenHash, LocalDateTime.now());

            // 3. 如果数据库中存在但Redis中不存在，重新缓存
            if (existsInDb) {
                BlacklistToken blacklistToken = blacklistTokenRepository.findByTokenHash(tokenHash);
                if (blacklistToken != null && !blacklistToken.isExpired()) {
                    Duration remainingTtl = Duration.between(LocalDateTime.now(), blacklistToken.getExpiresAt());
                    redisTemplate.opsForValue().set(redisKey, "blacklisted", remainingTtl);
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("检查黑名单失败: token={}", token, e);
            // 安全起见，出现异常时认为令牌有效
            return false;
        }
    }

    /**
     * 批量将令牌加入黑名单
     * @param tokens 令牌列表
     * @param ttl 存活时间
     */
    public void addToBlacklistBatch(List<String> tokens, Duration ttl) {
        try {
            List<BlacklistToken> blacklistTokens = new ArrayList<>();

            for (String token : tokens) {
                String tokenHash = hashToken(token);
                String redisKey = BLACKLIST_KEY_PREFIX + tokenHash;

                // Redis批量操作
                redisTemplate.opsForValue().set(redisKey, "blacklisted", ttl);

                // 准备数据库批量插入
                BlacklistToken blacklistToken = BlacklistToken.builder()
                    .tokenHash(tokenHash)
                    .reason("BATCH_REVOCATION")
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plus(ttl))
                    .build();
                blacklistTokens.add(blacklistToken);
            }

            // 数据库批量插入
            blacklistTokenRepository.saveAll(blacklistTokens);

            log.info("批量加入黑名单成功: count={}", tokens.size());

        } catch (Exception e) {
            log.error("批量加入黑名单失败: count={}", tokens.size(), e);
            throw new BlacklistOperationException("批量加入黑名单失败", e);
        }
    }

    /**
     * 清理过期的黑名单令牌
     * @return 清理的数量
     */
    public int cleanupExpiredTokens() {
        try {
            // 1. 清理数据库中过期的记录
            int deletedCount = blacklistTokenRepository.deleteByExpiresAtBefore(LocalDateTime.now());

            // 2. Redis中的记录会自动过期，无需手动清理

            log.info("清理过期黑名单令牌: count={}", deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("清理过期黑名单令牌失败", e);
            return 0;
        }
    }

    /**
     * 获取黑名单统计信息
     * @return 统计信息
     */
    public BlacklistStats getBlacklistStats() {
        try {
            long totalCount = blacklistTokenRepository.count();
            long activeCount = blacklistTokenRepository.countByExpiresAtAfter(LocalDateTime.now());
            long expiredCount = totalCount - activeCount;

            return BlacklistStats.builder()
                .totalCount(totalCount)
                .activeCount(activeCount)
                .expiredCount(expiredCount)
                .build();

        } catch (Exception e) {
            log.error("获取黑名单统计信息失败", e);
            return BlacklistStats.builder().build();
        }
    }

    private String hashToken(String token) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(token.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
}
```

### 3. 用户会话服务

#### UserSessionService - 用户会话服务
```java
/**
 * 用户会话服务
 * 负责管理用户登录会话
 */
@Service
@Slf4j
public class UserSessionService {

    private final UserSessionRepository userSessionRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SessionProperties sessionProperties;

    private static final String SESSION_KEY_PREFIX = "session:";

    /**
     * 创建用户会话
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     */
    public void createSession(String sessionId, String userId, DeviceInfo deviceInfo,
                             String accessToken, String refreshToken) {
        try {
            UserSession session = UserSession.builder()
                .sessionId(sessionId)
                .userId(userId)
                .deviceInfo(deviceInfo)
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .createdAt(LocalDateTime.now())
                .lastAccessTime(LocalDateTime.now())
                .status(SessionStatus.ACTIVE)
                .build();

            // 1. 存储到数据库
            userSessionRepository.save(session);

            // 2. 缓存到Redis
            String redisKey = SESSION_KEY_PREFIX + sessionId;
            redisTemplate.opsForValue().set(redisKey, session,
                Duration.ofSeconds(sessionProperties.getMaxInactiveInterval()));

            // 3. 检查并限制用户并发会话数
            limitConcurrentSessions(userId);

            log.info("用户会话创建成功: sessionId={}, userId={}", sessionId, userId);

        } catch (Exception e) {
            log.error("创建用户会话失败: sessionId={}, userId={}", sessionId, userId, e);
            throw new SessionCreationException("创建用户会话失败", e);
        }
    }

    /**
     * 获取会话信息
     * @param sessionId 会话ID
     * @return 会话信息
     */
    public UserSession getSession(String sessionId) {
        try {
            String redisKey = SESSION_KEY_PREFIX + sessionId;

            // 1. 先从Redis获取
            UserSession session = (UserSession) redisTemplate.opsForValue().get(redisKey);
            if (session != null) {
                return session;
            }

            // 2. 从数据库获取
            Optional<UserSession> sessionOpt = userSessionRepository.findBySessionId(sessionId);
            if (sessionOpt.isPresent()) {
                session = sessionOpt.get();

                // 3. 重新缓存到Redis
                redisTemplate.opsForValue().set(redisKey, session,
                    Duration.ofSeconds(sessionProperties.getMaxInactiveInterval()));

                return session;
            }

            return null;

        } catch (Exception e) {
            log.error("获取会话信息失败: sessionId={}", sessionId, e);
            return null;
        }
    }

    /**
     * 检查会话是否活跃
     * @param sessionId 会话ID
     * @return 是否活跃
     */
    public boolean isSessionActive(String sessionId) {
        UserSession session = getSession(sessionId);
        return session != null &&
               session.getStatus() == SessionStatus.ACTIVE &&
               !session.isExpired(sessionProperties.getMaxInactiveInterval());
    }

    /**
     * 更新最后访问时间
     * @param sessionId 会话ID
     */
    public void updateLastAccessTime(String sessionId) {
        try {
            UserSession session = getSession(sessionId);
            if (session != null) {
                session.setLastAccessTime(LocalDateTime.now());

                // 更新数据库
                userSessionRepository.updateLastAccessTime(sessionId, LocalDateTime.now());

                // 更新Redis缓存
                String redisKey = SESSION_KEY_PREFIX + sessionId;
                redisTemplate.opsForValue().set(redisKey, session,
                    Duration.ofSeconds(sessionProperties.getMaxInactiveInterval()));
            }

        } catch (Exception e) {
            log.error("更新最后访问时间失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 失效会话
     * @param sessionId 会话ID
     */
    public void invalidateSession(String sessionId) {
        try {
            // 1. 更新数据库状态
            userSessionRepository.updateStatus(sessionId, SessionStatus.EXPIRED);

            // 2. 从Redis删除
            String redisKey = SESSION_KEY_PREFIX + sessionId;
            redisTemplate.delete(redisKey);

            log.info("会话失效成功: sessionId={}", sessionId);

        } catch (Exception e) {
            log.error("会话失效失败: sessionId={}", sessionId, e);
            throw new SessionInvalidationException("会话失效失败", e);
        }
    }

    /**
     * 获取用户活跃会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    public List<UserSession> getActiveSessionsByUserId(String userId) {
        return userSessionRepository.findByUserIdAndStatus(userId, SessionStatus.ACTIVE);
    }

    /**
     * 清理过期会话
     * @return 清理的数量
     */
    public int cleanupExpiredSessions() {
        try {
            LocalDateTime expiredBefore = LocalDateTime.now()
                .minusSeconds(sessionProperties.getMaxInactiveInterval());

            int deletedCount = userSessionRepository.deleteExpiredSessions(expiredBefore);

            log.info("清理过期会话: count={}", deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("清理过期会话失败", e);
            return 0;
        }
    }

    private void limitConcurrentSessions(String userId) {
        try {
            List<UserSession> activeSessions = getActiveSessionsByUserId(userId);
            int maxSessions = sessionProperties.getMaxConcurrentSessions();

            if (activeSessions.size() >= maxSessions) {
                // 按创建时间排序，删除最旧的会话
                activeSessions.sort(Comparator.comparing(UserSession::getCreatedAt));

                int sessionsToRemove = activeSessions.size() - maxSessions + 1;
                for (int i = 0; i < sessionsToRemove; i++) {
                    UserSession oldSession = activeSessions.get(i);
                    invalidateSession(oldSession.getSessionId());

                    log.info("超出并发会话限制，删除旧会话: userId={}, sessionId={}",
                        userId, oldSession.getSessionId());
                }
            }

        } catch (Exception e) {
            log.error("限制并发会话失败: userId={}", userId, e);
        }
    }
}
```

## 数据模型定义

### 1. 令牌相关实体

#### RefreshTokenEntity - 刷新令牌实体
```java
/**
 * 刷新令牌实体
 * 存储刷新令牌信息
 */
@Entity
@Table(name = "refresh_tokens")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String token;

    @Column(nullable = false)
    private String userId;

    @Column(nullable = false)
    private String sessionId;

    @Embedded
    private DeviceInfo deviceInfo;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime expiresAt;

    /**
     * 检查令牌是否过期
     * @return 是否过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
}
```

#### UserSession - 用户会话实体
```java
/**
 * 用户会话实体
 * 存储用户登录会话信息
 */
@Entity
@Table(name = "user_sessions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSession {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String sessionId;

    @Column(nullable = false)
    private String userId;

    @Embedded
    private DeviceInfo deviceInfo;

    @Column(length = 1000)
    private String accessToken;

    @Column(length = 500)
    private String refreshToken;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private SessionStatus status;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    @Column(nullable = false)
    private LocalDateTime lastAccessTime;

    /**
     * 检查会话是否过期
     * @param maxInactiveInterval 最大非活跃间隔（秒）
     * @return 是否过期
     */
    public boolean isExpired(long maxInactiveInterval) {
        return LocalDateTime.now()
            .isAfter(lastAccessTime.plusSeconds(maxInactiveInterval));
    }

    /**
     * 检查是否为当前会话
     * @return 是否为当前会话
     */
    public boolean isCurrent() {
        // 可以通过比较设备信息或其他方式判断
        return status == SessionStatus.ACTIVE;
    }
}
```

## 使用示例

### 基本令牌操作
```java
// 注入令牌服务
@Autowired
private JwtTokenService jwtTokenService;

// 生成令牌对
User user = getCurrentUser();
DeviceInfo deviceInfo = DeviceInfo.builder()
    .deviceType("WEB")
    .deviceId("browser-123")
    .deviceName("Chrome Browser")
    .ipAddress("*************")
    .userAgent("Mozilla/5.0...")
    .build();

TokenPair tokenPair = jwtTokenService.generateTokenPair(user, deviceInfo);

// 验证访问令牌
try {
    TokenClaims claims = jwtTokenService.validateAccessToken(tokenPair.getAccessToken());
    System.out.println("用户ID: " + claims.getUserId());
    System.out.println("会话ID: " + claims.getSessionId());
} catch (InvalidTokenException e) {
    System.err.println("令牌无效: " + e.getMessage());
}

// 刷新令牌
TokenPair newTokenPair = jwtTokenService.refreshAccessToken(tokenPair.getRefreshToken(), deviceInfo);

// 撤销令牌
jwtTokenService.revokeTokenPair(claims.getSessionId());
```

### 会话管理
```java
// 注入会话服务
@Autowired
private UserSessionService userSessionService;

// 获取用户活跃会话
List<UserSessionInfo> sessions = jwtTokenService.getUserActiveSessions("user123");
for (UserSessionInfo session : sessions) {
    System.out.println("设备: " + session.getDeviceType());
    System.out.println("位置: " + session.getLocation());
    System.out.println("最后访问: " + session.getLastAccessTime());
}

// 撤销特定会话
userSessionService.invalidateSession("session-456");

// 撤销用户所有会话
jwtTokenService.revokeAllUserTokens("user123");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的JWT令牌管理实现，支持双令牌架构、黑名单机制、会话管理等功能
