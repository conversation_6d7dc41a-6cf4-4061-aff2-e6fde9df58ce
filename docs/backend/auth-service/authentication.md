# 身份认证 (Authentication)

## 模块概述

身份认证模块负责用户身份验证，包括用户注册、登录、JWT令牌管理、多因素认证等功能，确保系统安全性。

## 核心功能

### 1. 认证管理器 (AuthenticationManager)

#### 功能描述
统一管理用户认证流程，包括密码验证、令牌生成、会话管理等。

#### 核心函数

```java
@Service
@Transactional
public class AuthenticationManager {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider jwtTokenProvider;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 用户登录认证
     * @param loginRequest 登录请求
     * @return 认证结果
     * @throws AuthenticationException 认证异常
     */
    public AuthenticationResult authenticateUser(LoginRequest loginRequest) 
            throws AuthenticationException;
    
    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册结果
     * @throws RegistrationException 注册异常
     */
    public RegistrationResult registerUser(RegisterRequest registerRequest) 
            throws RegistrationException;
    
    /**
     * 刷新访问令牌
     * @param refreshToken 刷新令牌
     * @return 新的令牌对
     * @throws TokenException 令牌异常
     */
    public TokenPair refreshAccessToken(String refreshToken) throws TokenException;
    
    /**
     * 用户登出
     * @param userId 用户ID
     * @param accessToken 访问令牌
     * @return 登出结果
     */
    public LogoutResult logoutUser(String userId, String accessToken);
    
    /**
     * 验证用户密码
     * @param userId 用户ID
     * @param rawPassword 原始密码
     * @return 验证结果
     */
    public boolean validateUserPassword(String userId, String rawPassword);
    
    /**
     * 更改用户密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 更改结果
     * @throws PasswordChangeException 密码更改异常
     */
    public PasswordChangeResult changeUserPassword(String userId, String oldPassword, 
                                                 String newPassword) throws PasswordChangeException;
    
    /**
     * 重置用户密码
     * @param email 用户邮箱
     * @return 重置结果
     */
    public PasswordResetResult resetUserPassword(String email);
    
    /**
     * 验证密码重置令牌
     * @param resetToken 重置令牌
     * @param newPassword 新密码
     * @return 验证结果
     */
    public boolean validatePasswordResetToken(String resetToken, String newPassword);
    
    /**
     * 锁定用户账户
     * @param userId 用户ID
     * @param reason 锁定原因
     * @param duration 锁定时长(分钟)
     */
    public void lockUserAccount(String userId, String reason, int duration);
    
    /**
     * 解锁用户账户
     * @param userId 用户ID
     * @return 解锁结果
     */
    public boolean unlockUserAccount(String userId);
}
```

### 2. JWT令牌提供者 (JwtTokenProvider)

#### 功能描述
负责JWT令牌的生成、验证、解析等操作。

#### 核心函数

```java
@Component
public class JwtTokenProvider {
    
    private final String jwtSecret;
    private final int accessTokenExpiration;
    private final int refreshTokenExpiration;
    private final Algorithm algorithm;
    
    /**
     * 生成访问令牌
     * @param userDetails 用户详情
     * @return 访问令牌
     */
    public String generateAccessToken(UserDetails userDetails);
    
    /**
     * 生成刷新令牌
     * @param userId 用户ID
     * @return 刷新令牌
     */
    public String generateRefreshToken(String userId);
    
    /**
     * 生成令牌对
     * @param userDetails 用户详情
     * @return 令牌对(访问令牌+刷新令牌)
     */
    public TokenPair generateTokenPair(UserDetails userDetails);
    
    /**
     * 验证令牌有效性
     * @param token JWT令牌
     * @return 验证结果
     */
    public TokenValidationResult validateToken(String token);
    
    /**
     * 从令牌中提取用户ID
     * @param token JWT令牌
     * @return 用户ID
     * @throws TokenException 令牌异常
     */
    public String extractUserIdFromToken(String token) throws TokenException;
    
    /**
     * 从令牌中提取用户名
     * @param token JWT令牌
     * @return 用户名
     * @throws TokenException 令牌异常
     */
    public String extractUsernameFromToken(String token) throws TokenException;
    
    /**
     * 从令牌中提取权限
     * @param token JWT令牌
     * @return 权限列表
     * @throws TokenException 令牌异常
     */
    public List<String> extractAuthoritiesFromToken(String token) throws TokenException;
    
    /**
     * 检查令牌是否过期
     * @param token JWT令牌
     * @return 是否过期
     */
    public boolean isTokenExpired(String token);
    
    /**
     * 获取令牌过期时间
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getTokenExpirationDate(String token);
    
    /**
     * 将令牌加入黑名单
     * @param token JWT令牌
     * @param reason 加入原因
     */
    public void blacklistToken(String token, String reason);
    
    /**
     * 检查令牌是否在黑名单中
     * @param token JWT令牌
     * @return 是否在黑名单
     */
    public boolean isTokenBlacklisted(String token);
}
```

### 3. 多因素认证管理器 (MFAManager)

#### 功能描述
管理多因素认证(MFA)，包括TOTP、短信验证码等。

#### 核心函数

```java
@Service
public class MFAManager {
    
    private final TOTPGenerator totpGenerator;
    private final SMSService smsService;
    private final EmailService emailService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 启用用户MFA
     * @param userId 用户ID
     * @param mfaType MFA类型 (TOTP, SMS, EMAIL)
     * @return MFA设置结果
     */
    public MFASetupResult enableUserMFA(String userId, MFAType mfaType);
    
    /**
     * 禁用用户MFA
     * @param userId 用户ID
     * @param verificationCode 验证码
     * @return 禁用结果
     */
    public boolean disableUserMFA(String userId, String verificationCode);
    
    /**
     * 生成TOTP密钥
     * @param userId 用户ID
     * @return TOTP密钥和二维码
     */
    public TOTPSetupResult generateTOTPSecret(String userId);
    
    /**
     * 验证TOTP代码
     * @param userId 用户ID
     * @param totpCode TOTP代码
     * @return 验证结果
     */
    public boolean verifyTOTPCode(String userId, String totpCode);
    
    /**
     * 发送短信验证码
     * @param userId 用户ID
     * @param phoneNumber 手机号码
     * @return 发送结果
     */
    public SMSVerificationResult sendSMSVerificationCode(String userId, String phoneNumber);
    
    /**
     * 验证短信验证码
     * @param userId 用户ID
     * @param verificationCode 验证码
     * @return 验证结果
     */
    public boolean verifySMSCode(String userId, String verificationCode);
    
    /**
     * 发送邮箱验证码
     * @param userId 用户ID
     * @param email 邮箱地址
     * @return 发送结果
     */
    public EmailVerificationResult sendEmailVerificationCode(String userId, String email);
    
    /**
     * 验证邮箱验证码
     * @param userId 用户ID
     * @param verificationCode 验证码
     * @return 验证结果
     */
    public boolean verifyEmailCode(String userId, String verificationCode);
    
    /**
     * 生成备用恢复代码
     * @param userId 用户ID
     * @return 备用代码列表
     */
    public List<String> generateBackupCodes(String userId);
    
    /**
     * 验证备用恢复代码
     * @param userId 用户ID
     * @param backupCode 备用代码
     * @return 验证结果
     */
    public boolean verifyBackupCode(String userId, String backupCode);
    
    /**
     * 检查用户是否启用MFA
     * @param userId 用户ID
     * @return 是否启用MFA
     */
    public boolean isUserMFAEnabled(String userId);
    
    /**
     * 获取用户MFA配置
     * @param userId 用户ID
     * @return MFA配置信息
     */
    public MFAConfig getUserMFAConfig(String userId);
}
```

### 4. 会话管理器 (SessionManager)

#### 功能描述
管理用户会话，包括会话创建、验证、清理等。

#### 核心函数

```java
@Service
public class SessionManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final SessionConfig sessionConfig;
    
    /**
     * 创建用户会话
     * @param userId 用户ID
     * @param deviceInfo 设备信息
     * @param ipAddress IP地址
     * @return 会话ID
     */
    public String createUserSession(String userId, DeviceInfo deviceInfo, String ipAddress);
    
    /**
     * 验证会话有效性
     * @param sessionId 会话ID
     * @return 会话验证结果
     */
    public SessionValidationResult validateSession(String sessionId);
    
    /**
     * 更新会话活动时间
     * @param sessionId 会话ID
     */
    public void updateSessionActivity(String sessionId);
    
    /**
     * 获取用户活跃会话
     * @param userId 用户ID
     * @return 活跃会话列表
     */
    public List<SessionInfo> getUserActiveSessions(String userId);
    
    /**
     * 终止指定会话
     * @param sessionId 会话ID
     * @param reason 终止原因
     * @return 终止结果
     */
    public boolean terminateSession(String sessionId, String reason);
    
    /**
     * 终止用户所有会话
     * @param userId 用户ID
     * @param excludeSessionId 排除的会话ID
     * @return 终止的会话数量
     */
    public int terminateAllUserSessions(String userId, String excludeSessionId);
    
    /**
     * 清理过期会话
     * @return 清理的会话数量
     */
    public int cleanupExpiredSessions();
    
    /**
     * 获取会话详情
     * @param sessionId 会话ID
     * @return 会话详情
     */
    public SessionDetails getSessionDetails(String sessionId);
    
    /**
     * 检查会话是否存在
     * @param sessionId 会话ID
     * @return 是否存在
     */
    public boolean sessionExists(String sessionId);
    
    /**
     * 更新会话设备信息
     * @param sessionId 会话ID
     * @param deviceInfo 新的设备信息
     */
    public void updateSessionDeviceInfo(String sessionId, DeviceInfo deviceInfo);
}
```

## API接口实现

### 认证控制器 (AuthController)

```java
@RestController
@RequestMapping("/api/v1/auth")
@Validated
@Slf4j
public class AuthController {
    
    private final AuthenticationManager authenticationManager;
    private final MFAManager mfaManager;
    private final SessionManager sessionManager;
    
    /**
     * 用户登录
     * POST /api/v1/auth/login
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(
            @RequestBody @Valid LoginRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            // 获取设备信息和IP
            DeviceInfo deviceInfo = extractDeviceInfo(httpRequest);
            String ipAddress = getClientIpAddress(httpRequest);
            
            // 执行认证
            AuthenticationResult authResult = authenticationManager.authenticateUser(request);
            
            if (authResult.isSuccess()) {
                // 检查是否需要MFA
                if (authResult.isMfaRequired()) {
                    return ResponseEntity.ok(ApiResponse.success(
                        LoginResponse.mfaRequired(authResult.getMfaChallenge())));
                }
                
                // 创建会话
                String sessionId = sessionManager.createUserSession(
                    authResult.getUserId(), deviceInfo, ipAddress);
                
                // 构建登录响应
                LoginResponse response = LoginResponse.builder()
                    .accessToken(authResult.getAccessToken())
                    .refreshToken(authResult.getRefreshToken())
                    .sessionId(sessionId)
                    .expiresIn(authResult.getExpiresIn())
                    .userInfo(authResult.getUserInfo())
                    .build();
                
                return ResponseEntity.ok(ApiResponse.success(response));
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(authResult.getErrorMessage()));
            }
            
        } catch (AuthenticationException e) {
            log.error("Authentication failed", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("认证失败: " + e.getMessage()));
        }
    }
    
    /**
     * 用户注册
     * POST /api/v1/auth/register
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<RegistrationResponse>> register(
            @RequestBody @Valid RegisterRequest request) {
        
        try {
            RegistrationResult result = authenticationManager.registerUser(request);
            
            if (result.isSuccess()) {
                RegistrationResponse response = RegistrationResponse.builder()
                    .userId(result.getUserId())
                    .message("注册成功")
                    .requiresEmailVerification(result.isRequiresEmailVerification())
                    .build();
                
                return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success(response));
            } else {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(result.getErrorMessage()));
            }
            
        } catch (RegistrationException e) {
            log.error("Registration failed", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("注册失败: " + e.getMessage()));
        }
    }
    
    /**
     * 刷新令牌
     * POST /api/v1/auth/refresh
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<TokenRefreshResponse>> refreshToken(
            @RequestBody @Valid TokenRefreshRequest request) {
        
        try {
            TokenPair tokenPair = authenticationManager.refreshAccessToken(request.getRefreshToken());
            
            TokenRefreshResponse response = TokenRefreshResponse.builder()
                .accessToken(tokenPair.getAccessToken())
                .refreshToken(tokenPair.getRefreshToken())
                .expiresIn(tokenPair.getExpiresIn())
                .build();
            
            return ResponseEntity.ok(ApiResponse.success(response));
            
        } catch (TokenException e) {
            log.error("Token refresh failed", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("令牌刷新失败: " + e.getMessage()));
        }
    }
    
    /**
     * 用户登出
     * POST /api/v1/auth/logout
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout(
            @RequestHeader("Authorization") String token,
            @RequestBody(required = false) LogoutRequest request) {
        
        try {
            String accessToken = extractTokenFromHeader(token);
            String userId = jwtTokenProvider.extractUserIdFromToken(accessToken);
            
            LogoutResult result = authenticationManager.logoutUser(userId, accessToken);
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(ApiResponse.success(null));
            } else {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error(result.getErrorMessage()));
            }
            
        } catch (Exception e) {
            log.error("Logout failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("登出失败"));
        }
    }
    
    /**
     * 验证MFA
     * POST /api/v1/auth/mfa/verify
     */
    @PostMapping("/mfa/verify")
    public ResponseEntity<ApiResponse<MFAVerificationResponse>> verifyMFA(
            @RequestBody @Valid MFAVerificationRequest request) {
        
        try {
            boolean isValid = false;
            
            switch (request.getMfaType()) {
                case TOTP:
                    isValid = mfaManager.verifyTOTPCode(request.getUserId(), request.getCode());
                    break;
                case SMS:
                    isValid = mfaManager.verifySMSCode(request.getUserId(), request.getCode());
                    break;
                case EMAIL:
                    isValid = mfaManager.verifyEmailCode(request.getUserId(), request.getCode());
                    break;
                case BACKUP:
                    isValid = mfaManager.verifyBackupCode(request.getUserId(), request.getCode());
                    break;
            }
            
            if (isValid) {
                // 生成最终的访问令牌
                // ... 实现逻辑
                
                MFAVerificationResponse response = MFAVerificationResponse.builder()
                    .success(true)
                    .accessToken("...")
                    .refreshToken("...")
                    .build();
                
                return ResponseEntity.ok(ApiResponse.success(response));
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error("MFA验证失败"));
            }
            
        } catch (Exception e) {
            log.error("MFA verification failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("MFA验证异常"));
        }
    }
}
```

## 连接流程

### 1. 用户登录流程

```mermaid
graph TD
    A[用户提交登录信息] --> B[验证用户名密码]
    B -->|验证失败| C[返回认证失败]
    B -->|验证成功| D[检查账户状态]
    D -->|账户锁定| E[返回账户锁定]
    D -->|账户正常| F[检查是否启用MFA]
    F -->|未启用MFA| G[生成JWT令牌]
    F -->|启用MFA| H[发送MFA挑战]
    H --> I[等待MFA验证]
    I -->|验证成功| G
    I -->|验证失败| J[返回MFA失败]
    G --> K[创建用户会话]
    K --> L[返回登录成功]
```

### 2. JWT令牌验证流程

```mermaid
graph TD
    A[接收API请求] --> B[提取Authorization头]
    B --> C[解析JWT令牌]
    C --> D[验证令牌签名]
    D -->|签名无效| E[返回认证失败]
    D -->|签名有效| F[检查令牌过期]
    F -->|已过期| G[返回令牌过期]
    F -->|未过期| H[检查黑名单]
    H -->|在黑名单| I[返回令牌无效]
    H -->|不在黑名单| J[提取用户信息]
    J --> K[验证用户状态]
    K --> L[认证成功]
```

### 3. MFA设置流程

```mermaid
graph TD
    A[用户请求启用MFA] --> B[验证用户身份]
    B --> C[选择MFA类型]
    C -->|TOTP| D[生成TOTP密钥]
    C -->|SMS| E[验证手机号码]
    C -->|EMAIL| F[验证邮箱地址]
    D --> G[显示二维码]
    E --> H[发送验证码]
    F --> I[发送验证码]
    G --> J[用户扫码验证]
    H --> K[用户输入验证码]
    I --> L[用户输入验证码]
    J --> M[验证TOTP代码]
    K --> N[验证SMS代码]
    L --> O[验证EMAIL代码]
    M --> P[保存MFA配置]
    N --> P
    O --> P
    P --> Q[生成备用代码]
    Q --> R[MFA设置完成]
```

## 数据模型

### 登录请求模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginRequest {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50字符之间")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 128, message = "密码长度必须在6-128字符之间")
    private String password;
    
    private String deviceId;
    private String deviceName;
    private Boolean rememberMe = false;
}
```

### 认证结果模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthenticationResult {
    private boolean success;
    private String userId;
    private String accessToken;
    private String refreshToken;
    private Long expiresIn;
    private UserInfo userInfo;
    private boolean mfaRequired;
    private MFAChallenge mfaChallenge;
    private String errorMessage;
    private String errorCode;
}
```

### 会话信息模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionInfo {
    private String sessionId;
    private String userId;
    private String deviceId;
    private String deviceName;
    private String ipAddress;
    private String location;
    private LocalDateTime createdAt;
    private LocalDateTime lastActiveAt;
    private boolean isActive;
    private Map<String, Object> metadata;
}
```

## 安全配置

### 密码策略配置

```yaml
security:
  password:
    minLength: 8
    maxLength: 128
    requireUppercase: true
    requireLowercase: true
    requireDigits: true
    requireSpecialChars: true
    maxFailedAttempts: 5
    lockoutDuration: 30 # 分钟
    
  jwt:
    secret: ${JWT_SECRET}
    accessTokenExpiration: 3600 # 1小时
    refreshTokenExpiration: 604800 # 7天
    
  mfa:
    totpIssuer: "Ark-Pets"
    smsCodeLength: 6
    emailCodeLength: 6
    codeExpiration: 300 # 5分钟
    maxAttempts: 3
    
  session:
    maxConcurrentSessions: 5
    sessionTimeout: 7200 # 2小时
    cleanupInterval: 3600 # 1小时
```

---

**模块负责人**: 认证服务开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
