# 权限授权管理 (Authorization)

## 模块概述

权限授权管理模块负责Ark-Pets AI Enhanced项目中的用户权限控制、角色管理、资源访问控制等功能。基于RBAC（基于角色的访问控制）模型，提供细粒度的权限管理，确保系统安全性和数据保护。

**核心职责**:
- 基于角色的访问控制（RBAC）
- 细粒度权限管理和验证
- 动态权限分配和回收
- 资源访问控制和审计
- 权限缓存和性能优化

## 核心功能架构

### 1. 权限管理架构

#### RBAC权限模型
```
┌─────────────────────────────────────┐
│           权限管理系统               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 用户管理  │ 角色管理  │ 权限管理  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           访问控制层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 权限验证  │ 资源控制  │ 审计日志  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据持久层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 用户数据  │ 角色数据  │ 权限数据  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 权限验证流程

#### 权限检查流程图
```mermaid
graph TB
    subgraph "权限验证流程"
        Request[访问请求]
        TokenCheck[Token验证]
        UserLoad[用户信息加载]
        RoleLoad[角色信息加载]
        PermissionCheck[权限检查]
        ResourceAccess[资源访问]
        AuditLog[审计日志]
        AccessGranted[访问允许]
        AccessDenied[访问拒绝]
    end
    
    subgraph "缓存优化"
        PermissionCache[权限缓存]
        RoleCache[角色缓存]
        UserCache[用户缓存]
    end
    
    subgraph "安全控制"
        RateLimiting[访问限流]
        SecurityAudit[安全审计]
        ThreatDetection[威胁检测]
    end
    
    Request --> TokenCheck
    TokenCheck -->|有效| UserLoad
    TokenCheck -->|无效| AccessDenied
    UserLoad --> UserCache
    UserCache --> RoleLoad
    RoleLoad --> RoleCache
    RoleCache --> PermissionCheck
    PermissionCheck --> PermissionCache
    PermissionCache -->|有权限| ResourceAccess
    PermissionCache -->|无权限| AccessDenied
    ResourceAccess --> RateLimiting
    RateLimiting --> SecurityAudit
    SecurityAudit --> ThreatDetection
    ThreatDetection --> AuditLog
    AuditLog --> AccessGranted
    
    AccessDenied --> AuditLog
```

## 核心类和接口

### 1. 权限管理服务

#### AuthorizationService - 权限管理主服务
```java
/**
 * 权限管理主服务
 * 负责用户权限验证、角色管理和访问控制
 */
@Service
@Slf4j
public class AuthorizationService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final PermissionCache permissionCache;
    private final AuditService auditService;
    private final SecurityEventPublisher securityEventPublisher;
    
    /**
     * 检查用户是否有指定权限
     * @param userId 用户ID
     * @param permission 权限标识
     * @param resource 资源标识
     * @return 是否有权限
     */
    public boolean hasPermission(String userId, String permission, String resource) {
        try {
            // 1. 参数验证
            validatePermissionRequest(userId, permission, resource);
            
            // 2. 检查缓存
            String cacheKey = buildPermissionCacheKey(userId, permission, resource);
            Boolean cachedResult = permissionCache.get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }
            
            // 3. 加载用户信息
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("用户不存在: " + userId));
            
            // 4. 检查用户状态
            if (!user.isActive()) {
                auditService.logSecurityEvent(SecurityEventType.ACCESS_DENIED_INACTIVE_USER, userId, permission, resource);
                return false;
            }
            
            // 5. 获取用户角色
            Set<Role> userRoles = getUserRoles(userId);
            
            // 6. 检查角色权限
            boolean hasPermission = checkRolePermissions(userRoles, permission, resource);
            
            // 7. 检查直接权限
            if (!hasPermission) {
                hasPermission = checkDirectPermissions(userId, permission, resource);
            }
            
            // 8. 缓存结果
            permissionCache.put(cacheKey, hasPermission, Duration.ofMinutes(15));
            
            // 9. 记录审计日志
            SecurityEventType eventType = hasPermission ? 
                SecurityEventType.ACCESS_GRANTED : SecurityEventType.ACCESS_DENIED;
            auditService.logSecurityEvent(eventType, userId, permission, resource);
            
            return hasPermission;
            
        } catch (Exception e) {
            log.error("权限检查失败: userId={}, permission={}, resource={}", userId, permission, resource, e);
            auditService.logSecurityEvent(SecurityEventType.PERMISSION_CHECK_ERROR, userId, permission, resource);
            return false;
        }
    }
    
    /**
     * 批量检查权限
     * @param userId 用户ID
     * @param permissionRequests 权限请求列表
     * @return 权限检查结果映射
     */
    public Map<String, Boolean> hasPermissions(String userId, List<PermissionRequest> permissionRequests) {
        Map<String, Boolean> results = new HashMap<>();
        
        for (PermissionRequest request : permissionRequests) {
            String key = request.getPermission() + ":" + request.getResource();
            boolean hasPermission = hasPermission(userId, request.getPermission(), request.getResource());
            results.put(key, hasPermission);
        }
        
        return results;
    }
    
    /**
     * 为用户分配角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param assignedBy 分配者ID
     */
    @Transactional
    public void assignRole(String userId, String roleId, String assignedBy) {
        try {
            // 1. 验证权限
            if (!hasPermission(assignedBy, "ROLE_ASSIGN", "USER:" + userId)) {
                throw new InsufficientPermissionException("无权限分配角色");
            }
            
            // 2. 验证用户和角色存在
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("用户不存在: " + userId));
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RoleNotFoundException("角色不存在: " + roleId));
            
            // 3. 检查是否已有该角色
            if (user.hasRole(roleId)) {
                log.warn("用户已拥有该角色: userId={}, roleId={}", userId, roleId);
                return;
            }
            
            // 4. 分配角色
            UserRole userRole = UserRole.builder()
                .userId(userId)
                .roleId(roleId)
                .assignedBy(assignedBy)
                .assignedAt(LocalDateTime.now())
                .build();
            
            user.addRole(userRole);
            userRepository.save(user);
            
            // 5. 清除权限缓存
            permissionCache.evictUserPermissions(userId);
            
            // 6. 发布事件
            securityEventPublisher.publishRoleAssigned(userId, roleId, assignedBy);
            
            // 7. 记录审计日志
            auditService.logRoleAssignment(userId, roleId, assignedBy);
            
            log.info("角色分配成功: userId={}, roleId={}, assignedBy={}", userId, roleId, assignedBy);
            
        } catch (Exception e) {
            log.error("角色分配失败: userId={}, roleId={}, assignedBy={}", userId, roleId, assignedBy, e);
            throw new RoleAssignmentException("角色分配失败", e);
        }
    }
    
    /**
     * 撤销用户角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param revokedBy 撤销者ID
     */
    @Transactional
    public void revokeRole(String userId, String roleId, String revokedBy) {
        try {
            // 1. 验证权限
            if (!hasPermission(revokedBy, "ROLE_REVOKE", "USER:" + userId)) {
                throw new InsufficientPermissionException("无权限撤销角色");
            }
            
            // 2. 验证用户存在
            User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException("用户不存在: " + userId));
            
            // 3. 检查是否有该角色
            if (!user.hasRole(roleId)) {
                log.warn("用户没有该角色: userId={}, roleId={}", userId, roleId);
                return;
            }
            
            // 4. 撤销角色
            user.removeRole(roleId);
            userRepository.save(user);
            
            // 5. 清除权限缓存
            permissionCache.evictUserPermissions(userId);
            
            // 6. 发布事件
            securityEventPublisher.publishRoleRevoked(userId, roleId, revokedBy);
            
            // 7. 记录审计日志
            auditService.logRoleRevocation(userId, roleId, revokedBy);
            
            log.info("角色撤销成功: userId={}, roleId={}, revokedBy={}", userId, roleId, revokedBy);
            
        } catch (Exception e) {
            log.error("角色撤销失败: userId={}, roleId={}, revokedBy={}", userId, roleId, revokedBy, e);
            throw new RoleRevocationException("角色撤销失败", e);
        }
    }
    
    /**
     * 获取用户权限列表
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<Permission> getUserPermissions(String userId) {
        Set<Permission> permissions = new HashSet<>();
        
        // 1. 获取角色权限
        Set<Role> userRoles = getUserRoles(userId);
        for (Role role : userRoles) {
            permissions.addAll(role.getPermissions());
        }
        
        // 2. 获取直接权限
        Set<Permission> directPermissions = getDirectPermissions(userId);
        permissions.addAll(directPermissions);
        
        return permissions;
    }
    
    /**
     * 获取用户角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    public Set<Role> getUserRoles(String userId) {
        return roleRepository.findByUserId(userId);
    }
    
    /**
     * 检查资源访问权限
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param action 操作类型
     * @return 是否有权限
     */
    public boolean canAccessResource(String userId, String resourceType, String resourceId, String action) {
        String permission = resourceType + ":" + action;
        String resource = resourceType + ":" + resourceId;
        return hasPermission(userId, permission, resource);
    }
    
    private void validatePermissionRequest(String userId, String permission, String resource) {
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(permission)) {
            throw new IllegalArgumentException("权限标识不能为空");
        }
        if (StringUtils.isBlank(resource)) {
            throw new IllegalArgumentException("资源标识不能为空");
        }
    }
    
    private String buildPermissionCacheKey(String userId, String permission, String resource) {
        return String.format("permission:%s:%s:%s", userId, permission, resource);
    }
    
    private boolean checkRolePermissions(Set<Role> roles, String permission, String resource) {
        for (Role role : roles) {
            if (role.hasPermission(permission, resource)) {
                return true;
            }
        }
        return false;
    }
    
    private boolean checkDirectPermissions(String userId, String permission, String resource) {
        Set<Permission> directPermissions = getDirectPermissions(userId);
        return directPermissions.stream()
            .anyMatch(p -> p.matches(permission, resource));
    }
    
    private Set<Permission> getDirectPermissions(String userId) {
        return permissionRepository.findDirectPermissionsByUserId(userId);
    }
}
```

### 2. 角色管理服务

#### RoleManagementService - 角色管理服务
```java
/**
 * 角色管理服务
 * 负责角色的创建、修改、删除和权限分配
 */
@Service
@Slf4j
public class RoleManagementService {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final AuthorizationService authorizationService;
    private final PermissionCache permissionCache;
    private final AuditService auditService;

    /**
     * 创建新角色
     * @param createRequest 角色创建请求
     * @param createdBy 创建者ID
     * @return 创建的角色
     */
    @Transactional
    public Role createRole(CreateRoleRequest createRequest, String createdBy) {
        try {
            // 1. 验证权限
            if (!authorizationService.hasPermission(createdBy, "ROLE_CREATE", "SYSTEM")) {
                throw new InsufficientPermissionException("无权限创建角色");
            }

            // 2. 验证角色名称唯一性
            if (roleRepository.existsByName(createRequest.getName())) {
                throw new RoleAlreadyExistsException("角色名称已存在: " + createRequest.getName());
            }

            // 3. 创建角色
            Role role = Role.builder()
                .id(UUID.randomUUID().toString())
                .name(createRequest.getName())
                .displayName(createRequest.getDisplayName())
                .description(createRequest.getDescription())
                .type(createRequest.getType())
                .status(RoleStatus.ACTIVE)
                .createdBy(createdBy)
                .createdAt(LocalDateTime.now())
                .build();

            // 4. 分配初始权限
            if (createRequest.getPermissionIds() != null && !createRequest.getPermissionIds().isEmpty()) {
                Set<Permission> permissions = permissionRepository.findByIdIn(createRequest.getPermissionIds());
                role.setPermissions(permissions);
            }

            // 5. 保存角色
            role = roleRepository.save(role);

            // 6. 记录审计日志
            auditService.logRoleCreation(role.getId(), role.getName(), createdBy);

            log.info("角色创建成功: roleId={}, roleName={}, createdBy={}", role.getId(), role.getName(), createdBy);

            return role;

        } catch (Exception e) {
            log.error("角色创建失败: roleName={}, createdBy={}", createRequest.getName(), createdBy, e);
            throw new RoleCreationException("角色创建失败", e);
        }
    }

    /**
     * 更新角色信息
     * @param roleId 角色ID
     * @param updateRequest 更新请求
     * @param updatedBy 更新者ID
     * @return 更新后的角色
     */
    @Transactional
    public Role updateRole(String roleId, UpdateRoleRequest updateRequest, String updatedBy) {
        try {
            // 1. 验证权限
            if (!authorizationService.hasPermission(updatedBy, "ROLE_UPDATE", "ROLE:" + roleId)) {
                throw new InsufficientPermissionException("无权限更新角色");
            }

            // 2. 获取角色
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RoleNotFoundException("角色不存在: " + roleId));

            // 3. 更新基本信息
            if (updateRequest.getDisplayName() != null) {
                role.setDisplayName(updateRequest.getDisplayName());
            }
            if (updateRequest.getDescription() != null) {
                role.setDescription(updateRequest.getDescription());
            }
            if (updateRequest.getStatus() != null) {
                role.setStatus(updateRequest.getStatus());
            }

            role.setUpdatedBy(updatedBy);
            role.setUpdatedAt(LocalDateTime.now());

            // 4. 保存更新
            role = roleRepository.save(role);

            // 5. 清除相关缓存
            permissionCache.evictRolePermissions(roleId);

            // 6. 记录审计日志
            auditService.logRoleUpdate(roleId, updatedBy);

            log.info("角色更新成功: roleId={}, updatedBy={}", roleId, updatedBy);

            return role;

        } catch (Exception e) {
            log.error("角色更新失败: roleId={}, updatedBy={}", roleId, updatedBy, e);
            throw new RoleUpdateException("角色更新失败", e);
        }
    }

    /**
     * 为角色分配权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param assignedBy 分配者ID
     */
    @Transactional
    public void assignPermissionsToRole(String roleId, Set<String> permissionIds, String assignedBy) {
        try {
            // 1. 验证权限
            if (!authorizationService.hasPermission(assignedBy, "ROLE_PERMISSION_ASSIGN", "ROLE:" + roleId)) {
                throw new InsufficientPermissionException("无权限分配角色权限");
            }

            // 2. 获取角色
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RoleNotFoundException("角色不存在: " + roleId));

            // 3. 获取权限
            Set<Permission> permissions = permissionRepository.findByIdIn(permissionIds);
            if (permissions.size() != permissionIds.size()) {
                throw new PermissionNotFoundException("部分权限不存在");
            }

            // 4. 分配权限
            role.addPermissions(permissions);
            roleRepository.save(role);

            // 5. 清除缓存
            permissionCache.evictRolePermissions(roleId);

            // 6. 记录审计日志
            auditService.logRolePermissionAssignment(roleId, permissionIds, assignedBy);

            log.info("角色权限分配成功: roleId={}, permissionCount={}, assignedBy={}",
                roleId, permissionIds.size(), assignedBy);

        } catch (Exception e) {
            log.error("角色权限分配失败: roleId={}, assignedBy={}", roleId, assignedBy, e);
            throw new RolePermissionAssignmentException("角色权限分配失败", e);
        }
    }

    /**
     * 撤销角色权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param revokedBy 撤销者ID
     */
    @Transactional
    public void revokePermissionsFromRole(String roleId, Set<String> permissionIds, String revokedBy) {
        try {
            // 1. 验证权限
            if (!authorizationService.hasPermission(revokedBy, "ROLE_PERMISSION_REVOKE", "ROLE:" + roleId)) {
                throw new InsufficientPermissionException("无权限撤销角色权限");
            }

            // 2. 获取角色
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RoleNotFoundException("角色不存在: " + roleId));

            // 3. 撤销权限
            role.removePermissions(permissionIds);
            roleRepository.save(role);

            // 4. 清除缓存
            permissionCache.evictRolePermissions(roleId);

            // 5. 记录审计日志
            auditService.logRolePermissionRevocation(roleId, permissionIds, revokedBy);

            log.info("角色权限撤销成功: roleId={}, permissionCount={}, revokedBy={}",
                roleId, permissionIds.size(), revokedBy);

        } catch (Exception e) {
            log.error("角色权限撤销失败: roleId={}, revokedBy={}", roleId, revokedBy, e);
            throw new RolePermissionRevocationException("角色权限撤销失败", e);
        }
    }

    /**
     * 删除角色
     * @param roleId 角色ID
     * @param deletedBy 删除者ID
     */
    @Transactional
    public void deleteRole(String roleId, String deletedBy) {
        try {
            // 1. 验证权限
            if (!authorizationService.hasPermission(deletedBy, "ROLE_DELETE", "ROLE:" + roleId)) {
                throw new InsufficientPermissionException("无权限删除角色");
            }

            // 2. 获取角色
            Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RoleNotFoundException("角色不存在: " + roleId));

            // 3. 检查是否有用户使用该角色
            long userCount = roleRepository.countUsersWithRole(roleId);
            if (userCount > 0) {
                throw new RoleInUseException("角色正在被使用，无法删除: " + userCount + " 个用户");
            }

            // 4. 软删除角色
            role.setStatus(RoleStatus.DELETED);
            role.setDeletedBy(deletedBy);
            role.setDeletedAt(LocalDateTime.now());
            roleRepository.save(role);

            // 5. 清除缓存
            permissionCache.evictRolePermissions(roleId);

            // 6. 记录审计日志
            auditService.logRoleDeletion(roleId, deletedBy);

            log.info("角色删除成功: roleId={}, deletedBy={}", roleId, deletedBy);

        } catch (Exception e) {
            log.error("角色删除失败: roleId={}, deletedBy={}", roleId, deletedBy, e);
            throw new RoleDeletionException("角色删除失败", e);
        }
    }

    /**
     * 获取所有可用角色
     * @return 角色列表
     */
    public List<Role> getAllActiveRoles() {
        return roleRepository.findByStatus(RoleStatus.ACTIVE);
    }

    /**
     * 根据类型获取角色
     * @param roleType 角色类型
     * @return 角色列表
     */
    public List<Role> getRolesByType(RoleType roleType) {
        return roleRepository.findByTypeAndStatus(roleType, RoleStatus.ACTIVE);
    }
}
```

## 数据模型定义

### 1. 权限相关实体

#### Permission - 权限实体
```java
/**
 * 权限实体
 * 定义系统中的具体权限
 */
@Entity
@Table(name = "permissions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Permission {

    @Id
    private String id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(nullable = false)
    private String displayName;

    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PermissionType type;

    @Column(nullable = false)
    private String resource;

    @Column(nullable = false)
    private String action;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PermissionStatus status;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    /**
     * 检查权限是否匹配
     * @param permission 权限标识
     * @param resource 资源标识
     * @return 是否匹配
     */
    public boolean matches(String permission, String resource) {
        return this.name.equals(permission) &&
               (this.resource.equals("*") || this.resource.equals(resource));
    }
}
```

#### Role - 角色实体
```java
/**
 * 角色实体
 * 定义用户角色和关联权限
 */
@Entity
@Table(name = "roles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Role {

    @Id
    private String id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(nullable = false)
    private String displayName;

    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RoleType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RoleStatus status;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();

    @Column(nullable = false)
    private String createdBy;

    @Column(nullable = false)
    private LocalDateTime createdAt;

    private String updatedBy;
    private LocalDateTime updatedAt;
    private String deletedBy;
    private LocalDateTime deletedAt;

    /**
     * 检查角色是否有指定权限
     * @param permission 权限标识
     * @param resource 资源标识
     * @return 是否有权限
     */
    public boolean hasPermission(String permission, String resource) {
        return permissions.stream()
            .anyMatch(p -> p.matches(permission, resource));
    }

    /**
     * 添加权限
     * @param newPermissions 新权限集合
     */
    public void addPermissions(Set<Permission> newPermissions) {
        this.permissions.addAll(newPermissions);
    }

    /**
     * 移除权限
     * @param permissionIds 权限ID集合
     */
    public void removePermissions(Set<String> permissionIds) {
        this.permissions.removeIf(p -> permissionIds.contains(p.getId()));
    }
}
```

## 使用示例

### 基本权限检查
```java
// 注入权限服务
@Autowired
private AuthorizationService authorizationService;

// 检查用户权限
String userId = "user123";
boolean canRead = authorizationService.hasPermission(userId, "CONVERSATION_READ", "CONVERSATION:conv456");
boolean canWrite = authorizationService.hasPermission(userId, "CONVERSATION_WRITE", "CONVERSATION:conv456");

// 批量权限检查
List<PermissionRequest> requests = Arrays.asList(
    new PermissionRequest("CONVERSATION_READ", "CONVERSATION:conv456"),
    new PermissionRequest("CONVERSATION_WRITE", "CONVERSATION:conv456"),
    new PermissionRequest("USER_PROFILE_UPDATE", "USER:user123")
);
Map<String, Boolean> results = authorizationService.hasPermissions(userId, requests);
```

### 角色管理
```java
// 注入角色管理服务
@Autowired
private RoleManagementService roleManagementService;

// 创建角色
CreateRoleRequest createRequest = CreateRoleRequest.builder()
    .name("PREMIUM_USER")
    .displayName("高级用户")
    .description("拥有高级功能访问权限的用户")
    .type(RoleType.USER_ROLE)
    .permissionIds(Set.of("CONVERSATION_CREATE", "CONVERSATION_READ", "CONVERSATION_WRITE"))
    .build();

Role role = roleManagementService.createRole(createRequest, "admin123");

// 分配角色给用户
authorizationService.assignRole("user123", role.getId(), "admin123");

// 为角色添加权限
Set<String> newPermissions = Set.of("AI_MODEL_ACCESS", "PREMIUM_FEATURES");
roleManagementService.assignPermissionsToRole(role.getId(), newPermissions, "admin123");
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的权限授权管理实现，支持RBAC模型、细粒度权限控制、角色管理等功能
