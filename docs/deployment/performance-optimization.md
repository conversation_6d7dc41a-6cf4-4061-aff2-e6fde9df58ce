# 性能优化指南 (Performance Optimization Guide)

## 模块概述

性能优化指南提供了Ark-Pets AI Enhanced项目的全面性能优化策略，包括应用层优化、数据库优化、缓存策略、网络优化、系统调优等方面的最佳实践。

**优化目标**:
- 提升系统响应速度和吞吐量
- 降低资源消耗和运营成本
- 增强系统稳定性和可扩展性
- 优化用户体验和服务质量

## 性能优化架构

### 1. 性能优化层次结构

#### 多层优化架构
```
┌─────────────────────────────────────┐
│           用户体验层优化              │
│    ┌─────────────┬─────────────┐    │
│    │  前端优化    │   CDN加速   │    │
│    └─────────────┴─────────────┘    │
├─────────────────────────────────────┤
│           应用层优化                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 代码优化  │ 算法优化  │ 并发优化  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           中间件优化                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 缓存优化  │ 数据库   │  消息队列 │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           基础设施优化               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 服务器   │  网络    │   存储   │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 性能监控体系

#### 性能指标监控流程
```mermaid
graph TB
    subgraph "性能监控"
        Metrics[性能指标收集]
        Analysis[性能分析]
        Alert[性能告警]
        Optimization[优化建议]
    end
    
    subgraph "监控指标"
        ResponseTime[响应时间]
        Throughput[吞吐量]
        ErrorRate[错误率]
        ResourceUsage[资源使用率]
    end
    
    subgraph "优化策略"
        CodeOpt[代码优化]
        DatabaseOpt[数据库优化]
        CacheOpt[缓存优化]
        InfraOpt[基础设施优化]
    end
    
    Metrics --> Analysis
    Analysis --> Alert
    Alert --> Optimization
    
    ResponseTime --> Metrics
    Throughput --> Metrics
    ErrorRate --> Metrics
    ResourceUsage --> Metrics
    
    Optimization --> CodeOpt
    Optimization --> DatabaseOpt
    Optimization --> CacheOpt
    Optimization --> InfraOpt
```

## 应用层性能优化

### 1. Java应用优化

#### JVM参数调优
```bash
# 生产环境JVM参数配置
JAVA_OPTS="-server \
  -Xms2g -Xmx4g \
  -XX:NewRatio=3 \
  -XX:SurvivorRatio=8 \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:G1HeapRegionSize=16m \
  -XX:+UseStringDeduplication \
  -XX:+OptimizeStringConcat \
  -XX:+UseCompressedOops \
  -XX:+UseCompressedClassPointers \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseCGroupMemoryLimitForHeap \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -XX:+PrintGCApplicationStoppedTime \
  -Xloggc:/app/logs/gc.log \
  -XX:+UseGCLogFileRotation \
  -XX:NumberOfGCLogFiles=5 \
  -XX:GCLogFileSize=100M"

# AI服务专用优化参数
AI_SERVICE_OPTS="-XX:+UseLargePages \
  -XX:+AggressiveOpts \
  -XX:+UseFastAccessorMethods \
  -XX:+OptimizeStringConcat \
  -XX:+UseStringCache"

# 用户服务优化参数
USER_SERVICE_OPTS="-XX:+UseParallelGC \
  -XX:ParallelGCThreads=4 \
  -XX:+UseAdaptiveSizePolicy"
```

#### Spring Boot应用优化
```java
@Configuration
@EnableConfigurationProperties(PerformanceProperties.class)
public class PerformanceConfiguration {
    
    /**
     * 异步任务执行器配置
     */
    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor(PerformanceProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.getThreadPool().getCoreSize());
        executor.setMaxPoolSize(properties.getThreadPool().getMaxSize());
        executor.setQueueCapacity(properties.getThreadPool().getQueueCapacity());
        executor.setKeepAliveSeconds(properties.getThreadPool().getKeepAliveSeconds());
        executor.setThreadNamePrefix("arkpets-async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
    
    /**
     * HTTP客户端连接池配置
     */
    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        
        // 连接池配置
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(50);
        connectionManager.setValidateAfterInactivity(30000);
        
        // HTTP客户端配置
        CloseableHttpClient httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .setConnectionTimeToLive(60, TimeUnit.SECONDS)
            .setDefaultRequestConfig(RequestConfig.custom()
                .setConnectTimeout(5000)
                .setSocketTimeout(30000)
                .setConnectionRequestTimeout(5000)
                .build())
            .build();
            
        factory.setHttpClient(httpClient);
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(30000);
        
        return new RestTemplate(factory);
    }
    
    /**
     * 缓存配置优化
     */
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager.builder(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration())
            .transactionAware();
            
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .disableCachingNullValues();
    }
}
```

### 2. 代码层面优化

#### 高性能AI服务实现
```java
@Service
@Slf4j
public class OptimizedChatService {
    
    private final ConversationRepository conversationRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final AIModelClient aiModelClient;
    private final ThreadPoolTaskExecutor taskExecutor;
    
    // 对话上下文缓存
    private final LoadingCache<String, ConversationContext> contextCache = Caffeine.newBuilder()
        .maximumSize(10000)
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .recordStats()
        .build(this::loadConversationContext);
    
    /**
     * 优化的聊天处理方法
     */
    @Async("taskExecutor")
    public CompletableFuture<ChatResponse> processChatMessageAsync(ChatRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 快速参数验证
                validateChatRequest(request);
                
                // 2. 获取缓存的对话上下文
                ConversationContext context = contextCache.get(request.getConversationId());
                
                // 3. 构建优化的提示词
                String optimizedPrompt = buildOptimizedPrompt(request, context);
                
                // 4. 异步调用AI模型
                CompletableFuture<AIResponse> aiResponseFuture = aiModelClient.sendMessageAsync(optimizedPrompt);
                
                // 5. 并行处理其他任务
                CompletableFuture<Void> updateContextFuture = CompletableFuture.runAsync(() -> {
                    updateConversationContext(context, request);
                }, taskExecutor);
                
                // 6. 等待AI响应
                AIResponse aiResponse = aiResponseFuture.get(30, TimeUnit.SECONDS);
                
                // 7. 等待上下文更新完成
                updateContextFuture.get(5, TimeUnit.SECONDS);
                
                // 8. 构建响应
                return buildChatResponse(request, aiResponse, context);
                
            } catch (Exception e) {
                log.error("聊天处理失败: {}", e.getMessage(), e);
                throw new ChatProcessingException("聊天处理失败", e);
            }
        }, taskExecutor);
    }

    /**
     * 批量预加载对话上下文
     */
    @EventListener
    public void preloadConversationContexts(UserLoginEvent event) {
        taskExecutor.execute(() -> {
            try {
                List<String> recentConversations = conversationRepository
                    .findRecentConversationIds(event.getUserId(), 10);

                // 预加载到缓存
                recentConversations.parallelStream()
                    .forEach(conversationId -> {
                        try {
                            contextCache.get(conversationId);
                        } catch (Exception e) {
                            log.warn("预加载对话上下文失败: {}", conversationId, e);
                        }
                    });

                log.info("为用户 {} 预加载了 {} 个对话上下文",
                    event.getUserId(), recentConversations.size());

            } catch (Exception e) {
                log.error("预加载对话上下文失败", e);
            }
        });
    }

    /**
     * 优化的提示词构建
     */
    private String buildOptimizedPrompt(ChatRequest request, ConversationContext context) {
        StringBuilder promptBuilder = new StringBuilder(1024);

        // 1. 角色设定（缓存的）
        promptBuilder.append(context.getCharacterPrompt());

        // 2. 精简的历史对话（只保留关键信息）
        List<Message> recentMessages = context.getRecentMessages(5);
        for (Message message : recentMessages) {
            promptBuilder.append("\n")
                .append(message.getRole()).append(": ")
                .append(truncateMessage(message.getContent(), 200));
        }

        // 3. 当前用户消息
        promptBuilder.append("\nUser: ").append(request.getMessage());
        promptBuilder.append("\nAssistant: ");

        return promptBuilder.toString();
    }

    /**
     * 消息截断优化
     */
    private String truncateMessage(String message, int maxLength) {
        if (message.length() <= maxLength) {
            return message;
        }
        return message.substring(0, maxLength - 3) + "...";
    }
}
```

## 数据库性能优化

### 1. PostgreSQL优化配置

#### postgresql.conf优化
```ini
# 内存配置
shared_buffers = 1GB                    # 共享缓冲区
effective_cache_size = 3GB              # 有效缓存大小
work_mem = 16MB                         # 工作内存
maintenance_work_mem = 256MB            # 维护工作内存

# 连接配置
max_connections = 200                   # 最大连接数
superuser_reserved_connections = 3      # 超级用户保留连接

# WAL配置
wal_buffers = 16MB                      # WAL缓冲区
checkpoint_completion_target = 0.9      # 检查点完成目标
checkpoint_timeout = 10min              # 检查点超时
max_wal_size = 2GB                      # 最大WAL大小
min_wal_size = 1GB                      # 最小WAL大小

# 查询优化
random_page_cost = 1.1                  # 随机页面成本
effective_io_concurrency = 200          # 有效IO并发
default_statistics_target = 100         # 默认统计目标

# 日志配置
log_min_duration_statement = 1000       # 记录慢查询（1秒）
log_checkpoints = on                    # 记录检查点
log_connections = on                    # 记录连接
log_disconnections = on                 # 记录断开连接
log_lock_waits = on                     # 记录锁等待
```

#### 索引优化策略
```sql
-- 1. 聊天消息表索引优化
CREATE INDEX CONCURRENTLY idx_messages_conversation_created
ON chat_messages (conversation_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_messages_user_created
ON chat_messages (user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_messages_content_gin
ON chat_messages USING gin(to_tsvector('english', content));

-- 2. 用户表索引优化
CREATE UNIQUE INDEX CONCURRENTLY idx_users_email_active
ON users (email) WHERE active = true;

CREATE INDEX CONCURRENTLY idx_users_last_login
ON users (last_login_at DESC) WHERE active = true;

-- 3. 对话表索引优化
CREATE INDEX CONCURRENTLY idx_conversations_user_updated
ON conversations (user_id, updated_at DESC);

CREATE INDEX CONCURRENTLY idx_conversations_character
ON conversations (character_name, created_at DESC);

-- 4. 复合索引优化
CREATE INDEX CONCURRENTLY idx_ai_requests_composite
ON ai_requests (user_id, model_provider, created_at DESC);
```

### 2. 查询优化

#### 高效查询实现
```java
@Repository
public class OptimizedConversationRepository {

    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    /**
     * 优化的分页查询
     */
    public Page<ConversationSummary> findUserConversations(String userId, Pageable pageable) {
        // 使用游标分页而不是OFFSET
        String sql = """
            SELECT c.id, c.title, c.character_name, c.updated_at,
                   (SELECT COUNT(*) FROM chat_messages m WHERE m.conversation_id = c.id) as message_count
            FROM conversations c
            WHERE c.user_id = :userId
              AND (:cursor IS NULL OR c.updated_at < :cursor)
            ORDER BY c.updated_at DESC
            LIMIT :limit
            """;

        Map<String, Object> params = Map.of(
            "userId", userId,
            "cursor", getCursorFromPageable(pageable),
            "limit", pageable.getPageSize()
        );

        List<ConversationSummary> conversations = namedParameterJdbcTemplate.query(
            sql, params, conversationSummaryRowMapper);

        return new PageImpl<>(conversations, pageable, getTotalCount(userId));

    /**
     * 批量查询优化
     */
    public Map<String, ConversationContext> findConversationContexts(List<String> conversationIds) {
        if (conversationIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 使用IN查询批量获取
        String sql = """
            SELECT c.id, c.character_name, c.personality_id, c.context_data,
                   array_agg(
                       json_build_object(
                           'role', m.role,
                           'content', m.content,
                           'timestamp', m.created_at
                       ) ORDER BY m.created_at DESC
                   ) FILTER (WHERE m.id IS NOT NULL) as recent_messages
            FROM conversations c
            LEFT JOIN (
                SELECT DISTINCT ON (conversation_id)
                       conversation_id, id, role, content, created_at
                FROM chat_messages
                WHERE conversation_id = ANY(:conversationIds)
                ORDER BY conversation_id, created_at DESC
                LIMIT 5
            ) m ON c.id = m.conversation_id
            WHERE c.id = ANY(:conversationIds)
            GROUP BY c.id, c.character_name, c.personality_id, c.context_data
            """;

        Map<String, Object> params = Map.of("conversationIds", conversationIds.toArray());

        return namedParameterJdbcTemplate.query(sql, params, rs -> {
            Map<String, ConversationContext> result = new HashMap<>();
            while (rs.next()) {
                ConversationContext context = mapRowToConversationContext(rs);
                result.put(rs.getString("id"), context);
            }
            return result;
        });
    }
}
```

## 缓存性能优化

### 1. Redis缓存策略

#### 多级缓存架构
```java
@Configuration
public class CacheOptimizationConfiguration {

    /**
     * 多级缓存管理器
     */
    @Bean
    public CacheManager multiLevelCacheManager() {
        CompositeCacheManager cacheManager = new CompositeCacheManager();

        // L1缓存：本地Caffeine缓存
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .recordStats());

        // L2缓存：Redis分布式缓存
        RedisCacheManager redisCacheManager = RedisCacheManager.builder(redisConnectionFactory())
            .cacheDefaults(redisCacheConfiguration())
            .build();

        cacheManager.setCacheManagers(Arrays.asList(caffeineCacheManager, redisCacheManager));
        cacheManager.setFallbackToNoOpCache(false);

        return cacheManager;
    }

    private RedisCacheConfiguration redisCacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .disableCachingNullValues()
            .computePrefixWith(cacheName -> "arkpets:cache:" + cacheName + ":");
    }
}
```

#### 智能缓存策略
```java
@Service
public class IntelligentCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final LoadingCache<String, Object> localCache;

    /**
     * 智能缓存获取
     */
    public <T> T getWithIntelligentCache(String key, Class<T> type, Supplier<T> dataLoader) {
        // 1. 尝试从本地缓存获取
        T value = (T) localCache.getIfPresent(key);
        if (value != null) {
            return value;
        }

        // 2. 尝试从Redis获取
        value = (T) redisTemplate.opsForValue().get(key);
        if (value != null) {
            // 异步更新本地缓存
            localCache.put(key, value);
            return value;
        }

        // 3. 从数据源加载
        value = dataLoader.get();
        if (value != null) {
            // 异步更新多级缓存
            CompletableFuture.runAsync(() -> {
                localCache.put(key, value);
                redisTemplate.opsForValue().set(key, value, Duration.ofHours(1));
            });
        }

        return value;
    }

    /**
     * 预热缓存
     */
    @EventListener
    public void warmupCache(ApplicationReadyEvent event) {
        CompletableFuture.runAsync(() -> {
            try {
                // 预热热点数据
                warmupHotData();

                // 预热用户数据
                warmupUserData();

                // 预热配置数据
                warmupConfigurationData();

                log.info("缓存预热完成");

            } catch (Exception e) {
                log.error("缓存预热失败", e);
            }
        });
    }

    private void warmupHotData() {
        // 预热角色数据
        List<Character> characters = characterService.getAllCharacters();
        characters.forEach(character -> {
            String key = "character:" + character.getName();
            redisTemplate.opsForValue().set(key, character, Duration.ofDays(1));
        });

        // 预热性格数据
        List<Personality> personalities = personalityService.getAllPersonalities();
        personalities.forEach(personality -> {
            String key = "personality:" + personality.getId();
            redisTemplate.opsForValue().set(key, personality, Duration.ofDays(1));
        });
    }
}
```

### 2. 缓存优化策略

#### 缓存穿透防护
```java
@Component
public class CachePenetrationProtection {

    private final RedisTemplate<String, Object> redisTemplate;
    private final BloomFilter<String> bloomFilter;

    /**
     * 布隆过滤器防护
     */
    public <T> T getWithBloomFilter(String key, Class<T> type, Supplier<T> dataLoader) {
        // 1. 布隆过滤器检查
        if (!bloomFilter.mightContain(key)) {
            return null; // 确定不存在
        }

        // 2. 查询缓存
        T value = (T) redisTemplate.opsForValue().get(key);
        if (value != null) {
            return value;
        }

        // 3. 查询数据库
        value = dataLoader.get();
        if (value != null) {
            // 更新缓存和布隆过滤器
            redisTemplate.opsForValue().set(key, value, Duration.ofHours(1));
            bloomFilter.put(key);
        } else {
            // 缓存空值，防止缓存穿透
            redisTemplate.opsForValue().set(key, "NULL", Duration.ofMinutes(5));
        }

        return value;
    }

    /**
     * 分布式锁防护
     */
    public <T> T getWithDistributedLock(String key, Class<T> type, Supplier<T> dataLoader) {
        String lockKey = "lock:" + key;
        String lockValue = UUID.randomUUID().toString();

        try {
            // 尝试获取分布式锁
            Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));

            if (Boolean.TRUE.equals(acquired)) {
                // 获取锁成功，执行数据加载
                T value = dataLoader.get();
                if (value != null) {
                    redisTemplate.opsForValue().set(key, value, Duration.ofHours(1));
                }
                return value;
            } else {
                // 获取锁失败，等待并重试
                Thread.sleep(100);
                return (T) redisTemplate.opsForValue().get(key);
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        } finally {
            // 释放锁
            releaseLock(lockKey, lockValue);
        }

    private void releaseLock(String lockKey, String lockValue) {
        String script = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
            """;
        redisTemplate.execute(new DefaultRedisScript<>(script, Long.class),
            Collections.singletonList(lockKey), lockValue);
    }
}
```

## 网络和基础设施优化

### 1. Nginx负载均衡优化

#### nginx.conf优化配置
```nginx
# 工作进程数
worker_processes auto;
worker_cpu_affinity auto;

# 事件模型
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # 基础优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;

    # 缓冲区优化
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上游服务器配置
    upstream ai_service {
        least_conn;
        server ai-service-1:8080 max_fails=3 fail_timeout=30s;
        server ai-service-2:8080 max_fails=3 fail_timeout=30s;
        server ai-service-3:8080 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream user_service {
        ip_hash;
        server user-service-1:8081 max_fails=3 fail_timeout=30s;
        server user-service-2:8081 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # 缓存配置
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=api_cache:10m
                     max_size=1g inactive=60m use_temp_path=off;

    server {
        listen 80;
        listen 443 ssl http2;
        server_name api.arkpets.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/arkpets.crt;
        ssl_certificate_key /etc/nginx/ssl/arkpets.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # AI服务代理
        location /api/v1/ai/ {
            proxy_pass http://ai_service/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            # 缓存配置
            proxy_cache api_cache;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404 1m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            add_header X-Cache-Status $upstream_cache_status;
        }

        # 用户服务代理
        location /api/v1/users/ {
            proxy_pass http://user_service/;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 3s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 静态资源缓存
        location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
        }
    }
}
```

### 2. 系统级优化

#### Linux系统参数调优
```bash
#!/bin/bash
# 系统性能优化脚本

# 网络参数优化
echo "net.core.rmem_max = 134217728" >> /etc/sysctl.conf
echo "net.core.wmem_max = 134217728" >> /etc/sysctl.conf
echo "net.ipv4.tcp_rmem = 4096 65536 134217728" >> /etc/sysctl.conf
echo "net.ipv4.tcp_wmem = 4096 65536 134217728" >> /etc/sysctl.conf
echo "net.core.netdev_max_backlog = 5000" >> /etc/sysctl.conf
echo "net.ipv4.tcp_congestion_control = bbr" >> /etc/sysctl.conf

# 文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 内存参数优化
echo "vm.swappiness = 10" >> /etc/sysctl.conf
echo "vm.dirty_ratio = 15" >> /etc/sysctl.conf
echo "vm.dirty_background_ratio = 5" >> /etc/sysctl.conf

# 应用生效
sysctl -p
```

## 性能监控和调优

### 1. 性能指标监控

#### 关键性能指标定义
```java
@Component
public class PerformanceMetrics {

    private final MeterRegistry meterRegistry;
    private final Timer.Sample chatProcessingTimer;
    private final Counter chatRequestCounter;
    private final Gauge activeConversationsGauge;

    /**
     * 聊天处理性能监控
     */
    public void recordChatProcessingTime(Duration duration) {
        Timer.builder("chat.processing.time")
            .description("聊天处理时间")
            .tag("service", "ai")
            .register(meterRegistry)
            .record(duration);
    }

    /**
     * 数据库查询性能监控
     */
    public void recordDatabaseQueryTime(String operation, Duration duration) {
        Timer.builder("database.query.time")
            .description("数据库查询时间")
            .tag("operation", operation)
            .register(meterRegistry)
            .record(duration);
    }

    /**
     * 缓存命中率监控
     */
    public void recordCacheHit(String cacheName, boolean hit) {
        Counter.builder("cache.requests")
            .description("缓存请求统计")
            .tag("cache", cacheName)
            .tag("result", hit ? "hit" : "miss")
            .register(meterRegistry)
            .increment();
    }

    /**
     * AI模型调用性能监控
     */
    public void recordAIModelCall(String provider, String model, Duration duration, boolean success) {
        Timer.builder("ai.model.call.time")
            .description("AI模型调用时间")
            .tag("provider", provider)
            .tag("model", model)
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .record(duration);
    }
}
```

### 2. 自动化性能调优

#### 自适应性能调优器
```java
@Component
@Slf4j
public class AdaptivePerformanceTuner {

    private final MeterRegistry meterRegistry;
    private final ThreadPoolTaskExecutor taskExecutor;
    private final RedisTemplate<String, Object> redisTemplate;

    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void autoTunePerformance() {
        try {
            // 1. 分析当前性能指标
            PerformanceAnalysis analysis = analyzeCurrentPerformance();

            // 2. 根据分析结果调整参数
            if (analysis.isHighLoad()) {
                adjustForHighLoad();
            } else if (analysis.isLowLoad()) {
                adjustForLowLoad();
            }

            // 3. 调整缓存策略
            adjustCacheStrategy(analysis);

            // 4. 调整线程池参数
            adjustThreadPoolParameters(analysis);

            log.info("自动性能调优完成: {}", analysis);

        } catch (Exception e) {
            log.error("自动性能调优失败", e);
        }
    }

    private void adjustForHighLoad() {
        // 增加线程池大小
        int currentMaxSize = taskExecutor.getMaxPoolSize();
        int newMaxSize = Math.min(currentMaxSize + 10, 200);
        taskExecutor.setMaxPoolSize(newMaxSize);

        // 调整缓存过期时间
        adjustCacheExpiration(0.8); // 减少20%的过期时间

        log.info("高负载调优: 线程池大小调整为 {}", newMaxSize);
    }

    private void adjustForLowLoad() {
        // 减少线程池大小
        int currentMaxSize = taskExecutor.getMaxPoolSize();
        int newMaxSize = Math.max(currentMaxSize - 5, 50);
        taskExecutor.setMaxPoolSize(newMaxSize);

        // 延长缓存过期时间
        adjustCacheExpiration(1.2); // 增加20%的过期时间

        log.info("低负载调优: 线程池大小调整为 {}", newMaxSize);
    }
}
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的性能优化指南，涵盖应用层、数据库、缓存、网络等各个层面的优化策略
