# Docker部署 (Docker Deployment)

## 模块概述

Docker部署模块提供了Ark-Pets AI Enhanced项目的容器化部署方案，包括前端应用、后端服务、数据库等组件的Docker配置和编排。

**部署特点**:
- 微服务架构的容器化部署
- 支持开发、测试、生产环境
- 自动化构建和部署流程
- 服务发现和负载均衡

## 核心组件

### 1. 后端服务 Dockerfile

#### AI服务 Dockerfile
```dockerfile
# 多阶段构建 - 构建阶段
FROM maven:3.8.6-openjdk-17 AS builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests

# 运行阶段
FROM openjdk:17-jre-slim

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r arkpets && useradd -r -g arkpets arkpets

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder /app/target/ark-pets-ai-service-*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs && chown -R arkpets:arkpets /app

# 切换到应用用户
USER arkpets

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "-Xmx512m", "-Xms256m", \
           "-Dspring.profiles.active=docker", \
           "-Djava.security.egd=file:/dev/./urandom", \
           "app.jar"]
```

#### 用户服务 Dockerfile
```dockerfile
FROM openjdk:17-jre-slim

RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

RUN groupadd -r arkpets && useradd -r -g arkpets arkpets

WORKDIR /app

COPY target/ark-pets-user-service-*.jar app.jar

RUN mkdir -p /app/logs && chown -R arkpets:arkpets /app

USER arkpets

HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/actuator/health || exit 1

EXPOSE 8081

ENTRYPOINT ["java", "-jar", "-Xmx256m", "-Xms128m", \
           "-Dspring.profiles.active=docker", \
           "app.jar"]
```

### 2. 前端应用 Dockerfile

#### 桌面应用 Dockerfile
```dockerfile
# 构建阶段
FROM openjdk:17-jdk-slim AS builder

# 安装构建工具
RUN apt-get update && apt-get install -y \
    maven \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制源代码
COPY . .

# 构建应用
RUN mvn clean package -DskipTests

# 运行阶段
FROM openjdk:17-jre-slim

# 安装X11和图形库
RUN apt-get update && apt-get install -y \
    libx11-6 \
    libxext6 \
    libxrender1 \
    libxtst6 \
    libxi6 \
    libgl1-mesa-glx \
    libgtk-3-0 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制构建产物
COPY --from=builder /app/target/ark-pets-desktop-*.jar app.jar
COPY --from=builder /app/characters ./characters
COPY --from=builder /app/config ./config

# 设置显示环境变量
ENV DISPLAY=:0

EXPOSE 8082

ENTRYPOINT ["java", "-jar", "-Xmx1g", "-Xms512m", \
           "-Djava.awt.headless=false", \
           "app.jar"]
```

### 3. Docker Compose 配置

#### 开发环境 (docker-compose.dev.yml)
```yaml
version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: arkpets-postgres-dev
    environment:
      POSTGRES_DB: arkpets_dev
      POSTGRES_USER: arkpets
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./sql/init:/docker-entrypoint-initdb.d
    networks:
      - arkpets-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: arkpets-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - arkpets-network

  # AI服务
  ai-service:
    build:
      context: ./backend/ai-service
      dockerfile: Dockerfile
    container_name: arkpets-ai-service-dev
    environment:
      SPRING_PROFILES_ACTIVE: dev
      SPRING_DATASOURCE_URL: *******************************************
      SPRING_DATASOURCE_USERNAME: arkpets
      SPRING_DATASOURCE_PASSWORD: dev_password
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - arkpets-network

  # 用户服务
  user-service:
    build:
      context: ./backend/user-service
      dockerfile: Dockerfile
    container_name: arkpets-user-service-dev
    environment:
      SPRING_PROFILES_ACTIVE: dev
      SPRING_DATASOURCE_URL: *******************************************
      SPRING_DATASOURCE_USERNAME: arkpets
      SPRING_DATASOURCE_PASSWORD: dev_password
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    ports:
      - "8081:8081"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - arkpets-network

  # API网关
  api-gateway:
    image: nginx:alpine
    container_name: arkpets-gateway-dev
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/dev.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ai-service
      - user-service
    networks:
      - arkpets-network

volumes:
  postgres_data_dev:
  redis_data_dev:

networks:
  arkpets-network:
    driver: bridge
```

#### 生产环境 (docker-compose.prod.yml)
```yaml
version: '3.8'

services:
  # 数据库服务 - 主从复制
  postgres-master:
    image: postgres:15-alpine
    container_name: arkpets-postgres-master
    environment:
      POSTGRES_DB: arkpets_prod
      POSTGRES_USER: arkpets
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD_FILE: /run/secrets/postgres_replication_password
    secrets:
      - postgres_password
      - postgres_replication_password
    volumes:
      - postgres_master_data:/var/lib/postgresql/data
      - ./postgresql/master.conf:/etc/postgresql/postgresql.conf
    networks:
      - arkpets-network
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  postgres-slave:
    image: postgres:15-alpine
    container_name: arkpets-postgres-slave
    environment:
      POSTGRES_MASTER_SERVICE: postgres-master
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD_FILE: /run/secrets/postgres_replication_password
    secrets:
      - postgres_replication_password
    volumes:
      - postgres_slave_data:/var/lib/postgresql/data
    networks:
      - arkpets-network
    depends_on:
      - postgres-master

  # Redis集群
  redis-master:
    image: redis:7-alpine
    container_name: arkpets-redis-master
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_master_data:/data
    networks:
      - arkpets-network

  redis-slave:
    image: redis:7-alpine
    container_name: arkpets-redis-slave
    command: redis-server --slaveof redis-master 6379 --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_slave_data:/data
    networks:
      - arkpets-network
    depends_on:
      - redis-master

  # AI服务 - 多实例
  ai-service:
    build:
      context: ./backend/ai-service
      dockerfile: Dockerfile.prod
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ***************************************************
      SPRING_DATASOURCE_USERNAME: arkpets
      SPRING_DATASOURCE_PASSWORD_FILE: /run/secrets/postgres_password
      SPRING_REDIS_HOST: redis-master
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
    secrets:
      - postgres_password
    volumes:
      - ./logs:/app/logs
    networks:
      - arkpets-network
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # 用户服务 - 多实例
  user-service:
    build:
      context: ./backend/user-service
      dockerfile: Dockerfile.prod
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ***************************************************
      SPRING_DATASOURCE_USERNAME: arkpets
      SPRING_DATASOURCE_PASSWORD_FILE: /run/secrets/postgres_password
      SPRING_REDIS_HOST: redis-master
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
    secrets:
      - postgres_password
    volumes:
      - ./logs:/app/logs
    networks:
      - arkpets-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    networks:
      - arkpets-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 256M

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - arkpets-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - arkpets-network

secrets:
  postgres_password:
    external: true
  postgres_replication_password:
    external: true

volumes:
  postgres_master_data:
  postgres_slave_data:
  redis_master_data:
  redis_slave_data:
  prometheus_data:
  grafana_data:

networks:
  arkpets-network:
    driver: overlay
    attachable: true
```

### 4. 部署脚本

#### 开发环境部署脚本 (deploy-dev.sh)
```bash
#!/bin/bash

set -e

echo "🚀 开始部署 Ark-Pets AI Enhanced 开发环境..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
mkdir -p logs ssl nginx/logs monitoring

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.dev.yml down

# 清理旧的镜像（可选）
read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose -f docker-compose.dev.yml build

# 启动服务
echo "▶️ 启动服务..."
docker-compose -f docker-compose.dev.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.dev.yml ps

# 运行健康检查
echo "🏥 运行健康检查..."
for service in ai-service user-service; do
    echo "检查 $service..."
    for i in {1..10}; do
        if curl -f http://localhost:808$((i-1))/actuator/health &> /dev/null; then
            echo "✅ $service 健康检查通过"
            break
        fi
        if [ $i -eq 10 ]; then
            echo "❌ $service 健康检查失败"
        fi
        sleep 5
    done
done

echo "🎉 开发环境部署完成！"
echo "📊 服务访问地址："
echo "  - AI服务: http://localhost:8080"
echo "  - 用户服务: http://localhost:8081"
echo "  - API网关: http://localhost"
echo "  - 数据库: localhost:5432"
echo "  - Redis: localhost:6379"
echo ""
echo "📝 查看日志: docker-compose -f docker-compose.dev.yml logs -f [service_name]"
echo "🛑 停止服务: docker-compose -f docker-compose.dev.yml down"
```

#### 生产环境部署脚本 (deploy-prod.sh)
```bash
#!/bin/bash

set -e

echo "🚀 开始部署 Ark-Pets AI Enhanced 生产环境..."

# 检查环境变量
if [ -z "$REDIS_PASSWORD" ] || [ -z "$GRAFANA_PASSWORD" ]; then
    echo "❌ 请设置必要的环境变量: REDIS_PASSWORD, GRAFANA_PASSWORD"
    exit 1
fi

# 检查Docker Swarm
if ! docker info | grep -q "Swarm: active"; then
    echo "🔧 初始化 Docker Swarm..."
    docker swarm init
fi

# 创建密钥
echo "🔐 创建密钥..."
echo "请输入PostgreSQL密码:"
read -s postgres_password
echo "$postgres_password" | docker secret create postgres_password -

echo "请输入PostgreSQL复制密码:"
read -s postgres_replication_password
echo "$postgres_replication_password" | docker secret create postgres_replication_password -

# 部署服务栈
echo "📦 部署服务栈..."
docker stack deploy -c docker-compose.prod.yml arkpets

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 60

# 检查服务状态
echo "🔍 检查服务状态..."
docker stack services arkpets

echo "🎉 生产环境部署完成！"
echo "📊 监控地址："
echo "  - Prometheus: http://localhost:9090"
echo "  - Grafana: http://localhost:3000"
echo ""
echo "📝 查看服务: docker stack services arkpets"
echo "📝 查看日志: docker service logs arkpets_[service_name]"
echo "🛑 停止服务: docker stack rm arkpets"
```

## 配置文件

### Nginx配置 (nginx/dev.conf)
```nginx
events {
    worker_connections 1024;
}

http {
    upstream ai_service {
        server ai-service:8080;
    }
    
    upstream user_service {
        server user-service:8081;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/chat/ {
            proxy_pass http://ai_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /api/user/ {
            proxy_pass http://user_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /api/auth/ {
            proxy_pass http://user_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location / {
            return 200 'Ark-Pets AI Enhanced API Gateway';
            add_header Content-Type text/plain;
        }
    }
}
```

## 部署流程

### 开发环境部署流程

```mermaid
graph TD
    A[开始部署] --> B[检查环境]
    B --> C[创建目录]
    C --> D[停止现有服务]
    D --> E[构建镜像]
    E --> F[启动服务]
    F --> G[健康检查]
    G --> H{检查通过?}
    H -->|是| I[部署完成]
    H -->|否| J[查看日志]
    J --> K[修复问题]
    K --> F
```

### 生产环境部署流程

```mermaid
graph TD
    A[开始部署] --> B[检查环境变量]
    B --> C[初始化Swarm]
    C --> D[创建密钥]
    D --> E[部署服务栈]
    E --> F[等待启动]
    F --> G[检查服务状态]
    G --> H{服务正常?}
    H -->|是| I[配置监控]
    H -->|否| J[回滚]
    I --> K[部署完成]
    J --> L[问题排查]
```

---

**模块负责人**: 部署运维开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
