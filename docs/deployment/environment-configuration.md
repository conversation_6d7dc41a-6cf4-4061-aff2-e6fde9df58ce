# 环境配置管理 (Environment Configuration)

## 模块概述

环境配置管理文档定义了Ark-Pets AI Enhanced项目在不同环境（开发、测试、预生产、生产）下的配置管理策略，包括配置文件管理、环境变量设置、密钥管理、服务配置等。

**配置原则**:
- 环境隔离和配置分离
- 敏感信息安全管理
- 配置版本控制和回滚
- 自动化配置部署

## 环境架构

### 1. 多环境架构设计

#### 环境分层结构
```
┌─────────────────────────────────────┐
│            生产环境 (Production)      │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 主集群   │ 灾备集群  │ CDN边缘  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           预生产环境 (Staging)        │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 性能测试  │ 集成测试  │ 用户验收 │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│            测试环境 (Testing)         │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 单元测试  │ API测试  │ E2E测试  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│            开发环境 (Development)     │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 本地开发  │ 功能开发  │ 调试测试 │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 配置管理架构

#### 配置中心架构
```mermaid
graph TB
    subgraph "配置中心"
        ConfigServer[配置服务器]
        ConfigDB[(配置数据库)]
        ConfigCache[配置缓存]
        ConfigAPI[配置API]
    end
    
    subgraph "环境配置"
        DevConfig[开发环境配置]
        TestConfig[测试环境配置]
        StagingConfig[预生产配置]
        ProdConfig[生产环境配置]
    end
    
    subgraph "应用服务"
        AIService[AI服务]
        UserService[用户服务]
        AuthService[认证服务]
        Gateway[API网关]
    end
    
    ConfigServer --> ConfigDB
    ConfigServer --> ConfigCache
    ConfigAPI --> ConfigServer
    
    DevConfig --> ConfigServer
    TestConfig --> ConfigServer
    StagingConfig --> ConfigServer
    ProdConfig --> ConfigServer
    
    AIService --> ConfigAPI
    UserService --> ConfigAPI
    AuthService --> ConfigAPI
    Gateway --> ConfigAPI
```

## 环境配置

### 1. 开发环境配置

#### application-dev.yml
```yaml
# 开发环境配置
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  profiles:
    active: dev
    
  # 数据库配置
  datasource:
    url: ********************************************
    username: arkpets_dev
    password: dev_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        
  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        
  # 消息队列配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    
# 日志配置
logging:
  level:
    com.arkpets: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    
# AI服务配置
arkpets:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:sk-test-key}
      base-url: https://api.openai.com/v1
      model: gpt-3.5-turbo
      timeout: 30s
      max-retries: 3
    claude:
      api-key: ${CLAUDE_API_KEY:test-key}
      base-url: https://api.anthropic.com
      model: claude-3-sonnet-20240229
      timeout: 30s
      
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:dev-secret-key-for-development-only}
      expiration: 86400 # 24小时
      refresh-expiration: 604800 # 7天
    cors:
      allowed-origins: 
        - http://localhost:3000
        - http://localhost:8080
      allowed-methods: "*"
      
  # 文件存储配置
  storage:
    type: local
    local:
      base-path: ./storage/dev
      max-file-size: 10MB
      
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 2. 测试环境配置

#### application-test.yml
```yaml
# 测试环境配置
server:
  port: 8080

spring:
  profiles:
    active: test
    
  # 测试数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  h2:
    console:
      enabled: true
      
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
  # 测试Redis配置
  redis:
    host: localhost
    port: 6379
    database: 1
    
# 测试专用配置
arkpets:
  ai:
    # 使用模拟的AI服务
    mock:
      enabled: true
      response-delay: 100ms
      
  security:
    jwt:
      secret: test-secret-key
      expiration: 3600 # 1小时
      
  storage:
    type: memory
    
# 测试日志配置
logging:
  level:
    com.arkpets: INFO
    org.springframework.test: DEBUG
    
# 禁用不必要的自动配置
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
```

### 3. 生产环境配置

#### application-prod.yml
```yaml
# 生产环境配置
server:
  port: 8080
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
  http2:
    enabled: true

spring:
  profiles:
    active: prod
    
  # 生产数据库配置
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:arkpets_prod}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
        
  # 生产Redis配置
  redis:
    cluster:
      nodes: ${REDIS_CLUSTER_NODES}
    password: ${REDIS_PASSWORD}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        
# 生产AI服务配置
arkpets:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
      model: ${OPENAI_MODEL:gpt-3.5-turbo}
      timeout: 60s
      max-retries: 5
      rate-limit:
        requests-per-minute: 1000
        
    claude:
      api-key: ${CLAUDE_API_KEY}
      base-url: ${CLAUDE_BASE_URL:https://api.anthropic.com}
      model: ${CLAUDE_MODEL:claude-3-sonnet-20240229}
      timeout: 60s
      
  # 生产安全配置
  security:
    jwt:
      secret: ${JWT_SECRET}
      expiration: ${JWT_EXPIRATION:3600}
      refresh-expiration: ${JWT_REFRESH_EXPIRATION:86400}
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS}
      
  # 生产存储配置
  storage:
    type: s3
    s3:
      bucket: ${S3_BUCKET}
      region: ${S3_REGION}
      access-key: ${S3_ACCESS_KEY}
      secret-key: ${S3_SECRET_KEY}
      
# 生产日志配置
logging:
  level:
    com.arkpets: INFO
    org.springframework.security: WARN
    org.hibernate: WARN
  file:
    name: /app/logs/arkpets.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 10GB
      
# 生产监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s
```

## 配置管理工具

### 1. 配置服务器实现

#### ConfigurationService
```java
@Service
public class ConfigurationService {
    
    private final ConfigRepository configRepository;
    private final EncryptionService encryptionService;
    private final ConfigCache configCache;
    private final ConfigValidator configValidator;
    
    /**
     * 获取配置值
     * @param environment 环境
     * @param key 配置键
     * @return 配置值
     */
    public String getConfigValue(String environment, String key) {
        // 1. 检查缓存
        String cacheKey = environment + ":" + key;
        String cachedValue = configCache.get(cacheKey);
        if (cachedValue != null) {
            return cachedValue;
        }
        
        // 2. 从数据库获取
        ConfigEntry entry = configRepository.findByEnvironmentAndKey(environment, key);
        if (entry == null) {
            throw new ConfigNotFoundException("配置不存在: " + key);
        }
        
        // 3. 解密敏感配置
        String value = entry.getValue();
        if (entry.isEncrypted()) {
            value = encryptionService.decrypt(value);
        }
        
        // 4. 缓存配置
        configCache.put(cacheKey, value, Duration.ofMinutes(5));
        
        return value;
    }
    
    /**
     * 更新配置值
     * @param environment 环境
     * @param key 配置键
     * @param value 配置值
     * @param encrypted 是否加密
     */
    @Transactional
    public void updateConfigValue(String environment, String key, String value, boolean encrypted) {
        // 1. 验证配置
        ValidationResult validation = configValidator.validate(key, value);
        if (!validation.isValid()) {
            throw new ConfigValidationException(validation.getErrors());
        }
        
        // 2. 加密敏感配置
        String storedValue = value;
        if (encrypted) {
            storedValue = encryptionService.encrypt(value);
        }
        
        // 3. 保存配置
        ConfigEntry entry = configRepository.findByEnvironmentAndKey(environment, key);
        if (entry == null) {
            entry = new ConfigEntry();
            entry.setEnvironment(environment);
            entry.setKey(key);
        }
        
        entry.setValue(storedValue);
        entry.setEncrypted(encrypted);
        entry.setUpdatedAt(LocalDateTime.now());
        entry.setUpdatedBy(getCurrentUser());
        
        configRepository.save(entry);
        
        // 4. 清除缓存
        configCache.evict(environment + ":" + key);
        
        // 5. 通知配置变更
        notifyConfigChange(environment, key, value);
    }
    
    /**
     * 批量导入配置
     * @param environment 环境
     * @param configFile 配置文件
     */
    @Transactional
    public void importConfigurations(String environment, MultipartFile configFile) {
        try {
            // 1. 解析配置文件
            Map<String, Object> configs = parseConfigFile(configFile);
            
            // 2. 验证配置
            for (Map.Entry<String, Object> entry : configs.entrySet()) {
                ValidationResult validation = configValidator.validate(entry.getKey(), entry.getValue().toString());
                if (!validation.isValid()) {
                    throw new ConfigValidationException("配置验证失败: " + entry.getKey());
                }
            }
            
            // 3. 批量保存
            for (Map.Entry<String, Object> entry : configs.entrySet()) {
                boolean isEncrypted = isSensitiveConfig(entry.getKey());
                updateConfigValue(environment, entry.getKey(), entry.getValue().toString(), isEncrypted);
            }
            
            logger.info("成功导入 {} 个配置到环境: {}", configs.size(), environment);
            
        } catch (Exception e) {
            logger.error("配置导入失败", e);
            throw new ConfigImportException("配置导入失败", e);
        }
    }
}
```

### 2. 环境变量管理

#### EnvironmentManager
```java
@Component
public class EnvironmentManager {
    
    private final Map<String, String> environmentVariables;
    private final EnvironmentValidator validator;
    
    public EnvironmentManager() {
        this.environmentVariables = new ConcurrentHashMap<>();
        this.validator = new EnvironmentValidator();
        loadEnvironmentVariables();
    }
    
    /**
     * 加载环境变量
     */
    private void loadEnvironmentVariables() {
        // 1. 加载系统环境变量
        environmentVariables.putAll(System.getenv());
        
        // 2. 加载.env文件
        loadDotEnvFile();
        
        // 3. 加载Kubernetes Secrets
        loadKubernetesSecrets();
        
        // 4. 验证必需的环境变量
        validateRequiredVariables();
    }
    
    /**
     * 加载.env文件
     */
    private void loadDotEnvFile() {
        try {
            Path envFile = Paths.get(".env");
            if (Files.exists(envFile)) {
                List<String> lines = Files.readAllLines(envFile);
                for (String line : lines) {
                    if (line.trim().isEmpty() || line.startsWith("#")) {
                        continue;
                    }
                    
                    String[] parts = line.split("=", 2);
                    if (parts.length == 2) {
                        environmentVariables.put(parts[0].trim(), parts[1].trim());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("无法加载.env文件", e);
        }
    }
    
    /**
     * 验证必需的环境变量
     */
    private void validateRequiredVariables() {
        String[] requiredVars = {
            "DB_HOST", "DB_USERNAME", "DB_PASSWORD",
            "REDIS_HOST", "JWT_SECRET",
            "OPENAI_API_KEY"
        };
        
        List<String> missingVars = new ArrayList<>();
        for (String var : requiredVars) {
            if (!environmentVariables.containsKey(var)) {
                missingVars.add(var);
            }
        }
        
        if (!missingVars.isEmpty()) {
            throw new EnvironmentException("缺少必需的环境变量: " + String.join(", ", missingVars));
        }
    }
    
    /**
     * 获取环境变量
     * @param key 变量名
     * @param defaultValue 默认值
     * @return 变量值
     */
    public String getEnvironmentVariable(String key, String defaultValue) {
        return environmentVariables.getOrDefault(key, defaultValue);
    }
    
    /**
     * 获取必需的环境变量
     * @param key 变量名
     * @return 变量值
     * @throws EnvironmentException 如果变量不存在
     */
    public String getRequiredEnvironmentVariable(String key) {
        String value = environmentVariables.get(key);
        if (value == null) {
            throw new EnvironmentException("必需的环境变量不存在: " + key);
        }
        return value;
    }
}
```

## 配置部署脚本

### 1. 配置部署脚本

#### deploy-config.sh
```bash
#!/bin/bash

set -e

ENVIRONMENT=${1:-dev}
CONFIG_DIR="./config"
BACKUP_DIR="./config-backup"

echo "🚀 开始部署 $ENVIRONMENT 环境配置..."

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份现有配置
if [ -d "$CONFIG_DIR/$ENVIRONMENT" ]; then
    echo "📦 备份现有配置..."
    cp -r "$CONFIG_DIR/$ENVIRONMENT" "$BACKUP_DIR/$ENVIRONMENT-$(date +%Y%m%d-%H%M%S)"
fi

# 验证配置文件
echo "🔍 验证配置文件..."
validate_config() {
    local config_file=$1
    
    # 检查YAML语法
    if ! python -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
        echo "❌ 配置文件语法错误: $config_file"
        return 1
    fi
    
    # 检查必需字段
    required_fields=("spring.datasource.url" "arkpets.ai.openai.api-key")
    for field in "${required_fields[@]}"; do
        if ! grep -q "$field" "$config_file"; then
            echo "⚠️  警告: 配置文件缺少字段 $field: $config_file"
        fi
    done
    
    echo "✅ 配置文件验证通过: $config_file"
    return 0
}

# 验证所有配置文件
for config_file in "$CONFIG_DIR/$ENVIRONMENT"/*.yml; do
    if [ -f "$config_file" ]; then
        validate_config "$config_file"
    fi
done

# 部署配置到Kubernetes
if [ "$ENVIRONMENT" = "prod" ] || [ "$ENVIRONMENT" = "staging" ]; then
    echo "☸️  部署配置到Kubernetes..."
    
    # 创建ConfigMap
    kubectl create configmap arkpets-config-$ENVIRONMENT \
        --from-file="$CONFIG_DIR/$ENVIRONMENT/" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 创建Secret
    kubectl create secret generic arkpets-secrets-$ENVIRONMENT \
        --from-env-file="$CONFIG_DIR/$ENVIRONMENT/.env" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    echo "✅ Kubernetes配置部署完成"
fi

# 重启相关服务
echo "🔄 重启服务以应用新配置..."
case $ENVIRONMENT in
    "dev")
        docker-compose -f docker-compose.dev.yml restart
        ;;
    "test")
        docker-compose -f docker-compose.test.yml restart
        ;;
    "staging"|"prod")
        kubectl rollout restart deployment/arkpets-ai-service -n arkpets-$ENVIRONMENT
        kubectl rollout restart deployment/arkpets-user-service -n arkpets-$ENVIRONMENT
        ;;
esac

echo "🎉 配置部署完成！"
```

### 2. 配置验证脚本

#### validate-config.py
```python
#!/usr/bin/env python3

import yaml
import sys
import os
import re
from typing import Dict, List, Any

class ConfigValidator:
    def __init__(self):
        self.errors = []
        self.warnings = []
        
    def validate_config_file(self, file_path: str) -> bool:
        """验证配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            self.validate_structure(config, file_path)
            self.validate_values(config, file_path)
            self.validate_security(config, file_path)
            
            return len(self.errors) == 0
            
        except yaml.YAMLError as e:
            self.errors.append(f"YAML语法错误 {file_path}: {e}")
            return False
        except Exception as e:
            self.errors.append(f"配置文件读取失败 {file_path}: {e}")
            return False
    
    def validate_structure(self, config: Dict, file_path: str):
        """验证配置结构"""
        required_sections = ['spring', 'arkpets']
        
        for section in required_sections:
            if section not in config:
                self.errors.append(f"缺少必需配置节: {section} in {file_path}")
    
    def validate_values(self, config: Dict, file_path: str):
        """验证配置值"""
        # 验证数据库配置
        if 'spring' in config and 'datasource' in config['spring']:
            ds = config['spring']['datasource']
            if 'url' not in ds:
                self.errors.append(f"缺少数据库URL配置 in {file_path}")
            elif not ds['url'].startswith(('jdbc:postgresql', 'jdbc:h2')):
                self.warnings.append(f"不支持的数据库类型 in {file_path}")
        
        # 验证端口配置
        if 'server' in config and 'port' in config['server']:
            port = config['server']['port']
            if not isinstance(port, int) or port < 1 or port > 65535:
                self.errors.append(f"无效的端口号: {port} in {file_path}")
    
    def validate_security(self, config: Dict, file_path: str):
        """验证安全配置"""
        # 检查是否包含明文密码
        config_str = yaml.dump(config)
        if re.search(r'password:\s*[^${\s]', config_str):
            self.warnings.append(f"检测到可能的明文密码 in {file_path}")
        
        # 检查JWT密钥
        if ('arkpets' in config and 'security' in config['arkpets'] and 
            'jwt' in config['arkpets']['security']):
            jwt_config = config['arkpets']['security']['jwt']
            if 'secret' in jwt_config:
                secret = jwt_config['secret']
                if len(secret) < 32:
                    self.errors.append(f"JWT密钥长度不足 in {file_path}")
    
    def print_results(self):
        """打印验证结果"""
        if self.errors:
            print("❌ 配置验证失败:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print("⚠️  配置警告:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        if not self.errors and not self.warnings:
            print("✅ 配置验证通过")

def main():
    if len(sys.argv) != 2:
        print("用法: python validate-config.py <config-file>")
        sys.exit(1)
    
    config_file = sys.argv[1]
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        sys.exit(1)
    
    validator = ConfigValidator()
    is_valid = validator.validate_config_file(config_file)
    validator.print_results()
    
    sys.exit(0 if is_valid else 1)

if __name__ == "__main__":
    main()
```

## 配置监控

### 1. 配置变更监控

#### ConfigChangeMonitor
```java
@Component
public class ConfigChangeMonitor {
    
    private final ConfigAuditService auditService;
    private final NotificationService notificationService;
    
    @EventListener
    public void handleConfigChange(ConfigChangeEvent event) {
        // 1. 记录配置变更
        ConfigAudit audit = new ConfigAudit();
        audit.setEnvironment(event.getEnvironment());
        audit.setConfigKey(event.getKey());
        audit.setOldValue(event.getOldValue());
        audit.setNewValue(event.getNewValue());
        audit.setChangedBy(event.getChangedBy());
        audit.setChangedAt(LocalDateTime.now());
        
        auditService.save(audit);
        
        // 2. 发送通知
        if (isCriticalConfig(event.getKey())) {
            notificationService.sendCriticalConfigChangeAlert(event);
        }
        
        // 3. 触发配置重载
        triggerConfigReload(event.getEnvironment());
    }
    
    private boolean isCriticalConfig(String key) {
        String[] criticalConfigs = {
            "spring.datasource.url",
            "arkpets.security.jwt.secret",
            "arkpets.ai.openai.api-key"
        };
        
        return Arrays.asList(criticalConfigs).contains(key);
    }
}
```

---

**模块负责人**: 环境配置开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
