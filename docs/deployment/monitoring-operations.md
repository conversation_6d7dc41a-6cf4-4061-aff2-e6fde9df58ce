# 监控运维 (Monitoring & Operations)

## 模块概述

监控运维模块提供了Ark-Pets AI Enhanced项目的完整监控体系和运维管理方案，包括系统监控、应用监控、日志管理、告警机制、性能优化等功能。

**监控目标**:
- 全方位的系统健康监控
- 实时的性能指标追踪
- 智能的异常检测和告警
- 高效的故障排查和恢复

## 监控架构

### 1. 监控体系架构

#### 分层监控架构
```
┌─────────────────────────────────────┐
│           用户体验监控                │
│    ┌─────────────┬─────────────┐    │
│    │  前端监控    │   API监控   │    │
│    └─────────────┴─────────────┘    │
├─────────────────────────────────────┤
│           应用层监控                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 业务指标  │ 性能指标  │ 错误监控  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           中间件监控                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据库   │  缓存    │  消息队列 │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           基础设施监控               │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 服务器   │  网络    │   存储   │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 监控技术栈

#### 核心监控组件
- **Prometheus**: 指标收集和存储
- **Grafana**: 可视化仪表板
- **AlertManager**: 告警管理
- **Jaeger**: 分布式链路追踪
- **ELK Stack**: 日志收集和分析
- **Uptime Kuma**: 服务可用性监控

## 监控配置

### 1. Prometheus配置

#### prometheus.yml
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'arkpets-prod'
    region: 'us-east-1'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用服务监控
  - job_name: 'arkpets-ai-service'
    static_configs:
      - targets: ['ai-service:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'arkpets-user-service'
    static_configs:
      - targets: ['user-service:8081']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s

  # 基础设施监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  # Nginx监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s

  # Docker监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # 黑盒监控
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://api.arkpets.com/health
        - https://arkpets.com
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115
```

### 2. 告警规则配置

#### alerts/application.yml
```yaml
groups:
  - name: arkpets-application
    rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.job }} service has been down for more than 1 minute"

      # 高错误率告警
      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{status=~"5.."}[5m]) /
            rate(http_requests_total[5m])
          ) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      # 响应时间告警
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket[5m])
          ) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.instance }}"
          description: "95th percentile response time is {{ $value }}s"

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: |
          100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: |
          (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value }}%"

      # 磁盘空间告警
      - alert: DiskSpaceLow
        expr: |
          (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low on {{ $labels.instance }}"
          description: "Disk usage is {{ $value }}% on {{ $labels.mountpoint }}"

      # 数据库连接告警
      - alert: DatabaseConnectionHigh
        expr: |
          pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections"

      # AI服务特定告警
      - alert: AIServiceTokenLimitApproaching
        expr: |
          arkpets_ai_tokens_used_total / arkpets_ai_tokens_limit_total > 0.9
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "AI service token limit approaching"
          description: "Token usage is at {{ $value | humanizePercentage }} of limit"

      - alert: ConversationQueueHigh
        expr: |
          arkpets_conversation_queue_size > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High conversation queue size"
          description: "Conversation queue has {{ $value }} pending requests"
```

### 3. AlertManager配置

#### alertmanager.yml
```yaml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your_password'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
    - match:
        severity: warning
      receiver: 'warning-alerts'
      repeat_interval: 30m

receivers:
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Ark-Pets] Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] Ark-Pets Alert: {{ .GroupLabels.alertname }}'
        body: |
          CRITICAL ALERT!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Time: {{ .StartsAt }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-critical'
        title: 'Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          {{ .Annotations.summary }}
          {{ .Annotations.description }}
          {{ end }}

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] Ark-Pets Alert: {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-warning'
        title: 'Warning: {{ .GroupLabels.alertname }}'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']
```

### 4. Grafana仪表板

#### 应用监控仪表板配置
```json
{
  "dashboard": {
    "title": "Ark-Pets Application Monitoring",
    "tags": ["arkpets", "application"],
    "timezone": "browser",
    "panels": [
      {
        "title": "Service Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=~\"arkpets-.*\"}",
            "legendFormat": "{{ job }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{ job }} - {{ method }} {{ status }}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      },
      {
        "title": "AI Service Metrics",
        "type": "graph",
        "targets": [
          {
            "expr": "arkpets_ai_conversations_active",
            "legendFormat": "Active Conversations"
          },
          {
            "expr": "rate(arkpets_ai_tokens_used_total[5m])",
            "legendFormat": "Token Usage Rate"
          },
          {
            "expr": "arkpets_ai_model_response_time",
            "legendFormat": "AI Model Response Time"
          }
        ]
      }
    ]
  }
}
```

## 日志管理

### 1. ELK Stack配置

#### Elasticsearch配置
```yaml
# elasticsearch.yml
cluster.name: arkpets-logs
node.name: es-node-1
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node

# JVM配置
-Xms2g
-Xmx2g
```

#### Logstash配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "arkpets-ai-service" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} \[%{DATA:thread}\] %{DATA:logger} - %{GREEDYDATA:message}" 
      }
    }
    
    if [message] =~ /AI_REQUEST/ {
      grok {
        match => { 
          "message" => "AI_REQUEST user_id=%{DATA:user_id} conversation_id=%{DATA:conversation_id} model=%{DATA:model} tokens=%{NUMBER:tokens}" 
        }
      }
      mutate {
        convert => { "tokens" => "integer" }
        add_tag => ["ai_request"]
      }
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "arkpets-logs-%{+YYYY.MM.dd}"
  }
}
```

#### Filebeat配置
```yaml
# filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /app/logs/*.log
    fields:
      service: arkpets-ai-service
    fields_under_root: true
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after

output.logstash:
  hosts: ["logstash:5044"]

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
```

### 2. 应用日志配置

#### Spring Boot日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="!prod">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <arguments/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <appender name="AI_METRICS" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/app/logs/ai-metrics.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/app/logs/ai-metrics.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>7</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{ISO8601} %msg%n</pattern>
            </encoder>
        </appender>
        
        <logger name="ai.metrics" level="INFO" additivity="false">
            <appender-ref ref="AI_METRICS"/>
        </logger>
        
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 性能监控

### 1. 应用性能指标

#### 自定义指标收集
```java
@Component
public class ArkPetsMetrics {
    
    private final Counter conversationCounter;
    private final Timer aiResponseTimer;
    private final Gauge activeConversationsGauge;
    private final Counter tokenUsageCounter;
    
    public ArkPetsMetrics(MeterRegistry meterRegistry) {
        this.conversationCounter = Counter.builder("arkpets.conversations.total")
            .description("Total number of conversations")
            .tag("type", "created")
            .register(meterRegistry);
            
        this.aiResponseTimer = Timer.builder("arkpets.ai.response.time")
            .description("AI response time")
            .register(meterRegistry);
            
        this.activeConversationsGauge = Gauge.builder("arkpets.conversations.active")
            .description("Number of active conversations")
            .register(meterRegistry, this, ArkPetsMetrics::getActiveConversations);
            
        this.tokenUsageCounter = Counter.builder("arkpets.ai.tokens.used")
            .description("Total tokens used")
            .register(meterRegistry);
    }
    
    public void recordConversationCreated() {
        conversationCounter.increment();
    }
    
    public void recordAIResponse(Duration duration) {
        aiResponseTimer.record(duration);
    }
    
    public void recordTokenUsage(int tokens) {
        tokenUsageCounter.increment(tokens);
    }
    
    private double getActiveConversations() {
        // 实现获取活跃对话数量的逻辑
        return conversationService.getActiveConversationCount();
    }
}
```

### 2. 链路追踪

#### Jaeger配置
```yaml
# jaeger-all-in-one.yml
version: '3.8'
services:
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
```

#### Spring Boot集成
```yaml
# application.yml
management:
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://jaeger:9411/api/v2/spans
```

## 运维脚本

### 1. 健康检查脚本

#### health-check.sh
```bash
#!/bin/bash

SERVICES=("ai-service:8080" "user-service:8081" "postgres:5432" "redis:6379")
FAILED_SERVICES=()

echo "🔍 开始健康检查..."

for service in "${SERVICES[@]}"; do
    IFS=':' read -r host port <<< "$service"
    
    if timeout 5 bash -c "</dev/tcp/$host/$port"; then
        echo "✅ $service - 健康"
    else
        echo "❌ $service - 不健康"
        FAILED_SERVICES+=("$service")
    fi
done

if [ ${#FAILED_SERVICES[@]} -eq 0 ]; then
    echo "🎉 所有服务健康"
    exit 0
else
    echo "⚠️  以下服务不健康: ${FAILED_SERVICES[*]}"
    exit 1
fi
```

### 2. 备份脚本

#### backup.sh
```bash
#!/bin/bash

BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

echo "📦 开始数据备份..."

# 数据库备份
docker exec arkpets-postgres pg_dump -U arkpets arkpets_prod | gzip > "$BACKUP_DIR/database.sql.gz"

# Redis备份
docker exec arkpets-redis redis-cli BGSAVE
docker cp arkpets-redis:/data/dump.rdb "$BACKUP_DIR/redis.rdb"

# 配置文件备份
tar -czf "$BACKUP_DIR/configs.tar.gz" /app/config

# 日志备份
tar -czf "$BACKUP_DIR/logs.tar.gz" /app/logs --exclude="*.log"

echo "✅ 备份完成: $BACKUP_DIR"

# 清理旧备份 (保留7天)
find /backup -type d -mtime +7 -exec rm -rf {} +
```

---

**模块负责人**: 监控运维开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
