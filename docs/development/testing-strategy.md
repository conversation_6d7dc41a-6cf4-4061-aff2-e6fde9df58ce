# 测试策略 (Testing Strategy)

## 模块概述

测试策略文档定义了Ark-Pets AI Enhanced项目的全面测试方法论，包括单元测试、集成测试、端到端测试、性能测试等各个层面的测试策略和最佳实践。

**测试目标**:
- 确保代码质量和功能正确性
- 提高系统稳定性和可靠性
- 支持持续集成和持续部署
- 降低生产环境故障风险

## 测试架构

### 1. 测试金字塔架构

#### 分层测试策略
```
┌─────────────────────────────────────┐
│            E2E测试 (5%)              │
│         ┌─────────────────┐         │
│         │   用户场景测试   │         │
│         └─────────────────┘         │
├─────────────────────────────────────┤
│          集成测试 (25%)              │
│    ┌─────────────┬─────────────┐    │
│    │  API测试    │  组件集成   │    │
│    └─────────────┴─────────────┘    │
├─────────────────────────────────────┤
│          单元测试 (70%)              │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 业务逻辑  │ 工具类   │  数据层  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 测试流程架构

#### 自动化测试流程
```mermaid
graph TB
    subgraph "开发阶段"
        UnitTest[单元测试]
        CodeReview[代码审查]
        StaticAnalysis[静态分析]
    end
    
    subgraph "集成阶段"
        IntegrationTest[集成测试]
        APITest[API测试]
        ContractTest[契约测试]
    end
    
    subgraph "部署阶段"
        E2ETest[端到端测试]
        PerformanceTest[性能测试]
        SecurityTest[安全测试]
    end
    
    subgraph "生产阶段"
        SmokeTest[冒烟测试]
        MonitoringTest[监控测试]
        ChaosTest[混沌测试]
    end
    
    UnitTest --> CodeReview
    CodeReview --> StaticAnalysis
    StaticAnalysis --> IntegrationTest
    IntegrationTest --> APITest
    APITest --> ContractTest
    ContractTest --> E2ETest
    E2ETest --> PerformanceTest
    PerformanceTest --> SecurityTest
    SecurityTest --> SmokeTest
    SmokeTest --> MonitoringTest
    MonitoringTest --> ChaosTest
```

## 单元测试策略

### 1. 测试框架配置

#### JUnit 5 + Mockito配置
```java
@ExtendWith(MockitoExtension.class)
@TestMethodOrder(OrderAnnotation.class)
class ChatServiceTest {
    
    @Mock
    private ConversationRepository conversationRepository;
    
    @Mock
    private AIModelClient aiModelClient;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @InjectMocks
    private ChatService chatService;
    
    @Captor
    private ArgumentCaptor<ChatRequest> chatRequestCaptor;
    
    @BeforeEach
    void setUp() {
        // 测试前置条件设置
        MockitoAnnotations.openMocks(this);
    }
    
    @AfterEach
    void tearDown() {
        // 测试后清理
        Mockito.reset(conversationRepository, aiModelClient, redisTemplate);
    }
}
```

### 2. 业务逻辑测试

#### AI服务单元测试
```java
class ChatServiceTest {
    
    @Test
    @DisplayName("应该成功处理聊天消息")
    void shouldProcessChatMessageSuccessfully() {
        // Given
        String userId = "user123";
        String conversationId = "conv456";
        ChatRequest request = ChatRequest.builder()
            .userId(userId)
            .conversationId(conversationId)
            .message("你好，阿米娅")
            .characterName("amiya")
            .build();
            
        ConversationContext context = ConversationContext.builder()
            .conversationId(conversationId)
            .characterName("amiya")
            .personalityId("default")
            .build();
            
        AIResponse aiResponse = AIResponse.builder()
            .content("博士，您好！有什么我可以帮助您的吗？")
            .tokens(50)
            .finishReason("stop")
            .build();
        
        when(conversationRepository.findById(conversationId))
            .thenReturn(Optional.of(context));
        when(aiModelClient.sendMessage(any(String.class)))
            .thenReturn(aiResponse);
        
        // When
        ChatResponse response = chatService.processChatMessage(request);
        
        // Then
        assertThat(response).isNotNull();
        assertThat(response.getContent()).isEqualTo("博士，您好！有什么我可以帮助您的吗？");
        assertThat(response.getConversationId()).isEqualTo(conversationId);
        
        verify(conversationRepository).findById(conversationId);
        verify(aiModelClient).sendMessage(chatRequestCaptor.capture());
        
        String capturedPrompt = chatRequestCaptor.getValue();
        assertThat(capturedPrompt).contains("你好，阿米娅");
    }
    
    @Test
    @DisplayName("当对话不存在时应该抛出异常")
    void shouldThrowExceptionWhenConversationNotFound() {
        // Given
        String conversationId = "nonexistent";
        ChatRequest request = ChatRequest.builder()
            .conversationId(conversationId)
            .message("测试消息")
            .build();
            
        when(conversationRepository.findById(conversationId))
            .thenReturn(Optional.empty());
        
        // When & Then
        assertThatThrownBy(() -> chatService.processChatMessage(request))
            .isInstanceOf(ConversationNotFoundException.class)
            .hasMessage("对话不存在: " + conversationId);
            
        verify(conversationRepository).findById(conversationId);
        verifyNoInteractions(aiModelClient);
    }
    
    @Test
    @DisplayName("应该正确处理AI服务异常")
    void shouldHandleAIServiceException() {
        // Given
        ChatRequest request = ChatRequest.builder()
            .conversationId("conv123")
            .message("测试消息")
            .build();
            
        ConversationContext context = ConversationContext.builder()
            .conversationId("conv123")
            .build();
            
        when(conversationRepository.findById("conv123"))
            .thenReturn(Optional.of(context));
        when(aiModelClient.sendMessage(any(String.class)))
            .thenThrow(new AIServiceException("AI服务不可用"));
        
        // When & Then
        assertThatThrownBy(() -> chatService.processChatMessage(request))
            .isInstanceOf(ChatProcessingException.class)
            .hasMessage("聊天处理失败")
            .hasCauseInstanceOf(AIServiceException.class);
    }
}
```

### 3. 数据层测试

#### Repository测试
```java
@DataJpaTest
@TestPropertySource(properties = {
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.datasource.url=jdbc:h2:mem:testdb"
})
class ConversationRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private ConversationRepository conversationRepository;
    
    @Test
    @DisplayName("应该根据用户ID查找对话")
    void shouldFindConversationsByUserId() {
        // Given
        String userId = "user123";
        Conversation conversation1 = Conversation.builder()
            .userId(userId)
            .title("对话1")
            .characterName("amiya")
            .build();
        Conversation conversation2 = Conversation.builder()
            .userId(userId)
            .title("对话2")
            .characterName("exusiai")
            .build();
            
        entityManager.persistAndFlush(conversation1);
        entityManager.persistAndFlush(conversation2);
        
        // When
        List<Conversation> conversations = conversationRepository.findByUserId(userId);
        
        // Then
        assertThat(conversations).hasSize(2);
        assertThat(conversations)
            .extracting(Conversation::getTitle)
            .containsExactlyInAnyOrder("对话1", "对话2");
    }
    
    @Test
    @DisplayName("应该支持分页查询用户对话")
    void shouldSupportPaginatedUserConversations() {
        // Given
        String userId = "user123";
        for (int i = 1; i <= 10; i++) {
            Conversation conversation = Conversation.builder()
                .userId(userId)
                .title("对话" + i)
                .characterName("amiya")
                .build();
            entityManager.persistAndFlush(conversation);
        }
        
        Pageable pageable = PageRequest.of(0, 5, Sort.by("createdAt").descending());
        
        // When
        Page<Conversation> page = conversationRepository.findByUserId(userId, pageable);
        
        // Then
        assertThat(page.getContent()).hasSize(5);
        assertThat(page.getTotalElements()).isEqualTo(10);
        assertThat(page.getTotalPages()).isEqualTo(2);
    }
}
```

## 集成测试策略

### 1. API集成测试

#### Spring Boot Test配置
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Testcontainers
class ChatControllerIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
        .withDatabaseName("arkpets_test")
        .withUsername("test")
        .withPassword("test");
        
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
        .withExposedPorts(6379);
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ConversationRepository conversationRepository;
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", redis::getFirstMappedPort);
    }
    
    @Test
    @DisplayName("应该成功创建新对话")
    void shouldCreateNewConversationSuccessfully() {
        // Given
        CreateConversationRequest request = CreateConversationRequest.builder()
            .title("测试对话")
            .characterName("amiya")
            .personalityId("default")
            .build();
            
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("valid-jwt-token");
        
        HttpEntity<CreateConversationRequest> entity = new HttpEntity<>(request, headers);
        
        // When
        ResponseEntity<ApiResponse<ConversationResponse>> response = restTemplate.exchange(
            "/api/v1/conversations",
            HttpMethod.POST,
            entity,
            new ParameterizedTypeReference<ApiResponse<ConversationResponse>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getCode()).isEqualTo(201);
        
        ConversationResponse conversationResponse = response.getBody().getData();
        assertThat(conversationResponse.getTitle()).isEqualTo("测试对话");
        assertThat(conversationResponse.getCharacterName()).isEqualTo("amiya");
        
        // 验证数据库中的数据
        Optional<Conversation> savedConversation = conversationRepository
            .findById(conversationResponse.getId());
        assertThat(savedConversation).isPresent();
        assertThat(savedConversation.get().getTitle()).isEqualTo("测试对话");
    }

    @Test
    @DisplayName("应该正确处理聊天消息")
    void shouldProcessChatMessageCorrectly() {
        // Given
        // 先创建一个对话
        Conversation conversation = Conversation.builder()
            .userId("user123")
            .title("测试对话")
            .characterName("amiya")
            .personalityId("default")
            .build();
        conversation = conversationRepository.save(conversation);

        ChatRequest chatRequest = ChatRequest.builder()
            .message("你好，阿米娅")
            .conversationId(conversation.getId())
            .build();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("valid-jwt-token");

        HttpEntity<ChatRequest> entity = new HttpEntity<>(chatRequest, headers);

        // When
        ResponseEntity<ApiResponse<ChatResponse>> response = restTemplate.exchange(
            "/api/v1/chat/message",
            HttpMethod.POST,
            entity,
            new ParameterizedTypeReference<ApiResponse<ChatResponse>>() {}
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getCode()).isEqualTo(200);

        ChatResponse chatResponse = response.getBody().getData();
        assertThat(chatResponse.getContent()).isNotBlank();
        assertThat(chatResponse.getConversationId()).isEqualTo(conversation.getId());
    }
}

### 2. 契约测试

#### Pact契约测试
```java
@ExtendWith(PactConsumerTestExt.class)
@PactTestFor(providerName = "ai-service")
class AIServiceContractTest {

    @Pact(consumer = "chat-service")
    public RequestResponsePact createChatPact(PactDslWithProvider builder) {
        return builder
            .given("AI服务正常运行")
            .uponReceiving("聊天请求")
            .path("/api/v1/ai/chat")
            .method("POST")
            .headers(Map.of("Content-Type", "application/json"))
            .body(new PactDslJsonBody()
                .stringType("prompt", "你好，阿米娅")
                .stringType("model", "gpt-3.5-turbo")
                .numberType("maxTokens", 150))
            .willRespondWith()
            .status(200)
            .headers(Map.of("Content-Type", "application/json"))
            .body(new PactDslJsonBody()
                .stringType("content", "博士，您好！")
                .numberType("tokens", 50)
                .stringType("finishReason", "stop"))
            .toPact();
    }

    @Test
    @PactTestFor(pactMethod = "createChatPact")
    void testChatWithAIService(MockServer mockServer) {
        // Given
        AIServiceClient client = new AIServiceClient(mockServer.getUrl());

        ChatRequest request = ChatRequest.builder()
            .prompt("你好，阿米娅")
            .model("gpt-3.5-turbo")
            .maxTokens(150)
            .build();

        // When
        AIResponse response = client.sendChatRequest(request);

        // Then
        assertThat(response.getContent()).isEqualTo("博士，您好！");
        assertThat(response.getTokens()).isEqualTo(50);
        assertThat(response.getFinishReason()).isEqualTo("stop");
    }
}
```

## 端到端测试策略

### 1. Selenium WebDriver测试

#### E2E测试框架配置
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Testcontainers
class ChatE2ETest {

    @Container
    static BrowserWebDriverContainer<?> chrome = new BrowserWebDriverContainer<>()
        .withCapabilities(new ChromeOptions());

    private WebDriver driver;

    @BeforeEach
    void setUp() {
        driver = chrome.getWebDriver();
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
    }

    @Test
    @DisplayName("用户应该能够完成完整的聊天流程")
    void userShouldCompleteFullChatFlow() {
        // Given
        driver.get("http://localhost:8080");

        // When - 用户登录
        WebElement loginButton = driver.findElement(By.id("login-button"));
        loginButton.click();

        WebElement usernameInput = driver.findElement(By.id("username"));
        usernameInput.sendKeys("<EMAIL>");

        WebElement passwordInput = driver.findElement(By.id("password"));
        passwordInput.sendKeys("password123");

        WebElement submitButton = driver.findElement(By.id("submit-login"));
        submitButton.click();

        // When - 创建新对话
        WebElement newChatButton = driver.findElement(By.id("new-chat"));
        newChatButton.click();

        WebElement characterSelect = driver.findElement(By.id("character-select"));
        Select select = new Select(characterSelect);
        select.selectByValue("amiya");

        WebElement createButton = driver.findElement(By.id("create-conversation"));
        createButton.click();

        // When - 发送消息
        WebElement messageInput = driver.findElement(By.id("message-input"));
        messageInput.sendKeys("你好，阿米娅");

        WebElement sendButton = driver.findElement(By.id("send-message"));
        sendButton.click();

        // Then - 验证响应
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(30));
        WebElement aiResponse = wait.until(ExpectedConditions.presenceOfElementLocated(
            By.cssSelector(".ai-message:last-child .message-content")));

        assertThat(aiResponse.getText()).isNotBlank();
        assertThat(aiResponse.getText()).contains("博士");

        // Then - 验证对话历史
        List<WebElement> messages = driver.findElements(By.cssSelector(".message"));
        assertThat(messages).hasSizeGreaterThanOrEqualTo(2); // 用户消息 + AI回复
    }
}
```

### 2. API端到端测试

#### REST Assured测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class APIEndToEndTest {

    @LocalServerPort
    private int port;

    private String baseUrl;
    private String authToken;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port;
        RestAssured.baseURI = baseUrl;

        // 获取认证令牌
        authToken = authenticateUser("<EMAIL>", "password123");
    }

    @Test
    @DisplayName("完整的聊天API流程测试")
    void completeAPIFlowTest() {
        // 1. 创建用户
        String userId = given()
            .contentType(ContentType.JSON)
            .body(Map.of(
                "email", "<EMAIL>",
                "password", "password123",
                "nickname", "测试用户"
            ))
        .when()
            .post("/api/v1/users/register")
        .then()
            .statusCode(201)
            .body("code", equalTo(201))
            .extract()
            .path("data.id");

        // 2. 用户登录
        String newUserToken = given()
            .contentType(ContentType.JSON)
            .body(Map.of(
                "email", "<EMAIL>",
                "password", "password123"
            ))
        .when()
            .post("/api/v1/auth/login")
        .then()
            .statusCode(200)
            .body("code", equalTo(200))
            .extract()
            .path("data.token");

        // 3. 创建对话
        String conversationId = given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer " + newUserToken)
            .body(Map.of(
                "title", "API测试对话",
                "characterName", "amiya",
                "personalityId", "default"
            ))
        .when()
            .post("/api/v1/conversations")
        .then()
            .statusCode(201)
            .body("code", equalTo(201))
            .body("data.title", equalTo("API测试对话"))
            .extract()
            .path("data.id");

        // 4. 发送聊天消息
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer " + newUserToken)
            .body(Map.of(
                "message", "你好，阿米娅",
                "conversationId", conversationId
            ))
        .when()
            .post("/api/v1/chat/message")
        .then()
            .statusCode(200)
            .body("code", equalTo(200))
            .body("data.content", notNullValue())
            .body("data.conversationId", equalTo(conversationId));

        // 5. 获取对话历史
        given()
            .header("Authorization", "Bearer " + newUserToken)
        .when()
            .get("/api/v1/conversations/{id}/messages", conversationId)
        .then()
            .statusCode(200)
            .body("code", equalTo(200))
            .body("data.items", hasSize(greaterThanOrEqualTo(2)));

        // 6. 删除对话
        given()
            .header("Authorization", "Bearer " + newUserToken)
        .when()
            .delete("/api/v1/conversations/{id}", conversationId)
        .then()
            .statusCode(204);
    }

    private String authenticateUser(String email, String password) {
        return given()
            .contentType(ContentType.JSON)
            .body(Map.of("email", email, "password", password))
        .when()
            .post("/api/v1/auth/login")
        .then()
            .statusCode(200)
            .extract()
            .path("data.token");
    }
}
```

## 性能测试策略

### 1. JMeter性能测试

#### 聊天API性能测试计划
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Ark-Pets Chat Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments">
          <elementProp name="base_url" elementType="Argument">
            <stringProp name="Argument.name">base_url</stringProp>
            <stringProp name="Argument.value">http://localhost:8080</stringProp>
          </elementProp>
          <elementProp name="concurrent_users" elementType="Argument">
            <stringProp name="Argument.name">concurrent_users</stringProp>
            <stringProp name="Argument.value">100</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>

    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Chat Load Test">
        <stringProp name="ThreadGroup.num_threads">${concurrent_users}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">60</stringProp>
        <stringProp name="ThreadGroup.duration">300</stringProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
      </ThreadGroup>

      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Send Chat Message">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{"message":"你好，阿米娅","conversationId":"${conversation_id}"}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">${base_url}</stringProp>
          <stringProp name="HTTPSampler.path">/api/v1/chat/message</stringProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
        </HTTPSamplerProxy>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

### 2. Gatling性能测试

#### Scala性能测试脚本
```scala
import io.gatling.core.Predef._
import io.gatling.http.Predef._
import scala.concurrent.duration._

class ChatPerformanceTest extends Simulation {

  val httpProtocol = http
    .baseUrl("http://localhost:8080")
    .acceptHeader("application/json")
    .contentTypeHeader("application/json")
    .authorizationHeader("Bearer ${auth_token}")

  val authFeeder = csv("auth_tokens.csv").random
  val conversationFeeder = csv("conversations.csv").random

  val chatScenario = scenario("Chat Performance Test")
    .feed(authFeeder)
    .feed(conversationFeeder)
    .exec(
      http("Send Chat Message")
        .post("/api/v1/chat/message")
        .body(StringBody("""{"message":"你好，阿米娅","conversationId":"${conversation_id}"}"""))
        .check(status.is(200))
        .check(jsonPath("$.data.content").exists)
        .check(responseTimeInMillis.lt(5000))
    )
    .pause(1, 3)

  setUp(
    chatScenario.inject(
      rampUsers(100) during (60 seconds),
      constantUsersPerSec(50) during (300 seconds)
    )
  ).protocols(httpProtocol)
   .assertions(
     global.responseTime.max.lt(10000),
     global.responseTime.mean.lt(2000),
     global.successfulRequests.percent.gt(95)
   )
}
```

## 测试自动化和CI/CD

### 1. GitHub Actions测试流水线

#### .github/workflows/test.yml
```yaml
name: Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: arkpets_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run unit tests
      run: mvn test -Dspring.profiles.active=test

    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Unit Test Results
        path: target/surefire-reports/*.xml
        reporter: java-junit

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: target/site/jacoco/jacoco.xml

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Run integration tests
      run: mvn verify -Dspring.profiles.active=integration-test

    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: target/failsafe-reports/

  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Build application
      run: mvn clean package -DskipTests

    - name: Start application
      run: |
        java -jar target/ark-pets-*.jar &
        sleep 30

    - name: Run E2E tests
      run: mvn test -Dtest=**/*E2ETest -Dspring.profiles.active=e2e

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: target/surefire-reports/
```

### 2. 测试质量门禁

#### SonarQube质量检查
```yaml
# sonar-project.properties
sonar.projectKey=ark-pets-ai-enhanced
sonar.projectName=Ark-Pets AI Enhanced
sonar.projectVersion=1.0

sonar.sources=src/main/java
sonar.tests=src/test/java
sonar.java.binaries=target/classes
sonar.java.test.binaries=target/test-classes

sonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml
sonar.junit.reportPaths=target/surefire-reports

# 质量门禁规则
sonar.qualitygate.wait=true
sonar.coverage.minimum=80
sonar.duplicated_lines_density.maximum=3
sonar.maintainability_rating.minimum=A
sonar.reliability_rating.minimum=A
sonar.security_rating.minimum=A
```

## 测试最佳实践

### 1. 测试命名规范

#### 测试方法命名
```java
// ✅ 好的命名
@Test
void shouldReturnChatResponseWhenValidRequestProvided() { }

@Test
void shouldThrowExceptionWhenConversationNotFound() { }

@Test
void shouldCacheConversationContextAfterFirstLoad() { }

// ❌ 不好的命名
@Test
void testChat() { }

@Test
void test1() { }

@Test
void chatTest() { }
```

### 2. 测试数据管理

#### 测试数据构建器
```java
public class TestDataBuilder {

    public static ConversationBuilder conversation() {
        return new ConversationBuilder();
    }

    public static class ConversationBuilder {
        private String id = "conv_" + UUID.randomUUID().toString();
        private String userId = "user_123";
        private String title = "测试对话";
        private String characterName = "amiya";
        private String personalityId = "default";

        public ConversationBuilder withId(String id) {
            this.id = id;
            return this;
        }

        public ConversationBuilder withUserId(String userId) {
            this.userId = userId;
            return this;
        }

        public ConversationBuilder withTitle(String title) {
            this.title = title;
            return this;
        }

        public ConversationBuilder withCharacter(String characterName) {
            this.characterName = characterName;
            return this;
        }

        public Conversation build() {
            return Conversation.builder()
                .id(id)
                .userId(userId)
                .title(title)
                .characterName(characterName)
                .personalityId(personalityId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        }
    }
}

// 使用示例
@Test
void shouldFindConversationsByUserId() {
    // Given
    String userId = "user123";
    Conversation conversation1 = TestDataBuilder.conversation()
        .withUserId(userId)
        .withTitle("对话1")
        .build();
    Conversation conversation2 = TestDataBuilder.conversation()
        .withUserId(userId)
        .withTitle("对话2")
        .withCharacter("exusiai")
        .build();

    // When & Then...
}
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的测试策略指南，涵盖单元测试、集成测试、端到端测试、性能测试等各个层面
