# 编码规范 (Coding Standards)

## 概述

本文档定义了Ark-Pets项目的编码规范，包括Java、JavaScript/TypeScript、配置文件等的编码标准，确保代码质量和团队协作效率。

## Java编码规范

### 1. 命名规范

#### 类命名 (Class Naming)
```java
// ✅ 正确示例
public class UserService {
    // 使用PascalCase，名词或名词短语
}

public class ChatMessageProcessor {
    // 复合词使用PascalCase
}

public interface ModelProvider {
    // 接口名称使用PascalCase
}

// ❌ 错误示例
public class userservice { } // 应使用PascalCase
public class ChatMsg { } // 避免缩写
```

#### 方法命名 (Method Naming)
```java
// ✅ 正确示例
public class UserService {
    
    /**
     * 创建新用户
     * @param userRequest 用户请求
     * @return 创建结果
     */
    public UserCreationResult createNewUser(UserCreationRequest userRequest) {
        // 动词开头，camelCase
    }
    
    /**
     * 验证用户密码
     * @param userId 用户ID
     * @param password 密码
     * @return 验证结果
     */
    public boolean validateUserPassword(String userId, String password) {
        // 布尔返回值使用is/has/can/should等前缀
    }
    
    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    public UserInfo getUserInfo(String userId) {
        // get前缀表示获取操作
    }
    
    /**
     * 检查用户是否存在
     * @param userId 用户ID
     * @return 是否存在
     */
    public boolean isUserExists(String userId) {
        // is前缀表示状态检查
    }
}

// ❌ 错误示例
public void user() { } // 应使用动词开头
public boolean checkUser() { } // 布尔方法应使用is/has等前缀
public void getUserData() { } // get方法应有返回值
```

#### 变量命名 (Variable Naming)
```java
// ✅ 正确示例
public class ChatService {
    
    // 常量使用UPPER_SNAKE_CASE
    private static final int MAX_MESSAGE_LENGTH = 4000;
    private static final String DEFAULT_MODEL_PROVIDER = "openai";
    
    // 实例变量使用camelCase
    private final ConversationRepository conversationRepository;
    private final MessageValidator messageValidator;
    private final AIServiceClient aiServiceClient;
    
    public void processMessage(String userMessage) {
        // 局部变量使用camelCase
        String sanitizedMessage = messageValidator.sanitize(userMessage);
        ConversationContext conversationContext = buildContext();
        AIResponse aiResponse = aiServiceClient.sendMessage(sanitizedMessage);
        
        // 循环变量使用有意义的名称
        for (Message historyMessage : conversationContext.getMessages()) {
            // 处理历史消息
        }
    }
}

// ❌ 错误示例
private String msg; // 避免缩写
private int i, j, k; // 避免无意义的变量名
private final String APIKEY; // 常量应使用下划线分隔
```

### 2. 代码格式

#### 缩进和空格
```java
// ✅ 正确示例
public class FormattingExample {
    
    public void methodWithProperFormatting() {
        if (condition) {
            // 4个空格缩进
            doSomething();
        } else {
            doSomethingElse();
        }
        
        // 方法调用
        String result = someMethod(
            parameter1,
            parameter2,
            parameter3
        );
    }
    
    // 方法之间空一行
    public void anotherMethod() {
        // 实现
    }
}
```

#### 大括号风格 (K&R Style)
```java
// ✅ 正确示例
public class BraceStyleExample {
    
    public void correctBraceStyle() {
        if (condition) {
            // 左大括号在同一行
            doSomething();
        } else {
            doSomethingElse();
        }
        
        try {
            riskyOperation();
        } catch (Exception e) {
            handleException(e);
        } finally {
            cleanup();
        }
    }
}

// ❌ 错误示例
public void incorrectBraceStyle()
{  // 左大括号应在同一行
    if (condition)
    {  // 左大括号应在同一行
        doSomething();
    }
}
```

### 3. 注释规范

#### JavaDoc注释
```java
/**
 * 聊天服务类，负责处理用户与AI的对话交互
 * 
 * <p>该服务提供以下功能：
 * <ul>
 *   <li>处理用户聊天消息</li>
 *   <li>管理对话上下文</li>
 *   <li>调用AI模型生成回复</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
public class ChatService {
    
    /**
     * 处理用户聊天消息
     * 
     * <p>该方法会验证消息内容，构建对话上下文，调用AI模型生成回复，
     * 并保存对话记录。
     * 
     * @param userId 用户ID，不能为null或空字符串
     * @param request 聊天请求，包含消息内容和配置参数
     * @return 聊天响应，包含AI回复和元数据
     * @throws ChatProcessingException 当消息处理失败时抛出
     * @throws IllegalArgumentException 当参数无效时抛出
     * 
     * @see ChatRequest
     * @see ChatResponse
     */
    public ChatResponse processChatMessage(String userId, ChatRequest request) 
            throws ChatProcessingException {
        // 实现逻辑
    }
}
```

#### 行内注释
```java
public class CommentExample {
    
    public void processData() {
        // 验证输入参数
        if (input == null || input.isEmpty()) {
            throw new IllegalArgumentException("输入不能为空");
        }
        
        // TODO: 优化算法性能 - 2025-01-15 - 张三
        String result = complexAlgorithm(input);
        
        // FIXME: 修复并发访问问题 - 2025-01-10 - 李四
        cache.put(key, result);
        
        // NOTE: 这里使用了特殊的处理逻辑，因为...
        if (specialCondition) {
            handleSpecialCase();
        }
    }
}
```

### 4. 异常处理

#### 异常定义
```java
/**
 * 聊天处理异常基类
 */
public class ChatProcessingException extends Exception {
    
    private final String errorCode;
    private final Map<String, Object> errorDetails;
    
    public ChatProcessingException(String message) {
        super(message);
        this.errorCode = "CHAT_ERROR";
        this.errorDetails = new HashMap<>();
    }
    
    public ChatProcessingException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetails = new HashMap<>();
    }
    
    public ChatProcessingException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "CHAT_ERROR";
        this.errorDetails = new HashMap<>();
    }
    
    // 具体异常类型
    public static class InvalidMessageException extends ChatProcessingException {
        public InvalidMessageException(String message) {
            super("无效消息: " + message, "INVALID_MESSAGE");
        }
    }
    
    public static class AIModelException extends ChatProcessingException {
        public AIModelException(String message, Throwable cause) {
            super("AI模型调用失败: " + message, cause);
        }
    }
}
```

#### 异常处理
```java
public class ExceptionHandlingExample {
    
    public void properExceptionHandling() {
        try {
            // 可能抛出异常的操作
            riskyOperation();
        } catch (SpecificException e) {
            // 处理特定异常
            log.error("特定异常处理: {}", e.getMessage(), e);
            handleSpecificException(e);
        } catch (GeneralException e) {
            // 处理一般异常
            log.warn("一般异常处理: {}", e.getMessage());
            handleGeneralException(e);
        } catch (Exception e) {
            // 处理未预期异常
            log.error("未预期异常: {}", e.getMessage(), e);
            throw new ServiceException("操作失败", e);
        } finally {
            // 清理资源
            cleanup();
        }
    }
    
    // ❌ 错误示例
    public void badExceptionHandling() {
        try {
            riskyOperation();
        } catch (Exception e) {
            // 不要忽略异常
            // 不要只打印堆栈跟踪
            e.printStackTrace();
        }
    }
}
```

## JavaScript/TypeScript编码规范

### 1. 命名规范

#### 变量和函数命名
```typescript
// ✅ 正确示例
const maxRetryAttempts = 3;
const apiBaseUrl = 'https://api.arkpets.com';

function calculateTokenUsage(messages: Message[]): number {
    // camelCase for functions
}

const processUserMessage = async (message: string): Promise<ChatResponse> => {
    // camelCase for arrow functions
};

// 类命名使用PascalCase
class ChatMessageProcessor {
    private readonly maxMessageLength = 4000;
    
    public async processMessage(message: string): Promise<ProcessedMessage> {
        // 实现逻辑
    }
}

// 接口命名使用PascalCase
interface ChatRequest {
    message: string;
    conversationId?: string;
    modelProvider: string;
}

// 类型别名使用PascalCase
type MessageRole = 'user' | 'assistant' | 'system';

// 枚举使用PascalCase
enum MessageType {
    TEXT = 'text',
    IMAGE = 'image',
    AUDIO = 'audio'
}

// ❌ 错误示例
const MAX_RETRY = 3; // 变量不应使用全大写
function ProcessMessage() { } // 函数不应使用PascalCase
class chatProcessor { } // 类应使用PascalCase
```

### 2. 代码格式

#### 缩进和空格
```typescript
// ✅ 正确示例
const config = {
    apiUrl: 'https://api.arkpets.com',
    timeout: 30000,
    retryAttempts: 3,
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    }
};

if (condition) {
    doSomething();
} else if (anotherCondition) {
    doSomethingElse();
} else {
    doDefault();
}

// 函数参数过多时换行
const result = await processComplexOperation(
    parameter1,
    parameter2,
    parameter3,
    parameter4
);
```

### 3. TypeScript类型定义

#### 接口定义
```typescript
/**
 * 聊天请求接口
 */
interface ChatRequest {
    /** 消息内容 */
    message: string;
    /** 对话ID，可选 */
    conversationId?: string;
    /** 宠物ID */
    petId: string;
    /** 性格ID */
    personalityId: string;
    /** 模型提供商 */
    modelProvider: 'openai' | 'claude' | 'local';
    /** 模型名称 */
    modelName: string;
    /** 模型参数 */
    parameters?: ModelParameters;
}

/**
 * 聊天响应接口
 */
interface ChatResponse {
    /** 对话ID */
    conversationId: string;
    /** 消息ID */
    messageId: string;
    /** 回复内容 */
    content: string;
    /** Token数量 */
    tokens: number;
    /** 使用的模型 */
    model: string;
    /** 时间戳 */
    timestamp: Date;
    /** 完成原因 */
    finishReason: string;
}

/**
 * 模型参数接口
 */
interface ModelParameters {
    /** 温度参数 0.0-2.0 */
    temperature?: number;
    /** Top-p参数 0.0-1.0 */
    topP?: number;
    /** 最大Token数 */
    maxTokens?: number;
    /** 频率惩罚 -2.0-2.0 */
    frequencyPenalty?: number;
    /** 存在惩罚 -2.0-2.0 */
    presencePenalty?: number;
}
```

## 配置文件规范

### 1. YAML配置
```yaml
# application.yml
# 使用小写字母和连字符
spring:
  application:
    name: ai-service
  
  datasource:
    url: ****************************************
    username: ${DB_USERNAME:arkpets}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

# 自定义配置使用kebab-case
arkpets:
  ai:
    providers:
      openai:
        api-endpoint: https://api.openai.com/v1
        api-key: ${OPENAI_API_KEY}
        timeout: 30000
        max-retries: 3
        rate-limit-per-minute: 60
        enabled: true
        default-parameters:
          temperature: 0.7
          top-p: 1.0
          max-tokens: 2048
```

### 2. JSON配置
```json
{
  "name": "ark-pets-frontend",
  "version": "1.0.0",
  "description": "Ark-Pets桌面客户端",
  "main": "src/main/java/com/arkpets/desktop/DesktopLauncher.java",
  "scripts": {
    "build": "gradle build",
    "test": "gradle test",
    "run": "gradle desktop:run"
  },
  "dependencies": {
    "libgdx": "1.11.0",
    "spine": "4.1.0"
  },
  "devDependencies": {
    "junit": "5.8.2",
    "mockito": "4.6.1"
  }
}
```

## 代码质量检查

### 1. Checkstyle配置
```xml
<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
    "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    
    <!-- 文件长度检查 -->
    <module name="FileLength">
        <property name="max" value="500"/>
    </module>
    
    <!-- 行长度检查 -->
    <module name="LineLength">
        <property name="max" value="120"/>
    </module>
    
    <module name="TreeWalker">
        <!-- 命名检查 -->
        <module name="TypeName"/>
        <module name="MethodName"/>
        <module name="VariableName"/>
        <module name="ConstantName"/>
        
        <!-- 导入检查 -->
        <module name="AvoidStarImport"/>
        <module name="UnusedImports"/>
        
        <!-- 代码复杂度检查 -->
        <module name="CyclomaticComplexity">
            <property name="max" value="10"/>
        </module>
        
        <!-- 方法长度检查 -->
        <module name="MethodLength">
            <property name="max" value="50"/>
        </module>
    </module>
</module>
```

### 2. ESLint配置
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "no-console": "warn",
    "no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "max-len": ["error", { "code": 120 }],
    "complexity": ["error", 10],
    "max-lines-per-function": ["error", 50]
  }
}
```

## Git提交规范

### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具变动

### 示例
```
feat(ai-service): 添加流式聊天功能

- 实现SSE流式响应
- 支持实时Token推送
- 添加停止生成功能

Closes #123
```

## 性能优化规范

### 1. 数据库查询优化
```java
// ✅ 正确示例
@Repository
public class ConversationRepository {
    
    // 使用分页查询
    @Query("SELECT c FROM Conversation c WHERE c.userId = :userId ORDER BY c.updatedAt DESC")
    Page<Conversation> findByUserIdOrderByUpdatedAtDesc(
        @Param("userId") String userId, Pageable pageable);
    
    // 使用索引字段查询
    @Query("SELECT c FROM Conversation c WHERE c.userId = :userId AND c.status = :status")
    List<Conversation> findByUserIdAndStatus(
        @Param("userId") String userId, @Param("status") String status);
    
    // 批量操作
    @Modifying
    @Query("UPDATE Conversation c SET c.status = :status WHERE c.id IN :ids")
    int updateStatusByIds(@Param("status") String status, @Param("ids") List<String> ids);
}

// ❌ 错误示例
// 避免N+1查询
// 避免全表扫描
// 避免在循环中执行查询
```

### 2. 缓存使用规范
```java
@Service
public class UserService {
    
    // 使用缓存注解
    @Cacheable(value = "users", key = "#userId")
    public UserInfo getUserInfo(String userId) {
        return userRepository.findById(userId);
    }
    
    // 缓存更新
    @CacheEvict(value = "users", key = "#userInfo.id")
    public void updateUserInfo(UserInfo userInfo) {
        userRepository.save(userInfo);
    }
    
    // 批量缓存清理
    @CacheEvict(value = "users", allEntries = true)
    public void clearAllUserCache() {
        // 清理所有用户缓存
    }
}
```

---

**文档负责人**: 开发规范组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
