# API文档规范 (API Documentation Standards)

## 模块概述

API文档规范定义了Ark-Pets AI Enhanced项目中所有REST API和WebSocket API的文档编写标准，包括接口描述、参数定义、响应格式、错误处理等规范要求。

**规范目标**:
- 统一的API文档格式和风格
- 完整的接口描述和示例
- 标准化的错误处理和状态码
- 自动化的文档生成和维护

## API文档标准

### 1. 文档结构规范

#### 标准文档模板
```markdown
# API名称

## 接口概述
简要描述接口的功能和用途

## 请求信息

### HTTP方法和路径
```http
POST /api/v1/resource
```

### 请求头
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer token |
| Content-Type | string | 是 | application/json |

### 路径参数
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| id | string | 是 | 资源ID |

### 查询参数
| 参数名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| size | integer | 否 | 20 | 页面大小 |

### 请求体
```json
{
  "field1": "string",
  "field2": 123,
  "field3": true
}
```

## 响应信息

### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "123",
    "name": "example"
  },
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "Bad Request",
  "error": "INVALID_PARAMETER",
  "details": "Parameter 'name' is required",
  "timestamp": "2025-01-01T12:00:00Z"
}
```

## 示例代码

### cURL示例
```bash
curl -X POST "https://api.arkpets.com/api/v1/resource" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"field1": "value1"}'
```

### JavaScript示例
```javascript
const response = await fetch('/api/v1/resource', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    field1: 'value1'
  })
});
```

## 错误处理
详细说明可能的错误情况和处理方式
```

### 2. OpenAPI规范

#### Swagger/OpenAPI 3.0配置
```yaml
openapi: 3.0.3
info:
  title: Ark-Pets AI Enhanced API
  description: 明日方舟智能桌宠系统API文档
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.arkpets.com/api/v1
    description: 生产环境
  - url: https://staging-api.arkpets.com/api/v1
    description: 测试环境
  - url: http://localhost:8080/api/v1
    description: 开发环境

paths:
  /chat/message:
    post:
      tags:
        - AI Chat
      summary: 发送聊天消息
      description: 向AI角色发送消息并获取回复
      operationId: sendChatMessage
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
            examples:
              basic_chat:
                summary: 基础聊天示例
                value:
                  message: "你好，阿米娅"
                  conversationId: "conv_123"
                  characterName: "amiya"
                  personalityId: "default"
      responses:
        '200':
          description: 聊天响应成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
              examples:
                success_response:
                  summary: 成功响应示例
                  value:
                    code: 200
                    message: "Success"
                    data:
                      conversationId: "conv_123"
                      messageId: "msg_456"
                      content: "博士，您好！有什么我可以帮助您的吗？"
                      emotionType: "friendly"
                      animationHint: "wave"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'
        '500':
          $ref: '#/components/responses/InternalError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    ChatRequest:
      type: object
      required:
        - message
        - conversationId
        - characterName
      properties:
        message:
          type: string
          description: 用户消息内容
          example: "你好，阿米娅"
          minLength: 1
          maxLength: 2000
        conversationId:
          type: string
          description: 对话ID
          example: "conv_123"
          pattern: '^conv_[a-zA-Z0-9]+$'
        characterName:
          type: string
          description: 角色名称
          example: "amiya"
          enum: ["amiya", "exusiai", "texas", "lappland"]
        personalityId:
          type: string
          description: 性格ID
          example: "default"
        context:
          type: object
          description: 额外的上下文信息
          additionalProperties: true

    ChatResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          $ref: '#/components/schemas/ChatData'
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
          example: "2025-01-01T12:00:00Z"

    ChatData:
      type: object
      properties:
        conversationId:
          type: string
          description: 对话ID
          example: "conv_123"
        messageId:
          type: string
          description: 消息ID
          example: "msg_456"
        content:
          type: string
          description: AI回复内容
          example: "博士，您好！有什么我可以帮助您的吗？"
        emotionType:
          type: string
          description: 情感类型
          example: "friendly"
          enum: ["happy", "sad", "angry", "surprised", "friendly", "neutral"]
        animationHint:
          type: string
          description: 动画提示
          example: "wave"
        behaviorHint:
          type: string
          description: 行为提示
          example: "greeting"
        tokens:
          $ref: '#/components/schemas/TokenUsage'

    TokenUsage:
      type: object
      properties:
        promptTokens:
          type: integer
          description: 提示词token数量
          example: 150
        completionTokens:
          type: integer
          description: 完成token数量
          example: 50
        totalTokens:
          type: integer
          description: 总token数量
          example: 200

    Error:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "Bad Request"
        error:
          type: string
          description: 错误类型
          example: "INVALID_PARAMETER"
        details:
          type: string
          description: 错误详情
          example: "Parameter 'message' is required"
        timestamp:
          type: string
          format: date-time
          description: 错误时间戳
          example: "2025-01-01T12:00:00Z"

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          examples:
            invalid_parameter:
              summary: 参数无效
              value:
                code: 400
                message: "Bad Request"
                error: "INVALID_PARAMETER"
                details: "Parameter 'message' is required"

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          examples:
            missing_token:
              summary: 缺少认证token
              value:
                code: 401
                message: "Unauthorized"
                error: "MISSING_TOKEN"
                details: "Authorization header is required"

    RateLimited:
      description: 请求频率超限
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          examples:
            rate_limit_exceeded:
              summary: 频率限制
              value:
                code: 429
                message: "Too Many Requests"
                error: "RATE_LIMIT_EXCEEDED"
                details: "Rate limit exceeded. Try again in 60 seconds"

    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          examples:
            server_error:
              summary: 服务器错误
              value:
                code: 500
                message: "Internal Server Error"
                error: "INTERNAL_ERROR"
                details: "An unexpected error occurred"
```

### 3. 状态码规范

#### HTTP状态码使用标准
```java
public class ApiStatusCodes {
    
    // 成功响应 (2xx)
    public static final int SUCCESS = 200;              // 请求成功
    public static final int CREATED = 201;              // 资源创建成功
    public static final int ACCEPTED = 202;             // 请求已接受，异步处理
    public static final int NO_CONTENT = 204;           // 成功，无返回内容
    
    // 客户端错误 (4xx)
    public static final int BAD_REQUEST = 400;          // 请求参数错误
    public static final int UNAUTHORIZED = 401;         // 未授权
    public static final int FORBIDDEN = 403;            // 禁止访问
    public static final int NOT_FOUND = 404;            // 资源不存在
    public static final int METHOD_NOT_ALLOWED = 405;   // 方法不允许
    public static final int CONFLICT = 409;             // 资源冲突
    public static final int UNPROCESSABLE_ENTITY = 422; // 参数验证失败
    public static final int TOO_MANY_REQUESTS = 429;    // 请求频率超限
    
    // 服务器错误 (5xx)
    public static final int INTERNAL_ERROR = 500;       // 服务器内部错误
    public static final int BAD_GATEWAY = 502;          // 网关错误
    public static final int SERVICE_UNAVAILABLE = 503;  // 服务不可用
    public static final int GATEWAY_TIMEOUT = 504;      // 网关超时
}
```

#### 业务错误码规范
```java
public enum BusinessErrorCode {
    
    // 用户相关错误 (1000-1999)
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    INVALID_CREDENTIALS(1003, "用户名或密码错误"),
    USER_DISABLED(1004, "用户账户已禁用"),
    
    // 对话相关错误 (2000-2999)
    CONVERSATION_NOT_FOUND(2001, "对话不存在"),
    CONVERSATION_LIMIT_EXCEEDED(2002, "对话数量超限"),
    INVALID_CHARACTER(2003, "无效的角色"),
    INVALID_PERSONALITY(2004, "无效的性格设置"),
    
    // AI服务相关错误 (3000-3999)
    AI_SERVICE_UNAVAILABLE(3001, "AI服务不可用"),
    MODEL_NOT_SUPPORTED(3002, "不支持的模型"),
    TOKEN_LIMIT_EXCEEDED(3003, "Token使用量超限"),
    CONTENT_FILTERED(3004, "内容被过滤"),
    
    // 系统相关错误 (9000-9999)
    SYSTEM_MAINTENANCE(9001, "系统维护中"),
    DATABASE_ERROR(9002, "数据库错误"),
    EXTERNAL_SERVICE_ERROR(9003, "外部服务错误");
    
    private final int code;
    private final String message;
    
    BusinessErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() { return code; }
    public String getMessage() { return message; }
}
```

### 4. 响应格式规范

#### 统一响应包装器
```java
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    private int code;
    private String message;
    private T data;
    private String error;
    private String details;
    private LocalDateTime timestamp;
    
    // 成功响应
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.code = 200;
        response.message = "Success";
        response.data = data;
        response.timestamp = LocalDateTime.now();
        return response;
    }
    
    // 错误响应
    public static <T> ApiResponse<T> error(int code, String message, String error, String details) {
        ApiResponse<T> response = new ApiResponse<>();
        response.code = code;
        response.message = message;
        response.error = error;
        response.details = details;
        response.timestamp = LocalDateTime.now();
        return response;
    }
    
    // 业务错误响应
    public static <T> ApiResponse<T> businessError(BusinessErrorCode errorCode) {
        return error(400, "Business Error", errorCode.name(), errorCode.getMessage());
    }
    
    // getter和setter方法...
}
```

#### 分页响应格式
```java
public class PageResponse<T> {
    
    private List<T> items;
    private PageInfo pageInfo;
    
    public static class PageInfo {
        private int page;
        private int size;
        private long total;
        private int totalPages;
        private boolean hasNext;
        private boolean hasPrevious;
        private String nextPageToken;
        
        // getter和setter方法...
    }
    
    // 构造方法和getter/setter...
}
```

### 5. 文档生成配置

#### Springdoc OpenAPI配置
```java
@Configuration
@EnableOpenApi
public class OpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("Ark-Pets AI Enhanced API")
                .description("明日方舟智能桌宠系统API文档")
                .version("1.0.0")
                .contact(new Contact()
                    .name("API Support")
                    .email("<EMAIL>"))
                .license(new License()
                    .name("MIT")
                    .url("https://opensource.org/licenses/MIT")))
            .servers(Arrays.asList(
                new Server().url("https://api.arkpets.com/api/v1").description("生产环境"),
                new Server().url("https://staging-api.arkpets.com/api/v1").description("测试环境"),
                new Server().url("http://localhost:8080/api/v1").description("开发环境")))
            .components(new Components()
                .addSecuritySchemes("bearerAuth", new SecurityScheme()
                    .type(SecurityScheme.Type.HTTP)
                    .scheme("bearer")
                    .bearerFormat("JWT")
                    .description("JWT token for authentication")));
    }
    
    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
            .group("public")
            .pathsToMatch("/api/v1/**")
            .build();
    }
}
```

#### 注解使用示例
```java
@RestController
@RequestMapping("/api/v1/chat")
@Tag(name = "AI Chat", description = "AI聊天相关接口")
public class ChatController {
    
    @PostMapping("/message")
    @Operation(
        summary = "发送聊天消息",
        description = "向AI角色发送消息并获取回复",
        responses = {
            @ApiResponse(responseCode = "200", description = "聊天响应成功",
                content = @Content(schema = @Schema(implementation = ChatResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误",
                content = @Content(schema = @Schema(implementation = ApiResponse.class))),
            @ApiResponse(responseCode = "401", description = "未授权访问",
                content = @Content(schema = @Schema(implementation = ApiResponse.class)))
        }
    )
    public ResponseEntity<ApiResponse<ChatData>> sendMessage(
            @Parameter(description = "聊天请求", required = true)
            @Valid @RequestBody ChatRequest request) {
        // 实现逻辑...
    }
}
```

## 文档维护流程

### 1. 自动化生成
- 使用Springdoc自动生成OpenAPI规范
- CI/CD流程中自动更新文档
- 版本控制和变更追踪

### 2. 文档审查
- API变更必须更新文档
- 代码审查包含文档审查
- 定期文档质量检查

### 3. 文档发布
- 自动部署到文档站点
- 多版本文档管理
- 用户反馈收集和处理

---

**模块负责人**: API文档开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
