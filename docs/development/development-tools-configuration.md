# 开发工具配置 (Development Tools Configuration)

## 模块概述

开发工具配置文档提供了Ark-Pets AI Enhanced项目的完整开发环境配置指南，包括IDE配置、构建工具、代码质量工具、调试工具等的详细设置说明。

**配置目标**:
- 统一的开发环境和工具配置
- 提高开发效率和代码质量
- 支持团队协作和代码规范
- 简化项目设置和部署流程

## 开发环境架构

### 1. 开发工具栈

#### 核心开发工具
```
┌─────────────────────────────────────┐
│            开发工具栈                │
│  ┌──────────┬──────────┬──────────┐ │
│  │   IDE    │  构建工具 │ 版本控制  │ │
│  │ IntelliJ │  Maven   │   Git    │ │
│  │ VS Code  │ Gradle   │ GitHub   │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│            质量工具                  │
│  ┌──────────┬──────────┬──────────┐ │
│  │ SonarQube│ Checkstyle│ SpotBugs │ │
│  │ ESLint   │ Prettier │ Husky    │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│            调试工具                  │
│  ┌──────────┬──────────┬──────────┐ │
│  │ JProfiler│ VisualVM │ JConsole │ │
│  │ Chrome   │ Postman  │ Insomnia │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 开发流程工具链

#### 工具集成流程
```mermaid
graph TB
    subgraph "开发阶段"
        IDE[IDE开发环境]
        CodeFormat[代码格式化]
        StaticAnalysis[静态分析]
    end
    
    subgraph "构建阶段"
        Maven[Maven构建]
        Gradle[Gradle构建]
        Docker[Docker构建]
    end
    
    subgraph "质量检查"
        SonarQube[SonarQube分析]
        Checkstyle[代码风格检查]
        SpotBugs[Bug检测]
    end
    
    subgraph "测试调试"
        UnitTest[单元测试]
        Debug[调试工具]
        Profiling[性能分析]
    end
    
    IDE --> CodeFormat
    CodeFormat --> StaticAnalysis
    StaticAnalysis --> Maven
    Maven --> Gradle
    Gradle --> Docker
    Docker --> SonarQube
    SonarQube --> Checkstyle
    Checkstyle --> SpotBugs
    SpotBugs --> UnitTest
    UnitTest --> Debug
    Debug --> Profiling
```

## IDE配置

### 1. IntelliJ IDEA配置

#### 项目设置文件 (.idea/codeStyles/Project.xml)
```xml
<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="OTHER_INDENT_OPTIONS">
      <value>
        <option name="INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="4" />
        <option name="USE_TAB_CHARACTER" value="false" />
      </value>
    </option>
    <JavaCodeStyleSettings>
      <option name="ANNOTATION_PARAMETER_WRAP" value="1" />
      <option name="ALIGN_MULTILINE_ANNOTATION_PARAMETERS" value="true" />
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="java" withSubpackages="true" static="false" />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="org" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="com" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="true" />
        </value>
      </option>
    </JavaCodeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="RIGHT_MARGIN" value="120" />
      <option name="KEEP_LINE_BREAKS" value="false" />
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
      <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
      <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
      <option name="BLANK_LINES_BEFORE_PACKAGE" value="0" />
      <option name="BLANK_LINES_AFTER_PACKAGE" value="1" />
      <option name="BLANK_LINES_BEFORE_IMPORTS" value="1" />
      <option name="BLANK_LINES_AFTER_IMPORTS" value="1" />
      <option name="BLANK_LINES_AROUND_CLASS" value="1" />
      <option name="BLANK_LINES_AROUND_FIELD" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD" value="1" />
      <option name="BLANK_LINES_BEFORE_METHOD_BODY" value="0" />
      <option name="BLANK_LINES_AROUND_FIELD_IN_INTERFACE" value="0" />
      <option name="BLANK_LINES_AROUND_METHOD_IN_INTERFACE" value="1" />
      <option name="BLANK_LINES_AFTER_CLASS_HEADER" value="0" />
      <option name="BLANK_LINES_AFTER_ANONYMOUS_CLASS_HEADER" value="0" />
    </codeStyleSettings>
  </code_scheme>
</component>
```

#### 插件配置 (.idea/externalDependencies.xml)
```xml
<project version="4">
  <component name="ExternalDependencies">
    <plugin id="com.intellij.java" />
    <plugin id="org.jetbrains.plugins.gradle" />
    <plugin id="org.jetbrains.idea.maven" />
    <plugin id="Docker" />
    <plugin id="org.sonarlint.idea" />
    <plugin id="CheckStyle-IDEA" />
    <plugin id="com.intellij.spring" />
    <plugin id="com.intellij.database" />
    <plugin id="org.jetbrains.plugins.yaml" />
    <plugin id="com.intellij.properties" />
    <plugin id="com.intellij.uiDesigner" />
    <plugin id="org.jetbrains.kotlin" />
    <plugin id="JavaScript" />
    <plugin id="com.intellij.css" />
    <plugin id="HtmlTools" />
  </component>
</project>
```

#### 运行配置 (.idea/runConfigurations/Application.xml)
```xml
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ArkPetsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <module name="ark-pets-ai-enhanced" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.arkpets.ArkPetsApplication" />
    <option name="ALTERNATIVE_JRE_PATH" />
    <option name="SHORTEN_COMMAND_LINE" value="NONE" />
    <option name="ENABLE_DEBUG_OUTPUT" value="false" />
    <option name="HIDE_BANNER" value="false" />
    <option name="VM_OPTIONS" value="-Xmx2g -Xms1g -Dspring.profiles.active=dev" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="ENVIRONMENT_VARIABLES">
      <map>
        <entry key="OPENAI_API_KEY" value="your-openai-api-key" />
        <entry key="CLAUDE_API_KEY" value="your-claude-api-key" />
      </map>
    </option>
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="USE_CLASSPATH_JAR" value="false" />
    <option name="INCLUDE_PROVIDED_SCOPE" value="true" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
```

### 2. VS Code配置

#### 工作区设置 (.vscode/settings.json)
```json
{
  "java.home": "/usr/lib/jvm/java-17-openjdk",
  "java.configuration.runtimes": [
    {
      "name": "JavaSE-17",
      "path": "/usr/lib/jvm/java-17-openjdk"
    }
  ],
  "java.compile.nullAnalysis.mode": "automatic",
  "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml",
  "java.format.settings.profile": "GoogleStyle",
  "java.saveActions.organizeImports": true,
  "java.checkstyle.configuration": "${workspaceFolder}/checkstyle.xml",
  "sonarlint.connectedMode.project": {
    "connectionId": "arkpets-sonarqube",
    "projectKey": "ark-pets-ai-enhanced"
  },
  "files.associations": {
    "*.yml": "yaml",
    "*.yaml": "yaml"
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll": true
  },
  "editor.rulers": [120],
  "editor.tabSize": 4,
  "editor.insertSpaces": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "javascript.preferences.importModuleSpecifier": "relative",
  "eslint.workingDirectories": ["frontend"],
  "prettier.configPath": "frontend/.prettierrc"
}
```

#### 推荐扩展 (.vscode/extensions.json)
```json
{
  "recommendations": [
    "vscjava.vscode-java-pack",
    "vscjava.vscode-spring-initializr",
    "vscjava.vscode-spring-boot-dashboard",
    "pivotal.vscode-spring-boot",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-json",
    "sonarsource.sonarlint-vscode",
    "shengchen.vscode-checkstyle",
    "ms-vscode.vscode-docker",
    "ms-kubernetes-tools.vscode-kubernetes-tools",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-git-graph",
    "eamodio.gitlens"
  ]
}
```

#### 调试配置 (.vscode/launch.json)
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "Debug ArkPets Application",
      "request": "launch",
      "mainClass": "com.arkpets.ArkPetsApplication",
      "projectName": "ark-pets-ai-enhanced",
      "args": "",
      "vmArgs": "-Xmx2g -Xms1g -Dspring.profiles.active=dev",
      "env": {
        "OPENAI_API_KEY": "your-openai-api-key",
        "CLAUDE_API_KEY": "your-claude-api-key"
      },
      "console": "internalConsole",
      "stopOnEntry": false,
      "internalConsoleOptions": "openOnSessionStart"
    },
    {
      "type": "java",
      "name": "Debug Tests",
      "request": "launch",
      "mainClass": "",
      "projectName": "ark-pets-ai-enhanced",
      "args": "",
      "vmArgs": "-Dspring.profiles.active=test",
      "console": "internalConsole",
      "stopOnEntry": false,
      "internalConsoleOptions": "openOnSessionStart"
    }
  ]
}
```

## 构建工具配置

### 1. Maven配置

#### 主POM文件 (pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.arkpets</groupId>
    <artifactId>ark-pets-ai-enhanced</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>Ark-Pets AI Enhanced</name>
    <description>明日方舟智能桌宠系统</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot版本 -->
        <spring-boot.version>3.2.0</spring-boot.version>
        
        <!-- 测试框架版本 -->
        <junit.version>5.10.0</junit.version>
        <mockito.version>5.7.0</mockito.version>
        <testcontainers.version>1.19.3</testcontainers.version>
        
        <!-- 代码质量工具版本 -->
        <checkstyle.version>10.12.5</checkstyle.version>
        <spotbugs.version>4.8.2</spotbugs.version>
        <jacoco.version>0.8.11</jacoco.version>
        <sonar.version>3.10.0.2594</sonar.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- Spring Boot插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- 测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/*IntegrationTest.java</exclude>
                        <exclude>**/*E2ETest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 集成测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <includes>
                        <include>**/*IntegrationTest.java</include>
                        <include>**/*E2ETest.java</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 代码覆盖率插件 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>BUNDLE</element>
                                    <limits>
                                        <limit>
                                            <counter>INSTRUCTION</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.80</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
            <!-- Checkstyle插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <linkXRef>false</linkXRef>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${checkstyle.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- SpotBugs插件 -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>4.8.2.0</version>
                <configuration>
                    <effort>Max</effort>
                    <threshold>Low</threshold>
                    <xmlOutput>true</xmlOutput>
                    <excludeFilterFile>spotbugs-exclude.xml</excludeFilterFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs</artifactId>
                        <version>${spotbugs.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- SonarQube插件 -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar.version}</version>
            </plugin>

            <!-- Docker插件 -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <repository>arkpets/${project.artifactId}</repository>
                    <tag>${project.version}</tag>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- 开发环境配置 -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>

        <!-- 测试环境配置 -->
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>

        <!-- 生产环境配置 -->
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>

        <!-- 代码质量检查配置 -->
        <profile>
            <id>quality</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
```

### 2. Gradle配置

#### 主构建文件 (build.gradle)
```gradle
plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.0'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'checkstyle'
    id 'com.github.spotbugs' version '5.2.1'
    id 'jacoco'
    id 'org.sonarqube' version '4.4.1.3373'
    id 'com.palantir.docker' version '0.35.0'
}

group = 'com.arkpets'
version = '1.0.0-SNAPSHOT'
sourceCompatibility = '17'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

ext {
    set('testcontainersVersion', "1.19.3")
}

dependencies {
    // Spring Boot核心依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-cache'

    // 数据库依赖
    runtimeOnly 'org.postgresql:postgresql'
    runtimeOnly 'com.h2database:h2'

    // 工具库
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:redis'
    testImplementation 'io.rest-assured:rest-assured'
    testImplementation 'org.awaitility:awaitility'
}

dependencyManagement {
    imports {
        mavenBom "org.testcontainers:testcontainers-bom:${testcontainersVersion}"
    }
}

// Java编译配置
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

// 测试配置
test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
    }
    finalizedBy jacocoTestReport
}

// 代码覆盖率配置
jacoco {
    toolVersion = "0.8.11"
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.required = true
        html.required = true
    }
    finalizedBy jacocoTestCoverageVerification
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.80
            }
        }
    }
}

// Checkstyle配置
checkstyle {
    toolVersion = '10.12.5'
    configFile = file("${rootDir}/checkstyle.xml")
    ignoreFailures = false
    maxWarnings = 0
}

// SpotBugs配置
spotbugs {
    toolVersion = '4.8.2'
    effort = 'max'
    reportLevel = 'low'
    excludeFilter = file("${rootDir}/spotbugs-exclude.xml")
}

spotbugsMain {
    reports {
        html {
            required = true
            outputLocation = file("$buildDir/reports/spotbugs/main/spotbugs.html")
        }
        xml {
            required = true
            outputLocation = file("$buildDir/reports/spotbugs/main/spotbugs.xml")
        }
    }
}

// SonarQube配置
sonarqube {
    properties {
        property "sonar.projectKey", "ark-pets-ai-enhanced"
        property "sonar.projectName", "Ark-Pets AI Enhanced"
        property "sonar.host.url", "http://localhost:9000"
        property "sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/jacoco/test/jacocoTestReport.xml"
        property "sonar.java.checkstyle.reportPaths", "${buildDir}/reports/checkstyle/main.xml"
        property "sonar.java.spotbugs.reportPaths", "${buildDir}/reports/spotbugs/main/spotbugs.xml"
    }
}

// Docker配置
docker {
    name "${project.group}/${project.name}:${project.version}"
    dockerfile file('Dockerfile')
    files bootJar.archiveFile.get()
    buildArgs(['JAR_FILE': "${bootJar.archiveFileName.get()}"])
}
```

## 代码质量工具配置

### 1. Checkstyle配置

#### checkstyle.xml
```xml
<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
    "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="warning"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- 文件长度检查 -->
    <module name="FileLength">
        <property name="max" value="2000"/>
    </module>

    <!-- 行长度检查 -->
    <module name="LineLength">
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
    </module>

    <!-- Tab字符检查 -->
    <module name="FileTabCharacter"/>

    <!-- 树遍历器 -->
    <module name="TreeWalker">
        <!-- 命名约定 -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <!-- 导入检查 -->
        <module name="AvoidStarImport"/>
        <module name="IllegalImport"/>
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>

        <!-- 大小限制 -->
        <module name="MethodLength">
            <property name="max" value="150"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="7"/>
        </module>

        <!-- 空白检查 -->
        <module name="EmptyForIteratorPad"/>
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>

        <!-- 修饰符检查 -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>

        <!-- 代码块检查 -->
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock"/>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>

        <!-- 编码问题 -->
        <module name="AvoidInlineConditionals"/>
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="HiddenField"/>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <module name="MagicNumber"/>
        <module name="MissingSwitchDefault"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>

        <!-- 类设计 -->
        <module name="DesignForExtension"/>
        <module name="FinalClass"/>
        <module name="HideUtilityClassConstructor"/>
        <module name="InterfaceIsType"/>
        <module name="VisibilityModifier"/>

        <!-- 杂项 -->
        <module name="ArrayTypeStyle"/>
        <module name="FinalParameters"/>
        <module name="TodoComment"/>
        <module name="UpperEll"/>
    </module>
</module>
```

### 2. SpotBugs配置

#### spotbugs-exclude.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>
    <!-- 排除测试类 -->
    <Match>
        <Class name="~.*Test$"/>
    </Match>

    <!-- 排除配置类 -->
    <Match>
        <Class name="~.*Configuration$"/>
    </Match>

    <!-- 排除DTO类的序列化警告 -->
    <Match>
        <Class name="~.*\.dto\..*"/>
        <Bug pattern="SE_NO_SERIALVERSIONID"/>
    </Match>

    <!-- 排除Lombok生成的代码 -->
    <Match>
        <Bug pattern="EI_EXPOSE_REP,EI_EXPOSE_REP2"/>
        <Class name="~.*\.entity\..*"/>
    </Match>
</FindBugsFilter>
```

### 3. SonarQube配置

#### sonar-project.properties
```properties
# 项目信息
sonar.projectKey=ark-pets-ai-enhanced
sonar.projectName=Ark-Pets AI Enhanced
sonar.projectVersion=1.0.0

# 源码配置
sonar.sources=src/main/java
sonar.tests=src/test/java
sonar.java.binaries=target/classes
sonar.java.test.binaries=target/test-classes

# 覆盖率报告
sonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml
sonar.junit.reportPaths=target/surefire-reports

# 质量门禁
sonar.qualitygate.wait=true

# 排除文件
sonar.exclusions=**/*Test.java,**/*Configuration.java,**/dto/**,**/entity/**

# 代码重复度
sonar.cpd.java.minimumtokens=50

# 语言设置
sonar.sourceEncoding=UTF-8
```

## 调试和性能分析工具

### 1. JProfiler配置

#### jprofiler.properties
```properties
# JProfiler配置文件
jprofiler.agent.path=/opt/jprofiler/bin/linux-x64/libjprofilerti.so
jprofiler.port=8849
jprofiler.nowait=true
jprofiler.sessionId=arkpets-session

# 内存分析配置
jprofiler.memory.recordObjects=true
jprofiler.memory.recordGC=true
jprofiler.memory.recordAllocations=true

# CPU分析配置
jprofiler.cpu.sampling=true
jprofiler.cpu.samplingInterval=10
jprofiler.cpu.recordThreads=true

# 数据库分析配置
jprofiler.jdbc.record=true
jprofiler.jdbc.recordStatements=true
```

### 2. 应用监控配置

#### application-monitoring.yml
```yaml
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      sla:
        http.server.requests: 100ms, 500ms, 1s

# 日志配置
logging:
  level:
    com.arkpets: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/arkpets.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
```

## Git配置和工作流

### 1. Git配置

#### .gitignore
```gitignore
# 编译输出
target/
build/
out/
bin/

# IDE文件
.idea/
*.iml
*.ipr
*.iws
.vscode/
.settings/
.project
.classpath

# 操作系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 环境配置
.env
.env.local
.env.development
.env.test
.env.production

# 依赖目录
node_modules/
.pnp
.pnp.js

# 测试覆盖率
coverage/
.nyc_output

# 数据库文件
*.db
*.sqlite

# 密钥文件
*.key
*.pem
*.p12
*.jks

# Docker
.dockerignore
```

#### .gitattributes
```gitattributes
# 自动检测文本文件并执行LF标准化
* text=auto

# 明确声明你想要始终被标准化并转换为本地行结束符的文件
*.java text
*.xml text
*.properties text
*.yml text
*.yaml text
*.json text
*.md text
*.txt text
*.sql text

# 声明那些完全不应该被标准化的文件
*.jar binary
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.tar.gz binary
```

### 2. Git Hooks配置

#### pre-commit hook
```bash
#!/bin/sh
# pre-commit hook

echo "Running pre-commit checks..."

# 检查代码格式
echo "Checking code format..."
mvn checkstyle:check
if [ $? -ne 0 ]; then
    echo "Checkstyle failed. Please fix the issues before committing."
    exit 1
fi

# 运行单元测试
echo "Running unit tests..."
mvn test
if [ $? -ne 0 ]; then
    echo "Unit tests failed. Please fix the issues before committing."
    exit 1
fi

# 检查代码质量
echo "Running SpotBugs analysis..."
mvn spotbugs:check
if [ $? -ne 0 ]; then
    echo "SpotBugs found issues. Please fix them before committing."
    exit 1
fi

echo "All pre-commit checks passed!"
exit 0
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的开发工具配置指南，涵盖IDE、构建工具、代码质量工具、调试工具等各个方面
