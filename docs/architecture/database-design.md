# 数据库设计 (Database Design)

## 模块概述

数据库设计文档定义了Ark-Pets AI Enhanced项目的完整数据模型，基于**Supabase + PostgreSQL**的现代化开源技术栈，采用迁移驱动的数据库管理方式，确保数据库变更的版本控制和环境一致性。

**技术选型**:
- **数据库**: PostgreSQL 15+ (通过Supabase托管)
- **迁移管理**: Supabase CLI + SQL迁移文件
- **实时功能**: Supabase Realtime (基于PostgreSQL逻辑复制)
- **API层**: PostgREST (自动生成REST API)
- **认证**: Supabase Auth (基于JWT)

**设计原则**:
- 基于开源技术栈，避免厂商锁定
- 迁移驱动的数据库版本控制
- 支持实时数据同步和订阅
- 自动生成类型安全的API
- 内置行级安全策略(RLS)

## 数据库架构

### 1. 基于Supabase的现代化架构

#### Supabase技术栈架构
```
┌─────────────────────────────────────┐
│         前端应用层 (Frontend)         │
│  ┌──────────┬──────────┬──────────┐ │
│  │ JavaFX   │ Web UI   │ Mobile   │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│         Supabase服务层               │
│  ┌──────────┬──────────┬──────────┐ │
│  │PostgREST │ Realtime │ Auth     │ │
│  │(REST API)│ (实时同步)│ (认证)   │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│         数据库层 (PostgreSQL)        │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 主数据库  │ 只读副本  │ 扩展功能  │ │
│  │(Primary) │(Replica) │(Extensions)│ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. Supabase项目配置

#### 项目初始化
```bash
# 1. 安装Supabase CLI
npm install -g @supabase/cli

# 2. 初始化项目
supabase init

# 3. 启动本地开发环境
supabase start

# 4. 链接到远程项目
supabase link --project-ref your-project-ref
```

#### 环境配置
```yaml
# supabase/config.toml
[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322
shadow_port = 54320
major_version = 15

[realtime]
enabled = true
ip_version = "ipv4"

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
```

## 核心数据表设计

### 1. 数据库迁移管理

#### 创建第一个迁移
```bash
# 创建用户表迁移
supabase migration new create_users_table
```

#### 用户管理模块迁移

##### 001_create_users_table.sql
```sql
-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表 (扩展Supabase Auth)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    display_name TEXT,
    avatar_url TEXT,
    language TEXT DEFAULT 'zh_CN',
    timezone TEXT DEFAULT 'Asia/Shanghai',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 启用行级安全
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- 创建索引
CREATE INDEX idx_profiles_username ON public.profiles(username);
CREATE INDEX idx_profiles_created_at ON public.profiles(created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();
```

##### 002_create_user_preferences.sql
```sql
-- 创建用户偏好表
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    theme TEXT DEFAULT 'default',
    language TEXT DEFAULT 'zh_CN',
    enable_notifications BOOLEAN DEFAULT true,
    enable_voice BOOLEAN DEFAULT false,
    enable_ai BOOLEAN DEFAULT true,
    default_character TEXT,
    default_personality UUID,
    custom_settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id)
);

-- 启用行级安全
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    FOR ALL USING (auth.uid() = user_id);

-- 创建索引
CREATE INDEX idx_user_preferences_user_id ON public.user_preferences(user_id);

-- 添加更新时间触发器
CREATE TRIGGER user_preferences_updated_at
    BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();
```

##### 003_create_user_sessions.sql
```sql
-- 创建用户会话表 (扩展Supabase Auth会话)
CREATE TABLE public.user_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 启用行级安全
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

-- 创建索引
CREATE INDEX idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX idx_user_sessions_last_accessed ON public.user_sessions(last_accessed_at);
```

### 2. AI服务模块迁移

##### 004_create_conversations.sql
```sql
-- 创建对话表
CREATE TABLE public.conversations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    character_name TEXT NOT NULL,
    personality_id UUID,
    model_provider TEXT NOT NULL,
    model_name TEXT NOT NULL,
    title TEXT,
    status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'ARCHIVED', 'DELETED')),

    -- 模型配置 (JSONB)
    model_config JSONB DEFAULT '{}',

    -- 统计信息
    message_count INTEGER DEFAULT 0,
    total_tokens BIGINT DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_message_at TIMESTAMPTZ
);

-- 启用行级安全
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can manage own conversations" ON public.conversations
    FOR ALL USING (auth.uid() = user_id);

-- 创建索引
CREATE INDEX idx_conversations_user_id ON public.conversations(user_id);
CREATE INDEX idx_conversations_character ON public.conversations(character_name);
CREATE INDEX idx_conversations_status ON public.conversations(status);
CREATE INDEX idx_conversations_updated ON public.conversations(updated_at DESC);
CREATE INDEX idx_conversations_last_message ON public.conversations(last_message_at DESC);

-- 添加更新时间触发器
CREATE TRIGGER conversations_updated_at
    BEFORE UPDATE ON public.conversations
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- 启用实时订阅
ALTER PUBLICATION supabase_realtime ADD TABLE public.conversations;
```

##### 005_create_messages.sql
```sql
-- 创建消息表 (分区表)
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,

    -- AI响应相关
    model_name TEXT,
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost DECIMAL(8,4),

    -- 情感和行为分析
    emotion_type TEXT,
    emotion_intensity FLOAT CHECK (emotion_intensity >= 0 AND emotion_intensity <= 1),
    animation_hint TEXT,
    behavior_hint TEXT,

    -- 元数据
    metadata JSONB DEFAULT '{}',

    created_at TIMESTAMPTZ DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- 创建分区表 (按月分区)
CREATE TABLE public.messages_2025_01 PARTITION OF public.messages
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE public.messages_2025_02 PARTITION OF public.messages
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 启用行级安全
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view messages from own conversations" ON public.messages
    FOR SELECT USING (
        conversation_id IN (
            SELECT id FROM public.conversations WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages to own conversations" ON public.messages
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT id FROM public.conversations WHERE user_id = auth.uid()
        )
    );

-- 创建索引
CREATE INDEX idx_messages_conversation_id ON public.messages(conversation_id);
CREATE INDEX idx_messages_role ON public.messages(role);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX idx_messages_emotion ON public.messages(emotion_type);

-- 启用实时订阅
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
```

#### personalities - 性格表
```sql
CREATE TABLE personalities (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- 'OFFICIAL', 'CUSTOM', 'COMMUNITY'
    creator_id VARCHAR(36),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    
    -- 性格配置
    speech_style VARCHAR(100),
    emotional_tendency VARCHAR(100),
    interaction_style VARCHAR(100),
    formality_level FLOAT DEFAULT 0.5,
    humor_level FLOAT DEFAULT 0.5,
    empathy_level FLOAT DEFAULT 0.5,
    assertiveness_level FLOAT DEFAULT 0.5,
    
    -- 统计信息
    usage_count BIGINT DEFAULT 0,
    rating FLOAT DEFAULT 0,
    rating_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_personalities_type (type),
    INDEX idx_personalities_creator (creator_id),
    INDEX idx_personalities_status (status),
    INDEX idx_personalities_rating (rating)
);
```

#### personality_traits - 性格特征表
```sql
CREATE TABLE personality_traits (
    id VARCHAR(36) PRIMARY KEY,
    personality_id VARCHAR(36) NOT NULL,
    name VARCHAR(50) NOT NULL,
    description VARCHAR(200),
    intensity FLOAT NOT NULL, -- 0.0-1.0
    category VARCHAR(20) NOT NULL,
    
    FOREIGN KEY (personality_id) REFERENCES personalities(id) ON DELETE CASCADE,
    INDEX idx_personality_traits_personality_id (personality_id),
    INDEX idx_personality_traits_category (category)
);
```

### 3. 角色管理模块

#### characters - 角色表
```sql
CREATE TABLE characters (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    faction VARCHAR(50),
    profession VARCHAR(50),
    rarity VARCHAR(10),
    
    -- 模型资源路径
    model_path VARCHAR(255),
    atlas_path VARCHAR(255),
    skeleton_path VARCHAR(255),
    
    -- 角色背景
    background TEXT,
    personality_description TEXT,
    
    -- 配置信息
    display_scale FLOAT DEFAULT 1.0,
    opacity_normal FLOAT DEFAULT 1.0,
    opacity_dim FLOAT DEFAULT 0.5,
    
    -- 支持的性格列表 (JSON数组)
    supported_personalities JSONB,
    
    -- 状态和统计
    status VARCHAR(20) DEFAULT 'ACTIVE',
    usage_count BIGINT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_characters_name (name),
    INDEX idx_characters_faction (faction),
    INDEX idx_characters_profession (profession),
    INDEX idx_characters_status (status)
);
```

#### character_animations - 角色动画表
```sql
CREATE TABLE character_animations (
    id VARCHAR(36) PRIMARY KEY,
    character_name VARCHAR(50) NOT NULL,
    animation_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(150) NOT NULL,
    base_name VARCHAR(100),
    type VARCHAR(20) NOT NULL,
    modifier VARCHAR(20),
    stage VARCHAR(20),
    duration FLOAT NOT NULL,
    is_loop BOOLEAN DEFAULT false,
    mobility INTEGER DEFAULT 0, -- -1左, 0无, 1右
    weight FLOAT DEFAULT 1.0,
    
    FOREIGN KEY (character_name) REFERENCES characters(name) ON DELETE CASCADE,
    INDEX idx_character_animations_character (character_name),
    INDEX idx_character_animations_type (type),
    UNIQUE KEY uk_character_animation (character_name, animation_name)
);
```

### 4. 系统配置模块

#### system_configs - 系统配置表
```sql
CREATE TABLE system_configs (
    id VARCHAR(36) PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'STRING',
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_system_configs_key (config_key),
    INDEX idx_system_configs_public (is_public)
);
```

#### audit_logs - 审计日志表
```sql
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(36),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(36),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_audit_logs_user_id (user_id),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_resource (resource_type, resource_id),
    INDEX idx_audit_logs_created_at (created_at)
);

-- 分区表设计 (按月分区)
CREATE TABLE audit_logs_y2025m01 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

## 数据库关系图

### 核心实体关系

```mermaid
erDiagram
    users ||--o{ conversations : "创建"
    users ||--o{ user_preferences : "拥有"
    users ||--o{ user_sessions : "登录"
    users ||--o{ personalities : "创建自定义"
    
    conversations ||--o{ messages : "包含"
    conversations }o--|| personalities : "使用"
    conversations }o--|| characters : "选择"
    
    personalities ||--o{ personality_traits : "具有"
    
    characters ||--o{ character_animations : "拥有"
    
    users ||--o{ audit_logs : "操作记录"
```

## 性能优化策略

### 1. 索引优化

#### 复合索引设计
```sql
-- 对话查询优化
CREATE INDEX idx_conversations_user_status_updated 
ON conversations(user_id, status, updated_at DESC);

-- 消息查询优化
CREATE INDEX idx_messages_conversation_created 
ON messages(conversation_id, created_at DESC);

-- 性格搜索优化
CREATE INDEX idx_personalities_type_status_rating 
ON personalities(type, status, rating DESC);
```

#### 部分索引
```sql
-- 只为活跃对话创建索引
CREATE INDEX idx_conversations_active_updated 
ON conversations(updated_at DESC) 
WHERE status = 'ACTIVE';

-- 只为用户消息创建索引
CREATE INDEX idx_messages_user_content 
ON messages USING gin(to_tsvector('english', content)) 
WHERE role = 'user';
```

### 2. 分区策略

#### 时间分区
```sql
-- 消息表按月分区
CREATE TABLE messages (
    -- 列定义...
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);

-- 审计日志按月分区
CREATE TABLE audit_logs (
    -- 列定义...
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);
```

#### 哈希分区
```sql
-- 大表按用户ID哈希分区
CREATE TABLE user_activities (
    -- 列定义...
    user_id VARCHAR(36) NOT NULL
) PARTITION BY HASH (user_id);
```

### 3. 查询优化

#### 常用查询模式
```sql
-- 获取用户最近对话 (优化版)
SELECT c.*, 
       m.content as last_message,
       m.created_at as last_message_at
FROM conversations c
LEFT JOIN LATERAL (
    SELECT content, created_at 
    FROM messages 
    WHERE conversation_id = c.id 
    ORDER BY created_at DESC 
    LIMIT 1
) m ON true
WHERE c.user_id = ? 
  AND c.status = 'ACTIVE'
ORDER BY c.updated_at DESC
LIMIT 20;

-- 性格使用统计 (优化版)
SELECT p.id, p.name, 
       COUNT(c.id) as usage_count,
       AVG(m.total_tokens) as avg_tokens
FROM personalities p
LEFT JOIN conversations c ON c.personality_id = p.id
LEFT JOIN (
    SELECT conversation_id, SUM(total_tokens) as total_tokens
    FROM messages 
    WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY conversation_id
) m ON m.conversation_id = c.id
WHERE p.status = 'ACTIVE'
GROUP BY p.id, p.name
ORDER BY usage_count DESC;
```

## Supabase数据管理

### 1. 数据库备份策略

#### 自动备份 (Supabase托管)
```bash
# Supabase自动提供：
# - 每日自动备份
# - 时间点恢复 (PITR)
# - 跨区域复制
# - 99.9%可用性保证
```

#### 手动备份
```bash
# 导出数据库结构
supabase db dump --data-only > backup_data.sql

# 导出完整数据库
supabase db dump > backup_full.sql

# 导出特定表
supabase db dump --table=public.messages > backup_messages.sql
```

### 2. 数据恢复

#### 从备份恢复
```bash
# 恢复到本地开发环境
supabase db reset
psql -h localhost -p 54322 -d postgres < backup_full.sql

# 恢复特定表数据
psql -h localhost -p 54322 -d postgres < backup_messages.sql
```

#### 时间点恢复 (Supabase Pro)
```bash
# 通过Supabase Dashboard进行时间点恢复
# 支持恢复到过去7天内的任意时间点
```

## 监控和维护

### 1. Supabase性能监控

#### Dashboard监控指标
- **数据库连接数**: 实时连接监控
- **查询性能**: 慢查询识别和优化建议
- **存储使用量**: 表大小和增长趋势
- **API请求**: PostgREST API调用统计
- **实时连接**: Realtime订阅数量

#### 自定义监控查询
```sql
-- 查看表大小
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看索引使用情况
SELECT
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### 2. 数据库维护

#### 自动维护 (Supabase托管)
```bash
# Supabase自动处理：
# - VACUUM和ANALYZE
# - 索引维护
# - 统计信息更新
# - 连接池管理
```

#### 手动优化
```sql
-- 分析表统计信息
ANALYZE public.messages;

-- 查看查询计划
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM public.messages
WHERE conversation_id = 'uuid-here'
ORDER BY created_at DESC;
```

## 部署和CI/CD

### 1. 环境管理
```bash
# 开发环境
supabase start

# 预览环境 (分支)
supabase branches create preview-feature
supabase link --project-ref preview-ref

# 生产环境
supabase link --project-ref prod-ref
supabase db push
```

### 2. GitHub Actions集成
```yaml
# .github/workflows/database.yml
name: Database CI/CD
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/setup-cli@v1
      - run: supabase start
      - run: supabase db test

  deploy:
    if: github.ref == 'refs/heads/main'
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/setup-cli@v1
      - run: supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
      - run: supabase db push
```

---

**技术架构**: Supabase + PostgreSQL + 迁移驱动开发
**文档版本**: v2.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 基于Supabase的现代化数据库设计，采用开源技术栈和最佳实践，支持迁移管理、实时同步、行级安全和自动API生成
