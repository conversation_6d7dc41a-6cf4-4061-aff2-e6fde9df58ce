# 安全架构设计 (Security Architecture)

## 模块概述

安全架构设计文档定义了Ark-Pets AI Enhanced项目的完整安全体系，包括身份认证、授权控制、数据加密、网络安全、API安全等多层次的安全防护机制。

**安全原则**:
- 零信任安全模型
- 深度防御策略
- 最小权限原则
- 数据隐私保护

## 安全架构总览

### 1. 多层安全架构

#### 安全防护层次
```
┌─────────────────────────────────────┐
│           用户接入层                 │
│    ┌─────────────┬─────────────┐    │
│    │  Web防火墙  │   DDoS防护  │    │
│    └─────────────┴─────────────┘    │
├─────────────────────────────────────┤
│           网络安全层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │   VPC    │  安全组  │   NACL   │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           应用安全层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ API网关  │  认证授权 │  限流熔断 │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           服务安全层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 服务认证  │  加密传输 │  审计日志 │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据安全层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据加密  │  访问控制 │  备份恢复 │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 安全组件架构

#### 核心安全组件
```mermaid
graph TB
    subgraph "安全网关层"
        WAF[Web应用防火墙]
        LB[负载均衡器]
        SSL[SSL终端]
    end
    
    subgraph "认证授权层"
        AuthGW[认证网关]
        JWT[JWT服务]
        OAuth[OAuth2服务]
        RBAC[权限控制]
    end
    
    subgraph "API安全层"
        RateLimit[限流控制]
        Validator[参数验证]
        Encrypt[加密解密]
        Audit[审计日志]
    end
    
    subgraph "数据安全层"
        DataEncrypt[数据加密]
        KeyMgmt[密钥管理]
        Backup[安全备份]
        Monitor[安全监控]
    end
    
    WAF --> AuthGW
    AuthGW --> RateLimit
    RateLimit --> DataEncrypt
```

## 身份认证与授权

### 1. 多因素认证系统

#### 认证流程设计
```java
@Service
public class MultiFactorAuthenticationService {
    
    private final UserService userService;
    private final TOTPService totpService;
    private final SMSService smsService;
    private final EmailService emailService;
    private final SecurityEventService securityEventService;
    
    /**
     * 执行多因素认证
     * @param authRequest 认证请求
     * @return 认证结果
     */
    public AuthenticationResult performMFA(MFARequest authRequest) {
        try {
            // 1. 第一因素：用户名密码验证
            User user = validateCredentials(authRequest.getUsername(), authRequest.getPassword());
            if (user == null) {
                securityEventService.recordFailedLogin(authRequest.getUsername(), "INVALID_CREDENTIALS");
                return AuthenticationResult.failed("用户名或密码错误");
            }
            
            // 2. 检查账户状态
            if (!user.isActive() || user.isLocked()) {
                securityEventService.recordFailedLogin(user.getId(), "ACCOUNT_LOCKED");
                return AuthenticationResult.failed("账户已被锁定");
            }
            
            // 3. 第二因素验证
            if (user.isMFAEnabled()) {
                MFAResult mfaResult = performSecondFactor(user, authRequest);
                if (!mfaResult.isSuccess()) {
                    return AuthenticationResult.failed(mfaResult.getErrorMessage());
                }
            }
            
            // 4. 设备信任验证
            if (!isDeviceTrusted(user.getId(), authRequest.getDeviceFingerprint())) {
                return requestDeviceVerification(user, authRequest);
            }
            
            // 5. 生成安全令牌
            SecurityToken token = generateSecurityToken(user, authRequest);
            
            // 6. 记录成功登录
            securityEventService.recordSuccessfulLogin(user.getId(), authRequest.getClientInfo());
            
            return AuthenticationResult.success(token);
            
        } catch (Exception e) {
            logger.error("MFA认证失败", e);
            return AuthenticationResult.failed("认证服务暂时不可用");
        }
    }
    
    /**
     * 执行第二因素验证
     * @param user 用户对象
     * @param authRequest 认证请求
     * @return MFA验证结果
     */
    private MFAResult performSecondFactor(User user, MFARequest authRequest) {
        MFAMethod method = user.getPreferredMFAMethod();
        
        switch (method) {
            case TOTP:
                return validateTOTP(user, authRequest.getTotpCode());
            case SMS:
                return validateSMS(user, authRequest.getSmsCode());
            case EMAIL:
                return validateEmail(user, authRequest.getEmailCode());
            case HARDWARE_TOKEN:
                return validateHardwareToken(user, authRequest.getHardwareToken());
            default:
                return MFAResult.failed("不支持的MFA方法");
        }
    }
    
    /**
     * 验证TOTP代码
     * @param user 用户对象
     * @param totpCode TOTP代码
     * @return 验证结果
     */
    private MFAResult validateTOTP(User user, String totpCode) {
        if (totpCode == null || totpCode.length() != 6) {
            return MFAResult.failed("TOTP代码格式错误");
        }
        
        String secretKey = user.getTotpSecretKey();
        if (totpService.validateCode(secretKey, totpCode)) {
            return MFAResult.success();
        } else {
            securityEventService.recordFailedMFA(user.getId(), "INVALID_TOTP");
            return MFAResult.failed("TOTP代码错误");
        }
    }
    
    /**
     * 生成安全令牌
     * @param user 用户对象
     * @param authRequest 认证请求
     * @return 安全令牌
     */
    private SecurityToken generateSecurityToken(User user, MFARequest authRequest) {
        // 生成访问令牌
        String accessToken = jwtService.generateAccessToken(user, authRequest.getScopes());
        
        // 生成刷新令牌
        String refreshToken = jwtService.generateRefreshToken(user);
        
        // 设置令牌属性
        SecurityToken token = new SecurityToken();
        token.setAccessToken(accessToken);
        token.setRefreshToken(refreshToken);
        token.setTokenType("Bearer");
        token.setExpiresIn(jwtService.getAccessTokenExpiration());
        token.setScope(String.join(" ", authRequest.getScopes()));
        
        // 记录令牌信息
        tokenService.recordTokenIssuance(user.getId(), token, authRequest.getClientInfo());
        
        return token;
    }
}
```

### 2. 基于角色的访问控制 (RBAC)

#### 权限模型设计
```java
@Entity
@Table(name = "roles")
public class Role {
    @Id
    private String id;
    
    @Column(unique = true)
    private String name;
    
    private String description;
    
    @Enumerated(EnumType.STRING)
    private RoleType type; // SYSTEM, CUSTOM
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
    
    // 构造函数、getter、setter等
}

@Entity
@Table(name = "permissions")
public class Permission {
    @Id
    private String id;
    
    @Column(unique = true)
    private String name;
    
    private String description;
    
    @Enumerated(EnumType.STRING)
    private ResourceType resourceType;
    
    @Enumerated(EnumType.STRING)
    private ActionType actionType;
    
    private String resourcePattern; // 资源匹配模式
    
    // 构造函数、getter、setter等
}

@Service
public class AuthorizationService {
    
    /**
     * 检查用户权限
     * @param userId 用户ID
     * @param resource 资源
     * @param action 操作
     * @return 是否有权限
     */
    public boolean hasPermission(String userId, String resource, String action) {
        User user = userService.getUserById(userId);
        if (user == null) {
            return false;
        }
        
        // 获取用户所有角色的权限
        Set<Permission> userPermissions = getUserPermissions(user);
        
        // 检查权限匹配
        return userPermissions.stream()
            .anyMatch(permission -> matchesPermission(permission, resource, action));
    }
    
    /**
     * 权限匹配检查
     * @param permission 权限对象
     * @param resource 请求资源
     * @param action 请求操作
     * @return 是否匹配
     */
    private boolean matchesPermission(Permission permission, String resource, String action) {
        // 检查操作匹配
        if (!permission.getActionType().name().equalsIgnoreCase(action)) {
            return false;
        }
        
        // 检查资源模式匹配
        String pattern = permission.getResourcePattern();
        return resource.matches(pattern);
    }
}
```

## 数据安全

### 1. 数据加密策略

#### 多层加密实现
```java
@Service
public class DataEncryptionService {
    
    private final AESEncryption aesEncryption;
    private final RSAEncryption rsaEncryption;
    private final KeyManagementService keyService;
    
    /**
     * 加密敏感数据
     * @param data 原始数据
     * @param dataType 数据类型
     * @return 加密后的数据
     */
    public EncryptedData encryptSensitiveData(String data, DataType dataType) {
        try {
            // 1. 根据数据类型选择加密策略
            EncryptionStrategy strategy = getEncryptionStrategy(dataType);
            
            // 2. 生成数据加密密钥
            String dataKey = keyService.generateDataKey();
            
            // 3. 使用数据密钥加密数据
            String encryptedData = aesEncryption.encrypt(data, dataKey);
            
            // 4. 使用主密钥加密数据密钥
            String encryptedKey = rsaEncryption.encrypt(dataKey, keyService.getMasterKey());
            
            // 5. 构建加密结果
            EncryptedData result = new EncryptedData();
            result.setEncryptedData(encryptedData);
            result.setEncryptedKey(encryptedKey);
            result.setAlgorithm(strategy.getAlgorithm());
            result.setKeyVersion(keyService.getCurrentKeyVersion());
            result.setCreatedAt(LocalDateTime.now());
            
            return result;
            
        } catch (Exception e) {
            logger.error("数据加密失败", e);
            throw new EncryptionException("数据加密失败", e);
        }
    }
    
    /**
     * 解密敏感数据
     * @param encryptedData 加密数据
     * @return 解密后的数据
     */
    public String decryptSensitiveData(EncryptedData encryptedData) {
        try {
            // 1. 获取对应版本的主密钥
            String masterKey = keyService.getMasterKey(encryptedData.getKeyVersion());
            
            // 2. 解密数据密钥
            String dataKey = rsaEncryption.decrypt(encryptedData.getEncryptedKey(), masterKey);
            
            // 3. 解密数据
            String decryptedData = aesEncryption.decrypt(encryptedData.getEncryptedData(), dataKey);
            
            return decryptedData;
            
        } catch (Exception e) {
            logger.error("数据解密失败", e);
            throw new DecryptionException("数据解密失败", e);
        }
    }
}
```

### 2. 密钥管理系统

#### 密钥轮换和管理
```java
@Service
public class KeyManagementService {
    
    private final KeyRepository keyRepository;
    private final HSMService hsmService; // 硬件安全模块
    private final KeyRotationScheduler rotationScheduler;
    
    /**
     * 生成新的主密钥
     * @return 密钥版本
     */
    @Scheduled(cron = "0 0 2 1 * ?") // 每月1号凌晨2点执行
    public String rotateMasterKey() {
        try {
            // 1. 生成新的主密钥
            String newMasterKey = hsmService.generateMasterKey();
            
            // 2. 创建密钥版本记录
            KeyVersion keyVersion = new KeyVersion();
            keyVersion.setId(UUID.randomUUID().toString());
            keyVersion.setVersion(generateKeyVersion());
            keyVersion.setKeyData(newMasterKey);
            keyVersion.setStatus(KeyStatus.ACTIVE);
            keyVersion.setCreatedAt(LocalDateTime.now());
            keyVersion.setExpiresAt(LocalDateTime.now().plusMonths(13)); // 13个月后过期
            
            // 3. 保存新密钥
            keyRepository.save(keyVersion);
            
            // 4. 标记旧密钥为已弃用
            deprecateOldKeys();
            
            // 5. 通知相关服务更新密钥
            notifyKeyRotation(keyVersion.getVersion());
            
            logger.info("主密钥轮换完成，新版本: {}", keyVersion.getVersion());
            return keyVersion.getVersion();
            
        } catch (Exception e) {
            logger.error("主密钥轮换失败", e);
            throw new KeyManagementException("主密钥轮换失败", e);
        }
    }
    
    /**
     * 获取当前活跃的主密钥
     * @return 主密钥
     */
    public String getCurrentMasterKey() {
        KeyVersion activeKey = keyRepository.findByStatus(KeyStatus.ACTIVE);
        if (activeKey == null) {
            throw new KeyManagementException("未找到活跃的主密钥");
        }
        return activeKey.getKeyData();
    }
    
    /**
     * 安全删除过期密钥
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupExpiredKeys() {
        List<KeyVersion> expiredKeys = keyRepository.findExpiredKeys(LocalDateTime.now());
        
        for (KeyVersion key : expiredKeys) {
            // 1. 验证密钥不再被使用
            if (isKeyInUse(key.getVersion())) {
                logger.warn("密钥仍在使用中，跳过删除: {}", key.getVersion());
                continue;
            }
            
            // 2. 安全擦除密钥数据
            secureEraseKey(key);
            
            // 3. 删除密钥记录
            keyRepository.delete(key);
            
            logger.info("已安全删除过期密钥: {}", key.getVersion());
        }
    }
}
```

## 网络安全

### 1. API安全防护

#### API网关安全配置
```yaml
# API Gateway Security Configuration
security:
  cors:
    allowed-origins:
      - "https://arkpets.com"
      - "https://*.arkpets.com"
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers:
      - Authorization
      - Content-Type
      - X-Requested-With
    max-age: 3600
    
  rate-limiting:
    global:
      requests-per-minute: 1000
      burst-capacity: 100
    per-user:
      requests-per-minute: 100
      burst-capacity: 20
    per-ip:
      requests-per-minute: 200
      burst-capacity: 50
      
  request-validation:
    max-request-size: 10MB
    max-header-size: 8KB
    timeout: 30s
    
  ssl:
    enabled: true
    protocols:
      - TLSv1.2
      - TLSv1.3
    ciphers:
      - TLS_AES_256_GCM_SHA384
      - TLS_CHACHA20_POLY1305_SHA256
      - TLS_AES_128_GCM_SHA256
```

#### 输入验证和清理
```java
@Component
public class InputValidationService {
    
    private final XSSProtection xssProtection;
    private final SQLInjectionProtection sqlProtection;
    private final CommandInjectionProtection cmdProtection;
    
    /**
     * 验证和清理用户输入
     * @param input 用户输入
     * @param inputType 输入类型
     * @return 清理后的输入
     */
    public String validateAndSanitizeInput(String input, InputType inputType) {
        if (input == null) {
            return null;
        }
        
        // 1. 基础验证
        validateBasicConstraints(input, inputType);
        
        // 2. XSS防护
        input = xssProtection.sanitize(input);
        
        // 3. SQL注入防护
        input = sqlProtection.sanitize(input);
        
        // 4. 命令注入防护
        input = cmdProtection.sanitize(input);
        
        // 5. 特定类型验证
        validateSpecificType(input, inputType);
        
        return input;
    }
    
    /**
     * 验证基础约束
     * @param input 输入内容
     * @param inputType 输入类型
     */
    private void validateBasicConstraints(String input, InputType inputType) {
        InputConstraints constraints = getConstraints(inputType);
        
        // 长度检查
        if (input.length() > constraints.getMaxLength()) {
            throw new ValidationException("输入长度超过限制");
        }
        
        // 字符集检查
        if (!input.matches(constraints.getAllowedCharPattern())) {
            throw new ValidationException("包含不允许的字符");
        }
        
        // 恶意模式检查
        for (String maliciousPattern : constraints.getMaliciousPatterns()) {
            if (input.toLowerCase().contains(maliciousPattern)) {
                throw new ValidationException("检测到潜在的恶意输入");
            }
        }
    }
}
```

### 2. 传输安全

#### TLS配置和证书管理
```java
@Configuration
@EnableWebSecurity
public class TLSSecurityConfig {
    
    @Bean
    public TomcatServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory() {
            @Override
            protected void postProcessContext(Context context) {
                SecurityConstraint securityConstraint = new SecurityConstraint();
                securityConstraint.setUserConstraint("CONFIDENTIAL");
                SecurityCollection collection = new SecurityCollection();
                collection.addPattern("/*");
                securityConstraint.addCollection(collection);
                context.addConstraint(securityConstraint);
            }
        };
        
        // HTTPS连接器配置
        tomcat.addAdditionalTomcatConnectors(createHttpsConnector());
        return tomcat;
    }
    
    private Connector createHttpsConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        connector.setScheme("https");
        connector.setSecure(true);
        connector.setPort(8443);
        
        // SSL配置
        connector.setProperty("SSLEnabled", "true");
        connector.setProperty("sslProtocol", "TLS");
        connector.setProperty("sslEnabledProtocols", "TLSv1.2,TLSv1.3");
        connector.setProperty("ciphers", "TLS_AES_256_GCM_SHA384,TLS_CHACHA20_POLY1305_SHA256");
        connector.setProperty("keystoreFile", "/etc/ssl/arkpets.p12");
        connector.setProperty("keystorePass", "${ssl.keystore.password}");
        connector.setProperty("keystoreType", "PKCS12");
        
        return connector;
    }
}
```

## 安全监控和审计

### 1. 安全事件监控

#### 实时安全监控
```java
@Service
public class SecurityMonitoringService {
    
    private final SecurityEventRepository eventRepository;
    private final AlertService alertService;
    private final ThreatDetectionEngine threatEngine;
    
    /**
     * 记录安全事件
     * @param event 安全事件
     */
    public void recordSecurityEvent(SecurityEvent event) {
        try {
            // 1. 保存事件记录
            eventRepository.save(event);
            
            // 2. 实时威胁检测
            ThreatLevel threatLevel = threatEngine.analyzeThreat(event);
            
            // 3. 根据威胁级别处理
            handleThreatLevel(event, threatLevel);
            
            // 4. 更新安全指标
            updateSecurityMetrics(event);
            
        } catch (Exception e) {
            logger.error("记录安全事件失败", e);
        }
    }
    
    /**
     * 处理威胁级别
     * @param event 安全事件
     * @param threatLevel 威胁级别
     */
    private void handleThreatLevel(SecurityEvent event, ThreatLevel threatLevel) {
        switch (threatLevel) {
            case CRITICAL:
                // 立即告警并阻断
                alertService.sendCriticalAlert(event);
                blockSuspiciousActivity(event);
                break;
            case HIGH:
                // 发送告警
                alertService.sendHighPriorityAlert(event);
                increaseSecurity(event);
                break;
            case MEDIUM:
                // 记录并监控
                alertService.sendMediumPriorityAlert(event);
                break;
            case LOW:
                // 仅记录
                logger.info("低威胁安全事件: {}", event);
                break;
        }
    }
    
    /**
     * 检测异常行为模式
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void detectAnomalousPatterns() {
        // 1. 分析最近的安全事件
        List<SecurityEvent> recentEvents = eventRepository.findRecentEvents(
            LocalDateTime.now().minusMinutes(10));
        
        // 2. 检测异常模式
        List<AnomalousPattern> patterns = threatEngine.detectPatterns(recentEvents);
        
        // 3. 处理检测到的异常
        for (AnomalousPattern pattern : patterns) {
            handleAnomalousPattern(pattern);
        }
    }
}
```

### 2. 审计日志系统

#### 审计日志记录
```java
@Aspect
@Component
public class AuditLoggingAspect {
    
    private final AuditLogService auditLogService;
    
    @Around("@annotation(Auditable)")
    public Object auditMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        Auditable auditable = getAuditableAnnotation(joinPoint);
        
        // 1. 记录操作开始
        AuditLog auditLog = createAuditLog(joinPoint, auditable);
        auditLog.setStatus(AuditStatus.STARTED);
        auditLogService.save(auditLog);
        
        try {
            // 2. 执行原方法
            Object result = joinPoint.proceed();
            
            // 3. 记录操作成功
            auditLog.setStatus(AuditStatus.SUCCESS);
            auditLog.setEndTime(LocalDateTime.now());
            auditLogService.update(auditLog);
            
            return result;
            
        } catch (Exception e) {
            // 4. 记录操作失败
            auditLog.setStatus(AuditStatus.FAILED);
            auditLog.setErrorMessage(e.getMessage());
            auditLog.setEndTime(LocalDateTime.now());
            auditLogService.update(auditLog);
            
            throw e;
        }
    }
    
    private AuditLog createAuditLog(ProceedingJoinPoint joinPoint, Auditable auditable) {
        AuditLog log = new AuditLog();
        log.setId(UUID.randomUUID().toString());
        log.setUserId(getCurrentUserId());
        log.setAction(auditable.action());
        log.setResource(auditable.resource());
        log.setMethod(joinPoint.getSignature().getName());
        log.setParameters(extractParameters(joinPoint));
        log.setStartTime(LocalDateTime.now());
        log.setIpAddress(getCurrentUserIP());
        log.setUserAgent(getCurrentUserAgent());
        
        return log;
    }
}
```

## 安全配置

### 1. Spring Security配置

#### 安全配置类
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/auth/login", "/api/auth/register").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/characters/**").hasRole("USER")
                .requestMatchers(HttpMethod.POST, "/api/chat/**").hasRole("USER")
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
            )
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(customAuthenticationEntryPoint())
                .accessDeniedHandler(customAccessDeniedHandler())
            );
            
        return http.build();
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
    
    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
    }
}
```

---

**模块负责人**: 安全架构开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
