# API设计规范 (API Design)

## 模块概述

API设计规范模块定义了Ark-Pets AI Enhanced项目中所有API接口的设计标准、规范和最佳实践。包括RESTful API设计原则、统一响应格式、错误处理机制、版本控制策略、安全认证等内容，确保API的一致性、可维护性和可扩展性。

**核心职责**:
- RESTful API设计规范和标准
- 统一的请求响应格式定义
- 错误处理和状态码规范
- API版本控制和兼容性策略
- 安全认证和授权机制

## 核心功能架构

### 1. API设计架构

#### 分层API架构模型
```
┌─────────────────────────────────────┐
│           API网关层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 路由管理  │ 限流控制  │ 认证授权  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           业务API层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 用户API  │ AI服务API│ 配置API  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据访问层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 数据库   │ 缓存     │ 外部服务  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. API请求处理流程

#### API请求处理流程图
```mermaid
graph TB
    subgraph "API请求处理流程"
        Request[客户端请求]
        Gateway[API网关]
        Auth[身份认证]
        RateLimit[限流检查]
        Validation[参数验证]
        Business[业务处理]
        Response[响应生成]
        Logging[日志记录]
        Monitor[监控统计]
    end
    
    subgraph "错误处理"
        ErrorHandler[错误处理器]
        ErrorFormat[错误格式化]
        ErrorLog[错误日志]
    end
    
    subgraph "响应处理"
        DataFormat[数据格式化]
        Compression[响应压缩]
        Cache[缓存控制]
    end
    
    Request --> Gateway
    Gateway --> Auth
    Auth --> RateLimit
    RateLimit --> Validation
    Validation --> Business
    Business --> DataFormat
    DataFormat --> Compression
    Compression --> Cache
    Cache --> Response
    Response --> Logging
    Logging --> Monitor
    
    Auth -->|认证失败| ErrorHandler
    RateLimit -->|超出限制| ErrorHandler
    Validation -->|验证失败| ErrorHandler
    Business -->|业务异常| ErrorHandler
    
    ErrorHandler --> ErrorFormat
    ErrorFormat --> ErrorLog
    ErrorLog --> Response
```

## API设计规范

### 1. RESTful API设计原则

#### 资源命名规范
```yaml
# 资源命名规则
resources:
  # 使用复数名词
  users: /api/v1/users
  conversations: /api/v1/conversations
  
  # 层级资源关系
  user_profiles: /api/v1/users/{userId}/profile
  user_preferences: /api/v1/users/{userId}/preferences
  conversation_messages: /api/v1/conversations/{conversationId}/messages
  
  # 特殊操作使用动词
  user_login: /api/v1/auth/login
  user_logout: /api/v1/auth/logout
  password_reset: /api/v1/auth/password/reset
```

#### HTTP方法使用规范
```yaml
# HTTP方法映射
methods:
  GET:
    purpose: "获取资源"
    examples:
      - "GET /api/v1/users - 获取用户列表"
      - "GET /api/v1/users/{id} - 获取特定用户"
      - "GET /api/v1/users/{id}/conversations - 获取用户对话列表"
  
  POST:
    purpose: "创建资源"
    examples:
      - "POST /api/v1/users - 创建新用户"
      - "POST /api/v1/conversations - 创建新对话"
      - "POST /api/v1/auth/login - 用户登录"
  
  PUT:
    purpose: "完整更新资源"
    examples:
      - "PUT /api/v1/users/{id} - 完整更新用户信息"
      - "PUT /api/v1/users/{id}/profile - 更新用户档案"
  
  PATCH:
    purpose: "部分更新资源"
    examples:
      - "PATCH /api/v1/users/{id} - 部分更新用户信息"
      - "PATCH /api/v1/conversations/{id}/status - 更新对话状态"
  
  DELETE:
    purpose: "删除资源"
    examples:
      - "DELETE /api/v1/users/{id} - 删除用户"
      - "DELETE /api/v1/conversations/{id} - 删除对话"
```

### 2. 统一响应格式

#### 成功响应格式
```java
/**
 * 统一API响应格式
 * @param <T> 数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private long timestamp;
    
    /**
     * 请求ID（用于追踪）
     */
    private String requestId;
    
    /**
     * 分页信息（当data为列表时）
     */
    private PageInfo pagination;
    
    /**
     * 创建成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("Success")
            .data(data)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
    
    /**
     * 创建成功响应（带分页）
     */
    public static <T> ApiResponse<T> success(T data, PageInfo pagination) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("Success")
            .data(data)
            .pagination(pagination)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
    
    /**
     * 创建错误响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return ApiResponse.<T>builder()
            .code(code)
            .message(message)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
}
```

#### 分页信息格式
```java
/**
 * 分页信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageInfo {
    
    /**
     * 当前页码（从1开始）
     */
    private int page;
    
    /**
     * 每页大小
     */
    private int size;
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 是否为第一页
     */
    private boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private boolean isLast;
    
    /**
     * 从Spring Data Page对象创建
     */
    public static PageInfo fromPage(Page<?> page) {
        return PageInfo.builder()
            .page(page.getNumber() + 1) // Spring Data从0开始，API从1开始
            .size(page.getSize())
            .total(page.getTotalElements())
            .totalPages(page.getTotalPages())
            .hasNext(page.hasNext())
            .hasPrevious(page.hasPrevious())
            .isFirst(page.isFirst())
            .isLast(page.isLast())
            .build();
    }
}
```

### 5. 安全认证机制

#### JWT认证实现
```java
/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenService jwtTokenService;
    private final UserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        try {
            String token = extractTokenFromRequest(request);

            if (StringUtils.isNotBlank(token)) {
                TokenClaims claims = jwtTokenService.validateAccessToken(token);

                if (claims != null) {
                    UserDetails userDetails = userDetailsService.loadUserByUsername(claims.getUserId());

                    UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());

                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        } catch (Exception e) {
            logger.warn("JWT认证失败: {}", e.getMessage());
        }

        filterChain.doFilter(request, response);
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

#### API权限控制
```java
/**
 * API权限控制注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    String value();
    String resource() default "";
}

/**
 * 权限检查切面
 */
@Aspect
@Component
public class PermissionCheckAspect {

    private final AuthorizationService authorizationService;

    @Around("@annotation(requirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint,
                                RequirePermission requirePermission) throws Throwable {

        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("用户未认证");
        }

        String userId = authentication.getName();
        String permission = requirePermission.value();
        String resource = requirePermission.resource();

        // 检查权限
        if (!authorizationService.hasPermission(userId, permission, resource)) {
            throw new ForbiddenException("权限不足");
        }

        return joinPoint.proceed();
    }
}
```

### 6. API限流控制

#### 限流实现
```java
/**
 * API限流配置
 */
@Configuration
public class RateLimitConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setDefaultSerializer(new GenericJackson2JsonRedisSerializer());
        return template;
    }

    @Bean
    public RateLimiter rateLimiter(RedisTemplate<String, Object> redisTemplate) {
        return new RedisRateLimiter(redisTemplate);
    }
}

/**
 * 限流注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    int requests() default 100;
    int window() default 60; // 时间窗口（秒）
    String key() default ""; // 限流键
}

/**
 * 限流切面
 */
@Aspect
@Component
public class RateLimitAspect {

    private final RateLimiter rateLimiter;

    @Around("@annotation(rateLimit)")
    public Object checkRateLimit(ProceedingJoinPoint joinPoint,
                                RateLimit rateLimit) throws Throwable {

        String key = buildRateLimitKey(joinPoint, rateLimit);

        if (!rateLimiter.tryAcquire(key, rateLimit.requests(), rateLimit.window())) {
            throw new TooManyRequestsException("请求过于频繁，请稍后再试");
        }

        return joinPoint.proceed();
    }

    private String buildRateLimitKey(ProceedingJoinPoint joinPoint, RateLimit rateLimit) {
        if (StringUtils.isNotBlank(rateLimit.key())) {
            return rateLimit.key();
        }

        // 默认使用方法名和用户ID
        String methodName = joinPoint.getSignature().getName();
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        return String.format("rate_limit:%s:%s", methodName, userId);
    }
}
```

## API文档规范

### 1. OpenAPI/Swagger配置

#### Swagger配置
```java
/**
 * Swagger API文档配置
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
            .apiInfo(apiInfo())
            .select()
            .apis(RequestHandlerSelectors.basePackage("com.arkpets.controller"))
            .paths(PathSelectors.ant("/api/**"))
            .build()
            .securitySchemes(Arrays.asList(apiKeyScheme()))
            .securityContexts(Arrays.asList(securityContext()));
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title("Ark-Pets AI Enhanced API")
            .description("Ark-Pets AI Enhanced项目API文档")
            .version("v1.0")
            .contact(new Contact("Ark-Pets Team", "https://arkpets.com", "<EMAIL>"))
            .license("MIT License")
            .licenseUrl("https://opensource.org/licenses/MIT")
            .build();
    }

    private ApiKey apiKeyScheme() {
        return new ApiKey("JWT", "Authorization", "header");
    }

    private SecurityContext securityContext() {
        return SecurityContext.builder()
            .securityReferences(defaultAuth())
            .forPaths(PathSelectors.ant("/api/**"))
            .build();
    }

    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        return Arrays.asList(new SecurityReference("JWT", authorizationScopes));
    }
}
```

### 2. API文档注解规范

#### 控制器文档注解
```java
/**
 * 用户管理API
 */
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理", description = "用户相关操作接口")
@Validated
public class UserController {

    private final UserService userService;

    @GetMapping
    @ApiOperation(value = "获取用户列表", notes = "分页获取用户列表，支持搜索和过滤")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "20", dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "keyword", value = "搜索关键词", dataType = "string", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "用户状态", dataType = "string", paramType = "query")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功"),
        @ApiResponse(code = 400, message = "参数错误"),
        @ApiResponse(code = 401, message = "未认证"),
        @ApiResponse(code = 403, message = "无权限")
    })
    @RequirePermission("USER_READ")
    @RateLimit(requests = 100, window = 60)
    public ApiResponse<List<UserResponse>> getUsers(
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "20") @Range(min = 1, max = 100) int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) UserStatus status) {

        Pageable pageable = PageRequest.of(page - 1, size);
        Page<UserResponse> users = userService.getUsers(keyword, status, pageable);

        return ApiResponse.success(users.getContent(), PageInfo.fromPage(users));
    }

    @PostMapping
    @ApiOperation(value = "创建用户", notes = "创建新用户账户")
    @RequirePermission("USER_CREATE")
    @RateLimit(requests = 10, window = 60)
    public ApiResponse<UserResponse> createUser(
            @RequestBody @Valid CreateUserRequest request) {

        UserResponse user = userService.createUser(request);
        return ApiResponse.success(user);
    }

    @GetMapping("/{userId}")
    @ApiOperation(value = "获取用户详情", notes = "根据用户ID获取用户详细信息")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "string", paramType = "path")
    @RequirePermission("USER_READ")
    public ApiResponse<UserResponse> getUser(@PathVariable String userId) {
        UserResponse user = userService.getUser(userId);
        return ApiResponse.success(user);
    }
}
```

## 使用示例

### 基本API调用示例
```javascript
// 用户登录
const loginResponse = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'API-Version': 'v1'
    },
    body: JSON.stringify({
        username: '<EMAIL>',
        password: 'password123'
    })
});

const loginData = await loginResponse.json();
const token = loginData.data.accessToken;

// 获取用户列表（带认证）
const usersResponse = await fetch('/api/v1/users?page=1&size=20', {
    method: 'GET',
    headers: {
        'Authorization': `Bearer ${token}`,
        'API-Version': 'v1'
    }
});

const usersData = await usersResponse.json();
console.log('用户列表:', usersData.data);
console.log('分页信息:', usersData.pagination);

// 创建新用户
const createUserResponse = await fetch('/api/v1/users', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'API-Version': 'v1'
    },
    body: JSON.stringify({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'newpassword123'
    })
});

const newUser = await createUserResponse.json();
```

### 错误处理示例
```javascript
// 错误处理示例
try {
    const response = await fetch('/api/v1/users/invalid-id', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });

    if (!response.ok) {
        const errorData = await response.json();

        switch (response.status) {
            case 400:
                console.error('请求参数错误:', errorData.message);
                break;
            case 401:
                console.error('未认证，请重新登录');
                // 重定向到登录页面
                break;
            case 403:
                console.error('权限不足:', errorData.message);
                break;
            case 404:
                console.error('用户不存在');
                break;
            case 429:
                console.error('请求过于频繁，请稍后再试');
                break;
            default:
                console.error('服务器错误:', errorData.message);
        }

        return;
    }

    const data = await response.json();
    console.log('用户信息:', data.data);

} catch (error) {
    console.error('网络错误:', error.message);
}
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的API设计规范，包含RESTful设计、统一响应格式、错误处理、版本控制、安全认证等

### 3. 错误处理规范

#### 错误响应格式
```java
/**
 * 错误详情信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorDetail {
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 错误字段（用于参数验证错误）
     */
    private String field;
    
    /**
     * 错误值
     */
    private Object rejectedValue;
    
    /**
     * 详细描述
     */
    private String description;
}

/**
 * 错误响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponse {
    
    /**
     * HTTP状态码
     */
    private int status;
    
    /**
     * 错误类型
     */
    private String error;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 错误详情列表
     */
    private List<ErrorDetail> details;
    
    /**
     * 错误路径
     */
    private String path;
    
    /**
     * 时间戳
     */
    private long timestamp;
    
    /**
     * 请求ID
     */
    private String requestId;
}
```

#### 状态码规范
```yaml
# HTTP状态码使用规范
status_codes:
  success:
    200: "OK - 请求成功"
    201: "Created - 资源创建成功"
    202: "Accepted - 请求已接受，异步处理中"
    204: "No Content - 请求成功，无返回内容"
  
  client_error:
    400: "Bad Request - 请求参数错误"
    401: "Unauthorized - 未认证"
    403: "Forbidden - 无权限"
    404: "Not Found - 资源不存在"
    409: "Conflict - 资源冲突"
    422: "Unprocessable Entity - 参数验证失败"
    429: "Too Many Requests - 请求过于频繁"
  
  server_error:
    500: "Internal Server Error - 服务器内部错误"
    502: "Bad Gateway - 网关错误"
    503: "Service Unavailable - 服务不可用"
    504: "Gateway Timeout - 网关超时"

# 业务错误码规范
business_codes:
  user_service:
    10001: "用户不存在"
    10002: "用户已存在"
    10003: "密码错误"
    10004: "用户已被禁用"
  
  auth_service:
    20001: "Token无效"
    20002: "Token已过期"
    20003: "权限不足"
    20004: "登录失败"
  
  ai_service:
    30001: "AI模型不可用"
    30002: "对话不存在"
    30003: "消息发送失败"
    30004: "上下文过长"
```

### 4. API版本控制

#### 版本控制策略
```java
/**
 * API版本控制配置
 */
@Configuration
public class ApiVersionConfig {
    
    /**
     * URL路径版本控制
     * 示例: /api/v1/users, /api/v2/users
     */
    @Bean
    public RequestMappingHandlerMapping versionedRequestMappingHandlerMapping() {
        return new VersionedRequestMappingHandlerMapping();
    }
    
    /**
     * Header版本控制
     * 示例: API-Version: v1
     */
    @Component
    public class ApiVersionInterceptor implements HandlerInterceptor {
        
        @Override
        public boolean preHandle(HttpServletRequest request, 
                               HttpServletResponse response, 
                               Object handler) throws Exception {
            
            String apiVersion = request.getHeader("API-Version");
            if (StringUtils.isBlank(apiVersion)) {
                apiVersion = "v1"; // 默认版本
            }
            
            // 设置版本信息到请求上下文
            RequestContextHolder.setApiVersion(apiVersion);
            
            return true;
        }
    }
}

/**
 * API版本注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiVersion {
    String value() default "v1";
    String[] deprecated() default {};
    String since() default "";
}
```

#### 版本兼容性处理
```java
/**
 * API版本兼容性处理器
 */
@Component
public class ApiCompatibilityHandler {
    
    /**
     * 处理版本兼容性
     */
    public Object handleVersionCompatibility(Object response, String apiVersion) {
        if ("v1".equals(apiVersion)) {
            return convertToV1Format(response);
        } else if ("v2".equals(apiVersion)) {
            return convertToV2Format(response);
        }
        return response;
    }
    
    /**
     * 转换为V1格式
     */
    private Object convertToV1Format(Object response) {
        // 移除V2新增字段，保持V1兼容性
        if (response instanceof UserResponse) {
            UserResponse userResponse = (UserResponse) response;
            return UserResponseV1.builder()
                .id(userResponse.getId())
                .username(userResponse.getUsername())
                .email(userResponse.getEmail())
                // 不包含V2新增的字段
                .build();
        }
        return response;
    }
    
    /**
     * 转换为V2格式
     */
    private Object convertToV2Format(Object response) {
        // 包含所有字段
        return response;
    }
}
```
