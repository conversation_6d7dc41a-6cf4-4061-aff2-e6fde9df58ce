# 原始Ark-Pets项目分析 (Original Project Analysis)

## 项目概述

基于获取到的Ark-Pets v3.8.0源代码进行的深度分析，为二次开发提供技术参考。

### 基本信息
- **版本**: v3.8.0
- **许可证**: GPL-3.0 License
- **开发语言**: Java
- **构建工具**: Gradle 7.6
- **目标平台**: Windows (主要), macOS/Linux (实验性)

## 技术栈分析

### 核心依赖
```gradle
dependencies {
    // LibGDX 游戏引擎
    implementation "com.badlogicgames.gdx:gdx:1.11.0"
    implementation "com.badlogicgames.gdx:gdx-backend-lwjgl3:1.11.0"
    
    // Spine 动画系统
    implementation "com.esotericsoftware.spine:spine-libgdx:3.8.99.1"
    
    // JavaFX UI框架
    implementation "org.openjfx:javafx-controls:17.0.8"
    implementation "org.openjfx:javafx-fxml:17.0.8"
    
    // JNA 系统调用
    implementation "net.java.dev.jna:jna:5.12.1"
    implementation "net.java.dev.jna:jna-platform:5.12.1"
    
    // JSON处理
    implementation "com.google.code.gson:gson:2.10.1"
    
    // 网络请求
    implementation "com.squareup.okhttp3:okhttp:4.10.0"
}
```

### 架构特点
1. **模块化设计**: core + desktop 双模块结构
2. **跨平台支持**: 基于LibGDX的跨平台能力
3. **插件化资源**: 支持外部模型和配置文件
4. **事件驱动**: 基于LibGDX的事件系统

## 核心组件分析

### 1. ArkPets - 主应用类

#### 功能职责
- LibGDX应用生命周期管理
- 渲染循环控制
- 资源管理和释放

#### 关键方法
```java
public class ArkPets extends ApplicationAdapter {
    
    /**
     * 应用创建时初始化
     */
    @Override
    public void create() {
        // 初始化渲染器、输入处理器等
    }
    
    /**
     * 主渲染循环
     */
    @Override
    public void render() {
        // 每帧渲染逻辑
    }
    
    /**
     * 窗口大小改变处理
     */
    @Override
    public void resize(int width, int height) {
        // 视口调整
    }
    
    /**
     * 应用销毁时清理
     */
    @Override
    public void dispose() {
        // 资源释放
    }
}
```

### 2. ArkChar - 角色管理类

#### 功能职责
- 角色模型加载和管理
- 动画播放控制
- 物理状态更新
- 用户交互处理

#### 核心属性
```java
public class ArkChar {
    private Skeleton skeleton;              // Spine骨骼
    private AnimationState animationState;  // 动画状态
    private AnimComposer animComposer;      // 动画组合器
    private Behavior behavior;              // 行为控制器
    private Vector2 position;               // 位置坐标
    private Vector2 velocity;               // 速度向量
    private boolean onGround;               // 是否在地面
}
```

### 3. 动画系统

#### AnimComposer - 动画组合器
```java
public class AnimComposer {
    private AnimData playing;               // 当前播放动画
    private final AnimationState state;    // Spine动画状态
    private final int coreTrackId = 0;      // 核心轨道ID
    
    /**
     * 请求播放动画
     */
    public boolean offer(AnimData animData) {
        // 动画切换逻辑
    }
}
```

#### AnimData - 动画数据
```java
public record AnimData(
    AnimClip animClip,      // 动画剪辑
    AnimData animNext,      // 下一个动画
    boolean isLoop,         // 是否循环
    boolean isStrict,       // 是否严格模式
    int mobility           // 移动性 (-1左, 0无, 1右)
) {
    // 动画数据记录
}
```

#### AnimClip - 动画剪辑
```java
public class AnimClip {
    public final String fullName;      // 完整名称
    public final String baseName;      // 基础名称
    public final AnimType type;        // 动画类型
    public final AnimModifier modifier; // 修饰符
    public final AnimStage stage;      // 阶段
    public final float duration;       // 时长
}
```

### 4. 行为系统

#### Behavior - 行为基类
```java
public abstract class Behavior {
    protected final ArkChar character;
    
    /**
     * 行为更新
     */
    public abstract void update(float deltaTime);
    
    /**
     * 行为开始
     */
    public abstract void onStart();
    
    /**
     * 行为结束
     */
    public abstract void onEnd();
}
```

#### GeneralBehavior - 通用行为
- 基于权重的随机动画选择
- 定时触发行为变化
- 支持动画链和循环

### 5. 物理系统

#### 物理特性
- **重力模拟**: 自由落体和反弹
- **碰撞检测**: 窗口边界和桌面检测
- **排斥力**: 多角色间的相互排斥
- **阻尼系统**: 速度衰减和稳定

#### 关键参数
```java
public class PhysicsConfig {
    public static final float GRAVITY = -980f;        // 重力加速度
    public static final float BOUNCE_DAMPING = 0.7f;  // 反弹阻尼
    public static final float FRICTION = 0.95f;       // 摩擦系数
    public static final float REPULSION_FORCE = 100f; // 排斥力
}
```

### 6. 输入系统

#### InputApplicationAdaptor - 输入适配器
```java
public class InputApplicationAdaptor extends InputAdapter {
    
    /**
     * 鼠标按下处理
     */
    @Override
    public boolean touchDown(int screenX, int screenY, int pointer, int button) {
        // 拖拽开始、右键菜单等
    }
    
    /**
     * 鼠标拖拽处理
     */
    @Override
    public boolean touchDragged(int screenX, int screenY, int pointer) {
        // 角色拖拽移动
    }
    
    /**
     * 键盘按键处理
     */
    @Override
    public boolean keyDown(int keycode) {
        // 快捷键处理
    }
}
```

## 资源管理分析

### 模型文件结构
```
assets/
├── characters/
│   ├── amiya/
│   │   ├── amiya.skel          # Spine骨骼数据
│   │   ├── amiya.atlas         # 纹理图集
│   │   └── amiya.png           # 纹理文件
│   └── other_characters/
├── config/
│   ├── characters.json         # 角色配置
│   └── animations.json         # 动画配置
└── ui/
    ├── icons/                  # 图标资源
    └── fonts/                  # 字体文件
```

### 配置文件格式
```json
{
  "characters": [
    {
      "name": "amiya",
      "displayName": "阿米娅",
      "modelPath": "characters/amiya/",
      "animations": {
        "idle": { "weight": 10, "loop": true },
        "move": { "weight": 5, "loop": true },
        "sit": { "weight": 3, "loop": false }
      }
    }
  ]
}
```

## 二次开发建议

### 1. 保留的核心组件
- **ArkChar**: 角色管理核心，可扩展AI交互功能
- **AnimComposer**: 动画系统完善，直接复用
- **Behavior**: 行为系统基础良好，可扩展AI驱动行为
- **物理系统**: 成熟稳定，保持不变

### 2. 需要扩展的部分
- **网络通信**: 增加与后端AI服务的通信
- **UI系统**: 扩展对话界面和设置面板
- **配置管理**: 增加AI相关配置项
- **事件系统**: 扩展支持AI事件

### 3. 新增模块建议
- **AIInteractionManager**: AI交互管理器
- **ConversationUI**: 对话界面组件
- **EmotionSystem**: 情感表达系统
- **VoiceSystem**: 语音合成和识别

### 4. 架构升级路径
1. **第一阶段**: 在现有架构基础上增加AI模块
2. **第二阶段**: 重构为前后端分离架构
3. **第三阶段**: 微服务化和云端部署

## 兼容性考虑

### 1. 版本兼容
- 保持与现有模型文件的兼容性
- 支持原有配置文件格式
- 维护API向后兼容

### 2. 性能优化
- 保持原有的高性能渲染
- 优化AI调用的异步处理
- 合理控制内存使用

### 3. 用户体验
- 保持原有的交互方式
- 渐进式引入AI功能
- 提供功能开关选项

---

**文档负责人**: 架构分析组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
