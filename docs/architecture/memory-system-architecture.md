# 记忆系统技术架构 (Memory System Architecture)

## 架构概述

Ark-Pets AI Enhanced的记忆管理系统采用**混合架构**设计，结合开源组件的成熟能力和自研逻辑的定制化优势，构建一个高效、智能、可扩展的记忆管理系统。

## 技术选型策略

### 1. 混合架构设计原则

#### 开源组件负责 (基础设施层)
- **数据存储**: 使用成熟的数据库技术
- **向量搜索**: 利用专业的向量数据库
- **基础算法**: 采用经过验证的开源算法

#### 自研组件负责 (业务逻辑层)
- **记忆重要性评估**: 桌宠专用的评估算法
- **情感记忆处理**: 针对桌宠交互的情感分析
- **个性化学习**: 用户行为模式学习
- **记忆巩固策略**: 桌宠特有的记忆管理策略

## 核心技术栈

### 1. 向量数据库选型

#### 主选方案: Chroma
```yaml
# Chroma 配置
chroma:
  version: "0.4.x"
  deployment: "embedded"  # 嵌入式部署，适合桌面应用
  storage: "local"        # 本地存储
  features:
    - 语义相似度搜索
    - 元数据过滤
    - 批量操作
    - Python/Java客户端
```

**选择理由**:
- ✅ 轻量级，适合桌面应用
- ✅ 支持嵌入式部署，无需独立服务器
- ✅ 优秀的Python/Java生态
- ✅ 活跃的社区支持
- ✅ 简单易用的API

#### 备选方案: Qdrant
```yaml
# Qdrant 配置 (高性能需求时)
qdrant:
  version: "1.7.x"
  deployment: "docker"
  features:
    - 高性能向量搜索
    - 复杂过滤查询
    - 集群支持
    - REST API
```

### 2. 关系数据库选型

#### 主选方案: PostgreSQL + pgvector
```yaml
# PostgreSQL 配置
postgresql:
  version: "15.x"
  extensions:
    - pgvector    # 向量操作支持
    - pg_trgm     # 文本相似度
    - btree_gin   # 索引优化
  features:
    - JSON/JSONB支持
    - 全文搜索
    - 事务保证
    - 丰富的数据类型
```

### 3. 记忆管理框架

#### 自研 + LangChain Memory 集成
```java
// 记忆管理架构
@Component
public class HybridMemoryManager {
    
    // 开源组件
    private final ChromaVectorStore vectorStore;
    private final PostgreSQLRepository metadataRepository;
    private final LangChainMemoryChain memoryChain;
    
    // 自研组件
    private final PetMemoryImportanceEvaluator importanceEvaluator;
    private final EmotionalMemoryProcessor emotionalProcessor;
    private final PersonalizationEngine personalizationEngine;
    private final MemoryConsolidationStrategy consolidationStrategy;
}
```

## 详细架构设计

### 1. 分层架构

```
┌─────────────────────────────────────────────────────────┐
│                   应用接口层                             │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ REST API    │ GraphQL     │ WebSocket               │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                   业务逻辑层 (自研)                      │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ 重要性评估   │ 情感分析     │ 个性化学习               │ │
│  │ 记忆巩固     │ 智能遗忘     │ 关联分析                │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                   数据访问层 (混合)                      │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ 向量操作     │ 关系查询     │ 缓存管理                │ │
│  │ (Chroma)    │ (PostgreSQL)│ (Redis)                 │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                   存储基础层 (开源)                      │
│  ┌─────────────┬─────────────┬─────────────────────────┐ │
│  │ 向量数据库   │ 关系数据库   │ 缓存数据库               │ │
│  │ Chroma      │ PostgreSQL  │ Redis                   │ │
│  └─────────────┴─────────────┴─────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2. 数据流架构

```mermaid
graph TB
    subgraph "记忆输入处理"
        Input[用户交互输入]
        Preprocess[预处理]
        Embedding[向量化]
        Importance[重要性评估]
    end
    
    subgraph "存储层 (开源)"
        Chroma[(Chroma向量库)]
        PostgreSQL[(PostgreSQL)]
        Redis[(Redis缓存)]
    end
    
    subgraph "检索层 (混合)"
        VectorSearch[向量搜索]
        MetadataQuery[元数据查询]
        CacheQuery[缓存查询]
    end
    
    subgraph "智能处理层 (自研)"
        Consolidation[记忆巩固]
        Forgetting[智能遗忘]
        Association[关联分析]
        Personalization[个性化]
    end
    
    Input --> Preprocess
    Preprocess --> Embedding
    Embedding --> Importance
    
    Importance --> Chroma
    Importance --> PostgreSQL
    Importance --> Redis
    
    Chroma --> VectorSearch
    PostgreSQL --> MetadataQuery
    Redis --> CacheQuery
    
    VectorSearch --> Consolidation
    MetadataQuery --> Forgetting
    CacheQuery --> Association
    
    Consolidation --> Personalization
    Forgetting --> Personalization
    Association --> Personalization
```

## 开源组件集成方案

### 1. Chroma 集成

#### 依赖配置
```xml
<!-- Maven 依赖 -->
<dependency>
    <groupId>tech.amikos</groupId>
    <artifactId>chromadb-java-client</artifactId>
    <version>0.1.12</version>
</dependency>
```

#### 配置类
```java
@Configuration
public class ChromaConfig {
    
    @Bean
    public ChromaClient chromaClient() {
        return new ChromaClient("http://localhost:8000");
    }
    
    @Bean
    public Collection memoryCollection(ChromaClient client) {
        return client.getOrCreateCollection("pet_memories");
    }
}
```

#### 服务实现
```java
@Service
public class ChromaMemoryService {
    
    private final Collection memoryCollection;
    private final EmbeddingFunction embeddingFunction;
    
    public void storeMemory(String memoryId, String content, Map<String, Object> metadata) {
        memoryCollection.add(
            Arrays.asList(memoryId),
            Arrays.asList(content),
            Arrays.asList(metadata)
        );
    }
    
    public List<MemoryResult> searchSimilarMemories(String query, int limit) {
        QueryResponse response = memoryCollection.query(
            Arrays.asList(query),
            limit,
            null,
            null
        );
        
        return convertToMemoryResults(response);
    }
}
```

### 2. PostgreSQL 集成

#### 数据库模式
```sql
-- 记忆元数据表
CREATE TABLE memory_metadata (
    id BIGSERIAL PRIMARY KEY,
    memory_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    memory_type VARCHAR(50) NOT NULL,
    importance_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    access_count INTEGER DEFAULT 0,
    is_consolidated BOOLEAN DEFAULT FALSE,
    metadata JSONB
);

-- 记忆关联表
CREATE TABLE memory_associations (
    id BIGSERIAL PRIMARY KEY,
    source_memory_id VARCHAR(255) NOT NULL,
    target_memory_id VARCHAR(255) NOT NULL,
    association_type VARCHAR(50) NOT NULL,
    strength DECIMAL(3,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_memory_user_type ON memory_metadata(user_id, memory_type);
CREATE INDEX idx_memory_importance ON memory_metadata(importance_score DESC);
CREATE INDEX idx_memory_metadata_gin ON memory_metadata USING GIN(metadata);
```

### 3. LangChain Memory 集成

#### 自定义记忆类
```java
@Component
public class PetMemoryChain extends BaseMemory {
    
    private final ChromaMemoryService vectorMemory;
    private final PostgreSQLMemoryService metadataMemory;
    
    @Override
    public void saveContext(Map<String, Object> inputs, Map<String, Object> outputs) {
        // 保存对话上下文到向量数据库和关系数据库
        String memoryId = generateMemoryId();
        String content = formatMemoryContent(inputs, outputs);
        Map<String, Object> metadata = extractMetadata(inputs, outputs);
        
        // 评估重要性
        double importance = evaluateImportance(content, metadata);
        
        // 存储到向量数据库
        vectorMemory.storeMemory(memoryId, content, metadata);
        
        // 存储元数据到关系数据库
        metadataMemory.storeMetadata(memoryId, importance, metadata);
    }
    
    @Override
    public Map<String, Object> loadMemoryVariables(Map<String, Object> inputs) {
        // 从记忆中检索相关信息
        String query = extractQuery(inputs);
        List<MemoryResult> relevantMemories = vectorMemory.searchSimilarMemories(query, 10);
        
        return Map.of("relevant_memories", relevantMemories);
    }
}
```

## 部署配置

### 1. Docker Compose 配置
```yaml
version: '3.8'
services:
  # Chroma 向量数据库
  chroma:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
  
  # PostgreSQL 数据库
  postgres:
    image: pgvector/pgvector:pg15
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=arkpets
      - POSTGRES_USER=arkpets
      - POSTGRES_PASSWORD=arkpets123
  
  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  chroma_data:
  postgres_data:
  redis_data:
```

### 2. 应用配置
```yaml
# application.yml
spring:
  datasource:
    url: ****************************************
    username: arkpets
    password: arkpets123
  
  redis:
    host: localhost
    port: 6379

# 记忆系统配置
memory:
  chroma:
    host: localhost
    port: 8000
    collection: pet_memories
  
  consolidation:
    interval: 3600000  # 1小时
    importance_threshold: 0.6
  
  forgetting:
    interval: 86400000  # 24小时
    retention_days: 30
```

## 性能优化策略

### 1. 缓存策略
- **热点记忆缓存**: 频繁访问的记忆存储在Redis
- **查询结果缓存**: 相似查询结果缓存
- **向量缓存**: 常用向量表示缓存

### 2. 索引优化
- **向量索引**: Chroma的HNSW索引
- **关系索引**: PostgreSQL的B-tree和GIN索引
- **复合索引**: 多字段组合索引

### 3. 分片策略
- **用户分片**: 按用户ID分片存储
- **时间分片**: 按时间范围分片
- **类型分片**: 按记忆类型分片

---

**文档版本**: v1.0  
**创建日期**: 2025-01-01  
**最后更新**: 2025-01-01  
**维护团队**: Ark-Pets开发团队  
**文档说明**: 记忆系统技术架构设计，采用混合架构结合开源组件和自研逻辑
