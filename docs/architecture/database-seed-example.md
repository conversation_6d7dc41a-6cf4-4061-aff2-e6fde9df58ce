# 数据库种子数据示例 (Database Seed Example)

## 概述

本文档展示了如何使用Supabase的种子数据功能来初始化Ark-Pets AI Enhanced项目的数据库，包括默认角色、性格配置、系统设置等基础数据。

## 种子文件结构

```
supabase/
├── migrations/
│   ├── 001_create_users_table.sql
│   ├── 002_create_user_preferences.sql
│   └── ...
├── seed.sql                    # 主种子文件
└── seeds/
    ├── characters.sql          # 角色数据
    ├── personalities.sql       # 性格数据
    └── system_configs.sql      # 系统配置
```

## 主种子文件

### supabase/seed.sql
```sql
-- 主种子数据文件
-- 在数据库重置时自动执行

-- 插入默认系统配置
INSERT INTO public.system_configs (config_key, config_value, config_type, description, is_public) VALUES
('app_version', '3.8.0', 'STRING', '应用版本号', true),
('default_language', 'zh_CN', 'STRING', '默认语言', true),
('max_conversations_per_user', '100', 'INTEGER', '每用户最大对话数', false),
('default_model_provider', 'openai', 'STRING', '默认模型提供商', false),
('default_model_name', 'gpt-4o-mini', 'STRING', '默认模型名称', false),
('enable_realtime', 'true', 'BOOLEAN', '启用实时功能', true),
('max_message_length', '4000', 'INTEGER', '最大消息长度', true);

-- 插入默认角色
INSERT INTO public.characters (name, display_name, description, faction, profession, rarity, background, personality_description, supported_personalities) VALUES
('amiya', '阿米娅', '罗德岛的领袖，温和而坚定的兔子女孩', '罗德岛', '术师', 'SSR', 
 '阿米娅是罗德岛的公开领袖，拥有强大的源石技艺和领导能力。她总是为了他人着想，是大家信赖的伙伴。',
 '温和、善良、有责任感，总是为他人着想，具有强大的领导力和同理心。',
 '["caring", "leader", "gentle", "responsible"]'::jsonb),

('exusiai', '能天使', '企鹅物流的快递员，活泼开朗的天使', '企鹅物流', '狙击', 'SSR',
 '来自拉特兰的天使，现在是企鹅物流的快递员。性格开朗活泼，喜欢苹果派和各种有趣的事物。',
 '活泼、开朗、乐观，充满活力，喜欢交朋友，对生活充满热情。',
 '["cheerful", "energetic", "friendly", "optimistic"]'::jsonb),

('silverash', '银灰', '喀兰贸易的CEO，优雅的雪豹绅士', '喀兰贸易', '近卫', 'SSR',
 '喀兰贸易的CEO，维多利亚贵族出身。优雅、理智，具有敏锐的商业头脑和强大的战斗能力。',
 '优雅、理智、有商业头脑，说话得体，行事果断，具有贵族气质。',
 '["elegant", "intelligent", "business", "noble"]'::jsonb);

-- 插入默认性格配置
INSERT INTO public.personalities (id, name, description, type, speech_style, emotional_tendency, interaction_style, formality_level, humor_level, empathy_level, assertiveness_level) VALUES
(uuid_generate_v4(), '关怀型', '温和关怀，善于倾听和安慰', 'OFFICIAL', '温和亲切', '积极正面', '主动关怀', 0.3, 0.4, 0.9, 0.4),
(uuid_generate_v4(), '活泼型', '开朗活泼，充满活力和幽默感', 'OFFICIAL', '轻松幽默', '乐观开朗', '主动互动', 0.2, 0.8, 0.6, 0.7),
(uuid_generate_v4(), '优雅型', '优雅得体，具有贵族气质', 'OFFICIAL', '优雅正式', '平和理性', '礼貌回应', 0.8, 0.3, 0.5, 0.6),
(uuid_generate_v4(), '智慧型', '理性睿智，善于分析和建议', 'OFFICIAL', '理性客观', '冷静理性', '深度交流', 0.6, 0.4, 0.7, 0.8),
(uuid_generate_v4(), '可爱型', '天真可爱，充满童趣', 'OFFICIAL', '天真可爱', '天真烂漫', '撒娇互动', 0.1, 0.7, 0.8, 0.3);

-- 插入默认用户偏好模板
INSERT INTO public.user_preference_templates (name, description, settings) VALUES
('默认配置', '系统默认的用户偏好配置', '{
  "theme": "default",
  "language": "zh_CN",
  "enable_notifications": true,
  "enable_voice": false,
  "enable_ai": true,
  "auto_save_conversations": true,
  "max_conversation_history": 50
}'::jsonb),

('极简配置', '适合喜欢简洁界面的用户', '{
  "theme": "minimal",
  "language": "zh_CN",
  "enable_notifications": false,
  "enable_voice": false,
  "enable_ai": true,
  "show_animations": false,
  "auto_save_conversations": false
}'::jsonb),

('完整体验', '启用所有功能的完整体验配置', '{
  "theme": "rich",
  "language": "zh_CN",
  "enable_notifications": true,
  "enable_voice": true,
  "enable_ai": true,
  "enable_realtime": true,
  "show_animations": true,
  "auto_save_conversations": true,
  "max_conversation_history": 100
}'::jsonb);

-- 创建默认的系统用户 (用于系统消息)
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at) VALUES
('00000000-0000-0000-0000-000000000000', '<EMAIL>', NOW(), NOW(), NOW());

INSERT INTO public.profiles (id, username, display_name, is_active) VALUES
('00000000-0000-0000-0000-000000000000', 'system', '系统', true);

-- 插入示例对话模板
INSERT INTO public.conversation_templates (name, description, character_name, personality_id, initial_message) VALUES
('日常聊天', '与桌宠进行日常对话', 'amiya', 
 (SELECT id FROM public.personalities WHERE name = '关怀型' LIMIT 1),
 '你好！我是阿米娅，很高兴见到你。有什么我可以帮助你的吗？'),

('学习助手', '让桌宠帮助学习和工作', 'silverash',
 (SELECT id FROM public.personalities WHERE name = '智慧型' LIMIT 1),
 '欢迎！我是银灰，我可以帮助你分析问题、制定计划或提供建议。请告诉我你需要什么帮助。'),

('娱乐互动', '与桌宠进行轻松娱乐的互动', 'exusiai',
 (SELECT id FROM public.personalities WHERE name = '活泼型' LIMIT 1),
 '嗨！我是能天使！今天想做点什么有趣的事情吗？我们可以聊天、玩游戏，或者我给你讲个笑话！');
```

## 角色动画种子数据

### supabase/seeds/character_animations.sql
```sql
-- 角色动画数据
INSERT INTO public.character_animations (character_name, animation_name, full_name, base_name, type, modifier, stage, duration, is_loop, mobility, weight) VALUES
-- 阿米娅动画
('amiya', 'idle', 'Idle', 'Idle', 'IDLE', null, null, 3.0, true, 0, 1.0),
('amiya', 'move', 'Move', 'Move', 'MOVE', null, null, 1.5, true, 1, 1.0),
('amiya', 'interact', 'Interact', 'Interact', 'INTERACT', null, null, 2.0, false, 0, 1.0),
('amiya', 'sleep', 'Sleep', 'Sleep', 'SLEEP', null, null, 4.0, true, 0, 0.5),
('amiya', 'happy', 'Happy', 'Happy', 'EMOTION', 'happy', null, 2.5, false, 0, 1.0),
('amiya', 'sad', 'Sad', 'Sad', 'EMOTION', 'sad', null, 3.0, false, 0, 1.0),

-- 能天使动画
('exusiai', 'idle', 'Idle', 'Idle', 'IDLE', null, null, 2.5, true, 0, 1.0),
('exusiai', 'move', 'Move', 'Move', 'MOVE', null, null, 1.2, true, 1, 1.0),
('exusiai', 'interact', 'Interact', 'Interact', 'INTERACT', null, null, 1.8, false, 0, 1.0),
('exusiai', 'excited', 'Excited', 'Excited', 'EMOTION', 'excited', null, 2.0, false, 0, 1.2),
('exusiai', 'laugh', 'Laugh', 'Laugh', 'EMOTION', 'laugh', null, 2.2, false, 0, 1.0),

-- 银灰动画
('silverash', 'idle', 'Idle', 'Idle', 'IDLE', null, null, 3.5, true, 0, 1.0),
('silverash', 'move', 'Move', 'Move', 'MOVE', null, null, 1.8, true, 1, 1.0),
('silverash', 'interact', 'Interact', 'Interact', 'INTERACT', null, null, 2.5, false, 0, 1.0),
('silverash', 'thinking', 'Thinking', 'Thinking', 'EMOTION', 'thinking', null, 3.0, true, 0, 0.8),
('silverash', 'confident', 'Confident', 'Confident', 'EMOTION', 'confident', null, 2.8, false, 0, 1.0);
```

## 使用种子数据

### 1. 重置数据库并应用种子数据
```bash
# 重置本地数据库并应用所有迁移和种子数据
supabase db reset

# 只应用种子数据 (不重置)
supabase db seed
```

### 2. 生产环境种子数据
```bash
# 将种子数据推送到生产环境 (谨慎使用)
supabase db push --include-seed
```

### 3. 自定义种子脚本
```bash
# 运行特定的种子脚本
psql -h localhost -p 54322 -d postgres -f supabase/seeds/characters.sql
```

## 种子数据最佳实践

### 1. 数据一致性
- 使用UUID而不是自增ID
- 确保外键关系正确
- 使用事务保证数据完整性

### 2. 环境区分
```sql
-- 根据环境插入不同的数据
DO $$
BEGIN
    IF current_setting('app.environment', true) = 'development' THEN
        -- 开发环境的测试数据
        INSERT INTO public.profiles (id, username, display_name) VALUES
        ('11111111-1111-1111-1111-111111111111', 'test_user', '测试用户');
    END IF;
END $$;
```

### 3. 幂等性
```sql
-- 确保种子数据可以重复执行
INSERT INTO public.characters (name, display_name, description)
VALUES ('amiya', '阿米娅', '罗德岛的领袖')
ON CONFLICT (name) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description;
```

---

**文档版本**: v1.0  
**创建日期**: 2025-01-01  
**最后更新**: 2025-01-01  
**维护团队**: Ark-Pets开发团队  
**文档说明**: Supabase数据库种子数据示例，展示如何初始化项目基础数据
