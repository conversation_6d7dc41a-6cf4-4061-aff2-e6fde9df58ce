# 系统架构 (System Architecture)

## 架构概述

Ark-Pets 采用前后端分离的微服务架构，前端为桌面客户端应用，后端为云服务集群，通过RESTful API和WebSocket进行通信。

## 整体架构图

```mermaid
graph TB
    subgraph "客户端层 (Client Layer)"
        A[桌面客户端<br/>Ark-Pets Desktop]
        B[Web管理界面<br/>Admin Dashboard]
        C[移动端应用<br/>Mobile App]
    end
    
    subgraph "网关层 (Gateway Layer)"
        D[API网关<br/>Spring Cloud Gateway]
        E[负载均衡器<br/>Nginx/HAProxy]
    end
    
    subgraph "服务层 (Service Layer)"
        F[认证服务<br/>Auth Service]
        G[用户服务<br/>User Service]
        H[模型服务<br/>Model Service]
        I[AI服务<br/>AI Service]
        J[配置服务<br/>Config Service]
        K[通知服务<br/>Notification Service]
    end
    
    subgraph "数据层 (Data Layer)"
        L[PostgreSQL<br/>主数据库]
        M[Redis<br/>缓存数据库]
        N[MinIO<br/>对象存储]
        O[Elasticsearch<br/>搜索引擎]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        P[Kubernetes<br/>容器编排]
        Q[Prometheus<br/>监控系统]
        R[ELK Stack<br/>日志系统]
        S[Consul<br/>服务发现]
    end
    
    A --> E
    B --> E
    C --> E
    E --> D
    D --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
    
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
    
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    
    H --> N
    I --> N
    
    G --> O
    H --> O
    
    F -.-> S
    G -.-> S
    H -.-> S
    I -.-> S
    J -.-> S
    K -.-> S
    
    P --> F
    P --> G
    P --> H
    P --> I
    P --> J
    P --> K
    
    Q -.-> F
    Q -.-> G
    Q -.-> H
    Q -.-> I
    Q -.-> J
    Q -.-> K
    
    R -.-> F
    R -.-> G
    R -.-> H
    R -.-> I
    R -.-> J
    R -.-> K
```

## 架构层次

### 1. 客户端层 (Client Layer)

#### 桌面客户端 (Desktop Client)
- **技术栈**: Java + LibGDX + JavaFX
- **功能**: 桌宠显示、用户交互、AI对话
- **特点**: 跨平台、低资源占用、离线支持

#### Web管理界面 (Admin Dashboard)
- **技术栈**: React + TypeScript + Ant Design
- **功能**: 系统管理、用户管理、数据统计
- **特点**: 响应式设计、实时数据展示

#### 移动端应用 (Mobile App)
- **技术栈**: React Native / Flutter
- **功能**: 远程控制、消息推送、简单交互
- **特点**: 原生性能、推送通知

### 2. 网关层 (Gateway Layer)

#### API网关 (API Gateway)
```java
@Configuration
public class GatewayConfig {
    
    /**
     * 配置路由规则
     * @return 路由定位器
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            .route("auth-service", r -> r.path("/api/v1/auth/**")
                .uri("lb://auth-service"))
            .route("user-service", r -> r.path("/api/v1/users/**")
                .uri("lb://user-service"))
            .route("model-service", r -> r.path("/api/v1/models/**")
                .uri("lb://model-service"))
            .route("ai-service", r -> r.path("/api/v1/ai/**")
                .uri("lb://ai-service"))
            .route("config-service", r -> r.path("/api/v1/config/**")
                .uri("lb://config-service"))
            .route("notification-service", r -> r.path("/api/v1/notifications/**")
                .uri("lb://notification-service"))
            .build();
    }
    
    /**
     * 配置全局过滤器
     * @return 过滤器列表
     */
    @Bean
    public List<GlobalFilter> globalFilters() {
        return Arrays.asList(
            new AuthenticationFilter(),
            new RateLimitFilter(),
            new LoggingFilter(),
            new CorsFilter()
        );
    }
}
```

#### 负载均衡器 (Load Balancer)
```nginx
upstream api_gateway {
    server gateway-1:8080 weight=3;
    server gateway-2:8080 weight=3;
    server gateway-3:8080 weight=2;
}

server {
    listen 80;
    server_name api.arkpets.com;
    
    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 3. 服务层 (Service Layer)

#### 服务注册与发现
```java
@EnableEurekaClient
@SpringBootApplication
public class ServiceApplication {
    
    /**
     * 服务健康检查
     * @return 健康状态
     */
    @Bean
    public HealthIndicator serviceHealthIndicator() {
        return new ServiceHealthIndicator();
    }
    
    /**
     * 服务配置刷新
     * @return 配置刷新端点
     */
    @RefreshScope
    @Component
    public class ConfigRefreshComponent {
        // 配置刷新逻辑
    }
}
```

#### 服务间通信
```java
@Component
public class ServiceCommunicator {
    
    private final RestTemplate restTemplate;
    private final WebClient webClient;
    
    /**
     * 同步服务调用
     * @param serviceName 服务名称
     * @param endpoint 端点路径
     * @param request 请求数据
     * @return 响应数据
     */
    public <T, R> R callServiceSync(String serviceName, String endpoint, 
                                   T request, Class<R> responseType) {
        String url = "http://" + serviceName + endpoint;
        return restTemplate.postForObject(url, request, responseType);
    }
    
    /**
     * 异步服务调用
     * @param serviceName 服务名称
     * @param endpoint 端点路径
     * @param request 请求数据
     * @return 异步响应
     */
    public <T, R> Mono<R> callServiceAsync(String serviceName, String endpoint, 
                                          T request, Class<R> responseType) {
        String url = "http://" + serviceName + endpoint;
        return webClient.post()
            .uri(url)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(responseType);
    }
}
```

### 4. 数据层 (Data Layer)

#### 数据库分片策略
```java
@Configuration
public class DatabaseShardingConfig {
    
    /**
     * 配置分片规则
     * @return 分片规则配置
     */
    @Bean
    public ShardingRuleConfiguration shardingRuleConfig() {
        ShardingRuleConfiguration config = new ShardingRuleConfiguration();
        
        // 用户表分片
        config.getTableRuleConfigs().add(getUserTableRuleConfig());
        // 对话表分片
        config.getTableRuleConfigs().add(getConversationTableRuleConfig());
        // 消息表分片
        config.getTableRuleConfigs().add(getMessageTableRuleConfig());
        
        return config;
    }
    
    /**
     * 用户表分片规则
     * @return 表规则配置
     */
    private TableRuleConfiguration getUserTableRuleConfig() {
        TableRuleConfiguration config = new TableRuleConfiguration("users", 
            "ds${0..1}.users_${0..15}");
        config.setDatabaseShardingStrategyConfig(
            new StandardShardingStrategyConfiguration("user_id", 
                new UserDatabaseShardingAlgorithm()));
        config.setTableShardingStrategyConfig(
            new StandardShardingStrategyConfiguration("user_id", 
                new UserTableShardingAlgorithm()));
        return config;
    }
}
```

#### 缓存策略
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    /**
     * Redis缓存管理器
     * @return 缓存管理器
     */
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .transactionAware()
            .build();
    }
    
    /**
     * 多级缓存配置
     * @return 多级缓存管理器
     */
    @Bean
    public MultiLevelCacheManager multiLevelCacheManager() {
        return MultiLevelCacheManager.builder()
            .l1Cache(new CaffeineCache("l1", Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofMinutes(5))
                .build()))
            .l2Cache(new RedisCache("l2", redisTemplate))
            .build();
    }
}
```

## 部署架构

### Kubernetes部署配置

#### 命名空间配置
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: arkpets
  labels:
    name: arkpets
    environment: production
```

#### 服务部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-service
  namespace: arkpets
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-service
  template:
    metadata:
      labels:
        app: ai-service
    spec:
      containers:
      - name: ai-service
        image: arkpets/ai-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

#### 服务发现配置
```yaml
apiVersion: v1
kind: Service
metadata:
  name: ai-service
  namespace: arkpets
spec:
  selector:
    app: ai-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

### 监控和日志

#### Prometheus监控配置
```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: arkpets-services
  namespace: arkpets
spec:
  selector:
    matchLabels:
      monitoring: enabled
  endpoints:
  - port: metrics
    path: /actuator/prometheus
    interval: 30s
```

#### 日志收集配置
```yaml
apiVersion: logging.coreos.com/v1
kind: ClusterLogForwarder
metadata:
  name: arkpets-logs
  namespace: arkpets
spec:
  outputs:
  - name: elasticsearch
    type: elasticsearch
    url: http://elasticsearch:9200
  pipelines:
  - name: arkpets-pipeline
    inputRefs:
    - application
    filterRefs:
    - arkpets-filter
    outputRefs:
    - elasticsearch
```

## 安全架构

### 网络安全
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: arkpets-network-policy
  namespace: arkpets
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: arkpets
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: arkpets
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
```

### 密钥管理
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: arkpets-secrets
  namespace: arkpets
type: Opaque
data:
  database-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-jwt-secret>
  openai-api-key: <base64-encoded-openai-key>
  claude-api-key: <base64-encoded-claude-key>
```

## 性能优化

### 1. 缓存策略
- **L1缓存**: 本地Caffeine缓存，存储热点数据
- **L2缓存**: Redis分布式缓存，存储共享数据
- **CDN缓存**: 静态资源和模型文件缓存

### 2. 数据库优化
- **读写分离**: 主库写入，从库读取
- **分库分表**: 按用户ID分片
- **连接池**: HikariCP连接池优化

### 3. 服务优化
- **异步处理**: 非关键路径异步执行
- **批量操作**: 减少数据库访问次数
- **限流熔断**: Hystrix/Resilience4j保护服务

## 扩展性设计

### 1. 水平扩展
- **无状态服务**: 所有服务设计为无状态
- **负载均衡**: 支持动态扩缩容
- **数据分片**: 支持数据水平分片

### 2. 垂直扩展
- **资源配置**: 支持动态调整资源配置
- **性能监控**: 实时监控资源使用情况
- **自动扩容**: 基于指标自动扩容

### 3. 功能扩展
- **插件架构**: 支持功能插件化
- **API版本**: 支持多版本API共存
- **配置中心**: 动态配置管理

---

**文档负责人**: 架构设计组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
