# 启动器 (Launcher)

## 模块概述

启动器模块负责应用程序的启动流程管理，包括初始化检查、资源加载、配置验证等。基于原始Ark-Pets项目的DesktopLauncher进行扩展。

**基于原始Ark-Pets项目分析**:
- 原项目使用DesktopLauncher作为应用入口点
- 包含LibGDX应用配置和窗口设置
- 支持命令行参数和配置文件加载
- 实现了资源预加载和错误处理机制

## 核心功能

### 1. 应用启动器 (ApplicationLauncher) - 基于原始项目

#### 功能描述
基于原始Ark-Pets的DesktopLauncher实现，负责应用程序的完整启动流程。

#### 核心函数

```java
public class ApplicationLauncher {
    
    private static final Logger logger = LoggerFactory.getLogger(ApplicationLauncher.class);
    private static ApplicationConfig appConfig;
    private static boolean isDebugMode = false;
    private static String[] launchArgs;
    
    /**
     * 应用程序主入口点
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        launchArgs = args;
        
        try {
            // 1. 解析命令行参数
            parseCommandLineArguments(args);
            
            // 2. 初始化日志系统
            initializeLogging();
            
            // 3. 检查系统环境
            if (!checkSystemRequirements()) {
                showErrorAndExit("系统环境检查失败");
                return;
            }
            
            // 4. 加载应用配置
            if (!loadApplicationConfig()) {
                showErrorAndExit("配置文件加载失败");
                return;
            }
            
            // 5. 检查资源文件
            if (!validateResources()) {
                showErrorAndExit("资源文件验证失败");
                return;
            }
            
            // 6. 初始化AI服务连接
            if (!initializeAIService()) {
                logger.warn("AI服务初始化失败，将以离线模式运行");
            }
            
            // 7. 启动应用
            launchApplication();
            
        } catch (Exception e) {
            logger.error("应用启动失败", e);
            showErrorAndExit("应用启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析命令行参数
     * @param args 命令行参数
     */
    private static void parseCommandLineArguments(String[] args) {
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "--debug":
                case "-d":
                    isDebugMode = true;
                    break;
                case "--config":
                case "-c":
                    if (i + 1 < args.length) {
                        System.setProperty("config.file", args[++i]);
                    }
                    break;
                case "--character":
                    if (i + 1 < args.length) {
                        System.setProperty("default.character", args[++i]);
                    }
                    break;
                case "--help":
                case "-h":
                    showHelpAndExit();
                    break;
                case "--version":
                case "-v":
                    showVersionAndExit();
                    break;
            }
        }
    }
    
    /**
     * 初始化日志系统
     */
    private static void initializeLogging() {
        // 设置日志级别
        if (isDebugMode) {
            System.setProperty("log.level", "DEBUG");
        }
        
        // 配置日志输出
        System.setProperty("log.dir", "logs");
        
        logger.info("应用启动开始");
        logger.info("调试模式: {}", isDebugMode);
    }
    
    /**
     * 检查系统环境
     * @return 检查是否通过
     */
    private static boolean checkSystemRequirements() {
        try {
            // 检查Java版本
            String javaVersion = System.getProperty("java.version");
            logger.info("Java版本: {}", javaVersion);
            
            if (!isJavaVersionSupported(javaVersion)) {
                logger.error("不支持的Java版本: {}，需要Java 11或更高版本", javaVersion);
                return false;
            }
            
            // 检查操作系统
            String osName = System.getProperty("os.name");
            String osArch = System.getProperty("os.arch");
            logger.info("操作系统: {} {}", osName, osArch);
            
            // 检查内存
            long maxMemory = Runtime.getRuntime().maxMemory();
            logger.info("最大可用内存: {} MB", maxMemory / 1024 / 1024);
            
            if (maxMemory < 256 * 1024 * 1024) { // 256MB
                logger.warn("可用内存较少，可能影响性能");
            }
            
            // 检查图形环境
            if (!checkGraphicsEnvironment()) {
                logger.error("图形环境检查失败");
                return false;
            }
            
            return true;
        } catch (Exception e) {
            logger.error("系统环境检查异常", e);
            return false;
        }
    }
    
    /**
     * 检查Java版本是否支持
     * @param version Java版本字符串
     * @return 是否支持
     */
    private static boolean isJavaVersionSupported(String version) {
        try {
            // 解析版本号
            String[] parts = version.split("\\.");
            int majorVersion = Integer.parseInt(parts[0]);
            
            // Java 9+的版本格式
            if (majorVersion >= 11) {
                return true;
            }
            
            // Java 8的版本格式 (1.8.x)
            if (majorVersion == 1 && parts.length > 1) {
                int minorVersion = Integer.parseInt(parts[1]);
                return minorVersion >= 8;
            }
            
            return false;
        } catch (Exception e) {
            logger.warn("无法解析Java版本: {}", version);
            return true; // 假设支持
        }
    }
    
    /**
     * 检查图形环境
     * @return 检查是否通过
     */
    private static boolean checkGraphicsEnvironment() {
        try {
            // 检查是否为无头模式
            if (GraphicsEnvironment.isHeadless()) {
                logger.error("检测到无头模式，无法运行图形应用");
                return false;
            }
            
            // 检查显示设备
            GraphicsDevice[] devices = GraphicsEnvironment.getLocalGraphicsEnvironment().getScreenDevices();
            logger.info("检测到 {} 个显示设备", devices.length);
            
            for (int i = 0; i < devices.length; i++) {
                GraphicsDevice device = devices[i];
                DisplayMode mode = device.getDisplayMode();
                logger.info("显示设备 {}: {}x{} @{}Hz", 
                    i, mode.getWidth(), mode.getHeight(), mode.getRefreshRate());
            }
            
            return devices.length > 0;
        } catch (Exception e) {
            logger.error("图形环境检查异常", e);
            return false;
        }
    }
    
    /**
     * 加载应用配置
     * @return 加载是否成功
     */
    private static boolean loadApplicationConfig() {
        try {
            String configFile = System.getProperty("config.file", "config.json");
            logger.info("加载配置文件: {}", configFile);
            
            File file = new File(configFile);
            if (file.exists()) {
                appConfig = ApplicationConfig.loadFromFile(configFile);
                logger.info("配置文件加载成功");
            } else {
                logger.info("配置文件不存在，使用默认配置");
                appConfig = new ApplicationConfig();
                
                // 保存默认配置
                appConfig.saveToFile(configFile);
            }
            
            // 应用命令行覆盖
            applyCommandLineOverrides();
            
            return true;
        } catch (Exception e) {
            logger.error("配置文件加载失败", e);
            return false;
        }
    }
    
    /**
     * 应用命令行参数覆盖
     */
    private static void applyCommandLineOverrides() {
        String defaultCharacter = System.getProperty("default.character");
        if (defaultCharacter != null) {
            appConfig.setLastSelectedCharacter(defaultCharacter);
            logger.info("命令行指定默认角色: {}", defaultCharacter);
        }
    }
    
    /**
     * 验证资源文件
     * @return 验证是否通过
     */
    private static boolean validateResources() {
        try {
            logger.info("开始验证资源文件");
            
            // 检查必需的目录
            String[] requiredDirs = {"characters", "config", "assets"};
            for (String dir : requiredDirs) {
                File directory = new File(dir);
                if (!directory.exists()) {
                    logger.error("必需目录不存在: {}", dir);
                    return false;
                }
            }
            
            // 检查角色文件
            if (!validateCharacterResources()) {
                return false;
            }
            
            // 检查UI资源
            if (!validateUIResources()) {
                return false;
            }
            
            logger.info("资源文件验证完成");
            return true;
        } catch (Exception e) {
            logger.error("资源文件验证异常", e);
            return false;
        }
    }
    
    /**
     * 验证角色资源
     * @return 验证是否通过
     */
    private static boolean validateCharacterResources() {
        try {
            File charactersDir = new File("characters");
            File[] characterDirs = charactersDir.listFiles(File::isDirectory);
            
            if (characterDirs == null || characterDirs.length == 0) {
                logger.error("未找到任何角色目录");
                return false;
            }
            
            int validCharacters = 0;
            for (File characterDir : characterDirs) {
                String characterName = characterDir.getName();
                
                // 检查必需文件
                File skelFile = new File(characterDir, characterName + ".skel");
                File atlasFile = new File(characterDir, characterName + ".atlas");
                
                if (skelFile.exists() && atlasFile.exists()) {
                    validCharacters++;
                    logger.debug("角色资源有效: {}", characterName);
                } else {
                    logger.warn("角色资源不完整: {}", characterName);
                }
            }
            
            if (validCharacters == 0) {
                logger.error("未找到有效的角色资源");
                return false;
            }
            
            logger.info("找到 {} 个有效角色", validCharacters);
            return true;
        } catch (Exception e) {
            logger.error("角色资源验证异常", e);
            return false;
        }
    }
    
    /**
     * 验证UI资源
     * @return 验证是否通过
     */
    private static boolean validateUIResources() {
        try {
            // 检查FXML文件
            String[] requiredFXML = {
                "/fxml/MainWindow.fxml",
                "/fxml/CharacterSelection.fxml",
                "/fxml/Settings.fxml"
            };
            
            for (String fxml : requiredFXML) {
                if (getClass().getResource(fxml) == null) {
                    logger.error("FXML文件不存在: {}", fxml);
                    return false;
                }
            }
            
            // 检查CSS文件
            String[] requiredCSS = {
                "/css/main.css",
                "/css/settings.css"
            };
            
            for (String css : requiredCSS) {
                if (getClass().getResource(css) == null) {
                    logger.warn("CSS文件不存在: {}", css);
                }
            }
            
            return true;
        } catch (Exception e) {
            logger.error("UI资源验证异常", e);
            return false;
        }
    }
    
    /**
     * 初始化AI服务
     * @return 初始化是否成功
     */
    private static boolean initializeAIService() {
        try {
            AIServiceConfig aiConfig = appConfig.getAiService();
            if (aiConfig == null || aiConfig.getServiceUrl() == null) {
                logger.info("AI服务未配置，跳过初始化");
                return false;
            }
            
            logger.info("初始化AI服务连接: {}", aiConfig.getServiceUrl());
            
            // 创建AI服务客户端
            AIServiceClient client = new AIServiceClient(aiConfig);
            
            // 测试连接
            if (client.testConnection()) {
                logger.info("AI服务连接成功");
                return true;
            } else {
                logger.warn("AI服务连接失败");
                return false;
            }
        } catch (Exception e) {
            logger.error("AI服务初始化异常", e);
            return false;
        }
    }
    
    /**
     * 启动应用程序
     */
    private static void launchApplication() {
        logger.info("启动应用程序");
        
        // 设置JavaFX系统属性
        System.setProperty("javafx.preloader", "cn.harryh.arkpets.Preloader");
        
        // 启动JavaFX应用
        Platform.startup(() -> {
            try {
                Stage primaryStage = new Stage();
                MainWindowManager mainWindow = new MainWindowManager();
                
                if (mainWindow.initializeMainWindow(primaryStage)) {
                    primaryStage.show();
                    
                    // 如果是新用户，显示欢迎界面
                    if (appConfig.isNewcomer()) {
                        showWelcomeDialog();
                        appConfig.setNewcomer(false);
                        appConfig.saveToFile("config.json");
                    } else {
                        // 直接显示角色选择
                        mainWindow.showCharacterSelection();
                    }
                } else {
                    logger.error("主窗口初始化失败");
                    Platform.exit();
                }
            } catch (Exception e) {
                logger.error("应用启动异常", e);
                Platform.exit();
            }
        });
    }
    
    /**
     * 显示欢迎对话框
     */
    private static void showWelcomeDialog() {
        Alert welcome = new Alert(Alert.AlertType.INFORMATION);
        welcome.setTitle("欢迎使用 Ark-Pets AI Enhanced");
        welcome.setHeaderText("欢迎！");
        welcome.setContentText("这是您第一次使用 Ark-Pets AI Enhanced。\n\n" +
                               "本应用在原版 Ark-Pets 基础上增加了 AI 智能交互功能，\n" +
                               "让您的桌宠更加智能和有趣！\n\n" +
                               "请选择您喜欢的角色开始体验。");
        welcome.showAndWait();
    }
    
    /**
     * 显示帮助信息并退出
     */
    private static void showHelpAndExit() {
        System.out.println("Ark-Pets AI Enhanced");
        System.out.println("用法: java -jar ark-pets.jar [选项]");
        System.out.println();
        System.out.println("选项:");
        System.out.println("  -d, --debug          启用调试模式");
        System.out.println("  -c, --config FILE    指定配置文件");
        System.out.println("  --character NAME     指定默认角色");
        System.out.println("  -h, --help           显示此帮助信息");
        System.out.println("  -v, --version        显示版本信息");
        System.exit(0);
    }
    
    /**
     * 显示版本信息并退出
     */
    private static void showVersionAndExit() {
        System.out.println("Ark-Pets AI Enhanced v1.0.0");
        System.out.println("基于 Ark-Pets v3.8.0 二次开发");
        System.out.println("Copyright (c) 2025 Ark-Pets Development Team");
        System.exit(0);
    }
    
    /**
     * 显示错误信息并退出
     * @param message 错误信息
     */
    private static void showErrorAndExit(String message) {
        logger.error(message);
        
        // 尝试显示图形错误对话框
        try {
            if (!GraphicsEnvironment.isHeadless()) {
                Platform.runLater(() -> {
                    Alert alert = new Alert(Alert.AlertType.ERROR);
                    alert.setTitle("启动错误");
                    alert.setHeaderText("应用启动失败");
                    alert.setContentText(message);
                    alert.showAndWait();
                    System.exit(1);
                });
                return;
            }
        } catch (Exception e) {
            // 忽略图形界面错误
        }
        
        // 控制台输出错误信息
        System.err.println("错误: " + message);
        System.exit(1);
    }
}
```

### 2. 预加载器 (Preloader)

#### 功能描述
显示应用启动时的加载进度，提升用户体验。

#### 核心函数

```java
public class Preloader extends javafx.application.Preloader {
    
    private Stage preloaderStage;
    private ProgressBar progressBar;
    private Label statusLabel;
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        this.preloaderStage = primaryStage;
        
        // 创建预加载界面
        VBox root = new VBox(20);
        root.setAlignment(Pos.CENTER);
        root.setPadding(new Insets(50));
        root.setStyle("-fx-background-color: #2c3e50;");
        
        // 应用图标
        ImageView logo = new ImageView(new Image("/images/logo.png"));
        logo.setFitWidth(128);
        logo.setFitHeight(128);
        logo.setPreserveRatio(true);
        
        // 应用标题
        Label titleLabel = new Label("Ark-Pets AI Enhanced");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;");
        
        // 进度条
        progressBar = new ProgressBar(0);
        progressBar.setPrefWidth(300);
        progressBar.setStyle("-fx-accent: #3498db;");
        
        // 状态标签
        statusLabel = new Label("正在初始化...");
        statusLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #bdc3c7;");
        
        root.getChildren().addAll(logo, titleLabel, progressBar, statusLabel);
        
        Scene scene = new Scene(root, 400, 300);
        primaryStage.setScene(scene);
        primaryStage.setTitle("Ark-Pets AI Enhanced - 启动中");
        primaryStage.initStyle(StageStyle.UNDECORATED);
        primaryStage.setAlwaysOnTop(true);
        primaryStage.centerOnScreen();
        primaryStage.show();
    }
    
    @Override
    public void handleProgressNotification(ProgressNotification info) {
        Platform.runLater(() -> {
            progressBar.setProgress(info.getProgress());
        });
    }
    
    @Override
    public void handleStateChangeNotification(StateChangeNotification info) {
        Platform.runLater(() -> {
            switch (info.getType()) {
                case BEFORE_LOAD:
                    statusLabel.setText("正在加载资源...");
                    break;
                case BEFORE_INIT:
                    statusLabel.setText("正在初始化...");
                    break;
                case BEFORE_START:
                    statusLabel.setText("正在启动应用...");
                    break;
            }
        });
    }
    
    @Override
    public void handleApplicationNotification(PreloaderNotification info) {
        if (info instanceof ProgressNotification) {
            handleProgressNotification((ProgressNotification) info);
        } else if (info instanceof StateChangeNotification) {
            handleStateChangeNotification((StateChangeNotification) info);
        }
    }
}
```

## 启动流程图

### 应用启动流程

```mermaid
graph TD
    A[main方法] --> B[解析命令行参数]
    B --> C[初始化日志系统]
    C --> D[检查系统环境]
    D --> E{环境检查通过?}
    E -->|否| F[显示错误并退出]
    E -->|是| G[加载应用配置]
    G --> H{配置加载成功?}
    H -->|否| F
    H -->|是| I[验证资源文件]
    I --> J{资源验证通过?}
    J -->|否| F
    J -->|是| K[初始化AI服务]
    K --> L[启动JavaFX应用]
    L --> M[显示主窗口]
    M --> N{是新用户?}
    N -->|是| O[显示欢迎界面]
    N -->|否| P[显示角色选择]
    O --> P
    P --> Q[应用运行中]
```

## 配置文件

### 启动配置 (launcher.properties)

```properties
# 应用信息
app.name=Ark-Pets AI Enhanced
app.version=1.0.0
app.build=20250101

# 系统要求
java.min.version=11
memory.min.mb=256
memory.recommended.mb=512

# 启动选项
startup.splash=true
startup.check.updates=true
startup.preload.characters=true

# 调试选项
debug.enable.console=false
debug.log.level=INFO
debug.performance.monitor=false

# 资源路径
resources.characters.dir=characters
resources.config.dir=config
resources.assets.dir=assets
```

---

**模块负责人**: 启动器开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
