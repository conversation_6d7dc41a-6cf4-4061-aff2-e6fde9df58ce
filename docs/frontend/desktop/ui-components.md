# UI组件 (UI Components)

## 模块概述

UI组件模块负责桌面应用的用户界面实现，包括主窗口、设置面板、对话界面等。基于原始Ark-Pets项目的JavaFX界面进行现代化升级。

**基于原始Ark-Pets项目分析**:
- 原项目使用JavaFX实现桌面UI界面
- 包含角色选择、设置配置、系统托盘等功能
- 支持FXML布局文件和CSS样式
- 实现了响应式布局和多语言支持

## 核心组件

### 1. 主窗口管理器 (MainWindowManager) - 基于原始项目

#### 功能描述
基于原始Ark-Pets的主窗口实现，管理应用的主要界面和生命周期。

#### 核心函数

```java
public class MainWindowManager {
    
    private Stage primaryStage;
    private Scene mainScene;
    private BorderPane rootLayout;
    private CharacterSelectionController characterController;
    private SettingsController settingsController;
    private AIConversationController conversationController;
    
    /**
     * 初始化主窗口 (基于原始项目的start方法)
     * @param primaryStage 主舞台
     * @return 初始化是否成功
     */
    public boolean initializeMainWindow(Stage primaryStage) {
        this.primaryStage = primaryStage;
        
        try {
            // 1. 加载根布局
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource("/fxml/MainWindow.fxml"));
            rootLayout = loader.load();
            
            // 2. 创建场景
            mainScene = new Scene(rootLayout);
            mainScene.getStylesheets().add(getClass().getResource("/css/main.css").toExternalForm());
            
            // 3. 配置主舞台
            primaryStage.setTitle("Ark-Pets AI Enhanced");
            primaryStage.setScene(mainScene);
            primaryStage.setResizable(true);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            
            // 4. 初始化控制器
            initializeControllers();
            
            // 5. 设置事件处理
            setupEventHandlers();
            
            return true;
        } catch (Exception e) {
            Logger.error("Failed to initialize main window", e);
            return false;
        }
    }
    
    /**
     * 显示角色选择界面
     */
    public void showCharacterSelection() {
        try {
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource("/fxml/CharacterSelection.fxml"));
            AnchorPane characterSelection = loader.load();
            
            characterController = loader.getController();
            characterController.setMainWindow(this);
            
            rootLayout.setCenter(characterSelection);
        } catch (Exception e) {
            Logger.error("Failed to show character selection", e);
        }
    }
    
    /**
     * 显示设置界面
     */
    public void showSettings() {
        try {
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource("/fxml/Settings.fxml"));
            VBox settingsPane = loader.load();
            
            settingsController = loader.getController();
            settingsController.setMainWindow(this);
            
            // 创建设置对话框
            Stage settingsStage = new Stage();
            settingsStage.setTitle("设置");
            settingsStage.initModality(Modality.WINDOW_MODAL);
            settingsStage.initOwner(primaryStage);
            
            Scene settingsScene = new Scene(settingsPane);
            settingsScene.getStylesheets().add(getClass().getResource("/css/settings.css").toExternalForm());
            settingsStage.setScene(settingsScene);
            settingsStage.showAndWait();
        } catch (Exception e) {
            Logger.error("Failed to show settings", e);
        }
    }
    
    /**
     * 显示AI对话界面 (新增功能)
     */
    public void showAIConversation() {
        try {
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource("/fxml/AIConversation.fxml"));
            VBox conversationPane = loader.load();
            
            conversationController = loader.getController();
            conversationController.setMainWindow(this);
            
            // 创建对话窗口
            Stage conversationStage = new Stage();
            conversationStage.setTitle("AI对话");
            conversationStage.initModality(Modality.NONE);
            conversationStage.setAlwaysOnTop(true);
            
            Scene conversationScene = new Scene(conversationPane);
            conversationScene.getStylesheets().add(getClass().getResource("/css/conversation.css").toExternalForm());
            conversationStage.setScene(conversationScene);
            conversationStage.show();
        } catch (Exception e) {
            Logger.error("Failed to show AI conversation", e);
        }
    }
    
    /**
     * 初始化控制器
     */
    private void initializeControllers() {
        // 初始化各个控制器的通用设置
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 窗口关闭事件
        primaryStage.setOnCloseRequest(event -> {
            handleWindowClose();
        });
        
        // 键盘快捷键
        mainScene.setOnKeyPressed(event -> {
            handleKeyboardShortcuts(event);
        });
    }
    
    /**
     * 处理窗口关闭
     */
    private void handleWindowClose() {
        // 保存配置
        ApplicationConfig.getInstance().saveToFile("config.json");
        
        // 清理资源
        if (conversationController != null) {
            conversationController.cleanup();
        }
        
        // 退出应用
        Platform.exit();
    }
    
    /**
     * 处理键盘快捷键
     * @param event 键盘事件
     */
    private void handleKeyboardShortcuts(KeyEvent event) {
        if (event.isControlDown()) {
            switch (event.getCode()) {
                case S:
                    showSettings();
                    break;
                case T:
                    showAIConversation();
                    break;
                case Q:
                    primaryStage.close();
                    break;
            }
        }
    }
}
```

### 2. 角色选择控制器 (CharacterSelectionController)

#### 功能描述
管理角色选择界面，支持角色搜索、筛选、预览等功能。

#### 核心函数

```java
public class CharacterSelectionController implements Initializable {
    
    @FXML private ListView<ArkCharacter> characterListView;
    @FXML private TextField searchField;
    @FXML private ComboBox<String> factionFilter;
    @FXML private ComboBox<String> professionFilter;
    @FXML private ImageView characterPreview;
    @FXML private Label characterNameLabel;
    @FXML private TextArea characterDescriptionArea;
    @FXML private Button selectButton;
    @FXML private Button previewButton;
    
    private MainWindowManager mainWindow;
    private ObservableList<ArkCharacter> allCharacters;
    private FilteredList<ArkCharacter> filteredCharacters;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupCharacterList();
        setupFilters();
        setupEventHandlers();
        loadCharacters();
    }
    
    /**
     * 设置角色列表
     */
    private void setupCharacterList() {
        allCharacters = FXCollections.observableArrayList();
        filteredCharacters = new FilteredList<>(allCharacters);
        characterListView.setItems(filteredCharacters);
        
        // 自定义单元格渲染
        characterListView.setCellFactory(listView -> new CharacterListCell());
        
        // 选择事件处理
        characterListView.getSelectionModel().selectedItemProperty().addListener(
            (observable, oldValue, newValue) -> {
                if (newValue != null) {
                    displayCharacterDetails(newValue);
                }
            }
        );
    }
    
    /**
     * 设置过滤器
     */
    private void setupFilters() {
        // 搜索过滤
        searchField.textProperty().addListener((observable, oldValue, newValue) -> {
            updateFilters();
        });
        
        // 阵营过滤
        factionFilter.getItems().addAll("全部", "罗德岛", "龙门", "炎国", "维多利亚", "乌萨斯");
        factionFilter.setValue("全部");
        factionFilter.setOnAction(e -> updateFilters());
        
        // 职业过滤
        professionFilter.getItems().addAll("全部", "先锋", "狙击", "医疗", "术师", "重装", "辅助", "特种", "近卫");
        professionFilter.setValue("全部");
        professionFilter.setOnAction(e -> updateFilters());
    }
    
    /**
     * 更新过滤器
     */
    private void updateFilters() {
        filteredCharacters.setPredicate(character -> {
            // 搜索过滤
            String searchText = searchField.getText().toLowerCase();
            if (!searchText.isEmpty()) {
                if (!character.getCharacterName().toLowerCase().contains(searchText) &&
                    !character.getDisplayName().toLowerCase().contains(searchText)) {
                    return false;
                }
            }
            
            // 阵营过滤
            String selectedFaction = factionFilter.getValue();
            if (!"全部".equals(selectedFaction) && !selectedFaction.equals(character.getFaction())) {
                return false;
            }
            
            // 职业过滤
            String selectedProfession = professionFilter.getValue();
            if (!"全部".equals(selectedProfession) && !selectedProfession.equals(character.getProfession())) {
                return false;
            }
            
            return true;
        });
    }
    
    /**
     * 显示角色详情
     * @param character 选中的角色
     */
    private void displayCharacterDetails(ArkCharacter character) {
        characterNameLabel.setText(character.getDisplayName());
        characterDescriptionArea.setText(character.getDescription());
        
        // 加载角色预览图
        loadCharacterPreview(character);
        
        // 启用按钮
        selectButton.setDisable(false);
        previewButton.setDisable(false);
    }
    
    /**
     * 加载角色预览图
     * @param character 角色对象
     */
    private void loadCharacterPreview(ArkCharacter character) {
        try {
            String previewPath = character.getFullModelPath().replace(".skel", "_preview.png");
            Image previewImage = new Image(getClass().getResourceAsStream(previewPath));
            characterPreview.setImage(previewImage);
        } catch (Exception e) {
            // 使用默认预览图
            Image defaultImage = new Image(getClass().getResourceAsStream("/images/default_preview.png"));
            characterPreview.setImage(defaultImage);
        }
    }
    
    /**
     * 选择角色
     */
    @FXML
    private void handleSelectCharacter() {
        ArkCharacter selectedCharacter = characterListView.getSelectionModel().getSelectedItem();
        if (selectedCharacter != null) {
            // 保存选择
            ApplicationConfig.getInstance().setLastSelectedCharacter(selectedCharacter.getCharacterName());
            
            // 启动桌宠
            launchDesktopPet(selectedCharacter);
            
            // 关闭主窗口
            mainWindow.getPrimaryStage().close();
        }
    }
    
    /**
     * 预览角色
     */
    @FXML
    private void handlePreviewCharacter() {
        ArkCharacter selectedCharacter = characterListView.getSelectionModel().getSelectedItem();
        if (selectedCharacter != null) {
            // 创建预览窗口
            createPreviewWindow(selectedCharacter);
        }
    }
    
    /**
     * 启动桌宠
     * @param character 选中的角色
     */
    private void launchDesktopPet(ArkCharacter character) {
        try {
            // 创建LibGDX应用配置
            Lwjgl3ApplicationConfiguration config = new Lwjgl3ApplicationConfiguration();
            config.setTitle("Ark-Pets - " + character.getDisplayName());
            config.setWindowedMode(400, 400);
            config.setDecorated(false);
            config.setResizable(false);
            config.setWindowPosition(-1, -1); // 居中显示
            
            // 启动桌宠应用
            ArkPetsApplication arkPets = new ArkPetsApplication(character);
            new Lwjgl3Application(arkPets, config);
            
        } catch (Exception e) {
            Logger.error("Failed to launch desktop pet", e);
            showErrorDialog("启动失败", "无法启动桌宠: " + e.getMessage());
        }
    }
    
    /**
     * 创建预览窗口
     * @param character 角色对象
     */
    private void createPreviewWindow(ArkCharacter character) {
        // 实现角色预览窗口
        Stage previewStage = new Stage();
        previewStage.setTitle("预览 - " + character.getDisplayName());
        previewStage.initModality(Modality.APPLICATION_MODAL);
        
        // 创建预览内容
        VBox previewContent = new VBox(10);
        previewContent.setPadding(new Insets(20));
        previewContent.setAlignment(Pos.CENTER);
        
        // 添加预览图像
        ImageView previewImageView = new ImageView();
        loadCharacterPreview(character);
        previewImageView.setImage(characterPreview.getImage());
        previewImageView.setFitWidth(300);
        previewImageView.setPreserveRatio(true);
        
        // 添加角色信息
        Label nameLabel = new Label(character.getDisplayName());
        nameLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
        
        Label infoLabel = new Label(String.format("阵营: %s | 职业: %s | 稀有度: %s", 
            character.getFaction(), character.getProfession(), character.getRarity()));
        
        TextArea descArea = new TextArea(character.getDescription());
        descArea.setEditable(false);
        descArea.setPrefRowCount(3);
        descArea.setWrapText(true);
        
        // 添加按钮
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        
        Button selectBtn = new Button("选择此角色");
        selectBtn.setOnAction(e -> {
            previewStage.close();
            handleSelectCharacter();
        });
        
        Button closeBtn = new Button("关闭");
        closeBtn.setOnAction(e -> previewStage.close());
        
        buttonBox.getChildren().addAll(selectBtn, closeBtn);
        
        previewContent.getChildren().addAll(previewImageView, nameLabel, infoLabel, descArea, buttonBox);
        
        Scene previewScene = new Scene(previewContent);
        previewStage.setScene(previewScene);
        previewStage.showAndWait();
    }
    
    /**
     * 加载角色数据
     */
    private void loadCharacters() {
        try {
            // 从配置文件加载角色列表
            CharacterDataLoader loader = new CharacterDataLoader();
            List<ArkCharacter> characters = loader.loadAllCharacters();
            allCharacters.setAll(characters);
            
            // 选择上次使用的角色
            String lastSelected = ApplicationConfig.getInstance().getLastSelectedCharacter();
            if (lastSelected != null) {
                for (ArkCharacter character : characters) {
                    if (character.getCharacterName().equals(lastSelected)) {
                        characterListView.getSelectionModel().select(character);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            Logger.error("Failed to load characters", e);
            showErrorDialog("加载失败", "无法加载角色数据: " + e.getMessage());
        }
    }
    
    /**
     * 显示错误对话框
     * @param title 标题
     * @param message 消息
     */
    private void showErrorDialog(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    public void setMainWindow(MainWindowManager mainWindow) {
        this.mainWindow = mainWindow;
    }
}
```

### 3. 自定义角色列表单元格

#### CharacterListCell - 角色列表单元格
```java
public class CharacterListCell extends ListCell<ArkCharacter> {
    
    private HBox content;
    private ImageView icon;
    private VBox textContent;
    private Label nameLabel;
    private Label infoLabel;
    
    public CharacterListCell() {
        super();
        createContent();
    }
    
    private void createContent() {
        // 创建图标
        icon = new ImageView();
        icon.setFitWidth(48);
        icon.setFitHeight(48);
        icon.setPreserveRatio(true);
        
        // 创建文本内容
        nameLabel = new Label();
        nameLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");
        
        infoLabel = new Label();
        infoLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666666;");
        
        textContent = new VBox(2);
        textContent.getChildren().addAll(nameLabel, infoLabel);
        
        // 创建主容器
        content = new HBox(10);
        content.setAlignment(Pos.CENTER_LEFT);
        content.setPadding(new Insets(5));
        content.getChildren().addAll(icon, textContent);
    }
    
    @Override
    protected void updateItem(ArkCharacter character, boolean empty) {
        super.updateItem(character, empty);
        
        if (empty || character == null) {
            setGraphic(null);
        } else {
            nameLabel.setText(character.getDisplayName());
            infoLabel.setText(String.format("%s | %s | %s★", 
                character.getFaction(), character.getProfession(), character.getRarity()));
            
            // 加载角色图标
            loadCharacterIcon(character);
            
            setGraphic(content);
        }
    }
    
    private void loadCharacterIcon(ArkCharacter character) {
        try {
            String iconPath = "/images/characters/" + character.getCharacterName() + "_icon.png";
            Image iconImage = new Image(getClass().getResourceAsStream(iconPath));
            icon.setImage(iconImage);
        } catch (Exception e) {
            // 使用默认图标
            Image defaultIcon = new Image(getClass().getResourceAsStream("/images/default_icon.png"));
            icon.setImage(defaultIcon);
        }
    }
}
```

## 样式定义

### 主样式文件 (main.css)

```css
/* 主窗口样式 */
.root {
    -fx-background-color: #f5f5f5;
    -fx-font-family: "Microsoft YaHei", "SimHei", sans-serif;
}

/* 按钮样式 */
.button {
    -fx-background-color: #4CAF50;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-padding: 8 16;
    -fx-background-radius: 4;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #45a049;
}

.button:pressed {
    -fx-background-color: #3d8b40;
}

.button:disabled {
    -fx-background-color: #cccccc;
    -fx-text-fill: #666666;
    -fx-cursor: default;
}

/* 列表视图样式 */
.list-view {
    -fx-background-color: white;
    -fx-border-color: #ddd;
    -fx-border-width: 1;
    -fx-border-radius: 4;
}

.list-cell {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #eee;
}

.list-cell:selected {
    -fx-background-color: #e3f2fd;
}

.list-cell:hover {
    -fx-background-color: #f5f5f5;
}

/* 文本字段样式 */
.text-field {
    -fx-background-color: white;
    -fx-border-color: #ddd;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-padding: 8;
}

.text-field:focused {
    -fx-border-color: #4CAF50;
}

/* 组合框样式 */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #ddd;
    -fx-border-width: 1;
    -fx-border-radius: 4;
}

.combo-box:focused {
    -fx-border-color: #4CAF50;
}
```

---

**模块负责人**: UI组件开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
