# 文字互动模块 (Text Interaction Module)

## 模块概述

文字互动模块负责Ark-Pets AI Enhanced项目中用户与桌宠的文字交流功能，包括文字输入处理、对话界面管理、消息显示、表情符号支持、快捷回复等功能。提供直观友好的文字交互体验，支持多种输入方式和智能回复建议。

**核心职责**:
- 文字输入和输出处理
- 对话界面设计和管理
- 消息历史记录和搜索
- 表情符号和富文本支持
- 智能回复建议和快捷操作

## 核心功能架构

### 1. 文字互动架构

#### 分层文字互动架构模型
```
┌─────────────────────────────────────┐
│           交互界面层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 输入组件  │ 显示组件  │ 控制组件  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           处理逻辑层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 消息处理  │ 格式化   │ 智能建议  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           数据管理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 历史记录  │ 用户偏好  │ 缓存管理  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 文字互动流程

#### 文字交互处理流程图
```mermaid
graph TB
    subgraph "用户输入流程"
        UserInput[用户输入文字]
        ValidateInput[输入验证]
        ProcessText[文本处理]
        FormatMessage[消息格式化]
        SendToAI[发送给AI]
    end
    
    subgraph "AI响应流程"
        ReceiveResponse[接收AI响应]
        ProcessResponse[响应处理]
        FormatDisplay[显示格式化]
        UpdateHistory[更新历史]
        ShowMessage[显示消息]
    end
    
    subgraph "智能辅助流程"
        AnalyzeContext[分析上下文]
        GenerateSuggestions[生成建议]
        ShowQuickReplies[显示快捷回复]
        HandleEmojis[处理表情]
        UpdatePreferences[更新偏好]
    end
    
    UserInput --> ValidateInput
    ValidateInput --> ProcessText
    ProcessText --> FormatMessage
    FormatMessage --> SendToAI
    
    SendToAI --> ReceiveResponse
    ReceiveResponse --> ProcessResponse
    ProcessResponse --> FormatDisplay
    FormatDisplay --> UpdateHistory
    UpdateHistory --> ShowMessage
    
    ProcessText --> AnalyzeContext
    AnalyzeContext --> GenerateSuggestions
    GenerateSuggestions --> ShowQuickReplies
    ShowQuickReplies --> HandleEmojis
    HandleEmojis --> UpdatePreferences
```

## 核心类和接口

### 1. 文字互动主类

#### TextInteractionManager - 文字互动管理器
```java
/**
 * 文字互动管理器
 * 负责用户与桌宠的文字交流功能
 */
@Component
public class TextInteractionManager {
    
    private final ChatInputComponent chatInputComponent;
    private final MessageDisplayComponent messageDisplayComponent;
    private final ConversationHistoryManager historyManager;
    private final SmartSuggestionService suggestionService;
    private final EmojiManager emojiManager;
    private final TextFormattingService formattingService;
    private final UserPreferenceService preferenceService;
    private final AIServiceClient aiServiceClient;
    
    /**
     * 初始化文字互动界面
     */
    public void initializeTextInteraction() {
        // 1. 初始化输入组件
        initializeChatInput();
        
        // 2. 初始化消息显示组件
        initializeMessageDisplay();
        
        // 3. 加载历史对话
        loadConversationHistory();
        
        // 4. 设置事件监听器
        setupEventListeners();
        
        // 5. 初始化智能建议
        initializeSmartSuggestions();
        
        log.info("文字互动界面初始化完成");
    }
    
    /**
     * 处理用户文字输入
     * @param userInput 用户输入的文字
     */
    public void handleUserInput(String userInput) {
        try {
            // 1. 验证输入
            if (!validateInput(userInput)) {
                showInputError("输入内容无效");
                return;
            }
            
            // 2. 处理文本
            ProcessedText processedText = processUserText(userInput);
            
            // 3. 显示用户消息
            displayUserMessage(processedText);
            
            // 4. 发送给AI处理
            sendToAI(processedText);
            
            // 5. 更新输入建议
            updateInputSuggestions(processedText);
            
            // 6. 清空输入框
            clearInputField();
            
        } catch (Exception e) {
            log.error("处理用户输入失败: {}", userInput, e);
            showErrorMessage("消息发送失败，请重试");
        }
    }
    
    /**
     * 处理AI响应
     * @param aiResponse AI的响应
     */
    public void handleAIResponse(AIResponse aiResponse) {
        try {
            // 1. 处理响应内容
            ProcessedResponse processedResponse = processAIResponse(aiResponse);
            
            // 2. 格式化显示内容
            FormattedMessage formattedMessage = formatResponseForDisplay(processedResponse);
            
            // 3. 显示AI消息
            displayAIMessage(formattedMessage);
            
            // 4. 更新对话历史
            updateConversationHistory(formattedMessage);
            
            // 5. 生成快捷回复建议
            generateQuickReplySuggestions(processedResponse);
            
            // 6. 更新桌宠表情和动作
            updatePetExpressionAndAction(processedResponse);
            
        } catch (Exception e) {
            log.error("处理AI响应失败: {}", aiResponse, e);
            showErrorMessage("接收消息失败");
        }
    }
    
    /**
     * 显示用户消息
     * @param processedText 处理后的文本
     */
    private void displayUserMessage(ProcessedText processedText) {
        UserMessage userMessage = UserMessage.builder()
            .content(processedText.getFormattedText())
            .timestamp(LocalDateTime.now())
            .messageType(MessageType.USER)
            .emojis(processedText.getEmojis())
            .attachments(processedText.getAttachments())
            .build();
        
        messageDisplayComponent.addMessage(userMessage);
        
        // 滚动到最新消息
        messageDisplayComponent.scrollToBottom();
    }
    
    /**
     * 显示AI消息
     * @param formattedMessage 格式化的消息
     */
    private void displayAIMessage(FormattedMessage formattedMessage) {
        AIMessage aiMessage = AIMessage.builder()
            .content(formattedMessage.getContent())
            .timestamp(LocalDateTime.now())
            .messageType(MessageType.AI)
            .emotion(formattedMessage.getEmotion())
            .actions(formattedMessage.getActions())
            .suggestions(formattedMessage.getSuggestions())
            .build();
        
        // 使用打字机效果显示消息
        messageDisplayComponent.addMessageWithTypingEffect(aiMessage);
    }
    
    /**
     * 生成快捷回复建议
     * @param processedResponse 处理后的响应
     */
    private void generateQuickReplySuggestions(ProcessedResponse processedResponse) {
        List<QuickReply> suggestions = suggestionService.generateQuickReplies(
            processedResponse.getContext(),
            processedResponse.getEmotion(),
            preferenceService.getUserPreferences()
        );
        
        chatInputComponent.showQuickReplySuggestions(suggestions);
    }
    
    /**
     * 处理快捷回复选择
     * @param quickReply 选择的快捷回复
     */
    public void handleQuickReplySelection(QuickReply quickReply) {
        // 1. 设置输入框内容
        chatInputComponent.setInputText(quickReply.getText());
        
        // 2. 如果是自动发送，直接发送
        if (quickReply.isAutoSend()) {
            handleUserInput(quickReply.getText());
        }
        
        // 3. 记录使用统计
        suggestionService.recordQuickReplyUsage(quickReply);
    }
    
    /**
     * 处理表情符号输入
     * @param emoji 表情符号
     */
    public void handleEmojiInput(Emoji emoji) {
        // 1. 在当前光标位置插入表情
        chatInputComponent.insertEmojiAtCursor(emoji);
        
        // 2. 更新表情使用频率
        emojiManager.recordEmojiUsage(emoji);
        
        // 3. 更新最近使用的表情
        emojiManager.updateRecentEmojis(emoji);
    }
    
    /**
     * 搜索对话历史
     * @param searchQuery 搜索关键词
     * @return 搜索结果
     */
    public List<ConversationSearchResult> searchConversationHistory(String searchQuery) {
        try {
            return historyManager.searchConversations(searchQuery);
        } catch (Exception e) {
            log.error("搜索对话历史失败: {}", searchQuery, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 导出对话记录
     * @param exportRequest 导出请求
     * @return 导出结果
     */
    public ConversationExportResult exportConversation(ConversationExportRequest exportRequest) {
        try {
            return historyManager.exportConversation(exportRequest);
        } catch (Exception e) {
            log.error("导出对话记录失败: {}", exportRequest, e);
            throw new ConversationExportException("导出对话记录失败", e);
        }
    }
    
    /**
     * 设置文字互动偏好
     * @param preferences 偏好设置
     */
    public void setTextInteractionPreferences(TextInteractionPreferences preferences) {
        try {
            // 1. 保存偏好设置
            preferenceService.saveTextInteractionPreferences(preferences);
            
            // 2. 应用字体设置
            applyFontSettings(preferences.getFontSettings());
            
            // 3. 应用主题设置
            applyThemeSettings(preferences.getThemeSettings());
            
            // 4. 应用行为设置
            applyBehaviorSettings(preferences.getBehaviorSettings());
            
            log.info("文字互动偏好设置已更新");
            
        } catch (Exception e) {
            log.error("设置文字互动偏好失败: {}", preferences, e);
            throw new PreferenceUpdateException("设置文字互动偏好失败", e);
        }
    }
    
    private boolean validateInput(String input) {
        return StringUtils.isNotBlank(input) && 
               input.length() <= MAX_MESSAGE_LENGTH &&
               !containsProhibitedContent(input);
    }
    
    private ProcessedText processUserText(String userInput) {
        return ProcessedText.builder()
            .originalText(userInput)
            .formattedText(formattingService.formatUserText(userInput))
            .emojis(emojiManager.extractEmojis(userInput))
            .mentions(extractMentions(userInput))
            .attachments(extractAttachments(userInput))
            .build();
    }
    
    private void sendToAI(ProcessedText processedText) {
        ChatRequest chatRequest = ChatRequest.builder()
            .message(processedText.getFormattedText())
            .context(historyManager.getCurrentContext())
            .userPreferences(preferenceService.getUserPreferences())
            .build();
        
        // 异步发送给AI服务
        aiServiceClient.sendChatRequestAsync(chatRequest)
            .thenAccept(this::handleAIResponse)
            .exceptionally(throwable -> {
                log.error("AI请求失败", throwable);
                showErrorMessage("AI服务暂时不可用");
                return null;
            });
    }
}
```

### 2. 聊天输入组件

#### ChatInputComponent - 聊天输入组件
```java
/**
 * 聊天输入组件
 * 负责用户文字输入的界面和交互
 */
@Component
public class ChatInputComponent extends JPanel {

    private JTextArea inputTextArea;
    private JButton sendButton;
    private JButton emojiButton;
    private JPanel quickReplyPanel;
    private EmojiPickerDialog emojiPicker;
    private AutoCompletePopup autoCompletePopup;

    /**
     * 初始化输入组件
     */
    public void initializeComponent() {
        setLayout(new BorderLayout());

        // 1. 创建输入区域
        createInputArea();

        // 2. 创建按钮面板
        createButtonPanel();

        // 3. 创建快捷回复面板
        createQuickReplyPanel();

        // 4. 设置事件监听器
        setupEventListeners();

        // 5. 应用样式
        applyStyles();
    }

    /**
     * 设置输入文本
     * @param text 要设置的文本
     */
    public void setInputText(String text) {
        SwingUtilities.invokeLater(() -> {
            inputTextArea.setText(text);
            inputTextArea.setCaretPosition(text.length());
        });
    }

    /**
     * 获取输入文本
     * @return 当前输入的文本
     */
    public String getInputText() {
        return inputTextArea.getText();
    }

    /**
     * 清空输入框
     */
    public void clearInput() {
        SwingUtilities.invokeLater(() -> {
            inputTextArea.setText("");
            inputTextArea.requestFocus();
        });
    }

    /**
     * 在光标位置插入表情
     * @param emoji 要插入的表情
     */
    public void insertEmojiAtCursor(Emoji emoji) {
        SwingUtilities.invokeLater(() -> {
            int caretPosition = inputTextArea.getCaretPosition();
            String currentText = inputTextArea.getText();
            String newText = currentText.substring(0, caretPosition) +
                           emoji.getUnicode() +
                           currentText.substring(caretPosition);

            inputTextArea.setText(newText);
            inputTextArea.setCaretPosition(caretPosition + emoji.getUnicode().length());
        });
    }

    /**
     * 显示快捷回复建议
     * @param quickReplies 快捷回复列表
     */
    public void showQuickReplySuggestions(List<QuickReply> quickReplies) {
        SwingUtilities.invokeLater(() -> {
            quickReplyPanel.removeAll();

            for (QuickReply reply : quickReplies) {
                JButton replyButton = createQuickReplyButton(reply);
                quickReplyPanel.add(replyButton);
            }

            quickReplyPanel.revalidate();
            quickReplyPanel.repaint();
            quickReplyPanel.setVisible(!quickReplies.isEmpty());
        });
    }

    private JButton createQuickReplyButton(QuickReply reply) {
        JButton button = new JButton(reply.getDisplayText());
        button.setToolTipText(reply.getTooltip());
        button.addActionListener(e -> {
            if (quickReplySelectionListener != null) {
                quickReplySelectionListener.onQuickReplySelected(reply);
            }
        });

        // 应用快捷回复按钮样式
        styleQuickReplyButton(button);

        return button;
    }
}
```

## 数据模型定义

### 1. 消息相关模型

#### UserMessage - 用户消息模型
```java
/**
 * 用户消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMessage {
    private String content;
    private LocalDateTime timestamp;
    private MessageType messageType;
    private List<Emoji> emojis;
    private List<MessageAttachment> attachments;
    private String messageId;
    private MessageStatus status;
}
```

#### AIMessage - AI消息模型
```java
/**
 * AI消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIMessage {
    private String content;
    private LocalDateTime timestamp;
    private MessageType messageType;
    private EmotionType emotion;
    private List<PetAction> actions;
    private List<QuickReply> suggestions;
    private String messageId;
    private Double confidence;
}
```

#### QuickReply - 快捷回复模型
```java
/**
 * 快捷回复模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuickReply {
    private String id;
    private String text;
    private String displayText;
    private String tooltip;
    private String category;
    private boolean autoSend;
    private int usageCount;
    private LocalDateTime lastUsed;
}
```

## 使用示例

### 基本文字互动操作
```java
// 初始化文字互动管理器
TextInteractionManager textManager = new TextInteractionManager();
textManager.initializeTextInteraction();

// 处理用户输入
textManager.handleUserInput("你好，今天天气怎么样？");

// 设置快捷回复选择监听器
textManager.setQuickReplySelectionListener(quickReply -> {
    System.out.println("用户选择了快捷回复: " + quickReply.getText());
});

// 搜索对话历史
List<ConversationSearchResult> results = textManager.searchConversationHistory("天气");
System.out.println("找到 " + results.size() + " 条相关对话");

// 设置文字互动偏好
TextInteractionPreferences preferences = TextInteractionPreferences.builder()
    .fontSettings(FontSettings.builder()
        .fontFamily("微软雅黑")
        .fontSize(14)
        .fontColor(Color.BLACK)
        .build())
    .themeSettings(ThemeSettings.builder()
        .themeName("默认主题")
        .backgroundColor(Color.WHITE)
        .messageBackgroundColor(Color.LIGHT_GRAY)
        .build())
    .behaviorSettings(BehaviorSettings.builder()
        .enableAutoComplete(true)
        .enableQuickReplies(true)
        .enableTypingIndicator(true)
        .maxMessageLength(1000)
        .build())
    .build();

textManager.setTextInteractionPreferences(preferences);

// 导出对话记录
ConversationExportRequest exportRequest = ConversationExportRequest.builder()
    .startDate(LocalDate.now().minusDays(7))
    .endDate(LocalDate.now())
    .format(ExportFormat.JSON)
    .includeEmojis(true)
    .includeTimestamps(true)
    .build();

ConversationExportResult exportResult = textManager.exportConversation(exportRequest);
System.out.println("对话记录已导出到: " + exportResult.getFilePath());
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的文字互动模块实现，支持文字输入输出、智能建议、表情符号、对话历史等功能
