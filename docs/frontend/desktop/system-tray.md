# 系统托盘 (System Tray)

## 模块概述

系统托盘模块负责管理应用在系统托盘中的显示和交互，包括托盘图标、右键菜单、通知消息等功能。基于原始Ark-Pets项目的托盘功能进行扩展。

**基于原始Ark-Pets项目分析**:
- 原项目实现了基础的系统托盘功能
- 支持托盘图标显示和右键菜单
- 包含基本的应用控制功能
- 扩展增加AI交互和通知功能

## 核心功能

### 1. 系统托盘管理器 (SystemTrayManager) - 基于原始项目

#### 功能描述
管理应用在系统托盘中的所有功能，包括图标显示、菜单管理、事件处理等。

#### 核心函数

```java
public class SystemTrayManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemTrayManager.class);
    
    private SystemTray systemTray;
    private TrayIcon trayIcon;
    private PopupMenu trayMenu;
    private boolean isInitialized = false;
    private ApplicationController appController;
    private NotificationManager notificationManager;
    
    /**
     * 初始化系统托盘
     * @param appController 应用控制器
     * @return 初始化是否成功
     */
    public boolean initializeSystemTray(ApplicationController appController) {
        this.appController = appController;
        
        try {
            // 检查系统是否支持托盘
            if (!SystemTray.isSupported()) {
                logger.warn("系统不支持托盘功能");
                return false;
            }
            
            systemTray = SystemTray.getSystemTray();
            
            // 创建托盘图标
            if (!createTrayIcon()) {
                return false;
            }
            
            // 创建托盘菜单
            createTrayMenu();
            
            // 设置事件监听器
            setupEventListeners();
            
            // 添加到系统托盘
            systemTray.add(trayIcon);
            
            // 初始化通知管理器
            notificationManager = new NotificationManager(trayIcon);
            
            isInitialized = true;
            logger.info("系统托盘初始化成功");
            return true;
            
        } catch (Exception e) {
            logger.error("系统托盘初始化失败", e);
            return false;
        }
    }
    
    /**
     * 创建托盘图标
     * @return 创建是否成功
     */
    private boolean createTrayIcon() {
        try {
            // 加载托盘图标
            Image iconImage = loadTrayIcon();
            if (iconImage == null) {
                logger.error("无法加载托盘图标");
                return false;
            }
            
            // 创建托盘图标对象
            trayIcon = new TrayIcon(iconImage, "Ark-Pets AI Enhanced");
            trayIcon.setImageAutoSize(true);
            trayIcon.setToolTip("Ark-Pets AI Enhanced - 智能桌宠");
            
            return true;
        } catch (Exception e) {
            logger.error("创建托盘图标失败", e);
            return false;
        }
    }
    
    /**
     * 加载托盘图标
     * @return 图标图像
     */
    private Image loadTrayIcon() {
        try {
            // 根据系统DPI选择合适的图标尺寸
            Dimension trayIconSize = systemTray.getTrayIconSize();
            int iconSize = Math.max(trayIconSize.width, trayIconSize.height);
            
            String iconPath;
            if (iconSize <= 16) {
                iconPath = "/images/tray/icon_16.png";
            } else if (iconSize <= 24) {
                iconPath = "/images/tray/icon_24.png";
            } else if (iconSize <= 32) {
                iconPath = "/images/tray/icon_32.png";
            } else {
                iconPath = "/images/tray/icon_48.png";
            }
            
            URL iconURL = getClass().getResource(iconPath);
            if (iconURL != null) {
                return Toolkit.getDefaultToolkit().getImage(iconURL);
            } else {
                // 使用默认图标
                return createDefaultIcon(iconSize);
            }
        } catch (Exception e) {
            logger.error("加载托盘图标失败", e);
            return createDefaultIcon(16);
        }
    }
    
    /**
     * 创建默认图标
     * @param size 图标尺寸
     * @return 默认图标
     */
    private Image createDefaultIcon(int size) {
        BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制圆形背景
        g2d.setColor(new Color(52, 152, 219));
        g2d.fillOval(2, 2, size - 4, size - 4);
        
        // 绘制字母A
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Arial", Font.BOLD, size / 2));
        FontMetrics fm = g2d.getFontMetrics();
        String text = "A";
        int x = (size - fm.stringWidth(text)) / 2;
        int y = (size - fm.getHeight()) / 2 + fm.getAscent();
        g2d.drawString(text, x, y);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 创建托盘菜单
     */
    private void createTrayMenu() {
        trayMenu = new PopupMenu();
        
        // 角色管理菜单
        Menu characterMenu = new Menu("角色管理");
        
        MenuItem selectCharacter = new MenuItem("选择角色");
        selectCharacter.addActionListener(e -> appController.showCharacterSelection());
        characterMenu.add(selectCharacter);
        
        MenuItem characterInfo = new MenuItem("角色信息");
        characterInfo.addActionListener(e -> appController.showCharacterInfo());
        characterMenu.add(characterInfo);
        
        characterMenu.addSeparator();
        
        MenuItem hideCharacter = new MenuItem("隐藏角色");
        hideCharacter.addActionListener(e -> appController.hideCharacter());
        characterMenu.add(hideCharacter);
        
        MenuItem showCharacter = new MenuItem("显示角色");
        showCharacter.addActionListener(e -> appController.showCharacter());
        characterMenu.add(showCharacter);
        
        trayMenu.add(characterMenu);
        
        // AI交互菜单 (新增)
        Menu aiMenu = new Menu("AI交互");
        
        MenuItem startConversation = new MenuItem("开始对话");
        startConversation.addActionListener(e -> appController.startAIConversation());
        aiMenu.add(startConversation);
        
        MenuItem conversationHistory = new MenuItem("对话历史");
        conversationHistory.addActionListener(e -> appController.showConversationHistory());
        aiMenu.add(conversationHistory);
        
        aiMenu.addSeparator();
        
        MenuItem personalitySettings = new MenuItem("性格设置");
        personalitySettings.addActionListener(e -> appController.showPersonalitySettings());
        aiMenu.add(personalitySettings);
        
        MenuItem voiceSettings = new MenuItem("语音设置");
        voiceSettings.addActionListener(e -> appController.showVoiceSettings());
        aiMenu.add(voiceSettings);
        
        trayMenu.add(aiMenu);
        
        // 设置菜单
        Menu settingsMenu = new Menu("设置");
        
        MenuItem generalSettings = new MenuItem("常规设置");
        generalSettings.addActionListener(e -> appController.showGeneralSettings());
        settingsMenu.add(generalSettings);
        
        MenuItem displaySettings = new MenuItem("显示设置");
        displaySettings.addActionListener(e -> appController.showDisplaySettings());
        settingsMenu.add(displaySettings);
        
        MenuItem physicsSettings = new MenuItem("物理设置");
        physicsSettings.addActionListener(e -> appController.showPhysicsSettings());
        settingsMenu.add(physicsSettings);
        
        settingsMenu.addSeparator();
        
        MenuItem aiServiceSettings = new MenuItem("AI服务设置");
        aiServiceSettings.addActionListener(e -> appController.showAIServiceSettings());
        settingsMenu.add(aiServiceSettings);
        
        trayMenu.add(settingsMenu);
        
        trayMenu.addSeparator();
        
        // 工具菜单
        MenuItem aboutItem = new MenuItem("关于");
        aboutItem.addActionListener(e -> appController.showAbout());
        trayMenu.add(aboutItem);
        
        MenuItem helpItem = new MenuItem("帮助");
        helpItem.addActionListener(e -> appController.showHelp());
        trayMenu.add(helpItem);
        
        trayMenu.addSeparator();
        
        // 退出菜单
        MenuItem exitItem = new MenuItem("退出");
        exitItem.addActionListener(e -> appController.exitApplication());
        trayMenu.add(exitItem);
        
        // 设置菜单到托盘图标
        trayIcon.setPopupMenu(trayMenu);
    }
    
    /**
     * 设置事件监听器
     */
    private void setupEventListeners() {
        // 双击事件 - 显示主窗口或角色选择
        trayIcon.addActionListener(e -> {
            if (appController.hasActiveCharacter()) {
                appController.showCharacterInfo();
            } else {
                appController.showCharacterSelection();
            }
        });
        
        // 鼠标事件
        trayIcon.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getButton() == MouseEvent.BUTTON1 && e.getClickCount() == 2) {
                    // 双击左键
                    handleDoubleClick();
                } else if (e.getButton() == MouseEvent.BUTTON2) {
                    // 中键点击 - 快速AI对话
                    appController.quickAIInteraction();
                }
            }
        });
    }
    
    /**
     * 处理双击事件
     */
    private void handleDoubleClick() {
        if (appController.hasActiveCharacter()) {
            // 如果有活动角色，切换显示/隐藏
            if (appController.isCharacterVisible()) {
                appController.hideCharacter();
            } else {
                appController.showCharacter();
            }
        } else {
            // 如果没有活动角色，显示角色选择
            appController.showCharacterSelection();
        }
    }
    
    /**
     * 显示通知消息
     * @param title 标题
     * @param message 消息内容
     * @param messageType 消息类型
     */
    public void showNotification(String title, String message, TrayIcon.MessageType messageType) {
        if (isInitialized && trayIcon != null) {
            trayIcon.displayMessage(title, message, messageType);
        }
    }
    
    /**
     * 显示信息通知
     * @param title 标题
     * @param message 消息内容
     */
    public void showInfoNotification(String title, String message) {
        showNotification(title, message, TrayIcon.MessageType.INFO);
    }
    
    /**
     * 显示警告通知
     * @param title 标题
     * @param message 消息内容
     */
    public void showWarningNotification(String title, String message) {
        showNotification(title, message, TrayIcon.MessageType.WARNING);
    }
    
    /**
     * 显示错误通知
     * @param title 标题
     * @param message 消息内容
     */
    public void showErrorNotification(String title, String message) {
        showNotification(title, message, TrayIcon.MessageType.ERROR);
    }
    
    /**
     * 更新托盘图标
     * @param iconPath 新图标路径
     */
    public void updateTrayIcon(String iconPath) {
        try {
            URL iconURL = getClass().getResource(iconPath);
            if (iconURL != null && trayIcon != null) {
                Image newIcon = Toolkit.getDefaultToolkit().getImage(iconURL);
                trayIcon.setImage(newIcon);
            }
        } catch (Exception e) {
            logger.error("更新托盘图标失败", e);
        }
    }
    
    /**
     * 更新托盘提示文本
     * @param tooltip 新的提示文本
     */
    public void updateTooltip(String tooltip) {
        if (trayIcon != null) {
            trayIcon.setToolTip(tooltip);
        }
    }
    
    /**
     * 启用/禁用菜单项
     * @param menuItemText 菜单项文本
     * @param enabled 是否启用
     */
    public void setMenuItemEnabled(String menuItemText, boolean enabled) {
        if (trayMenu != null) {
            for (int i = 0; i < trayMenu.getItemCount(); i++) {
                MenuItem item = trayMenu.getItem(i);
                if (item != null && menuItemText.equals(item.getLabel())) {
                    item.setEnabled(enabled);
                    break;
                }
            }
        }
    }
    
    /**
     * 清理系统托盘
     */
    public void cleanup() {
        try {
            if (systemTray != null && trayIcon != null) {
                systemTray.remove(trayIcon);
            }
            
            if (notificationManager != null) {
                notificationManager.cleanup();
            }
            
            isInitialized = false;
            logger.info("系统托盘清理完成");
        } catch (Exception e) {
            logger.error("系统托盘清理失败", e);
        }
    }
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
}
```

### 2. 通知管理器 (NotificationManager)

#### 功能描述
管理系统通知的显示和队列，支持不同类型的通知消息。

#### 核心函数

```java
public class NotificationManager {
    
    private final TrayIcon trayIcon;
    private final Queue<NotificationInfo> notificationQueue;
    private final Timer notificationTimer;
    private boolean isShowingNotification = false;
    
    public NotificationManager(TrayIcon trayIcon) {
        this.trayIcon = trayIcon;
        this.notificationQueue = new LinkedList<>();
        this.notificationTimer = new Timer(3000, this::processNextNotification);
    }
    
    /**
     * 添加通知到队列
     * @param title 标题
     * @param message 消息
     * @param type 类型
     * @param priority 优先级
     */
    public void addNotification(String title, String message, 
                              TrayIcon.MessageType type, NotificationPriority priority) {
        NotificationInfo notification = new NotificationInfo(title, message, type, priority);
        
        // 根据优先级插入队列
        if (priority == NotificationPriority.HIGH) {
            ((LinkedList<NotificationInfo>) notificationQueue).addFirst(notification);
        } else {
            notificationQueue.offer(notification);
        }
        
        // 如果当前没有显示通知，立即处理
        if (!isShowingNotification) {
            processNextNotification(null);
        }
    }
    
    /**
     * 处理下一个通知
     * @param event 定时器事件
     */
    private void processNextNotification(ActionEvent event) {
        if (!notificationQueue.isEmpty()) {
            NotificationInfo notification = notificationQueue.poll();
            showNotificationNow(notification);
        } else {
            isShowingNotification = false;
            notificationTimer.stop();
        }
    }
    
    /**
     * 立即显示通知
     * @param notification 通知信息
     */
    private void showNotificationNow(NotificationInfo notification) {
        isShowingNotification = true;
        trayIcon.displayMessage(notification.title, notification.message, notification.type);
        
        // 启动定时器处理下一个通知
        notificationTimer.restart();
    }
    
    /**
     * 清理通知管理器
     */
    public void cleanup() {
        notificationTimer.stop();
        notificationQueue.clear();
    }
    
    /**
     * 通知信息类
     */
    private static class NotificationInfo {
        final String title;
        final String message;
        final TrayIcon.MessageType type;
        final NotificationPriority priority;
        
        NotificationInfo(String title, String message, 
                        TrayIcon.MessageType type, NotificationPriority priority) {
            this.title = title;
            this.message = message;
            this.type = type;
            this.priority = priority;
        }
    }
    
    /**
     * 通知优先级枚举
     */
    public enum NotificationPriority {
        LOW, NORMAL, HIGH
    }
}
```

## 托盘功能流程

### 托盘初始化流程

```mermaid
graph TD
    A[应用启动] --> B[检查系统托盘支持]
    B --> C{支持托盘?}
    C -->|否| D[跳过托盘功能]
    C -->|是| E[创建托盘图标]
    E --> F[创建托盘菜单]
    F --> G[设置事件监听器]
    G --> H[添加到系统托盘]
    H --> I[初始化通知管理器]
    I --> J[托盘功能就绪]
```

### 通知处理流程

```mermaid
graph TD
    A[接收通知请求] --> B[创建通知信息]
    B --> C{当前是否在显示通知?}
    C -->|是| D[添加到队列]
    C -->|否| E[立即显示通知]
    E --> F[启动定时器]
    F --> G[等待显示时间]
    G --> H[处理下一个通知]
    H --> I{队列是否为空?}
    I -->|否| E
    I -->|是| J[停止通知处理]
    D --> I
```

## 配置参数

### 托盘配置 (tray-config.json)

```json
{
  "tray": {
    "enabled": true,
    "icon": {
      "path": "/images/tray/",
      "autoSize": true,
      "sizes": [16, 24, 32, 48]
    },
    "tooltip": "Ark-Pets AI Enhanced",
    "doubleClickAction": "toggle_character",
    "middleClickAction": "quick_ai_chat"
  },
  "notifications": {
    "enabled": true,
    "displayTime": 3000,
    "maxQueueSize": 10,
    "showAIResponses": true,
    "showSystemMessages": true
  },
  "menu": {
    "showCharacterSubmenu": true,
    "showAISubmenu": true,
    "showSettingsSubmenu": true,
    "customItems": []
  }
}
```

## 错误处理

### 异常类型

```java
public class TrayException extends Exception {
    public static class TrayNotSupportedException extends TrayException {}
    public static class TrayIconCreationException extends TrayException {}
    public static class TrayMenuException extends TrayException {}
}
```

### 错误处理策略

1. **托盘不支持**: 禁用托盘功能，使用替代方案
2. **图标加载失败**: 使用默认生成的图标
3. **菜单创建失败**: 使用简化菜单
4. **通知显示失败**: 记录日志，继续处理队列

---

**模块负责人**: 系统托盘开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
