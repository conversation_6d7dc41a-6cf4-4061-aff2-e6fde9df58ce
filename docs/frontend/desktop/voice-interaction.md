# 语音互动模块 (Voice Interaction Module)

## 模块概述

语音互动模块负责Ark-Pets AI Enhanced项目中用户与桌宠的语音交流功能，包括语音识别、语音合成、音频处理、语音指令识别、多语言支持等功能。提供自然流畅的语音交互体验，支持实时语音对话和离线语音处理。

**核心职责**:
- 语音输入识别和处理
- 语音输出合成和播放
- 音频信号处理和降噪
- 语音指令识别和执行
- 多语言和方言支持

## 核心功能架构

### 1. 语音互动架构

#### 分层语音互动架构模型
```
┌─────────────────────────────────────┐
│           语音交互层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 语音识别  │ 语音合成  │ 指令处理  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           音频处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 信号处理  │ 降噪算法  │ 音效增强  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           设备适配层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 麦克风   │ 扬声器   │ 音频驱动  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 语音互动流程

#### 语音交互处理流程图
```mermaid
graph TB
    subgraph "语音输入流程"
        VoiceInput[语音输入]
        AudioCapture[音频捕获]
        NoiseReduction[降噪处理]
        SpeechRecognition[语音识别]
        TextProcessing[文本处理]
    end
    
    subgraph "语音输出流程"
        TextToSpeech[文本转语音]
        VoiceSynthesis[语音合成]
        AudioProcessing[音频处理]
        VoiceOutput[语音输出]
        EmotionExpression[情感表达]
    end
    
    subgraph "指令处理流程"
        CommandRecognition[指令识别]
        IntentAnalysis[意图分析]
        ActionExecution[动作执行]
        FeedbackGeneration[反馈生成]
        ResponseSynthesis[响应合成]
    end
    
    VoiceInput --> AudioCapture
    AudioCapture --> NoiseReduction
    NoiseReduction --> SpeechRecognition
    SpeechRecognition --> TextProcessing
    
    TextProcessing --> CommandRecognition
    CommandRecognition --> IntentAnalysis
    IntentAnalysis --> ActionExecution
    ActionExecution --> FeedbackGeneration
    FeedbackGeneration --> ResponseSynthesis
    
    ResponseSynthesis --> TextToSpeech
    TextToSpeech --> VoiceSynthesis
    VoiceSynthesis --> AudioProcessing
    AudioProcessing --> VoiceOutput
    VoiceOutput --> EmotionExpression
```

## 核心类和接口

### 1. 语音互动主类

#### VoiceInteractionManager - 语音互动管理器
```java
/**
 * 语音互动管理器
 * 负责用户与桌宠的语音交流功能
 */
@Component
public class VoiceInteractionManager {
    
    private final SpeechRecognitionService speechRecognitionService;
    private final TextToSpeechService textToSpeechService;
    private final AudioProcessingService audioProcessingService;
    private final VoiceCommandProcessor voiceCommandProcessor;
    private final AudioDeviceManager audioDeviceManager;
    private final VoicePreferenceService voicePreferenceService;
    private final EmotionVoiceService emotionVoiceService;
    
    private boolean isListening = false;
    private boolean isSpeaking = false;
    private AudioInputStream currentAudioStream;
    
    /**
     * 初始化语音互动系统
     */
    public void initializeVoiceInteraction() {
        try {
            // 1. 初始化音频设备
            audioDeviceManager.initializeAudioDevices();
            
            // 2. 初始化语音识别服务
            speechRecognitionService.initialize();
            
            // 3. 初始化语音合成服务
            textToSpeechService.initialize();
            
            // 4. 加载语音偏好设置
            loadVoicePreferences();
            
            // 5. 设置事件监听器
            setupVoiceEventListeners();
            
            log.info("语音互动系统初始化完成");
            
        } catch (Exception e) {
            log.error("语音互动系统初始化失败", e);
            throw new VoiceInteractionInitException("语音互动系统初始化失败", e);
        }
    }
    
    /**
     * 开始语音监听
     */
    public void startListening() {
        if (isListening) {
            log.warn("语音监听已经在进行中");
            return;
        }
        
        try {
            // 1. 检查麦克风权限
            if (!audioDeviceManager.hasMicrophonePermission()) {
                requestMicrophonePermission();
                return;
            }
            
            // 2. 开始音频捕获
            currentAudioStream = audioDeviceManager.startAudioCapture();
            
            // 3. 设置监听状态
            isListening = true;
            
            // 4. 启动语音识别
            speechRecognitionService.startRecognition(currentAudioStream, new SpeechRecognitionCallback() {
                @Override
                public void onRecognitionResult(SpeechRecognitionResult result) {
                    handleSpeechRecognitionResult(result);
                }
                
                @Override
                public void onRecognitionError(SpeechRecognitionError error) {
                    handleSpeechRecognitionError(error);
                }
                
                @Override
                public void onRecognitionComplete() {
                    stopListening();
                }
            });
            
            // 5. 显示监听状态
            showListeningIndicator();
            
            log.info("开始语音监听");
            
        } catch (Exception e) {
            log.error("开始语音监听失败", e);
            isListening = false;
            showVoiceError("无法开始语音监听");
        }
    }
    
    /**
     * 停止语音监听
     */
    public void stopListening() {
        if (!isListening) {
            return;
        }
        
        try {
            // 1. 停止语音识别
            speechRecognitionService.stopRecognition();
            
            // 2. 停止音频捕获
            if (currentAudioStream != null) {
                audioDeviceManager.stopAudioCapture(currentAudioStream);
                currentAudioStream = null;
            }
            
            // 3. 更新状态
            isListening = false;
            
            // 4. 隐藏监听指示器
            hideListeningIndicator();
            
            log.info("停止语音监听");
            
        } catch (Exception e) {
            log.error("停止语音监听失败", e);
        }
    }
    
    /**
     * 语音回复
     * @param text 要转换为语音的文本
     * @param emotion 情感类型
     */
    public void speakText(String text, EmotionType emotion) {
        if (isSpeaking) {
            // 如果正在说话，先停止当前语音
            stopSpeaking();
        }
        
        try {
            // 1. 处理文本内容
            ProcessedText processedText = processTextForSpeech(text);
            
            // 2. 根据情感选择语音参数
            VoiceParameters voiceParams = emotionVoiceService.getVoiceParameters(emotion);
            
            // 3. 生成语音
            AudioData audioData = textToSpeechService.synthesizeSpeech(
                processedText.getText(), 
                voiceParams
            );
            
            // 4. 音频后处理
            AudioData processedAudio = audioProcessingService.processAudio(audioData);
            
            // 5. 播放语音
            isSpeaking = true;
            showSpeakingIndicator();
            
            audioDeviceManager.playAudio(processedAudio, new AudioPlaybackCallback() {
                @Override
                public void onPlaybackComplete() {
                    isSpeaking = false;
                    hideSpeakingIndicator();
                }
                
                @Override
                public void onPlaybackError(AudioPlaybackError error) {
                    isSpeaking = false;
                    hideSpeakingIndicator();
                    log.error("语音播放失败", error);
                }
            });
            
            log.info("开始语音回复: {}", text);
            
        } catch (Exception e) {
            log.error("语音回复失败: {}", text, e);
            isSpeaking = false;
            showVoiceError("语音回复失败");
        }
    }
    
    /**
     * 停止语音播放
     */
    public void stopSpeaking() {
        if (!isSpeaking) {
            return;
        }
        
        try {
            audioDeviceManager.stopAudioPlayback();
            isSpeaking = false;
            hideSpeakingIndicator();
            
            log.info("停止语音播放");
            
        } catch (Exception e) {
            log.error("停止语音播放失败", e);
        }
    }
    
    /**
     * 处理语音识别结果
     * @param result 识别结果
     */
    private void handleSpeechRecognitionResult(SpeechRecognitionResult result) {
        try {
            String recognizedText = result.getText();
            double confidence = result.getConfidence();
            
            log.info("语音识别结果: {} (置信度: {})", recognizedText, confidence);
            
            // 1. 检查置信度
            if (confidence < voicePreferenceService.getMinConfidenceThreshold()) {
                handleLowConfidenceRecognition(result);
                return;
            }
            
            // 2. 检查是否为语音指令
            if (voiceCommandProcessor.isVoiceCommand(recognizedText)) {
                VoiceCommand command = voiceCommandProcessor.parseVoiceCommand(recognizedText);
                executeVoiceCommand(command);
                return;
            }
            
            // 3. 作为普通对话处理
            handleVoiceConversation(recognizedText);
            
        } catch (Exception e) {
            log.error("处理语音识别结果失败", e);
            showVoiceError("语音识别处理失败");
        }
    }
    
    /**
     * 执行语音指令
     * @param command 语音指令
     */
    private void executeVoiceCommand(VoiceCommand command) {
        try {
            VoiceCommandResult result = voiceCommandProcessor.executeCommand(command);
            
            if (result.isSuccess()) {
                // 语音反馈执行成功
                speakText(result.getSuccessMessage(), EmotionType.HAPPY);
            } else {
                // 语音反馈执行失败
                speakText(result.getErrorMessage(), EmotionType.CONFUSED);
            }
            
        } catch (Exception e) {
            log.error("执行语音指令失败: {}", command, e);
            speakText("抱歉，我无法执行这个指令", EmotionType.APOLOGETIC);
        }
    }
    
    /**
     * 处理语音对话
     * @param text 识别的文本
     */
    private void handleVoiceConversation(String text) {
        // 发送到AI服务进行处理
        ChatRequest chatRequest = ChatRequest.builder()
            .message(text)
            .inputType(InputType.VOICE)
            .context(getConversationContext())
            .build();
        
        aiServiceClient.sendChatRequestAsync(chatRequest)
            .thenAccept(response -> {
                // 语音回复AI的响应
                EmotionType emotion = determineEmotionFromResponse(response);
                speakText(response.getMessage(), emotion);
            })
            .exceptionally(throwable -> {
                log.error("AI语音对话处理失败", throwable);
                speakText("抱歉，我现在无法理解你说的话", EmotionType.CONFUSED);
                return null;
            });
    }
    
    /**
     * 设置语音偏好
     * @param preferences 语音偏好设置
     */
    public void setVoicePreferences(VoicePreferences preferences) {
        try {
            // 1. 保存偏好设置
            voicePreferenceService.saveVoicePreferences(preferences);
            
            // 2. 应用语音设置
            applyVoiceSettings(preferences.getVoiceSettings());
            
            // 3. 应用识别设置
            applyRecognitionSettings(preferences.getRecognitionSettings());
            
            // 4. 应用音频设置
            applyAudioSettings(preferences.getAudioSettings());
            
            log.info("语音偏好设置已更新");
            
        } catch (Exception e) {
            log.error("设置语音偏好失败", e);
            throw new VoicePreferenceException("设置语音偏好失败", e);
        }
    }
    
    /**
     * 获取可用的语音列表
     * @return 语音列表
     */
    public List<VoiceInfo> getAvailableVoices() {
        return textToSpeechService.getAvailableVoices();
    }
    
    /**
     * 测试语音
     * @param voiceId 语音ID
     * @param testText 测试文本
     */
    public void testVoice(String voiceId, String testText) {
        try {
            VoiceParameters testParams = VoiceParameters.builder()
                .voiceId(voiceId)
                .speed(1.0f)
                .pitch(1.0f)
                .volume(0.8f)
                .build();
            
            AudioData audioData = textToSpeechService.synthesizeSpeech(testText, testParams);
            audioDeviceManager.playAudio(audioData, null);
            
        } catch (Exception e) {
            log.error("测试语音失败: voiceId={}", voiceId, e);
            showVoiceError("语音测试失败");
        }
    }
}
```

### 2. 语音识别服务

#### SpeechRecognitionService - 语音识别服务
```java
/**
 * 语音识别服务
 * 负责将语音转换为文本
 */
@Service
public class SpeechRecognitionService {

    private final SpeechRecognitionEngine recognitionEngine;
    private final AudioPreprocessor audioPreprocessor;
    private final LanguageModelService languageModelService;

    /**
     * 开始语音识别
     * @param audioStream 音频流
     * @param callback 识别回调
     */
    public void startRecognition(AudioInputStream audioStream, SpeechRecognitionCallback callback) {
        try {
            // 1. 音频预处理
            AudioInputStream processedStream = audioPreprocessor.preprocess(audioStream);

            // 2. 启动识别引擎
            recognitionEngine.startRecognition(processedStream, new RecognitionEngineCallback() {
                @Override
                public void onPartialResult(String partialText) {
                    // 实时识别结果
                    callback.onPartialResult(partialText);
                }

                @Override
                public void onFinalResult(String finalText, double confidence) {
                    // 最终识别结果
                    SpeechRecognitionResult result = SpeechRecognitionResult.builder()
                        .text(finalText)
                        .confidence(confidence)
                        .language(getCurrentLanguage())
                        .timestamp(LocalDateTime.now())
                        .build();

                    callback.onRecognitionResult(result);
                }

                @Override
                public void onError(Exception error) {
                    callback.onRecognitionError(new SpeechRecognitionError(error));
                }
            });

        } catch (Exception e) {
            callback.onRecognitionError(new SpeechRecognitionError(e));
        }
    }
}
```

### 3. 文本转语音服务

#### TextToSpeechService - 文本转语音服务
```java
/**
 * 文本转语音服务
 * 负责将文本转换为语音
 */
@Service
public class TextToSpeechService {

    private final TTSEngine ttsEngine;
    private final VoiceLibrary voiceLibrary;
    private final AudioPostprocessor audioPostprocessor;

    /**
     * 合成语音
     * @param text 要合成的文本
     * @param voiceParams 语音参数
     * @return 音频数据
     */
    public AudioData synthesizeSpeech(String text, VoiceParameters voiceParams) {
        try {
            // 1. 文本预处理
            String processedText = preprocessText(text);

            // 2. 语音合成
            RawAudioData rawAudio = ttsEngine.synthesize(processedText, voiceParams);

            // 3. 音频后处理
            AudioData processedAudio = audioPostprocessor.process(rawAudio, voiceParams);

            return processedAudio;

        } catch (Exception e) {
            log.error("语音合成失败: text={}", text, e);
            throw new SpeechSynthesisException("语音合成失败", e);
        }
    }

    /**
     * 获取可用语音列表
     * @return 语音信息列表
     */
    public List<VoiceInfo> getAvailableVoices() {
        return voiceLibrary.getAvailableVoices();
    }
}
```

## 数据模型定义

### 1. 语音相关模型

#### VoicePreferences - 语音偏好设置
```java
/**
 * 语音偏好设置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoicePreferences {
    private VoiceSettings voiceSettings;
    private RecognitionSettings recognitionSettings;
    private AudioSettings audioSettings;
    private LanguageSettings languageSettings;
}
```

#### VoiceCommand - 语音指令
```java
/**
 * 语音指令模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoiceCommand {
    private String commandId;
    private String commandText;
    private VoiceCommandType commandType;
    private Map<String, Object> parameters;
    private double confidence;
    private LocalDateTime timestamp;
}
```

#### SpeechRecognitionResult - 语音识别结果
```java
/**
 * 语音识别结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpeechRecognitionResult {
    private String text;
    private double confidence;
    private String language;
    private LocalDateTime timestamp;
    private List<AlternativeResult> alternatives;
}
```

## 使用示例

### 基本语音互动操作
```java
// 初始化语音互动管理器
VoiceInteractionManager voiceManager = new VoiceInteractionManager();
voiceManager.initializeVoiceInteraction();

// 开始语音监听
voiceManager.startListening();

// 语音回复
voiceManager.speakText("你好，我是你的AI助手", EmotionType.FRIENDLY);

// 设置语音偏好
VoicePreferences preferences = VoicePreferences.builder()
    .voiceSettings(VoiceSettings.builder()
        .voiceId("zh-CN-XiaoxiaoNeural")
        .speed(1.0f)
        .pitch(1.0f)
        .volume(0.8f)
        .build())
    .recognitionSettings(RecognitionSettings.builder()
        .language("zh-CN")
        .minConfidenceThreshold(0.7)
        .enableContinuousRecognition(true)
        .noiseReduction(true)
        .build())
    .audioSettings(AudioSettings.builder()
        .inputDevice("默认麦克风")
        .outputDevice("默认扬声器")
        .inputVolume(0.8f)
        .outputVolume(0.9f)
        .build())
    .build();

voiceManager.setVoicePreferences(preferences);

// 获取可用语音列表
List<VoiceInfo> voices = voiceManager.getAvailableVoices();
for (VoiceInfo voice : voices) {
    System.out.println("语音: " + voice.getName() + " (" + voice.getLanguage() + ")");
}

// 测试语音
voiceManager.testVoice("zh-CN-XiaoxiaoNeural", "这是一个语音测试");

// 停止语音监听
voiceManager.stopListening();
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的语音互动模块实现，支持语音识别、语音合成、语音指令、多语言等功能
