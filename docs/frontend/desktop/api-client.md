# API客户端 (API Client)

## 模块概述

API客户端模块负责前端与后端AI服务的通信，包括HTTP请求、WebSocket连接、数据序列化等功能。提供统一的API调用接口和错误处理机制。

**扩展原始Ark-Pets项目**:
- 原项目主要为本地应用，网络功能有限
- 新增完整的HTTP客户端和WebSocket支持
- 实现请求重试、缓存、认证等高级功能
- 支持流式响应和实时通信

## 核心功能

### 1. AI服务客户端 (AIServiceClient)

#### 功能描述
与后端AI服务进行通信的主要客户端，支持聊天、认证、配置等各种API调用。

#### 核心函数

```java
public class AIServiceClient {
    
    private static final Logger logger = LoggerFactory.getLogger(AIServiceClient.class);
    
    private final OkHttpClient httpClient;
    private final WebSocketManager webSocketManager;
    private final String baseUrl;
    private final String apiKey;
    private final Gson gson;
    private final RequestInterceptor requestInterceptor;
    private final ResponseCache responseCache;
    
    private String authToken;
    private long tokenExpireTime;
    
    /**
     * 初始化AI服务客户端
     * @param config AI服务配置
     */
    public AIServiceClient(AIServiceConfig config) {
        this.baseUrl = config.getServiceUrl();
        this.apiKey = config.getApiKey();
        this.gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
            .create();
        
        // 配置HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(config.getTimeout(), TimeUnit.MILLISECONDS)
            .readTimeout(config.getTimeout(), TimeUnit.MILLISECONDS)
            .writeTimeout(config.getTimeout(), TimeUnit.MILLISECONDS)
            .addInterceptor(new AuthenticationInterceptor())
            .addInterceptor(new LoggingInterceptor())
            .addInterceptor(new RetryInterceptor(config.getMaxRetries()))
            .build();
        
        this.requestInterceptor = new RequestInterceptor();
        this.responseCache = new ResponseCache();
        this.webSocketManager = new WebSocketManager(this);
    }
    
    /**
     * 测试连接
     * @return 连接是否成功
     */
    public boolean testConnection() {
        try {
            Request request = new Request.Builder()
                .url(baseUrl + "/api/health")
                .get()
                .build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                return response.isSuccessful();
            }
        } catch (Exception e) {
            logger.error("连接测试失败", e);
            return false;
        }
    }
    
    /**
     * 用户认证
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    public CompletableFuture<AuthResponse> authenticate(String username, String password) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                AuthRequest authRequest = new AuthRequest(username, password);
                String json = gson.toJson(authRequest);
                
                RequestBody body = RequestBody.create(json, MediaType.get("application/json"));
                Request request = new Request.Builder()
                    .url(baseUrl + "/api/auth/login")
                    .post(body)
                    .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        AuthResponse authResponse = gson.fromJson(responseJson, AuthResponse.class);
                        
                        // 保存认证信息
                        this.authToken = authResponse.getToken();
                        this.tokenExpireTime = System.currentTimeMillis() + authResponse.getExpiresIn() * 1000;
                        
                        logger.info("用户认证成功: {}", username);
                        return authResponse;
                    } else {
                        throw new APIException("认证失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                logger.error("用户认证失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 发送聊天消息
     * @param request 聊天请求
     * @return 聊天响应
     */
    public CompletableFuture<ChatResponse> sendChatMessage(ChatRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 检查认证状态
                ensureAuthenticated();
                
                String json = gson.toJson(request);
                RequestBody body = RequestBody.create(json, MediaType.get("application/json"));
                
                Request httpRequest = new Request.Builder()
                    .url(baseUrl + "/api/chat/message")
                    .post(body)
                    .build();
                
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        ChatResponse chatResponse = gson.fromJson(responseJson, ChatResponse.class);
                        
                        // 缓存响应
                        responseCache.put(request.getRequestId(), chatResponse);
                        
                        return chatResponse;
                    } else {
                        throw new APIException("聊天请求失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                logger.error("发送聊天消息失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 发送流式聊天消息
     * @param request 聊天请求
     * @param callback 流式回调
     * @return 流式句柄
     */
    public StreamHandle sendStreamChatMessage(ChatRequest request, StreamCallback callback) {
        try {
            ensureAuthenticated();
            
            String json = gson.toJson(request);
            RequestBody body = RequestBody.create(json, MediaType.get("application/json"));
            
            Request httpRequest = new Request.Builder()
                .url(baseUrl + "/api/chat/stream")
                .post(body)
                .build();
            
            StreamHandle handle = new StreamHandle();
            
            httpClient.newCall(httpRequest).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    callback.onError(e);
                    handle.complete();
                }
                
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        callback.onError(new APIException("流式请求失败: " + response.code()));
                        handle.complete();
                        return;
                    }
                    
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(response.body().byteStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null && !handle.isCancelled()) {
                            if (line.startsWith("data: ")) {
                                String data = line.substring(6);
                                if (!"[DONE]".equals(data)) {
                                    StreamResponse streamResponse = gson.fromJson(data, StreamResponse.class);
                                    callback.onData(streamResponse);
                                }
                            }
                        }
                        callback.onComplete();
                    } catch (Exception e) {
                        callback.onError(e);
                    } finally {
                        handle.complete();
                    }
                }
            });
            
            return handle;
        } catch (Exception e) {
            logger.error("发送流式聊天消息失败", e);
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 获取对话历史
     * @param conversationId 对话ID
     * @param pageSize 页面大小
     * @param pageToken 页面令牌
     * @return 对话历史响应
     */
    public CompletableFuture<ConversationHistoryResponse> getConversationHistory(
            String conversationId, int pageSize, String pageToken) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ensureAuthenticated();
                
                HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl + "/api/chat/history").newBuilder()
                    .addQueryParameter("conversationId", conversationId)
                    .addQueryParameter("pageSize", String.valueOf(pageSize));
                
                if (pageToken != null) {
                    urlBuilder.addQueryParameter("pageToken", pageToken);
                }
                
                Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .get()
                    .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        return gson.fromJson(responseJson, ConversationHistoryResponse.class);
                    } else {
                        throw new APIException("获取对话历史失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                logger.error("获取对话历史失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 创建新对话
     * @param request 创建对话请求
     * @return 对话响应
     */
    public CompletableFuture<ConversationResponse> createConversation(CreateConversationRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ensureAuthenticated();
                
                String json = gson.toJson(request);
                RequestBody body = RequestBody.create(json, MediaType.get("application/json"));
                
                Request httpRequest = new Request.Builder()
                    .url(baseUrl + "/api/chat/conversation")
                    .post(body)
                    .build();
                
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        return gson.fromJson(responseJson, ConversationResponse.class);
                    } else {
                        throw new APIException("创建对话失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                logger.error("创建对话失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 获取用户信息
     * @return 用户信息响应
     */
    public CompletableFuture<UserInfoResponse> getUserInfo() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ensureAuthenticated();
                
                Request request = new Request.Builder()
                    .url(baseUrl + "/api/user/info")
                    .get()
                    .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        return gson.fromJson(responseJson, UserInfoResponse.class);
                    } else {
                        throw new APIException("获取用户信息失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                logger.error("获取用户信息失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 获取使用统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 使用统计响应
     */
    public CompletableFuture<UsageStatsResponse> getUsageStats(String startDate, String endDate) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ensureAuthenticated();
                
                HttpUrl url = HttpUrl.parse(baseUrl + "/api/user/usage").newBuilder()
                    .addQueryParameter("startDate", startDate)
                    .addQueryParameter("endDate", endDate)
                    .build();
                
                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        return gson.fromJson(responseJson, UsageStatsResponse.class);
                    } else {
                        throw new APIException("获取使用统计失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                logger.error("获取使用统计失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 建立WebSocket连接
     * @param listener WebSocket监听器
     * @return WebSocket连接
     */
    public WebSocketConnection connectWebSocket(WebSocketListener listener) {
        return webSocketManager.connect(baseUrl.replace("http", "ws") + "/api/ws", listener);
    }
    
    /**
     * 确保已认证
     */
    private void ensureAuthenticated() {
        if (authToken == null || System.currentTimeMillis() >= tokenExpireTime) {
            throw new AuthenticationException("未认证或令牌已过期");
        }
    }
    
    /**
     * 刷新认证令牌
     * @return 刷新是否成功
     */
    public CompletableFuture<Boolean> refreshToken() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (authToken == null) {
                    return false;
                }
                
                Request request = new Request.Builder()
                    .url(baseUrl + "/api/auth/refresh")
                    .post(RequestBody.create("", MediaType.get("application/json")))
                    .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseJson = response.body().string();
                        AuthResponse authResponse = gson.fromJson(responseJson, AuthResponse.class);
                        
                        this.authToken = authResponse.getToken();
                        this.tokenExpireTime = System.currentTimeMillis() + authResponse.getExpiresIn() * 1000;
                        
                        logger.info("令牌刷新成功");
                        return true;
                    } else {
                        logger.warn("令牌刷新失败: {}", response.code());
                        return false;
                    }
                }
            } catch (Exception e) {
                logger.error("令牌刷新异常", e);
                return false;
            }
        });
    }
    
    /**
     * 注销
     */
    public void logout() {
        try {
            if (authToken != null) {
                Request request = new Request.Builder()
                    .url(baseUrl + "/api/auth/logout")
                    .post(RequestBody.create("", MediaType.get("application/json")))
                    .build();
                
                httpClient.newCall(request).execute().close();
            }
        } catch (Exception e) {
            logger.warn("注销请求失败", e);
        } finally {
            authToken = null;
            tokenExpireTime = 0;
            responseCache.clear();
            webSocketManager.closeAll();
        }
    }
    
    /**
     * 清理客户端资源
     */
    public void cleanup() {
        logout();
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
}
```

### 2. 流式处理

#### StreamHandle - 流式处理句柄
```java
public class StreamHandle {
    private volatile boolean cancelled = false;
    private volatile boolean completed = false;
    private final CountDownLatch completionLatch = new CountDownLatch(1);
    
    /**
     * 取消流式处理
     */
    public void cancel() {
        cancelled = true;
        completionLatch.countDown();
    }
    
    /**
     * 检查是否已取消
     * @return 是否已取消
     */
    public boolean isCancelled() {
        return cancelled;
    }
    
    /**
     * 检查是否已完成
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return completed;
    }
    
    /**
     * 等待完成
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否在超时前完成
     */
    public boolean awaitCompletion(long timeout, TimeUnit unit) throws InterruptedException {
        return completionLatch.await(timeout, unit);
    }
    
    /**
     * 标记为完成
     */
    void complete() {
        completed = true;
        completionLatch.countDown();
    }
}
```

#### StreamCallback - 流式回调接口
```java
public interface StreamCallback {
    
    /**
     * 接收到数据时调用
     * @param data 流式响应数据
     */
    void onData(StreamResponse data);
    
    /**
     * 流式处理完成时调用
     */
    void onComplete();
    
    /**
     * 发生错误时调用
     * @param error 错误信息
     */
    void onError(Throwable error);
}
```

### 3. WebSocket管理

#### WebSocketManager - WebSocket管理器
```java
public class WebSocketManager {
    
    private final AIServiceClient client;
    private final Map<String, WebSocketConnection> connections;
    private final OkHttpClient webSocketClient;
    
    public WebSocketManager(AIServiceClient client) {
        this.client = client;
        this.connections = new ConcurrentHashMap<>();
        this.webSocketClient = new OkHttpClient.Builder()
            .pingInterval(30, TimeUnit.SECONDS)
            .build();
    }
    
    /**
     * 建立WebSocket连接
     * @param url WebSocket URL
     * @param listener 监听器
     * @return WebSocket连接
     */
    public WebSocketConnection connect(String url, WebSocketListener listener) {
        Request request = new Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer " + client.getAuthToken())
            .build();
        
        WebSocket webSocket = webSocketClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                listener.onOpen(webSocket, response);
            }
            
            @Override
            public void onMessage(WebSocket webSocket, String text) {
                listener.onMessage(webSocket, text);
            }
            
            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                listener.onMessage(webSocket, bytes);
            }
            
            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                listener.onClosing(webSocket, code, reason);
            }
            
            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                listener.onClosed(webSocket, code, reason);
                connections.remove(url);
            }
            
            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                listener.onFailure(webSocket, t, response);
                connections.remove(url);
            }
        });
        
        WebSocketConnection connection = new WebSocketConnection(webSocket);
        connections.put(url, connection);
        return connection;
    }
    
    /**
     * 关闭所有连接
     */
    public void closeAll() {
        for (WebSocketConnection connection : connections.values()) {
            connection.close();
        }
        connections.clear();
    }
}
```

## API请求/响应模型

### 聊天相关

#### ChatRequest - 聊天请求
```java
public class ChatRequest extends APIRequest {
    private String message;
    private String conversationId;
    private String characterName;
    private String personalityId;
    private Map<String, Object> context;
    
    // 构造函数、getter、setter等
}
```

#### ChatResponse - 聊天响应
```java
public class ChatResponse extends APIResponse {
    private String conversationId;
    private String messageId;
    private String content;
    private TokenUsage tokens;
    private String model;
    private String emotionType;
    private String animationHint;
    private String behaviorHint;
    
    // 构造函数、getter、setter等
}
```

## 错误处理

### 异常类型

```java
public class APIException extends Exception {
    public static class AuthenticationException extends APIException {}
    public static class NetworkException extends APIException {}
    public static class RateLimitException extends APIException {}
    public static class ServerException extends APIException {}
}
```

---

**模块负责人**: API客户端开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
