# 配置管理器 (Config Manager)

## 模块概述

配置管理器负责应用程序所有配置的加载、保存、验证和管理，包括用户设置、角色配置、AI服务配置等。基于原始Ark-Pets项目的配置系统进行扩展。

**基于原始Ark-Pets项目分析**:
- 原项目使用JSON格式存储配置信息
- 支持配置文件的热重载和自动保存
- 包含显示、物理、行为等多种配置类型
- 扩展增加AI服务和网络相关配置

## 核心功能

### 1. 配置管理器 (ConfigurationManager) - 基于原始项目

#### 功能描述
统一管理所有配置文件的加载、保存、验证和更新操作。

#### 核心函数

```java
public class ConfigurationManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    private static ConfigurationManager instance;
    
    private final Map<String, Object> configCache;
    private final Map<String, Long> configTimestamps;
    private final Map<String, List<ConfigChangeListener>> listeners;
    private final FileWatcher fileWatcher;
    private final Gson gson;
    
    private String configDirectory = "config";
    private boolean autoSave = true;
    private boolean hotReload = true;
    
    private ConfigurationManager() {
        this.configCache = new ConcurrentHashMap<>();
        this.configTimestamps = new ConcurrentHashMap<>();
        this.listeners = new ConcurrentHashMap<>();
        this.fileWatcher = new FileWatcher();
        this.gson = new GsonBuilder()
            .setPrettyPrinting()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();
    }
    
    /**
     * 获取配置管理器实例
     * @return 配置管理器实例
     */
    public static synchronized ConfigurationManager getInstance() {
        if (instance == null) {
            instance = new ConfigurationManager();
        }
        return instance;
    }
    
    /**
     * 初始化配置管理器
     * @param configDir 配置目录
     * @return 初始化是否成功
     */
    public boolean initialize(String configDir) {
        try {
            this.configDirectory = configDir;
            
            // 创建配置目录
            File configDirFile = new File(configDirectory);
            if (!configDirFile.exists()) {
                configDirFile.mkdirs();
            }
            
            // 加载所有配置文件
            loadAllConfigurations();
            
            // 启动文件监控
            if (hotReload) {
                startFileWatcher();
            }
            
            logger.info("配置管理器初始化成功，配置目录: {}", configDirectory);
            return true;
        } catch (Exception e) {
            logger.error("配置管理器初始化失败", e);
            return false;
        }
    }
    
    /**
     * 加载配置文件
     * @param configName 配置名称
     * @param configClass 配置类型
     * @return 配置对象
     */
    public <T> T loadConfiguration(String configName, Class<T> configClass) {
        try {
            String cacheKey = configName + ":" + configClass.getSimpleName();
            
            // 检查缓存
            T cachedConfig = getCachedConfig(cacheKey, configClass);
            if (cachedConfig != null) {
                return cachedConfig;
            }
            
            // 从文件加载
            File configFile = new File(configDirectory, configName + ".json");
            T config;
            
            if (configFile.exists()) {
                String json = Files.readString(configFile.toPath(), StandardCharsets.UTF_8);
                config = gson.fromJson(json, configClass);
                logger.debug("从文件加载配置: {}", configName);
            } else {
                // 创建默认配置
                config = createDefaultConfiguration(configClass);
                saveConfiguration(configName, config);
                logger.info("创建默认配置: {}", configName);
            }
            
            // 验证配置
            if (!validateConfiguration(config)) {
                logger.warn("配置验证失败，使用默认配置: {}", configName);
                config = createDefaultConfiguration(configClass);
            }
            
            // 缓存配置
            cacheConfiguration(cacheKey, config, configFile.lastModified());
            
            return config;
        } catch (Exception e) {
            logger.error("加载配置失败: {}", configName, e);
            return createDefaultConfiguration(configClass);
        }
    }
    
    /**
     * 保存配置文件
     * @param configName 配置名称
     * @param config 配置对象
     * @return 保存是否成功
     */
    public <T> boolean saveConfiguration(String configName, T config) {
        try {
            File configFile = new File(configDirectory, configName + ".json");
            String json = gson.toJson(config);
            
            // 创建备份
            if (configFile.exists()) {
                createBackup(configFile);
            }
            
            // 写入文件
            Files.writeString(configFile.toPath(), json, StandardCharsets.UTF_8);
            
            // 更新缓存
            String cacheKey = configName + ":" + config.getClass().getSimpleName();
            cacheConfiguration(cacheKey, config, configFile.lastModified());
            
            // 通知监听器
            notifyConfigurationChanged(configName, config);
            
            logger.debug("保存配置成功: {}", configName);
            return true;
        } catch (Exception e) {
            logger.error("保存配置失败: {}", configName, e);
            return false;
        }
    }
    
    /**
     * 获取应用配置
     * @return 应用配置
     */
    public ApplicationConfig getApplicationConfig() {
        return loadConfiguration("application", ApplicationConfig.class);
    }
    
    /**
     * 保存应用配置
     * @param config 应用配置
     * @return 保存是否成功
     */
    public boolean saveApplicationConfig(ApplicationConfig config) {
        return saveConfiguration("application", config);
    }
    
    /**
     * 获取显示配置
     * @return 显示配置
     */
    public DisplayConfig getDisplayConfig() {
        return loadConfiguration("display", DisplayConfig.class);
    }
    
    /**
     * 保存显示配置
     * @param config 显示配置
     * @return 保存是否成功
     */
    public boolean saveDisplayConfig(DisplayConfig config) {
        return saveConfiguration("display", config);
    }
    
    /**
     * 获取物理配置
     * @return 物理配置
     */
    public PhysicsConfig getPhysicsConfig() {
        return loadConfiguration("physics", PhysicsConfig.class);
    }
    
    /**
     * 保存物理配置
     * @param config 物理配置
     * @return 保存是否成功
     */
    public boolean savePhysicsConfig(PhysicsConfig config) {
        return saveConfiguration("physics", config);
    }
    
    /**
     * 获取AI服务配置
     * @return AI服务配置
     */
    public AIServiceConfig getAIServiceConfig() {
        return loadConfiguration("ai-service", AIServiceConfig.class);
    }
    
    /**
     * 保存AI服务配置
     * @param config AI服务配置
     * @return 保存是否成功
     */
    public boolean saveAIServiceConfig(AIServiceConfig config) {
        return saveConfiguration("ai-service", config);
    }
    
    /**
     * 获取角色配置
     * @param characterName 角色名称
     * @return 角色配置
     */
    public CharacterConfig getCharacterConfig(String characterName) {
        return loadConfiguration("characters/" + characterName, CharacterConfig.class);
    }
    
    /**
     * 保存角色配置
     * @param characterName 角色名称
     * @param config 角色配置
     * @return 保存是否成功
     */
    public boolean saveCharacterConfig(String characterName, CharacterConfig config) {
        return saveConfiguration("characters/" + characterName, config);
    }
    
    /**
     * 重置配置为默认值
     * @param configName 配置名称
     * @param configClass 配置类型
     * @return 重置是否成功
     */
    public <T> boolean resetConfiguration(String configName, Class<T> configClass) {
        try {
            T defaultConfig = createDefaultConfiguration(configClass);
            return saveConfiguration(configName, defaultConfig);
        } catch (Exception e) {
            logger.error("重置配置失败: {}", configName, e);
            return false;
        }
    }
    
    /**
     * 导出配置
     * @param configName 配置名称
     * @param exportPath 导出路径
     * @return 导出是否成功
     */
    public boolean exportConfiguration(String configName, String exportPath) {
        try {
            File sourceFile = new File(configDirectory, configName + ".json");
            File targetFile = new File(exportPath);
            
            if (!sourceFile.exists()) {
                logger.error("配置文件不存在: {}", configName);
                return false;
            }
            
            Files.copy(sourceFile.toPath(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            logger.info("配置导出成功: {} -> {}", configName, exportPath);
            return true;
        } catch (Exception e) {
            logger.error("配置导出失败: {}", configName, e);
            return false;
        }
    }
    
    /**
     * 导入配置
     * @param configName 配置名称
     * @param importPath 导入路径
     * @param configClass 配置类型
     * @return 导入是否成功
     */
    public <T> boolean importConfiguration(String configName, String importPath, Class<T> configClass) {
        try {
            File importFile = new File(importPath);
            if (!importFile.exists()) {
                logger.error("导入文件不存在: {}", importPath);
                return false;
            }
            
            // 验证导入的配置
            String json = Files.readString(importFile.toPath(), StandardCharsets.UTF_8);
            T config = gson.fromJson(json, configClass);
            
            if (!validateConfiguration(config)) {
                logger.error("导入的配置验证失败: {}", importPath);
                return false;
            }
            
            // 保存配置
            return saveConfiguration(configName, config);
        } catch (Exception e) {
            logger.error("配置导入失败: {}", configName, e);
            return false;
        }
    }
    
    /**
     * 添加配置变更监听器
     * @param configName 配置名称
     * @param listener 监听器
     */
    public void addConfigChangeListener(String configName, ConfigChangeListener listener) {
        listeners.computeIfAbsent(configName, k -> new ArrayList<>()).add(listener);
    }
    
    /**
     * 移除配置变更监听器
     * @param configName 配置名称
     * @param listener 监听器
     */
    public void removeConfigChangeListener(String configName, ConfigChangeListener listener) {
        List<ConfigChangeListener> configListeners = listeners.get(configName);
        if (configListeners != null) {
            configListeners.remove(listener);
        }
    }
    
    /**
     * 获取缓存的配置
     * @param cacheKey 缓存键
     * @param configClass 配置类型
     * @return 缓存的配置
     */
    @SuppressWarnings("unchecked")
    private <T> T getCachedConfig(String cacheKey, Class<T> configClass) {
        Object cached = configCache.get(cacheKey);
        if (cached != null && configClass.isInstance(cached)) {
            return (T) cached;
        }
        return null;
    }
    
    /**
     * 缓存配置
     * @param cacheKey 缓存键
     * @param config 配置对象
     * @param timestamp 时间戳
     */
    private void cacheConfiguration(String cacheKey, Object config, long timestamp) {
        configCache.put(cacheKey, config);
        configTimestamps.put(cacheKey, timestamp);
    }
    
    /**
     * 创建默认配置
     * @param configClass 配置类型
     * @return 默认配置
     */
    @SuppressWarnings("unchecked")
    private <T> T createDefaultConfiguration(Class<T> configClass) {
        try {
            return configClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            logger.error("创建默认配置失败: {}", configClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 验证配置
     * @param config 配置对象
     * @return 验证是否通过
     */
    private boolean validateConfiguration(Object config) {
        if (config == null) {
            return false;
        }
        
        // 如果配置实现了Validatable接口，调用验证方法
        if (config instanceof Validatable) {
            return ((Validatable) config).isValid();
        }
        
        return true;
    }
    
    /**
     * 创建配置备份
     * @param configFile 配置文件
     */
    private void createBackup(File configFile) {
        try {
            String backupName = configFile.getName() + ".backup." + System.currentTimeMillis();
            File backupFile = new File(configFile.getParent(), backupName);
            Files.copy(configFile.toPath(), backupFile.toPath());
            
            // 清理旧备份文件（保留最近5个）
            cleanupBackups(configFile);
        } catch (Exception e) {
            logger.warn("创建配置备份失败", e);
        }
    }
    
    /**
     * 清理旧备份文件
     * @param configFile 配置文件
     */
    private void cleanupBackups(File configFile) {
        try {
            File parentDir = configFile.getParentFile();
            String baseName = configFile.getName() + ".backup.";
            
            File[] backupFiles = parentDir.listFiles((dir, name) -> name.startsWith(baseName));
            if (backupFiles != null && backupFiles.length > 5) {
                Arrays.sort(backupFiles, (a, b) -> Long.compare(b.lastModified(), a.lastModified()));
                
                for (int i = 5; i < backupFiles.length; i++) {
                    backupFiles[i].delete();
                }
            }
        } catch (Exception e) {
            logger.warn("清理备份文件失败", e);
        }
    }
    
    /**
     * 通知配置变更
     * @param configName 配置名称
     * @param config 配置对象
     */
    private void notifyConfigurationChanged(String configName, Object config) {
        List<ConfigChangeListener> configListeners = listeners.get(configName);
        if (configListeners != null) {
            for (ConfigChangeListener listener : configListeners) {
                try {
                    listener.onConfigurationChanged(configName, config);
                } catch (Exception e) {
                    logger.error("配置变更通知失败", e);
                }
            }
        }
    }
    
    /**
     * 加载所有配置文件
     */
    private void loadAllConfigurations() {
        // 预加载常用配置
        getApplicationConfig();
        getDisplayConfig();
        getPhysicsConfig();
        getAIServiceConfig();
    }
    
    /**
     * 启动文件监控
     */
    private void startFileWatcher() {
        fileWatcher.watchDirectory(configDirectory, this::handleFileChange);
    }
    
    /**
     * 处理文件变更
     * @param filePath 文件路径
     * @param eventType 事件类型
     */
    private void handleFileChange(String filePath, FileWatcher.EventType eventType) {
        if (eventType == FileWatcher.EventType.MODIFIED && filePath.endsWith(".json")) {
            String configName = new File(filePath).getName().replace(".json", "");
            logger.info("检测到配置文件变更: {}", configName);
            
            // 清除缓存，强制重新加载
            configCache.entrySet().removeIf(entry -> entry.getKey().startsWith(configName + ":"));
        }
    }
    
    /**
     * 清理配置管理器
     */
    public void cleanup() {
        if (fileWatcher != null) {
            fileWatcher.stop();
        }
        
        configCache.clear();
        configTimestamps.clear();
        listeners.clear();
        
        logger.info("配置管理器清理完成");
    }
}
```

### 2. 配置变更监听器接口

#### ConfigChangeListener - 配置变更监听器
```java
public interface ConfigChangeListener {
    
    /**
     * 配置变更时调用
     * @param configName 配置名称
     * @param newConfig 新的配置对象
     */
    void onConfigurationChanged(String configName, Object newConfig);
}
```

### 3. 可验证配置接口

#### Validatable - 可验证接口
```java
public interface Validatable {
    
    /**
     * 验证配置是否有效
     * @return 是否有效
     */
    boolean isValid();
    
    /**
     * 获取验证错误信息
     * @return 错误信息列表
     */
    default List<String> getValidationErrors() {
        return Collections.emptyList();
    }
}
```

## 配置文件示例

### 应用配置 (application.json)

```json
{
  "version": "1.0.0",
  "isNewcomer": false,
  "lastSelectedCharacter": "amiya",
  "userPreferences": {
    "language": "zh_CN",
    "theme": "default",
    "autoStart": false,
    "checkUpdates": true
  },
  "display": {
    "fps": 60,
    "scale": 1.0,
    "multiMonitors": false,
    "marginBottom": 40
  },
  "physics": {
    "gravity": -980.0,
    "friction": 0.95,
    "bounce": 0.7
  },
  "aiService": {
    "serviceUrl": "http://localhost:8080",
    "timeout": 30000,
    "enableVoice": false,
    "enableEmotion": true
  }
}
```

---

**模块负责人**: 配置管理开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
