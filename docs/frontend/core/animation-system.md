# 动画系统 (Animation System)

## 模块概述

动画系统负责管理桌宠的所有动画效果，包括Spine动画播放、状态转换、动画混合等功能。基于LibGDX和Spine运行时实现。

**基于原始Ark-Pets项目分析**:
- 原项目使用完整的Spine动画系统 (Spine ********)
- 包含AnimClip、AnimData、AnimComposer等核心组件
- 支持动画循环、严格模式、移动性等高级特性
- 实现了基于权重的行为系统 (GeneralBehavior)

## 核心功能

### 1. 动画组合器 (AnimComposer) - 基于原始项目

#### 功能描述
基于原始Ark-Pets的AnimComposer实现，负责动画的播放控制和状态管理。

#### 主要职责
- 动画播放请求处理
- 动画状态跟踪
- 动画中断和切换逻辑
- 与Spine AnimationState的交互

#### 核心函数

```java
public class AnimComposer {

    private AnimData playing;
    private final AnimationState state;
    private final int coreTrackId = 0;

    /**
     * 请求播放动画 (基于原始项目的offer方法)
     * @param animData 动画数据，包含动画剪辑、循环、严格模式等信息
     * @return 是否成功开始播放动画
     */
    public boolean offerAnimation(AnimData animData) {
        if (animData != null && !animData.isEmpty()) {
            if (playing == null || playing.isEmpty() ||
                (!playing.isStrict() && !playing.equals(animData))) {
                playing = animData;
                state.setAnimation(coreTrackId, playing.name(), playing.isLoop());
                onAnimationApplied(playing);
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前播放的动画数据
     * @return 当前播放的AnimData，无动画时返回null
     */
    public AnimData getCurrentPlayingAnimation() {
        return playing;
    }

    /**
     * 重置动画状态
     * 清除当前播放的动画，设置为空动画
     */
    public void resetAnimationState() {
        playing = null;
        state.setEmptyAnimation(coreTrackId, 0f);
    }

    /**
     * 检查是否有动画正在播放
     * @return 是否有动画播放中
     */
    public boolean isAnimationPlaying() {
        return playing != null && !playing.isEmpty();
    }

    /**
     * 强制设置动画 (忽略严格模式)
     * @param animData 要设置的动画数据
     * @return 是否设置成功
     */
    public boolean forceSetAnimation(AnimData animData) {
        if (animData != null && !animData.isEmpty()) {
            playing = animData;
            state.setAnimation(coreTrackId, playing.name(), playing.isLoop());
            onAnimationApplied(playing);
            return true;
        }
        return false;
    }

    /**
     * 动画应用时的回调方法 (可被子类重写)
     * @param appliedAnimation 已应用的动画数据
     */
    protected void onAnimationApplied(AnimData appliedAnimation) {
        // 子类可以重写此方法来处理动画应用事件
    }

    /**
     * 更新动画系统 (每帧调用)
     * @param deltaTime 时间增量
     */
    public void updateAnimationComposer(float deltaTime) {
        state.update(deltaTime);
        state.apply(skeleton);
    }
}
```

### 2. 动画数据模型 (AnimData & AnimClip) - 基于原始项目

#### AnimData - 动画数据记录
基于原始项目的AnimData record实现，包含完整的动画信息。

```java
/**
 * 动画数据记录 (基于原始项目)
 * @param animClip 动画剪辑信息
 * @param animNext 下一个动画数据，当前动画结束后自动播放
 * @param isLoop 是否循环播放
 * @param isStrict 是否为严格模式，严格模式下不能被其他动画中断
 * @param mobility 移动性，0=无移动，1=向右，-1=向左
 */
public record AnimData(
        AnimClip animClip,
        AnimData animNext,
        boolean isLoop,
        boolean isStrict,
        int mobility
) {
    /**
     * 简化构造函数
     * @param animClip 动画剪辑
     */
    public AnimData(AnimClip animClip) {
        this(animClip, null, false, false, 0);
    }

    /**
     * 检查动画数据是否为空
     * @return 是否为空
     */
    public boolean isEmpty() {
        return animClip == null || animClip.fullName == null;
    }

    /**
     * 获取动画名称
     * @return 动画名称
     */
    public String name() {
        return animClip != null ? animClip.fullName : null;
    }

    /**
     * 创建衍生动画数据 (改变移动性)
     * @param newMobility 新的移动性
     * @return 新的动画数据
     */
    public AnimData derive(int newMobility) {
        return new AnimData(animClip, animNext, isLoop, isStrict, newMobility);
    }
}
```

#### AnimClip - 动画剪辑信息
基于原始项目的AnimClip实现，包含动画的详细信息。

```java
public class AnimClip {
    public final String fullName;      // 完整动画名称
    public final String baseName;      // 基础名称
    public final AnimType type;        // 动画类型
    public final AnimModifier modifier; // 动画修饰符
    public final AnimStage stage;      // 动画阶段
    public final float duration;       // 动画时长(秒)

    /**
     * 初始化动画剪辑
     * @param name 动画完整名称
     * @param duration 动画时长
     */
    public AnimClip(String name, float duration) {
        ArrayList<String> elements = splitAnimationName(name);
        RecognitionResult<AnimType> typeResult = recognizeAnimationType(elements);

        this.fullName = name;
        this.baseName = typeResult.according;
        this.type = typeResult.result;
        this.modifier = recognizeAnimationModifier(elements).result;
        this.stage = recognizeAnimationStage(elements).result;
        this.duration = duration;
    }

    /**
     * 从Spine Animation创建
     * @param spineAnimation Spine动画对象
     */
    public AnimClip(Animation spineAnimation) {
        this(spineAnimation.getName(), spineAnimation.getDuration());
    }
}
```

#### 动画类型定义 (基于原始项目)
```java
public enum AnimType {
    IDLE("Idle", "空闲", 0f),
    MOVE("Move", "移动", 0f),
    SIT("Sit", "坐下", 0f),
    SLEEP("Sleep", "睡觉", 0f),
    INTERACT("Interact", "交互", 0f),
    SPECIAL("Special", "特殊", 0f);

    public final String typeName;
    public final String description;
    public final float offsetY; // Y轴偏移

    AnimType(String typeName, String description, float offsetY) {
        this.typeName = typeName;
        this.description = description;
        this.offsetY = offsetY;
    }
}
```

### 3. 行为系统 (Behavior System) - 基于原始项目

#### 功能描述
基于原始Ark-Pets的Behavior和GeneralBehavior实现，管理桌宠的行为逻辑。

#### Behavior - 行为基类
```java
public abstract class Behavior {

    protected final ArkChar character;
    protected final AnimComposer animComposer;

    public Behavior(ArkChar character) {
        this.character = character;
        this.animComposer = character.getAnimComposer();
    }

    /**
     * 行为更新方法 (每帧调用)
     * @param deltaTime 时间增量
     */
    public abstract void updateBehavior(float deltaTime);

    /**
     * 行为开始时调用
     */
    public abstract void onBehaviorStart();

    /**
     * 行为结束时调用
     */
    public abstract void onBehaviorEnd();

    /**
     * 检查行为是否可以被中断
     * @return 是否可中断
     */
    public abstract boolean canBeInterrupted();
}
```

#### GeneralBehavior - 通用行为实现
基于原始项目的GeneralBehavior，实现基于权重的随机行为选择。

```java
public class GeneralBehavior extends Behavior {

    private final AnimClipGroup animClipGroup;
    private final Random random = new Random();
    private float behaviorTimer = 0f;
    private float nextBehaviorTime = 0f;

    public GeneralBehavior(ArkChar character, AnimClipGroup animClipGroup) {
        super(character);
        this.animClipGroup = animClipGroup;
        scheduleNextBehavior();
    }

    /**
     * 更新通用行为逻辑
     * @param deltaTime 时间增量
     */
    @Override
    public void updateBehavior(float deltaTime) {
        behaviorTimer += deltaTime;

        if (behaviorTimer >= nextBehaviorTime) {
            executeRandomBehavior();
            scheduleNextBehavior();
        }
    }

    /**
     * 执行随机行为
     * 基于权重从动画剪辑组中选择动画
     */
    private void executeRandomBehavior() {
        AnimDataWeight selectedAnim = selectWeightedRandomAnimation();
        if (selectedAnim != null) {
            AnimData animData = createAnimDataFromWeight(selectedAnim);
            animComposer.offerAnimation(animData);
        }
    }

    /**
     * 基于权重选择随机动画
     * @return 选中的动画权重对象
     */
    private AnimDataWeight selectWeightedRandomAnimation() {
        float totalWeight = animClipGroup.getTotalWeight();
        float randomValue = random.nextFloat() * totalWeight;
        float currentWeight = 0f;

        for (AnimDataWeight animWeight : animClipGroup.getAnimations()) {
            currentWeight += animWeight.weight();
            if (randomValue <= currentWeight) {
                return animWeight;
            }
        }

        return null; // 理论上不应该到达这里
    }

    /**
     * 从权重对象创建动画数据
     * @param animWeight 动画权重对象
     * @return 动画数据
     */
    private AnimData createAnimDataFromWeight(AnimDataWeight animWeight) {
        return new AnimData(
            animWeight.animClip(),
            null, // animNext
            animWeight.isLoop(),
            false, // isStrict
            0 // mobility
        );
    }

    /**
     * 安排下一次行为执行时间
     */
    private void scheduleNextBehavior() {
        behaviorTimer = 0f;
        // 随机间隔 5-15 秒
        nextBehaviorTime = 5f + random.nextFloat() * 10f;
    }

    @Override
    public void onBehaviorStart() {
        scheduleNextBehavior();
    }

    @Override
    public void onBehaviorEnd() {
        // 清理资源
    }

    @Override
    public boolean canBeInterrupted() {
        return true; // 通用行为可以被中断
    }
}
```

### 3. 动画事件处理器 (AnimationEventHandler)

#### 功能描述
处理动画播放过程中的各种事件，如动画完成、关键帧事件等。

#### 核心函数

```java
public class AnimationEventHandler {
    
    /**
     * 注册动画完成监听器
     * @param listener 动画完成监听器
     */
    public void registerAnimationCompleteListener(AnimationCompleteListener listener);
    
    /**
     * 注册关键帧事件监听器
     * @param eventName 事件名称
     * @param listener 事件监听器
     */
    public void registerKeyFrameEventListener(String eventName, KeyFrameEventListener listener);
    
    /**
     * 触发动画事件
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    public void triggerAnimationEvent(AnimationEventType eventType, Object eventData);
    
    /**
     * 移除事件监听器
     * @param listener 要移除的监听器
     */
    public void removeEventListener(AnimationEventListener listener);
}
```

## 连接流程

### 1. 初始化流程

```mermaid
graph TD
    A[启动应用] --> B[加载动画资源]
    B --> C[初始化AnimationManager]
    C --> D[创建AnimationStateMachine]
    D --> E[注册动画事件监听器]
    E --> F[设置初始动画状态]
    F --> G[开始动画循环]
```

### 2. 动画播放流程

```mermaid
graph TD
    A[接收动画请求] --> B[检查动画是否存在]
    B -->|存在| C[检查状态转换是否有效]
    B -->|不存在| H[返回错误]
    C -->|有效| D[停止当前动画]
    C -->|无效| I[忽略请求]
    D --> E[播放新动画]
    E --> F[更新状态机]
    F --> G[触发动画事件]
```

### 3. 状态转换流程

```mermaid
graph TD
    A[状态转换请求] --> B[验证转换条件]
    B -->|满足| C[执行淡出动画]
    B -->|不满足| F[拒绝转换]
    C --> D[切换到新状态]
    D --> E[执行淡入动画]
    E --> G[更新状态机状态]
```

## API接口

### 1. 外部调用接口

```java
public interface AnimationSystemAPI {
    
    /**
     * 播放空闲动画
     */
    void playIdleAnimation();
    
    /**
     * 播放行走动画
     * @param direction 行走方向 (LEFT, RIGHT, UP, DOWN)
     */
    void playWalkAnimation(Direction direction);
    
    /**
     * 播放睡眠动画
     */
    void playSleepAnimation();
    
    /**
     * 播放交互动画
     * @param interactionType 交互类型
     */
    void playInteractionAnimation(InteractionType interactionType);
    
    /**
     * 播放特殊动画
     * @param specialAnimationName 特殊动画名称
     */
    void playSpecialAnimation(String specialAnimationName);
    
    /**
     * 获取动画系统状态
     * @return 动画系统状态信息
     */
    AnimationSystemStatus getAnimationSystemStatus();
}
```

### 2. 内部组件接口

```java
public interface AnimationRenderer {
    
    /**
     * 渲染当前动画帧
     * @param batch 渲染批次
     * @param x X坐标
     * @param y Y坐标
     * @param scaleX X轴缩放
     * @param scaleY Y轴缩放
     */
    void renderAnimationFrame(SpriteBatch batch, float x, float y, float scaleX, float scaleY);
    
    /**
     * 设置动画渲染参数
     * @param alpha 透明度 (0.0-1.0)
     * @param tint 色调
     */
    void setRenderParameters(float alpha, Color tint);
}
```

## 配置参数

### 动画配置文件 (animation-config.json)

```json
{
  "defaultAnimations": {
    "idle": {
      "name": "idle",
      "loop": true,
      "priority": 1,
      "fadeInTime": 0.2,
      "fadeOutTime": 0.2
    },
    "walk": {
      "name": "walk",
      "loop": true,
      "priority": 2,
      "fadeInTime": 0.1,
      "fadeOutTime": 0.1
    },
    "sleep": {
      "name": "sleep",
      "loop": true,
      "priority": 3,
      "fadeInTime": 0.5,
      "fadeOutTime": 0.5
    }
  },
  "transitionRules": [
    {
      "from": "idle",
      "to": "walk",
      "condition": "movement_detected",
      "duration": 0.2
    },
    {
      "from": "walk",
      "to": "idle",
      "condition": "movement_stopped",
      "duration": 0.3
    }
  ],
  "performance": {
    "maxConcurrentAnimations": 3,
    "animationCacheSize": 10,
    "updateFrequency": 60
  }
}
```

## 错误处理

### 异常类型

```java
public class AnimationException extends Exception {
    public static class AnimationNotFoundException extends AnimationException {}
    public static class InvalidStateTransitionException extends AnimationException {}
    public static class AnimationResourceException extends AnimationException {}
    public static class AnimationPlaybackException extends AnimationException {}
}
```

### 错误处理策略

1. **资源加载失败**: 使用默认动画替代
2. **动画不存在**: 回退到空闲动画
3. **状态转换无效**: 保持当前状态
4. **播放异常**: 重新初始化动画系统

## 性能优化

### 1. 动画缓存策略
- 预加载常用动画
- LRU缓存淘汰机制
- 内存使用监控

### 2. 渲染优化
- 批量渲染
- 视锥剔除
- LOD (Level of Detail) 系统

### 3. 更新优化
- 时间片轮转更新
- 距离相关更新频率
- 后台动画暂停

---

**模块负责人**: 动画系统开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
