# AI交互逻辑 (AI Interaction Logic)

## 模块概述

AI交互逻辑模块负责处理桌宠与用户之间的智能交互，包括聊天对话、情感响应、行为决策等功能。作为前端与AI服务的桥梁。

**基于原始Ark-Pets项目扩展**:
- 原项目具备基础的用户交互功能 (鼠标拖拽、点击、键盘控制)
- 扩展增加AI智能对话系统
- 集成情感分析和智能行为决策
- 保持与原有动画系统和行为系统的兼容性

## 核心功能

### 1. AI交互管理器 (AIInteractionManager) - 扩展原始项目

#### 功能描述
基于原始Ark-Pets的ArkChar类扩展，增加AI对话功能，与现有的动画和行为系统集成。

#### 核心函数

```java
public class AIInteractionManager {

    private final ArkChar character;
    private final AIServiceClient apiClient;
    private final ConversationContext conversationContext;
    private final EmotionAnalyzer emotionAnalyzer;
    private final BehaviorDecisionMaker behaviorDecisionMaker;

    /**
     * 初始化AI交互管理器
     * @param character 桌宠角色对象 (原始项目的ArkChar)
     * @param apiClient AI服务客户端
     * @return 初始化是否成功
     */
    public boolean initializeAIInteraction(ArkChar character, AIServiceClient apiClient) {
        this.character = character;
        this.apiClient = apiClient;
        this.conversationContext = new ConversationContext(character.getCharacterName());
        this.emotionAnalyzer = new EmotionAnalyzer();
        this.behaviorDecisionMaker = new BehaviorDecisionMaker(character);

        // 注册到原始项目的输入处理系统
        registerInputHandlers();
        return true;
    }

    /**
     * 处理用户文本输入 (扩展原始项目的输入系统)
     * @param userInput 用户输入文本
     * @param inputSource 输入源 (KEYBOARD, VOICE, CLICK)
     */
    public void handleUserTextInput(String userInput, InputSource inputSource) {
        // 验证输入
        if (userInput == null || userInput.trim().isEmpty()) {
            return;
        }

        // 显示用户正在输入的状态
        showTypingIndicator();

        // 异步发送到AI服务
        CompletableFuture.supplyAsync(() -> {
            try {
                return apiClient.sendMessage(userInput, conversationContext.getConversationId());
            } catch (Exception e) {
                Logger.error("AI service call failed", e);
                return createErrorResponse(e.getMessage());
            }
        }).thenAccept(this::handleAIResponse);
    }

    /**
     * 处理AI响应
     * @param aiResponse AI服务响应
     */
    private void handleAIResponse(AIResponse aiResponse) {
        if (aiResponse == null || aiResponse.getContent() == null) {
            handleAIError("AI响应为空");
            return;
        }

        // 隐藏输入指示器
        hideTypingIndicator();

        // 分析情感
        EmotionType emotion = emotionAnalyzer.analyzeTextEmotion(aiResponse.getContent());

        // 决定行为
        BehaviorDecision decision = behaviorDecisionMaker.decideBehaviorFromAIResponse(
            aiResponse.getContent(), emotion, character.getCurrentStatus());

        // 执行行为 (与原始项目的动画系统集成)
        executeBehaviorDecision(decision);

        // 显示对话气泡 (如果启用)
        if (isDialogBubbleEnabled()) {
            showDialogBubble(aiResponse.getContent(), emotion);
        }

        // 语音合成 (如果启用)
        if (isSpeechSynthesisEnabled()) {
            synthesizeAndPlaySpeech(aiResponse.getContent(), emotion);
        }

        // 更新对话上下文
        conversationContext.addMessage(aiResponse);
    }

    /**
     * 执行行为决策 (与原始项目的行为系统集成)
     * @param decision 行为决策
     */
    private void executeBehaviorDecision(BehaviorDecision decision) {
        switch (decision.getBehaviorType()) {
            case ANIMATION_CHANGE:
                // 使用原始项目的AnimComposer播放动画
                AnimData animData = createAnimDataFromDecision(decision);
                character.getAnimComposer().offerAnimation(animData);
                break;

            case MOVEMENT:
                // 触发移动行为
                triggerMovementBehavior(decision.getMovementDirection());
                break;

            case INTERACTION:
                // 执行交互动画
                executeInteractionAnimation(decision.getInteractionType());
                break;

            case EMOTION_EXPRESSION:
                // 表情变化 (可能涉及动画切换)
                expressEmotion(decision.getEmotionType());
                break;
        }
    }

    /**
     * 注册输入处理器到原始项目的输入系统
     */
    private void registerInputHandlers() {
        // 扩展原始项目的InputApplicationAdaptor
        InputApplicationAdaptor inputAdaptor = character.getInputAdaptor();

        // 添加键盘输入处理 (例如按T键开始对话)
        inputAdaptor.addKeyDownHandler(Input.Keys.T, this::startTextConversation);

        // 添加鼠标右键菜单 (AI对话选项)
        inputAdaptor.addMouseRightClickHandler(this::showAIContextMenu);

        // 添加双击处理 (快速AI交互)
        inputAdaptor.addMouseDoubleClickHandler(this::triggerQuickAIInteraction);
    }

    /**
     * 开始文本对话
     */
    private void startTextConversation() {
        // 显示文本输入框或语音输入界面
        showTextInputDialog();
    }

    /**
     * 显示AI上下文菜单
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     */
    private void showAIContextMenu(float x, float y) {
        // 创建包含AI功能的右键菜单
        ContextMenu menu = new ContextMenu();
        menu.addItem("开始对话", this::startTextConversation);
        menu.addItem("切换性格", this::showPersonalitySelector);
        menu.addItem("对话历史", this::showConversationHistory);
        menu.show(x, y);
    }

    /**
     * 触发快速AI交互
     */
    private void triggerQuickAIInteraction() {
        // 发送预设的问候语或随机对话
        String[] quickMessages = {
            "你好！", "今天怎么样？", "在做什么呢？", "心情如何？"
        };
        String randomMessage = quickMessages[new Random().nextInt(quickMessages.length)];
        handleUserTextInput(randomMessage, InputSource.CLICK);
    }
}
```

### 2. 情感分析器 (EmotionAnalyzer)

#### 功能描述
分析AI回复内容，提取情感信息，用于驱动桌宠的情感表现。

#### 情感类型定义

```java
public enum EmotionType {
    HAPPY("happy", "开心", 0.8f),
    SAD("sad", "悲伤", 0.3f),
    ANGRY("angry", "愤怒", 0.2f),
    EXCITED("excited", "兴奋", 0.9f),
    CALM("calm", "平静", 0.5f),
    SURPRISED("surprised", "惊讶", 0.7f),
    CONFUSED("confused", "困惑", 0.4f),
    SLEEPY("sleepy", "困倦", 0.2f);
    
    private final String code;
    private final String description;
    private final float energyLevel; // 能量水平 0.0-1.0
}
```

#### 核心函数

```java
public class EmotionAnalyzer {
    
    /**
     * 分析文本情感
     * @param text AI回复文本
     * @return 情感分析结果
     */
    public EmotionAnalysisResult analyzeTextEmotion(String text);
    
    /**
     * 分析对话上下文情感
     * @param conversationHistory 对话历史
     * @return 上下文情感结果
     */
    public ContextEmotionResult analyzeConversationEmotion(List<Message> conversationHistory);
    
    /**
     * 获取情感强度
     * @param emotionType 情感类型
     * @param text 文本内容
     * @return 情感强度 (0.0-1.0)
     */
    public float getEmotionIntensity(EmotionType emotionType, String text);
    
    /**
     * 混合多种情感
     * @param emotions 情感列表
     * @return 混合后的主导情感
     */
    public EmotionType blendEmotions(List<EmotionWeight> emotions);
    
    /**
     * 更新情感状态
     * @param newEmotion 新情感
     * @param transitionTime 转换时间
     */
    public void updateEmotionState(EmotionType newEmotion, float transitionTime);
}
```

### 3. 行为决策器 (BehaviorDecisionMaker)

#### 功能描述
根据AI回复内容和情感状态，决定桌宠的具体行为表现。

#### 行为类型定义

```java
public enum BehaviorType {
    IDLE_BEHAVIOR("idle", "空闲行为"),
    TALK_BEHAVIOR("talk", "说话行为"),
    MOVE_BEHAVIOR("move", "移动行为"),
    GESTURE_BEHAVIOR("gesture", "手势行为"),
    EXPRESSION_BEHAVIOR("expression", "表情行为"),
    SPECIAL_BEHAVIOR("special", "特殊行为");
    
    private final String code;
    private final String description;
}
```

#### 核心函数

```java
public class BehaviorDecisionMaker {
    
    /**
     * 根据AI回复决定行为
     * @param aiResponse AI回复内容
     * @param currentEmotion 当前情感状态
     * @param petStatus 宠物状态
     * @return 行为决策结果
     */
    public BehaviorDecision decideBehaviorFromAIResponse(String aiResponse, 
                                                       EmotionType currentEmotion,
                                                       PetStatus petStatus);
    
    /**
     * 生成随机行为
     * @param currentState 当前状态
     * @param timeSinceLastBehavior 距离上次行为的时间
     * @return 随机行为决策
     */
    public BehaviorDecision generateRandomBehavior(PetState currentState, 
                                                 float timeSinceLastBehavior);
    
    /**
     * 执行行为决策
     * @param decision 行为决策
     * @param callback 执行结果回调
     */
    public void executeBehaviorDecision(BehaviorDecision decision, 
                                      BehaviorExecutionCallback callback);
    
    /**
     * 中断当前行为
     * @param interruptReason 中断原因
     */
    public void interruptCurrentBehavior(InterruptReason interruptReason);
    
    /**
     * 添加行为规则
     * @param condition 触发条件
     * @param behavior 对应行为
     * @param priority 优先级
     */
    public void addBehaviorRule(BehaviorCondition condition, BehaviorType behavior, int priority);
}
```

### 4. 语音合成器 (SpeechSynthesizer)

#### 功能描述
将AI回复文本转换为语音输出，提供更自然的交互体验。

#### 核心函数

```java
public class SpeechSynthesizer {
    
    /**
     * 初始化语音合成器
     * @param voiceConfig 语音配置
     * @return 初始化是否成功
     */
    public boolean initializeSpeechSynthesizer(VoiceConfig voiceConfig);
    
    /**
     * 合成语音
     * @param text 要合成的文本
     * @param voiceStyle 语音风格
     * @param callback 合成完成回调
     * @return 合成任务句柄
     */
    public SynthesisHandle synthesizeSpeech(String text, VoiceStyle voiceStyle, 
                                          SpeechSynthesisCallback callback);
    
    /**
     * 播放合成的语音
     * @param audioData 音频数据
     * @param volume 音量 (0.0-1.0)
     */
    public void playSynthesizedSpeech(byte[] audioData, float volume);
    
    /**
     * 停止语音播放
     */
    public void stopSpeechPlayback();
    
    /**
     * 设置语音参数
     * @param speed 语速 (0.5-2.0)
     * @param pitch 音调 (0.5-2.0)
     * @param volume 音量 (0.0-1.0)
     */
    public void setSpeechParameters(float speed, float pitch, float volume);
    
    /**
     * 检查语音合成是否可用
     * @return 是否可用
     */
    public boolean isSpeechSynthesisAvailable();
}
```

## 连接流程

### 1. AI交互初始化流程

```mermaid
graph TD
    A[启动AI交互模块] --> B[初始化API客户端]
    B --> C[加载用户配置]
    C --> D[初始化对话管理器]
    D --> E[初始化情感分析器]
    E --> F[初始化行为决策器]
    F --> G[初始化语音合成器]
    G --> H[注册事件监听器]
    H --> I[AI交互系统就绪]
```

### 2. 用户消息处理流程

```mermaid
graph TD
    A[用户输入消息] --> B[验证消息内容]
    B --> C[发送消息到AI服务]
    C --> D[等待AI响应]
    D --> E[接收AI回复]
    E --> F[分析回复情感]
    F --> G[决定行为表现]
    G --> H[执行动画和语音]
    H --> I[更新对话历史]
```

### 3. 流式对话处理流程

```mermaid
graph TD
    A[开始流式对话] --> B[建立SSE连接]
    B --> C[发送消息]
    C --> D[接收Token流]
    D --> E[实时更新显示]
    E --> F[检查是否完成]
    F -->|未完成| D
    F -->|完成| G[处理完整回复]
    G --> H[执行后续行为]
```

## API接口

### 1. 外部调用接口

```java
public interface AIInteractionAPI {
    
    /**
     * 发送用户消息
     * @param message 消息内容
     * @param isVoiceInput 是否为语音输入
     */
    void sendUserMessage(String message, boolean isVoiceInput);
    
    /**
     * 开始语音对话
     */
    void startVoiceConversation();
    
    /**
     * 结束语音对话
     */
    void endVoiceConversation();
    
    /**
     * 切换AI性格
     * @param personalityId 性格ID
     */
    void switchAIPersonality(String personalityId);
    
    /**
     * 获取当前情感状态
     * @return 当前情感状态
     */
    EmotionType getCurrentEmotionState();
    
    /**
     * 设置AI交互模式
     * @param mode 交互模式 (CHAT, COMPANION, ASSISTANT)
     */
    void setInteractionMode(InteractionMode mode);
}
```

### 2. 回调接口

```java
public interface AIResponseCallback {
    void onResponseReceived(String response, EmotionType emotion);
    void onResponseError(String error);
    void onResponseTimeout();
}

public interface AIStreamCallback {
    void onTokenReceived(String token);
    void onStreamComplete(String fullResponse);
    void onStreamError(String error);
}

public interface BehaviorExecutionCallback {
    void onBehaviorStarted(BehaviorType behavior);
    void onBehaviorCompleted(BehaviorType behavior);
    void onBehaviorFailed(BehaviorType behavior, String reason);
}
```

## 配置参数

### AI交互配置文件 (ai-interaction-config.json)

```json
{
  "aiService": {
    "baseUrl": "https://api.arkpets.com/api/v1/ai",
    "timeout": 30000,
    "retryAttempts": 3,
    "streamingEnabled": true
  },
  "emotion": {
    "analysisEnabled": true,
    "emotionTransitionTime": 2.0,
    "defaultEmotion": "calm",
    "emotionKeywords": {
      "happy": ["开心", "高兴", "快乐", "愉快"],
      "sad": ["难过", "悲伤", "沮丧", "失落"],
      "excited": ["兴奋", "激动", "热情", "活跃"]
    }
  },
  "behavior": {
    "randomBehaviorInterval": 30.0,
    "behaviorTransitionTime": 1.0,
    "maxConcurrentBehaviors": 2,
    "behaviorPriorities": {
      "talk": 10,
      "gesture": 8,
      "expression": 6,
      "move": 4,
      "idle": 1
    }
  },
  "speech": {
    "enabled": true,
    "defaultVoice": "female_young",
    "speed": 1.0,
    "pitch": 1.0,
    "volume": 0.8,
    "autoPlay": true
  }
}
```

## 数据模型

### 消息数据模型

```java
public class InteractionMessage {
    private String id;
    private String conversationId;
    private MessageRole role; // USER, ASSISTANT, SYSTEM
    private String content;
    private EmotionType emotion;
    private long timestamp;
    private Map<String, Object> metadata;
    
    // getters and setters
}
```

### 行为决策数据模型

```java
public class BehaviorDecision {
    private BehaviorType behaviorType;
    private String animationName;
    private float duration;
    private Map<String, Object> parameters;
    private int priority;
    private List<BehaviorAction> actions;
    
    // getters and setters
}
```

### 情感分析结果模型

```java
public class EmotionAnalysisResult {
    private EmotionType primaryEmotion;
    private float intensity;
    private Map<EmotionType, Float> emotionScores;
    private List<String> emotionTriggers;
    private float confidence;
    
    // getters and setters
}
```

## 错误处理

### 异常类型

```java
public class AIInteractionException extends Exception {
    public static class AIServiceConnectionException extends AIInteractionException {}
    public static class MessageProcessingException extends AIInteractionException {}
    public static class EmotionAnalysisException extends AIInteractionException {}
    public static class BehaviorExecutionException extends AIInteractionException {}
    public static class SpeechSynthesisException extends AIInteractionException {}
}
```

### 错误处理策略

1. **AI服务连接失败**: 切换到离线模式，使用预设回复
2. **消息处理异常**: 显示错误提示，重试发送
3. **情感分析失败**: 使用默认情感状态
4. **行为执行失败**: 回退到基础行为
5. **语音合成失败**: 仅显示文本，不播放语音

## 性能优化

### 1. 缓存策略
- 对话历史本地缓存
- 情感分析结果缓存
- 语音合成结果缓存

### 2. 异步处理
- 非阻塞消息发送
- 后台情感分析
- 异步语音合成

### 3. 资源管理
- 连接池管理
- 内存使用监控
- 定期清理缓存

---

**模块负责人**: AI交互开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
