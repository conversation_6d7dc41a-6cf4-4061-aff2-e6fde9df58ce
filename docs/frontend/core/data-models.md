# 数据模型 (Data Models)

## 模块概述

数据模型模块定义了前端应用中所有核心数据结构，包括角色模型、动画数据、配置信息等。基于原始Ark-Pets项目的数据结构进行扩展。

**基于原始Ark-Pets项目分析**:
- 原项目使用JSON格式存储配置和模型信息
- 包含完整的角色、动画、行为数据结构
- 支持模型资源的动态加载和管理
- 实现了配置文件的版本兼容性

## 核心数据模型

### 1. 角色相关模型

#### ArkCharacter - 角色基础信息 (基于原始项目)
```java
public class ArkCharacter {
    private String characterId;           // 角色ID
    private String characterName;         // 角色名称
    private String displayName;           // 显示名称
    private String description;           // 角色描述
    private String faction;               // 阵营
    private String profession;            // 职业
    private String rarity;                // 稀有度
    private String modelPath;             // 模型文件路径
    private String atlasPath;             // 图集文件路径
    private String skeletonPath;          // 骨骼文件路径
    private CharacterConfig config;       // 角色配置
    private List<String> availableStages; // 可用阶段
    private Map<String, Object> metadata; // 元数据
    
    // 构造函数、getter、setter等
    public ArkCharacter() {
        this.availableStages = new ArrayList<>();
        this.metadata = new HashMap<>();
    }
    
    /**
     * 检查角色是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return characterId != null && !characterId.isEmpty() &&
               characterName != null && !characterName.isEmpty() &&
               modelPath != null && !modelPath.isEmpty();
    }
    
    /**
     * 获取角色的完整路径
     * @return 完整路径
     */
    public String getFullModelPath() {
        return "characters/" + characterName + "/" + modelPath;
    }
}
```

#### CharacterConfig - 角色配置 (基于原始项目)
```java
public class CharacterConfig {
    private float displayScale;           // 显示缩放
    private float opacityNormal;          // 正常透明度
    private float opacityDim;             // 暗淡透明度
    private PhysicsConfig physics;        // 物理配置
    private AnimationConfig animation;    // 动画配置
    private BehaviorConfig behavior;      // 行为配置
    private AIConfig aiConfig;            // AI配置 (新增)
    
    public CharacterConfig() {
        this.displayScale = 1.0f;
        this.opacityNormal = 1.0f;
        this.opacityDim = 0.5f;
        this.physics = new PhysicsConfig();
        this.animation = new AnimationConfig();
        this.behavior = new BehaviorConfig();
        this.aiConfig = new AIConfig();
    }
}
```

### 2. 动画相关模型

#### AnimationClipInfo - 动画剪辑信息 (基于原始项目)
```java
public class AnimationClipInfo {
    private String name;                  // 动画名称
    private String fullName;              // 完整名称
    private String baseName;              // 基础名称
    private AnimationType type;           // 动画类型
    private AnimationModifier modifier;   // 修饰符
    private AnimationStage stage;         // 阶段
    private float duration;               // 持续时间
    private boolean isLoop;               // 是否循环
    private int mobility;                 // 移动性
    private float weight;                 // 权重
    private Map<String, Object> properties; // 自定义属性
    
    public AnimationClipInfo() {
        this.properties = new HashMap<>();
    }
    
    /**
     * 创建动画数据
     * @return AnimData对象
     */
    public AnimData createAnimData() {
        AnimClip clip = new AnimClip(fullName, duration);
        return new AnimData(clip, null, isLoop, false, mobility);
    }
}
```

#### AnimationType - 动画类型枚举 (基于原始项目)
```java
public enum AnimationType {
    IDLE("Idle", "空闲", 0f),
    MOVE("Move", "移动", 0f),
    SIT("Sit", "坐下", -20f),
    SLEEP("Sleep", "睡觉", -30f),
    INTERACT("Interact", "交互", 0f),
    SPECIAL("Special", "特殊", 0f),
    ATTACK("Attack", "攻击", 0f),
    SKILL("Skill", "技能", 0f);
    
    private final String typeName;
    private final String displayName;
    private final float offsetY;
    
    AnimationType(String typeName, String displayName, float offsetY) {
        this.typeName = typeName;
        this.displayName = displayName;
        this.offsetY = offsetY;
    }
    
    public String getTypeName() { return typeName; }
    public String getDisplayName() { return displayName; }
    public float getOffsetY() { return offsetY; }
}
```

### 3. 配置相关模型

#### ApplicationConfig - 应用配置 (基于原始项目扩展)
```java
public class ApplicationConfig {
    private String version;               // 版本号
    private DisplayConfig display;        // 显示配置
    private PhysicsConfig physics;        // 物理配置
    private BehaviorConfig behavior;      // 行为配置
    private WindowConfig window;          // 窗口配置
    private AIServiceConfig aiService;    // AI服务配置 (新增)
    private NetworkConfig network;        // 网络配置 (新增)
    private boolean isNewcomer;           // 是否新用户
    private String lastSelectedCharacter; // 上次选择的角色
    private Map<String, Object> userPreferences; // 用户偏好
    
    public ApplicationConfig() {
        this.version = "1.0.0";
        this.display = new DisplayConfig();
        this.physics = new PhysicsConfig();
        this.behavior = new BehaviorConfig();
        this.window = new WindowConfig();
        this.aiService = new AIServiceConfig();
        this.network = new NetworkConfig();
        this.isNewcomer = true;
        this.userPreferences = new HashMap<>();
    }
    
    /**
     * 保存配置到文件
     * @param filePath 文件路径
     * @return 是否保存成功
     */
    public boolean saveToFile(String filePath) {
        try {
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            String json = gson.toJson(this);
            Files.write(Paths.get(filePath), json.getBytes(StandardCharsets.UTF_8));
            return true;
        } catch (Exception e) {
            Logger.error("Failed to save config", e);
            return false;
        }
    }
    
    /**
     * 从文件加载配置
     * @param filePath 文件路径
     * @return 配置对象
     */
    public static ApplicationConfig loadFromFile(String filePath) {
        try {
            String json = Files.readString(Paths.get(filePath), StandardCharsets.UTF_8);
            Gson gson = new Gson();
            return gson.fromJson(json, ApplicationConfig.class);
        } catch (Exception e) {
            Logger.error("Failed to load config", e);
            return new ApplicationConfig(); // 返回默认配置
        }
    }
}
```

#### DisplayConfig - 显示配置 (基于原始项目)
```java
public class DisplayConfig {
    private int fps;                      // 帧率
    private float scale;                  // 缩放比例
    private boolean multiMonitors;        // 多显示器支持
    private int marginBottom;             // 底部边距
    private boolean enableOutline;        // 启用描边
    private boolean enableShadow;         // 启用阴影
    private boolean enableAngle;          // 启用ANGLE渲染
    private RenderOutlineMode outlineMode; // 描边模式
    
    public DisplayConfig() {
        this.fps = 60;
        this.scale = 1.0f;
        this.multiMonitors = false;
        this.marginBottom = 40;
        this.enableOutline = true;
        this.enableShadow = true;
        this.enableAngle = false;
        this.outlineMode = RenderOutlineMode.FOCUSED;
    }
}
```

### 4. AI相关模型 (新增)

#### AIServiceConfig - AI服务配置
```java
public class AIServiceConfig {
    private String serviceUrl;            // 服务地址
    private String apiKey;                // API密钥
    private int timeout;                  // 超时时间(毫秒)
    private int maxRetries;               // 最大重试次数
    private boolean enableVoice;          // 启用语音
    private boolean enableEmotion;        // 启用情感分析
    private String defaultPersonality;    // 默认性格
    private Map<String, String> modelMapping; // 模型映射
    
    public AIServiceConfig() {
        this.serviceUrl = "http://localhost:8080";
        this.timeout = 30000;
        this.maxRetries = 3;
        this.enableVoice = false;
        this.enableEmotion = true;
        this.defaultPersonality = "default";
        this.modelMapping = new HashMap<>();
    }
}
```

#### ConversationContext - 对话上下文
```java
public class ConversationContext {
    private String conversationId;        // 对话ID
    private String characterName;         // 角色名称
    private String personalityId;         // 性格ID
    private List<MessageInfo> messages;   // 消息列表
    private EmotionState currentEmotion;  // 当前情感状态
    private long lastInteractionTime;     // 最后交互时间
    private Map<String, Object> context;  // 上下文信息
    
    public ConversationContext(String characterName) {
        this.conversationId = UUID.randomUUID().toString();
        this.characterName = characterName;
        this.messages = new ArrayList<>();
        this.currentEmotion = EmotionState.NEUTRAL;
        this.lastInteractionTime = System.currentTimeMillis();
        this.context = new HashMap<>();
    }
    
    /**
     * 添加消息到上下文
     * @param message 消息信息
     */
    public void addMessage(MessageInfo message) {
        messages.add(message);
        lastInteractionTime = System.currentTimeMillis();
        
        // 保持上下文长度在合理范围内
        if (messages.size() > 50) {
            messages = messages.subList(messages.size() - 40, messages.size());
        }
    }
    
    /**
     * 获取最近的消息
     * @param count 消息数量
     * @return 消息列表
     */
    public List<MessageInfo> getRecentMessages(int count) {
        int start = Math.max(0, messages.size() - count);
        return new ArrayList<>(messages.subList(start, messages.size()));
    }
}
```

### 5. 网络相关模型

#### APIRequest - API请求基类
```java
public abstract class APIRequest {
    private String requestId;             // 请求ID
    private long timestamp;               // 时间戳
    private String userId;                // 用户ID
    private Map<String, String> headers;  // 请求头
    
    public APIRequest() {
        this.requestId = UUID.randomUUID().toString();
        this.timestamp = System.currentTimeMillis();
        this.headers = new HashMap<>();
    }
    
    /**
     * 验证请求参数
     * @return 验证结果
     */
    public abstract ValidationResult validate();
    
    /**
     * 转换为JSON
     * @return JSON字符串
     */
    public String toJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
```

#### APIResponse - API响应基类
```java
public abstract class APIResponse {
    private int code;                     // 响应码
    private String message;               // 响应消息
    private long timestamp;               // 时间戳
    private String requestId;             // 对应的请求ID
    
    public APIResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public APIResponse(int code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    /**
     * 检查响应是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return code >= 200 && code < 300;
    }
    
    /**
     * 从JSON创建响应对象
     * @param json JSON字符串
     * @param clazz 响应类型
     * @return 响应对象
     */
    public static <T extends APIResponse> T fromJson(String json, Class<T> clazz) {
        Gson gson = new Gson();
        return gson.fromJson(json, clazz);
    }
}
```

## 数据验证

### ValidationResult - 验证结果
```java
public class ValidationResult {
    private boolean isValid;              // 是否有效
    private List<String> errors;          // 错误列表
    private List<String> warnings;        // 警告列表
    
    public ValidationResult() {
        this.isValid = true;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
    }
    
    /**
     * 添加错误
     * @param error 错误信息
     */
    public void addError(String error) {
        errors.add(error);
        isValid = false;
    }
    
    /**
     * 添加警告
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        warnings.add(warning);
    }
    
    /**
     * 获取所有问题
     * @return 问题列表
     */
    public List<String> getAllIssues() {
        List<String> issues = new ArrayList<>(errors);
        issues.addAll(warnings);
        return issues;
    }
}
```

## 数据转换工具

### DataConverter - 数据转换器
```java
public class DataConverter {
    
    /**
     * 转换原始项目的角色数据
     * @param legacyCharacter 原始角色数据
     * @return 新的角色数据
     */
    public static ArkCharacter convertLegacyCharacter(Object legacyCharacter) {
        // 实现原始数据格式的转换逻辑
        ArkCharacter character = new ArkCharacter();
        // ... 转换逻辑
        return character;
    }
    
    /**
     * 转换配置文件格式
     * @param legacyConfig 原始配置
     * @return 新的配置
     */
    public static ApplicationConfig convertLegacyConfig(Object legacyConfig) {
        // 实现配置格式的转换逻辑
        ApplicationConfig config = new ApplicationConfig();
        // ... 转换逻辑
        return config;
    }
}
```

## 缓存管理

### DataCache - 数据缓存
```java
public class DataCache {
    private final Map<String, Object> cache;
    private final Map<String, Long> timestamps;
    private final long maxAge;
    
    public DataCache(long maxAgeMillis) {
        this.cache = new ConcurrentHashMap<>();
        this.timestamps = new ConcurrentHashMap<>();
        this.maxAge = maxAgeMillis;
    }
    
    /**
     * 缓存数据
     * @param key 键
     * @param value 值
     */
    public void put(String key, Object value) {
        cache.put(key, value);
        timestamps.put(key, System.currentTimeMillis());
    }
    
    /**
     * 获取缓存数据
     * @param key 键
     * @param clazz 数据类型
     * @return 缓存的数据
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        Long timestamp = timestamps.get(key);
        if (timestamp == null || System.currentTimeMillis() - timestamp > maxAge) {
            cache.remove(key);
            timestamps.remove(key);
            return null;
        }
        
        Object value = cache.get(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanExpired() {
        long now = System.currentTimeMillis();
        timestamps.entrySet().removeIf(entry -> {
            if (now - entry.getValue() > maxAge) {
                cache.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }
}
```

## 错误处理

### DataException - 数据异常
```java
public class DataException extends Exception {
    public static class InvalidDataFormatException extends DataException {}
    public static class DataValidationException extends DataException {}
    public static class DataConversionException extends DataException {}
    public static class CacheException extends DataException {}
}
```

---

**模块负责人**: 数据模型开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
