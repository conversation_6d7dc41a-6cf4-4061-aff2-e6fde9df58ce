# 物理引擎 (Physics Engine)

## 模块概述

物理引擎负责模拟桌宠的物理行为，包括重力、碰撞检测、排斥力等物理现象。基于原始Ark-Pets项目的Plane类实现。

**基于原始Ark-Pets项目分析**:
- 原项目实现了完整的2D物理模拟系统
- 支持重力、摩擦力、反弹、排斥力等物理效果
- 包含窗口边界检测和多显示器支持
- 实现了桌宠间的相互排斥和窗口边缘站立

## 核心功能

### 1. 物理平面管理器 (PhysicsPlaneManager) - 基于原始项目

#### 功能描述
基于原始Ark-Pets的Plane类实现，管理桌宠在2D平面上的物理行为。

#### 核心函数

```java
public class PhysicsPlaneManager {
    
    private final List<PhysicsBarrier> barriers;        // 物理障碍物
    private final List<PhysicsPointCharge> pointCharges; // 点电荷(排斥力)
    private final List<PhysicsWorldArea> worldAreas;     // 世界区域
    private final Vector2 objectSize;                   // 对象尺寸
    private final Vector2 position;                     // 当前位置
    private final Vector2 velocity;                     // 速度向量
    private final Vector2 speedLimit;                   // 速度限制
    
    private float gravity;                               // 重力加速度
    private float resilience;                           // 弹性系数
    private float airFriction;                          // 空气阻力
    private float staticFriction;                       // 静摩擦力
    
    /**
     * 初始化物理平面 (基于原始项目的构造函数)
     */
    public PhysicsPlaneManager() {
        this.barriers = new ArrayList<>();
        this.pointCharges = new ArrayList<>();
        this.worldAreas = new ArrayList<>();
        this.objectSize = new Vector2(0, 0);
        this.position = new Vector2(0, 0);
        this.velocity = new Vector2(0, 0);
        this.speedLimit = new Vector2(0, 0);
        this.gravity = 0;
        this.resilience = 0;
        this.airFriction = 0;
        this.staticFriction = 0;
    }
    
    /**
     * 设置重力加速度 (基于原始项目)
     * @param gravity 重力加速度 (像素/秒²)
     */
    public void setGravity(float gravity) {
        this.gravity = gravity;
    }
    
    /**
     * 设置弹性系数 (基于原始项目)
     * @param resilience 弹性系数 (0.0-1.0)
     */
    public void setResilience(float resilience) {
        this.resilience = Math.max(0f, Math.min(1f, resilience));
    }
    
    /**
     * 设置摩擦力参数 (基于原始项目)
     * @param airFriction 空气阻力系数
     * @param staticFriction 静摩擦力系数
     */
    public void setFrictionParameters(float airFriction, float staticFriction) {
        this.airFriction = airFriction;
        this.staticFriction = staticFriction;
    }
    
    /**
     * 设置对象尺寸 (基于原始项目)
     * @param width 对象宽度
     * @param height 对象高度
     */
    public void setObjectSize(float width, float height) {
        this.objectSize.set(width, height);
    }
    
    /**
     * 设置速度限制 (基于原始项目)
     * @param maxSpeedX X轴最大速度
     * @param maxSpeedY Y轴最大速度
     */
    public void setSpeedLimit(float maxSpeedX, float maxSpeedY) {
        this.speedLimit.set(maxSpeedX, maxSpeedY);
    }
    
    /**
     * 更新物理位置 (基于原始项目的核心方法)
     * @param deltaTime 时间增量(秒)
     */
    public void updatePhysicsPosition(float deltaTime) {
        // 1. 更新速度
        updateVelocity(deltaTime);
        
        // 2. 计算位移
        float deltaX = velocity.x * deltaTime;
        float deltaY = velocity.y * deltaTime;
        
        // 3. 检查边界碰撞
        final float bottomBoundary = getBottomBoundary();
        float droppedHeight = Math.max(Math.signum(gravity) * (position.y - bottomBoundary), 0);
        
        // 4. 处理落地逻辑
        if (position.y != bottomBoundary && limitY(deltaY + position.y) == bottomBoundary) {
            if (Math.signum(gravity) * (position.y - bottomBoundary) > 0) {
                onObjectDropped(droppedHeight);
            }
            velocity.y = 0;
        }
        
        // 5. 更新最终位置
        position.set(limitX(deltaX + position.x), limitY(deltaY + position.y));
    }
    
    /**
     * 更新速度向量 (基于原始项目的物理计算)
     * @param deltaTime 时间增量
     */
    private void updateVelocity(float deltaTime) {
        final float topBoundary = getTopBoundary();
        final float bottomBoundary = getBottomBoundary();
        
        // 1. 应用重力
        velocity.y -= gravity * deltaTime;
        if (position.y == bottomBoundary || (position.y + objectSize.y >= topBoundary && velocity.y > 0)) {
            velocity.y = 0;
        }
        
        // 2. 应用静电力(排斥力)
        for (PhysicsPointCharge charge : pointCharges) {
            float dx = position.x + objectSize.x / 2f - charge.x;
            float dy = position.y + objectSize.y / 2f - charge.y;
            float distance = (float) Math.hypot(dx, dy);
            
            if (distance > 0) {
                float forceX = applyElectrostaticForce(velocity.x, charge.force, distance, dx / distance, deltaTime);
                float forceY = applyElectrostaticForce(velocity.y, charge.force, distance, dy / distance, deltaTime);
                velocity.x = forceX;
                velocity.y = forceY;
            }
        }
        
        // 3. 应用摩擦力
        if (position.y == bottomBoundary) {
            velocity.x = applyFriction(velocity.x, staticFriction, deltaTime);
        }
        velocity.x = applyFriction(velocity.x, airFriction, deltaTime);
        velocity.y = applyFriction(velocity.y, airFriction, deltaTime);
        
        // 4. 限制速度
        if (speedLimit.x != 0 && Math.abs(velocity.x) > speedLimit.x) {
            velocity.x = Math.signum(velocity.x) * speedLimit.x;
        }
        if (speedLimit.y != 0 && Math.abs(velocity.y) > speedLimit.y) {
            velocity.y = Math.signum(velocity.y) * speedLimit.y;
        }
        
        // 5. 处理反弹
        if (resilience != 0 && (position.x == getLeftBoundary() || position.x == getRightBoundary())) {
            velocity.x = (float) (Math.sqrt(velocity.x * velocity.x * resilience) * Math.signum(-velocity.x));
        }
    }
    
    /**
     * 应用静电力效果 (基于原始项目)
     * @param currentVelocity 当前速度分量
     * @param chargeForce 电荷力大小
     * @param distance 距离
     * @param direction 方向分量
     * @param deltaTime 时间增量
     * @return 新的速度分量
     */
    private float applyElectrostaticForce(float currentVelocity, float chargeForce, 
                                        float distance, float direction, float deltaTime) {
        if (distance < 1f) distance = 1f; // 避免除零
        float force = chargeForce / (distance * distance) * direction;
        return currentVelocity + force * deltaTime;
    }
    
    /**
     * 应用摩擦力 (基于原始项目)
     * @param velocity 当前速度
     * @param friction 摩擦系数
     * @param deltaTime 时间增量
     * @return 应用摩擦力后的速度
     */
    private float applyFriction(float velocity, float friction, float deltaTime) {
        if (Math.abs(velocity) < 0.1f) return 0f;
        float frictionForce = friction * deltaTime;
        if (Math.abs(velocity) <= frictionForce) return 0f;
        return velocity - Math.signum(velocity) * frictionForce;
    }
    
    /**
     * 设置物理障碍物 (基于原始项目)
     * @param y Y坐标
     * @param x X坐标
     * @param width 宽度
     * @param isPlatform 是否为平台
     */
    public void setPhysicsBarrier(float y, float x, float width, boolean isPlatform) {
        barriers.add(new PhysicsBarrier(x, y, width, isPlatform));
    }
    
    /**
     * 设置点电荷(排斥力源) (基于原始项目)
     * @param y Y坐标
     * @param x X坐标
     * @param force 力大小
     */
    public void setPointCharge(float y, float x, float force) {
        pointCharges.add(new PhysicsPointCharge(x, y, force));
    }
    
    /**
     * 清除所有物理效果
     */
    public void clearPhysicsEffects() {
        barriers.clear();
        pointCharges.clear();
    }
    
    /**
     * 获取当前位置
     * @return 位置向量
     */
    public Vector2 getCurrentPosition() {
        return new Vector2(position);
    }
    
    /**
     * 获取当前速度
     * @return 速度向量
     */
    public Vector2 getCurrentVelocity() {
        return new Vector2(velocity);
    }
    
    /**
     * 检查是否在地面上
     * @return 是否在地面
     */
    public boolean isOnGround() {
        return Math.abs(position.y - getBottomBoundary()) < 1f;
    }
    
    /**
     * 强制设置位置 (基于原始项目)
     * @param deltaTime 时间增量
     * @param x 新的X坐标
     * @param y 新的Y坐标
     */
    public void forceSetPosition(float deltaTime, float x, float y) {
        position.set(x, y);
        // 重置物理状态
        velocity.set(0, 0);
    }
    
    // 边界计算方法 (基于原始项目的world区域)
    private float getLeftBoundary() {
        return worldAreas.isEmpty() ? 0 : worldAreas.get(0).left;
    }
    
    private float getRightBoundary() {
        return worldAreas.isEmpty() ? 1920 : worldAreas.get(0).right - objectSize.x;
    }
    
    private float getTopBoundary() {
        return worldAreas.isEmpty() ? 0 : worldAreas.get(0).top;
    }
    
    private float getBottomBoundary() {
        return worldAreas.isEmpty() ? 1080 : worldAreas.get(0).bottom;
    }
    
    private float limitX(float x) {
        return Math.max(getLeftBoundary(), Math.min(getRightBoundary(), x));
    }
    
    private float limitY(float y) {
        return Math.max(getBottomBoundary(), Math.min(getTopBoundary(), y));
    }
    
    /**
     * 对象落地事件回调
     * @param dropHeight 下落高度
     */
    protected void onObjectDropped(float dropHeight) {
        // 子类可以重写此方法处理落地事件
        // 例如播放落地动画、音效等
    }
}
```

### 2. 物理数据模型

#### PhysicsBarrier - 物理障碍物
```java
public class PhysicsBarrier {
    public final float x;           // X坐标
    public final float y;           // Y坐标
    public final float width;       // 宽度
    public final boolean isPlatform; // 是否为平台
    
    public PhysicsBarrier(float x, float y, float width, boolean isPlatform) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.isPlatform = isPlatform;
    }
}
```

#### PhysicsPointCharge - 点电荷
```java
public class PhysicsPointCharge {
    public final float x;      // X坐标
    public final float y;      // Y坐标
    public final float force;  // 力大小
    
    public PhysicsPointCharge(float x, float y, float force) {
        this.x = x;
        this.y = y;
        this.force = force;
    }
}
```

#### PhysicsWorldArea - 世界区域
```java
public class PhysicsWorldArea {
    public final float left;    // 左边界
    public final float right;   // 右边界
    public final float top;     // 上边界
    public final float bottom;  // 下边界
    
    public PhysicsWorldArea(float left, float right, float top, float bottom) {
        this.left = left;
        this.right = right;
        this.top = top;
        this.bottom = bottom;
    }
}
```

## 连接流程

### 1. 物理系统初始化流程

```mermaid
graph TD
    A[启动应用] --> B[创建PhysicsPlaneManager]
    B --> C[设置重力参数]
    C --> D[设置摩擦力参数]
    D --> E[设置对象尺寸]
    E --> F[设置速度限制]
    F --> G[初始化世界边界]
    G --> H[开始物理循环]
```

### 2. 物理更新循环

```mermaid
graph TD
    A[每帧更新] --> B[计算重力效果]
    B --> C[计算排斥力]
    C --> D[应用摩擦力]
    D --> E[限制速度]
    E --> F[检查边界碰撞]
    F --> G[更新位置]
    G --> H[触发物理事件]
    H --> A
```

## API接口

### 1. 物理配置接口

```java
public interface PhysicsConfigAPI {
    
    /**
     * 设置重力配置
     * @param gravity 重力加速度
     */
    void setGravityConfig(float gravity);
    
    /**
     * 设置摩擦力配置
     * @param airFriction 空气阻力
     * @param staticFriction 静摩擦力
     */
    void setFrictionConfig(float airFriction, float staticFriction);
    
    /**
     * 设置弹性配置
     * @param resilience 弹性系数
     */
    void setResilienceConfig(float resilience);
    
    /**
     * 获取物理状态
     * @return 物理状态信息
     */
    PhysicsStatus getPhysicsStatus();
}
```

## 配置参数

### 物理配置文件 (physics-config.json)

```json
{
  "gravity": {
    "acceleration": -980.0,
    "enabled": true
  },
  "friction": {
    "air": 0.95,
    "static": 0.85,
    "enabled": true
  },
  "bounce": {
    "resilience": 0.7,
    "enabled": true
  },
  "limits": {
    "maxSpeedX": 500.0,
    "maxSpeedY": 1000.0
  },
  "repulsion": {
    "force": 100.0,
    "range": 150.0,
    "enabled": true
  },
  "world": {
    "multiMonitor": true,
    "bottomMargin": 40
  }
}
```

## 错误处理

### 异常类型

```java
public class PhysicsException extends Exception {
    public static class InvalidPhysicsParameterException extends PhysicsException {}
    public static class PhysicsCalculationException extends PhysicsException {}
    public static class WorldBoundaryException extends PhysicsException {}
}
```

### 错误处理策略

1. **参数验证失败**: 使用默认安全值
2. **计算溢出**: 限制在安全范围内
3. **边界异常**: 强制回到有效区域
4. **性能问题**: 降低物理精度

## 性能优化

### 1. 计算优化
- 使用快速平方根算法
- 缓存常用计算结果
- 批量处理物理对象

### 2. 内存优化
- 对象池管理物理实体
- 及时清理无效障碍物
- 压缩物理状态数据

### 3. 精度控制
- 自适应时间步长
- 距离相关精度调整
- 休眠状态检测

---

**模块负责人**: 物理引擎开发组  
**文档版本**: v1.0  
**最后更新**: 2025-01-01
