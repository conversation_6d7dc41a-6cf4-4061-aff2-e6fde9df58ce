# 纹理管理器 (Texture Manager)

## 模块概述

纹理管理器负责Ark-Pets AI Enhanced项目中所有纹理资源的加载、缓存、优化和管理。提供高效的纹理内存管理、动态加载卸载、格式转换、压缩优化等功能，确保应用的流畅运行和内存使用效率。

**核心职责**:
- 多格式纹理文件加载和解析
- 纹理缓存和内存池管理
- 动态纹理压缩和优化
- 纹理图集管理和合并
- GPU纹理资源管理

## 核心功能架构

### 1. 纹理管理器架构

#### 分层管理架构
```
┌─────────────────────────────────────┐
│           纹理管理器                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 加载管理  │ 缓存管理  │ 优化处理  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           纹理处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 格式转换  │ 压缩处理  │ 图集合并  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           GPU资源层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 显存管理  │ 纹理绑定  │ 渲染优化  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 纹理加载流程

#### 智能加载流程图
```mermaid
graph TB
    subgraph "纹理加载流程"
        LoadRequest[加载请求]
        CacheCheck[缓存检查]
        FormatDetect[格式检测]
        FileLoad[文件加载]
        FormatConvert[格式转换]
        Compress[压缩优化]
        GPUUpload[GPU上传]
        CacheStore[缓存存储]
        LoadComplete[加载完成]
    end
    
    subgraph "优化处理"
        SizeOptimize[尺寸优化]
        QualityAdjust[质量调整]
        MemoryAlign[内存对齐]
    end
    
    subgraph "错误处理"
        ErrorHandle[错误处理]
        FallbackTexture[降级纹理]
        RetryLoad[重试加载]
    end
    
    LoadRequest --> CacheCheck
    CacheCheck -->|缓存命中| LoadComplete
    CacheCheck -->|缓存未命中| FormatDetect
    FormatDetect --> FileLoad
    FileLoad --> FormatConvert
    FormatConvert --> SizeOptimize
    SizeOptimize --> QualityAdjust
    QualityAdjust --> MemoryAlign
    MemoryAlign --> Compress
    Compress --> GPUUpload
    GPUUpload --> CacheStore
    CacheStore --> LoadComplete
    
    FileLoad -->|加载失败| ErrorHandle
    FormatConvert -->|转换失败| ErrorHandle
    GPUUpload -->|上传失败| ErrorHandle
    ErrorHandle --> FallbackTexture
    ErrorHandle --> RetryLoad
    RetryLoad -->|重试成功| FormatConvert
    RetryLoad -->|重试失败| FallbackTexture
```

## 核心类和接口

### 1. 主要纹理管理器类

#### TextureManager - 纹理管理器主类
```java
/**
 * 纹理管理器主类
 * 负责统一管理所有纹理资源的加载、缓存和优化
 */
public class TextureManager {
    
    private final TextureCache textureCache;
    private final TextureLoader textureLoader;
    private final TextureOptimizer textureOptimizer;
    private final AtlasManager atlasManager;
    private final GPUResourceManager gpuResourceManager;
    private final ExecutorService loadingExecutor;
    
    public TextureManager(TextureManagerConfig config) {
        this.textureCache = new TextureCache(config.getCacheConfig());
        this.textureLoader = new TextureLoader(config.getLoaderConfig());
        this.textureOptimizer = new TextureOptimizer(config.getOptimizerConfig());
        this.atlasManager = new AtlasManager(config.getAtlasConfig());
        this.gpuResourceManager = new GPUResourceManager();
        this.loadingExecutor = Executors.newFixedThreadPool(config.getLoadingThreads());
    }
    
    /**
     * 异步加载纹理
     * @param texturePath 纹理文件路径
     * @param options 加载选项
     * @return 纹理加载Future
     */
    public CompletableFuture<Texture> loadTextureAsync(String texturePath, TextureLoadOptions options) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 检查缓存
                String cacheKey = generateCacheKey(texturePath, options);
                Texture cachedTexture = textureCache.get(cacheKey);
                if (cachedTexture != null && cachedTexture.isValid()) {
                    return cachedTexture;
                }
                
                // 2. 加载原始数据
                TextureData rawData = textureLoader.loadRawData(texturePath);
                
                // 3. 应用加载选项
                if (options.shouldResize()) {
                    rawData = textureOptimizer.resize(rawData, options.getTargetSize());
                }
                
                if (options.shouldCompress()) {
                    rawData = textureOptimizer.compress(rawData, options.getCompressionFormat());
                }
                
                if (options.shouldGenerateMipmaps()) {
                    rawData = textureOptimizer.generateMipmaps(rawData);
                }
                
                // 4. 创建GPU纹理
                Texture texture = gpuResourceManager.createTexture(rawData, options);
                
                // 5. 缓存结果
                textureCache.put(cacheKey, texture);
                
                return texture;
                
            } catch (Exception e) {
                throw new TextureLoadingException("纹理加载失败: " + texturePath, e);
            }
        }, loadingExecutor);
    }
    
    /**
     * 同步加载纹理
     * @param texturePath 纹理文件路径
     * @param options 加载选项
     * @return 纹理对象
     */
    public Texture loadTexture(String texturePath, TextureLoadOptions options) {
        try {
            return loadTextureAsync(texturePath, options).get();
        } catch (Exception e) {
            throw new TextureLoadingException("同步加载纹理失败: " + texturePath, e);
        }
    }
    
    /**
     * 加载纹理图集
     * @param atlasPath 图集文件路径
     * @return 纹理图集
     */
    public TextureAtlas loadAtlas(String atlasPath) {
        return atlasManager.loadAtlas(atlasPath);
    }
    
    /**
     * 创建动态纹理图集
     * @param texturePaths 纹理路径列表
     * @param atlasSize 图集大小
     * @return 动态图集
     */
    public DynamicTextureAtlas createDynamicAtlas(List<String> texturePaths, Size atlasSize) {
        return atlasManager.createDynamicAtlas(texturePaths, atlasSize);
    }
    
    /**
     * 获取纹理区域
     * @param atlasPath 图集路径
     * @param regionName 区域名称
     * @return 纹理区域
     */
    public TextureRegion getTextureRegion(String atlasPath, String regionName) {
        TextureAtlas atlas = loadAtlas(atlasPath);
        return atlas.findRegion(regionName);
    }
    
    /**
     * 预加载纹理列表
     * @param texturePaths 纹理路径列表
     * @param options 加载选项
     */
    public CompletableFuture<Void> preloadTextures(List<String> texturePaths, TextureLoadOptions options) {
        List<CompletableFuture<Texture>> futures = texturePaths.stream()
            .map(path -> loadTextureAsync(path, options))
            .collect(Collectors.toList());
            
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }
    
    /**
     * 释放纹理资源
     * @param texturePath 纹理路径
     */
    public void releaseTexture(String texturePath) {
        textureCache.remove(texturePath);
    }
    
    /**
     * 清理未使用的纹理
     */
    public void cleanupUnusedTextures() {
        textureCache.cleanupUnused();
        gpuResourceManager.cleanupUnused();
    }
    
    /**
     * 获取内存使用统计
     * @return 内存统计信息
     */
    public TextureMemoryStats getMemoryStats() {
        return new TextureMemoryStats(
            textureCache.getMemoryUsage(),
            gpuResourceManager.getGPUMemoryUsage(),
            textureCache.getCacheStats()
        );
    }
    
    /**
     * 设置纹理质量级别
     * @param qualityLevel 质量级别
     */
    public void setTextureQuality(TextureQuality qualityLevel) {
        textureOptimizer.setQualityLevel(qualityLevel);
    }
    
    /**
     * 释放所有资源
     */
    public void dispose() {
        textureCache.dispose();
        atlasManager.dispose();
        gpuResourceManager.dispose();
        loadingExecutor.shutdown();
    }
    
    private String generateCacheKey(String path, TextureLoadOptions options) {
        return path + "_" + options.hashCode();
    }
}
```

### 2. 纹理加载器

#### TextureLoader - 纹理加载器类
```java
/**
 * 纹理加载器
 * 负责从各种来源加载纹理数据
 */
public class TextureLoader {
    
    private final Map<String, TextureDecoder> decoders;
    private final FileSystemManager fileSystemManager;
    private final NetworkResourceLoader networkLoader;
    
    public TextureLoader(TextureLoaderConfig config) {
        this.decoders = initializeDecoders();
        this.fileSystemManager = new FileSystemManager(config);
        this.networkLoader = new NetworkResourceLoader(config);
    }
    
    /**
     * 加载原始纹理数据
     * @param texturePath 纹理路径
     * @return 纹理数据
     */
    public TextureData loadRawData(String texturePath) throws IOException {
        // 1. 检测文件格式
        String extension = getFileExtension(texturePath);
        TextureDecoder decoder = decoders.get(extension.toLowerCase());
        
        if (decoder == null) {
            throw new UnsupportedFormatException("不支持的纹理格式: " + extension);
        }
        
        // 2. 加载文件数据
        byte[] fileData = loadFileBytes(texturePath);
        
        // 3. 解码纹理数据
        return decoder.decode(fileData);
    }
    
    /**
     * 检测纹理格式
     * @param data 文件数据
     * @return 纹理格式
     */
    public TextureFormat detectFormat(byte[] data) {
        for (TextureDecoder decoder : decoders.values()) {
            if (decoder.canDecode(data)) {
                return decoder.getFormat();
            }
        }
        return TextureFormat.UNKNOWN;
    }
    
    private byte[] loadFileBytes(String path) throws IOException {
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return networkLoader.loadBytes(path);
        } else {
            return fileSystemManager.readBytes(path);
        }
    }
    
    private Map<String, TextureDecoder> initializeDecoders() {
        Map<String, TextureDecoder> decoders = new HashMap<>();
        decoders.put("png", new PngDecoder());
        decoders.put("jpg", new JpegDecoder());
        decoders.put("jpeg", new JpegDecoder());
        decoders.put("bmp", new BmpDecoder());
        decoders.put("tga", new TgaDecoder());
        decoders.put("dds", new DdsDecoder());
        decoders.put("ktx", new KtxDecoder());
        return decoders;
    }
    
    private String getFileExtension(String path) {
        int lastDot = path.lastIndexOf('.');
        return lastDot > 0 ? path.substring(lastDot + 1) : "";
    }
}
```

### 3. 纹理优化器

#### TextureOptimizer - 纹理优化器类
```java
/**
 * 纹理优化器
 * 负责纹理的压缩、缩放、格式转换等优化处理
 */
public class TextureOptimizer {

    private TextureQuality currentQuality;
    private final Map<TextureFormat, TextureCompressor> compressors;
    private final TextureScaler textureScaler;
    private final MipmapGenerator mipmapGenerator;

    public TextureOptimizer(TextureOptimizerConfig config) {
        this.currentQuality = config.getDefaultQuality();
        this.compressors = initializeCompressors();
        this.textureScaler = new TextureScaler();
        this.mipmapGenerator = new MipmapGenerator();
    }

    /**
     * 调整纹理尺寸
     * @param textureData 原始纹理数据
     * @param targetSize 目标尺寸
     * @return 调整后的纹理数据
     */
    public TextureData resize(TextureData textureData, Size targetSize) {
        if (textureData.getWidth() == targetSize.width &&
            textureData.getHeight() == targetSize.height) {
            return textureData;
        }

        return textureScaler.scale(textureData, targetSize, getScalingAlgorithm());
    }

    /**
     * 压缩纹理数据
     * @param textureData 原始纹理数据
     * @param format 压缩格式
     * @return 压缩后的纹理数据
     */
    public TextureData compress(TextureData textureData, TextureFormat format) {
        TextureCompressor compressor = compressors.get(format);
        if (compressor == null) {
            throw new UnsupportedOperationException("不支持的压缩格式: " + format);
        }

        return compressor.compress(textureData, getCompressionQuality());
    }

    /**
     * 生成Mipmap
     * @param textureData 原始纹理数据
     * @return 包含Mipmap的纹理数据
     */
    public TextureData generateMipmaps(TextureData textureData) {
        return mipmapGenerator.generate(textureData);
    }

    /**
     * 自动优化纹理
     * @param textureData 原始纹理数据
     * @param constraints 优化约束
     * @return 优化后的纹理数据
     */
    public TextureData autoOptimize(TextureData textureData, OptimizationConstraints constraints) {
        TextureData optimized = textureData;

        // 1. 尺寸优化
        if (constraints.hasMaxSize()) {
            Size maxSize = constraints.getMaxSize();
            if (optimized.getWidth() > maxSize.width || optimized.getHeight() > maxSize.height) {
                Size targetSize = calculateOptimalSize(optimized, maxSize);
                optimized = resize(optimized, targetSize);
            }
        }

        // 2. 格式优化
        if (constraints.hasTargetFormat()) {
            TextureFormat targetFormat = selectOptimalFormat(optimized, constraints);
            if (targetFormat != optimized.getFormat()) {
                optimized = compress(optimized, targetFormat);
            }
        }

        // 3. 质量优化
        if (constraints.hasMemoryLimit()) {
            optimized = optimizeForMemory(optimized, constraints.getMemoryLimit());
        }

        return optimized;
    }

    /**
     * 设置质量级别
     * @param quality 质量级别
     */
    public void setQualityLevel(TextureQuality quality) {
        this.currentQuality = quality;
    }

    private ScalingAlgorithm getScalingAlgorithm() {
        return switch (currentQuality) {
            case HIGH -> ScalingAlgorithm.LANCZOS;
            case MEDIUM -> ScalingAlgorithm.BILINEAR;
            case LOW -> ScalingAlgorithm.NEAREST;
        };
    }

    private float getCompressionQuality() {
        return switch (currentQuality) {
            case HIGH -> 0.95f;
            case MEDIUM -> 0.85f;
            case LOW -> 0.75f;
        };
    }

    private Size calculateOptimalSize(TextureData data, Size maxSize) {
        float aspectRatio = (float) data.getWidth() / data.getHeight();

        if (data.getWidth() > data.getHeight()) {
            int width = Math.min(data.getWidth(), maxSize.width);
            int height = (int) (width / aspectRatio);
            return new Size(width, height);
        } else {
            int height = Math.min(data.getHeight(), maxSize.height);
            int width = (int) (height * aspectRatio);
            return new Size(width, height);
        }
    }

    private TextureFormat selectOptimalFormat(TextureData data, OptimizationConstraints constraints) {
        // 根据纹理特性和约束选择最优格式
        if (data.hasAlpha()) {
            return constraints.isHighQuality() ? TextureFormat.RGBA8 : TextureFormat.RGBA4;
        } else {
            return constraints.isHighQuality() ? TextureFormat.RGB8 : TextureFormat.RGB565;
        }
    }

    private TextureData optimizeForMemory(TextureData data, long memoryLimit) {
        long currentSize = data.getMemorySize();
        if (currentSize <= memoryLimit) {
            return data;
        }

        // 逐步降低质量直到满足内存限制
        float scaleFactor = (float) Math.sqrt((double) memoryLimit / currentSize);
        Size newSize = new Size(
            (int) (data.getWidth() * scaleFactor),
            (int) (data.getHeight() * scaleFactor)
        );

        return resize(data, newSize);
    }

    private Map<TextureFormat, TextureCompressor> initializeCompressors() {
        Map<TextureFormat, TextureCompressor> compressors = new HashMap<>();
        compressors.put(TextureFormat.DXT1, new DXT1Compressor());
        compressors.put(TextureFormat.DXT5, new DXT5Compressor());
        compressors.put(TextureFormat.ETC2, new ETC2Compressor());
        compressors.put(TextureFormat.ASTC, new ASTCCompressor());
        return compressors;
    }
}
```

## 使用示例

### 基本纹理加载
```java
// 创建纹理管理器
TextureManager textureManager = new TextureManager(config);

// 加载单个纹理
TextureLoadOptions options = new TextureLoadOptions()
    .setResize(true)
    .setTargetSize(new Size(512, 512))
    .setCompress(true)
    .setCompressionFormat(TextureFormat.DXT5)
    .setGenerateMipmaps(true);

Texture texture = textureManager.loadTexture("characters/amiya.png", options);

// 异步加载纹理
textureManager.loadTextureAsync("backgrounds/office.jpg", options)
    .thenAccept(loadedTexture -> {
        // 使用加载的纹理
        System.out.println("纹理加载完成: " + loadedTexture.getWidth() + "x" + loadedTexture.getHeight());
    });
```

### 纹理图集使用
```java
// 加载预制图集
TextureAtlas characterAtlas = textureManager.loadAtlas("atlases/characters.atlas");
TextureRegion amiyaRegion = characterAtlas.findRegion("amiya");

// 创建动态图集
List<String> texturePaths = Arrays.asList(
    "ui/button_normal.png",
    "ui/button_pressed.png",
    "ui/button_disabled.png"
);
DynamicTextureAtlas uiAtlas = textureManager.createDynamicAtlas(texturePaths, new Size(1024, 1024));
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的纹理管理器实现，支持多格式纹理加载、优化、缓存和图集管理
