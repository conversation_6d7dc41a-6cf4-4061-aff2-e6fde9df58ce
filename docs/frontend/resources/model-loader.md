# 模型加载器 (Model Loader)

## 模块概述

模型加载器负责Ark-Pets AI Enhanced项目中所有3D模型和2D精灵资源的加载、缓存、管理和优化。支持多种格式的模型文件，包括Spine动画、3D模型、纹理贴图等，提供高效的资源管理和内存优化机制。

**核心职责**:
- 多格式模型文件加载和解析
- 资源缓存和内存管理
- 异步加载和进度跟踪
- 模型数据预处理和优化
- 错误处理和资源回收

## 核心功能架构

### 1. 模型加载器架构

#### 分层加载架构
```
┌─────────────────────────────────────┐
│           模型加载器                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 格式解析  │ 缓存管理  │ 优化处理  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           资源管理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ Spine动画 │ 3D模型   │ 纹理贴图  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           文件系统层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 本地文件  │ 网络资源  │ 压缩包   │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 模型加载流程

#### 异步加载流程图
```mermaid
graph TB
    subgraph "模型加载流程"
        LoadRequest[加载请求]
        CacheCheck[缓存检查]
        FileLoad[文件加载]
        FormatParse[格式解析]
        DataProcess[数据处理]
        CacheStore[缓存存储]
        LoadComplete[加载完成]
    end
    
    subgraph "错误处理"
        ErrorHandle[错误处理]
        Retry[重试机制]
        Fallback[降级处理]
    end
    
    subgraph "优化处理"
        Compress[数据压缩]
        Optimize[性能优化]
        Validate[数据验证]
    end
    
    LoadRequest --> CacheCheck
    CacheCheck -->|缓存命中| LoadComplete
    CacheCheck -->|缓存未命中| FileLoad
    FileLoad --> FormatParse
    FormatParse --> DataProcess
    DataProcess --> Compress
    Compress --> Optimize
    Optimize --> Validate
    Validate --> CacheStore
    CacheStore --> LoadComplete
    
    FileLoad -->|加载失败| ErrorHandle
    FormatParse -->|解析失败| ErrorHandle
    ErrorHandle --> Retry
    Retry -->|重试成功| DataProcess
    Retry -->|重试失败| Fallback
```

## 核心类和接口

### 1. 主要模型加载器类

#### ModelLoader - 主加载器类
```java
/**
 * 模型加载器主类
 * 负责统一管理各种格式的模型加载
 */
public class ModelLoader {
    
    private final AssetManager assetManager;
    private final ModelCache modelCache;
    private final LoadingProgressTracker progressTracker;
    private final ExecutorService loadingExecutor;
    
    /**
     * 异步加载模型
     * @param modelPath 模型文件路径
     * @param modelType 模型类型
     * @param callback 加载完成回调
     * @return 加载任务Future
     */
    public CompletableFuture<ModelData> loadModelAsync(String modelPath, 
                                                       ModelType modelType, 
                                                       LoadingCallback callback) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 检查缓存
                ModelData cachedModel = modelCache.get(modelPath);
                if (cachedModel != null) {
                    callback.onProgress(1.0f);
                    callback.onComplete(cachedModel);
                    return cachedModel;
                }
                
                // 2. 开始加载
                callback.onStart(modelPath);
                progressTracker.startTracking(modelPath);
                
                // 3. 根据类型选择加载器
                ModelParser parser = getParserForType(modelType);
                
                // 4. 加载文件数据
                byte[] fileData = loadFileData(modelPath);
                progressTracker.updateProgress(modelPath, 0.3f);
                
                // 5. 解析模型数据
                ModelData modelData = parser.parse(fileData);
                progressTracker.updateProgress(modelPath, 0.7f);
                
                // 6. 优化处理
                optimizeModelData(modelData);
                progressTracker.updateProgress(modelPath, 0.9f);
                
                // 7. 缓存结果
                modelCache.put(modelPath, modelData);
                progressTracker.updateProgress(modelPath, 1.0f);
                
                callback.onComplete(modelData);
                return modelData;
                
            } catch (Exception e) {
                callback.onError(e);
                throw new ModelLoadingException("模型加载失败: " + modelPath, e);
            } finally {
                progressTracker.finishTracking(modelPath);
            }
        }, loadingExecutor);
    }
    
    /**
     * 同步加载模型
     * @param modelPath 模型文件路径
     * @param modelType 模型类型
     * @return 模型数据
     */
    public ModelData loadModel(String modelPath, ModelType modelType) {
        try {
            return loadModelAsync(modelPath, modelType, new SilentCallback()).get();
        } catch (Exception e) {
            throw new ModelLoadingException("同步加载模型失败: " + modelPath, e);
        }
    }
    
    /**
     * 批量加载模型
     * @param modelRequests 模型加载请求列表
     * @return 加载结果映射
     */
    public CompletableFuture<Map<String, ModelData>> loadModelsAsync(List<ModelRequest> modelRequests) {
        List<CompletableFuture<Pair<String, ModelData>>> futures = modelRequests.stream()
            .map(request -> loadModelAsync(request.getPath(), request.getType(), request.getCallback())
                .thenApply(data -> Pair.of(request.getPath(), data)))
            .collect(Collectors.toList());
            
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)));
    }
    
    /**
     * 预加载模型
     * @param modelPaths 模型路径列表
     */
    public void preloadModels(List<String> modelPaths) {
        modelPaths.forEach(path -> {
            ModelType type = detectModelType(path);
            loadModelAsync(path, type, new PreloadCallback());
        });
    }
    
    /**
     * 获取加载进度
     * @param modelPath 模型路径
     * @return 加载进度 (0.0-1.0)
     */
    public float getLoadingProgress(String modelPath) {
        return progressTracker.getProgress(modelPath);
    }
    
    /**
     * 取消加载任务
     * @param modelPath 模型路径
     */
    public void cancelLoading(String modelPath) {
        progressTracker.cancelTracking(modelPath);
        // 取消对应的Future任务
    }
    
    /**
     * 清理资源
     */
    public void dispose() {
        modelCache.clear();
        progressTracker.clear();
        loadingExecutor.shutdown();
    }
    
    private ModelParser getParserForType(ModelType type) {
        return switch (type) {
            case SPINE -> new SpineModelParser();
            case OBJ -> new ObjModelParser();
            case FBX -> new FbxModelParser();
            case GLTF -> new GltfModelParser();
            case TEXTURE -> new TextureParser();
            default -> throw new UnsupportedOperationException("不支持的模型类型: " + type);
        };
    }
    
    private byte[] loadFileData(String path) throws IOException {
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return loadFromNetwork(path);
        } else if (path.endsWith(".zip") || path.endsWith(".pak")) {
            return loadFromArchive(path);
        } else {
            return loadFromFile(path);
        }
    }
    
    private void optimizeModelData(ModelData modelData) {
        // 数据压缩
        if (modelData.getVertices().length > 10000) {
            modelData.compressVertices();
        }
        
        // 纹理优化
        if (modelData.hasTextures()) {
            modelData.optimizeTextures();
        }
        
        // 内存对齐
        modelData.alignMemory();
    }
}
```

### 2. 模型解析器接口

#### ModelParser - 模型解析器接口
```java
/**
 * 模型解析器接口
 * 定义各种格式模型的解析规范
 */
public interface ModelParser {
    
    /**
     * 解析模型数据
     * @param data 原始文件数据
     * @return 解析后的模型数据
     * @throws ModelParseException 解析异常
     */
    ModelData parse(byte[] data) throws ModelParseException;
    
    /**
     * 验证文件格式
     * @param data 文件数据
     * @return 是否为支持的格式
     */
    boolean isValidFormat(byte[] data);
    
    /**
     * 获取支持的文件扩展名
     * @return 扩展名列表
     */
    List<String> getSupportedExtensions();
    
    /**
     * 获取解析器版本
     * @return 版本信息
     */
    String getVersion();
}
```

#### SpineModelParser - Spine动画解析器
```java
/**
 * Spine动画模型解析器
 * 专门处理Spine格式的动画文件
 */
public class SpineModelParser implements ModelParser {
    
    private final SkeletonJson skeletonJson;
    private final AtlasLoader atlasLoader;
    
    public SpineModelParser() {
        this.skeletonJson = new SkeletonJson();
        this.atlasLoader = new AtlasLoader();
    }
    
    @Override
    public ModelData parse(byte[] data) throws ModelParseException {
        try {
            // 1. 解析JSON骨骼数据
            String jsonData = new String(data, StandardCharsets.UTF_8);
            SkeletonData skeletonData = skeletonJson.readSkeletonData(jsonData);
            
            // 2. 加载纹理图集
            TextureAtlas atlas = loadAtlas(skeletonData);
            
            // 3. 创建骨骼实例
            Skeleton skeleton = new Skeleton(skeletonData);
            
            // 4. 创建动画状态
            AnimationStateData stateData = new AnimationStateData(skeletonData);
            AnimationState animationState = new AnimationState(stateData);
            
            // 5. 构建模型数据
            SpineModelData modelData = new SpineModelData();
            modelData.setSkeleton(skeleton);
            modelData.setAnimationState(animationState);
            modelData.setAtlas(atlas);
            modelData.setSkeletonData(skeletonData);
            
            // 6. 预处理动画
            preprocessAnimations(modelData);
            
            return modelData;
            
        } catch (Exception e) {
            throw new ModelParseException("Spine模型解析失败", e);
        }
    }
    
    @Override
    public boolean isValidFormat(byte[] data) {
        try {
            String content = new String(data, StandardCharsets.UTF_8);
            return content.contains("\"skeleton\"") && 
                   content.contains("\"bones\"") && 
                   content.contains("\"animations\"");
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public List<String> getSupportedExtensions() {
        return Arrays.asList(".json", ".skel");
    }
    
    @Override
    public String getVersion() {
        return "Spine-4.1.0";
    }
    
    private TextureAtlas loadAtlas(SkeletonData skeletonData) {
        // 根据骨骼数据加载对应的纹理图集
        String atlasPath = deriveAtlasPath(skeletonData);
        return atlasLoader.load(atlasPath);
    }
    
    private void preprocessAnimations(SpineModelData modelData) {
        // 预处理动画数据，优化播放性能
        for (Animation animation : modelData.getSkeletonData().getAnimations()) {
            optimizeAnimation(animation);
        }
    }
    
    private void optimizeAnimation(Animation animation) {
        // 动画优化逻辑
        // 1. 关键帧压缩
        // 2. 插值优化
        // 3. 内存对齐
    }
}
```

### 3. 模型缓存管理

#### ModelCache - 模型缓存类
```java
/**
 * 模型缓存管理器
 * 提供高效的模型数据缓存和内存管理
 */
public class ModelCache {

    private final Map<String, CacheEntry> cache;
    private final long maxCacheSize;
    private final long maxEntryAge;
    private final ScheduledExecutorService cleanupExecutor;

    public ModelCache(long maxCacheSize, long maxEntryAge) {
        this.cache = new ConcurrentHashMap<>();
        this.maxCacheSize = maxCacheSize;
        this.maxEntryAge = maxEntryAge;
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();

        // 定期清理过期缓存
        cleanupExecutor.scheduleAtFixedRate(this::cleanup, 60, 60, TimeUnit.SECONDS);
    }

    /**
     * 获取缓存的模型数据
     * @param key 缓存键
     * @return 模型数据，如果不存在返回null
     */
    public ModelData get(String key) {
        CacheEntry entry = cache.get(key);
        if (entry == null) {
            return null;
        }

        // 检查是否过期
        if (isExpired(entry)) {
            cache.remove(key);
            return null;
        }

        // 更新访问时间
        entry.updateAccessTime();
        return entry.getData();
    }

    /**
     * 缓存模型数据
     * @param key 缓存键
     * @param data 模型数据
     */
    public void put(String key, ModelData data) {
        // 检查缓存大小限制
        if (getCurrentCacheSize() + data.getMemorySize() > maxCacheSize) {
            evictLeastRecentlyUsed();
        }

        CacheEntry entry = new CacheEntry(data);
        cache.put(key, entry);
    }

    /**
     * 移除缓存项
     * @param key 缓存键
     */
    public void remove(String key) {
        CacheEntry entry = cache.remove(key);
        if (entry != null) {
            entry.getData().dispose();
        }
    }

    /**
     * 清空所有缓存
     */
    public void clear() {
        cache.values().forEach(entry -> entry.getData().dispose());
        cache.clear();
    }

    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    public CacheStats getStats() {
        return new CacheStats(
            cache.size(),
            getCurrentCacheSize(),
            maxCacheSize,
            calculateHitRate()
        );
    }

    private boolean isExpired(CacheEntry entry) {
        return System.currentTimeMillis() - entry.getCreateTime() > maxEntryAge;
    }

    private long getCurrentCacheSize() {
        return cache.values().stream()
            .mapToLong(entry -> entry.getData().getMemorySize())
            .sum();
    }

    private void evictLeastRecentlyUsed() {
        cache.entrySet().stream()
            .min(Comparator.comparing(entry -> entry.getValue().getLastAccessTime()))
            .ifPresent(entry -> {
                cache.remove(entry.getKey());
                entry.getValue().getData().dispose();
            });
    }

    private void cleanup() {
        List<String> expiredKeys = cache.entrySet().stream()
            .filter(entry -> isExpired(entry.getValue()))
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        expiredKeys.forEach(this::remove);
    }

    private static class CacheEntry {
        private final ModelData data;
        private final long createTime;
        private volatile long lastAccessTime;

        public CacheEntry(ModelData data) {
            this.data = data;
            this.createTime = System.currentTimeMillis();
            this.lastAccessTime = createTime;
        }

        public ModelData getData() { return data; }
        public long getCreateTime() { return createTime; }
        public long getLastAccessTime() { return lastAccessTime; }

        public void updateAccessTime() {
            this.lastAccessTime = System.currentTimeMillis();
        }
    }
}
```

## 使用示例

### 基本使用方法
```java
// 创建模型加载器
ModelLoader loader = new ModelLoader();

// 异步加载Spine动画
loader.loadModelAsync("characters/amiya.json", ModelType.SPINE, new LoadingCallback() {
    @Override
    public void onStart(String path) {
        System.out.println("开始加载: " + path);
    }

    @Override
    public void onProgress(float progress) {
        System.out.println("加载进度: " + (progress * 100) + "%");
    }

    @Override
    public void onComplete(ModelData data) {
        System.out.println("加载完成: " + data.getName());
        // 使用模型数据
    }

    @Override
    public void onError(Exception error) {
        System.err.println("加载失败: " + error.getMessage());
    }
});

// 批量预加载
List<String> modelPaths = Arrays.asList(
    "characters/amiya.json",
    "characters/exusiai.json",
    "characters/texas.json"
);
loader.preloadModels(modelPaths);
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的模型加载器实现，支持多格式模型加载、缓存管理、进度跟踪等功能
