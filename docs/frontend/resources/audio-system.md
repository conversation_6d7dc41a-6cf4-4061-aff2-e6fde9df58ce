# 音频系统 (Audio System)

## 模块概述

音频系统负责Ark-Pets AI Enhanced项目中所有音频资源的管理、播放和控制。支持多种音频格式，提供背景音乐、音效、语音合成等功能，具备音频缓存、3D空间音效、动态音量控制等高级特性。

**核心职责**:
- 多格式音频文件加载和播放
- 背景音乐和音效管理
- 语音合成和TTS集成
- 3D空间音效处理
- 音频缓存和内存优化

## 核心功能架构

### 1. 音频系统架构

#### 分层音频架构
```
┌─────────────────────────────────────┐
│           音频系统                   │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 播放控制  │ 音效管理  │ 语音合成  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           音频处理层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 格式解码  │ 音效处理  │ 空间音效  │ │
│  └──────────┴──────────┴──────────┘ │
├─────────────────────────────────────┤
│           硬件抽象层                 │
│  ┌──────────┬──────────┬──────────┐ │
│  │ 音频设备  │ 混音器   │ 输出控制  │ │
│  └──────────┴──────────┴──────────┘ │
└─────────────────────────────────────┘
```

### 2. 音频播放流程

#### 智能播放流程图
```mermaid
graph TB
    subgraph "音频播放流程"
        PlayRequest[播放请求]
        AudioLoad[音频加载]
        FormatCheck[格式检查]
        Decode[音频解码]
        EffectProcess[音效处理]
        MixerAdd[添加到混音器]
        DeviceOutput[设备输出]
        PlayComplete[播放完成]
    end
    
    subgraph "音效处理"
        VolumeControl[音量控制]
        SpatialAudio[空间音效]
        FilterApply[滤波器应用]
    end
    
    subgraph "缓存管理"
        CacheCheck[缓存检查]
        CacheStore[缓存存储]
        MemoryManage[内存管理]
    end
    
    PlayRequest --> CacheCheck
    CacheCheck -->|缓存命中| MixerAdd
    CacheCheck -->|缓存未命中| AudioLoad
    AudioLoad --> FormatCheck
    FormatCheck --> Decode
    Decode --> VolumeControl
    VolumeControl --> SpatialAudio
    SpatialAudio --> FilterApply
    FilterApply --> EffectProcess
    EffectProcess --> CacheStore
    CacheStore --> MixerAdd
    MixerAdd --> DeviceOutput
    DeviceOutput --> PlayComplete
    
    CacheStore --> MemoryManage
```

## 核心类和接口

### 1. 主要音频系统类

#### AudioSystem - 音频系统主类
```java
/**
 * 音频系统主类
 * 负责统一管理所有音频功能
 */
public class AudioSystem {
    
    private final AudioEngine audioEngine;
    private final MusicManager musicManager;
    private final SoundEffectManager soundEffectManager;
    private final VoiceManager voiceManager;
    private final AudioCache audioCache;
    private final SpatialAudioProcessor spatialProcessor;
    
    public AudioSystem(AudioSystemConfig config) {
        this.audioEngine = new AudioEngine(config.getEngineConfig());
        this.musicManager = new MusicManager(audioEngine);
        this.soundEffectManager = new SoundEffectManager(audioEngine);
        this.voiceManager = new VoiceManager(config.getVoiceConfig());
        this.audioCache = new AudioCache(config.getCacheConfig());
        this.spatialProcessor = new SpatialAudioProcessor();
    }
    
    /**
     * 初始化音频系统
     */
    public void initialize() {
        audioEngine.initialize();
        spatialProcessor.initialize();
        
        // 设置默认音量
        setMasterVolume(0.8f);
        setMusicVolume(0.7f);
        setSoundEffectVolume(0.9f);
        setVoiceVolume(1.0f);
    }
    
    /**
     * 播放背景音乐
     * @param musicPath 音乐文件路径
     * @param loop 是否循环播放
     */
    public void playMusic(String musicPath, boolean loop) {
        musicManager.play(musicPath, loop);
    }
    
    /**
     * 停止背景音乐
     */
    public void stopMusic() {
        musicManager.stop();
    }
    
    /**
     * 暂停背景音乐
     */
    public void pauseMusic() {
        musicManager.pause();
    }
    
    /**
     * 恢复背景音乐
     */
    public void resumeMusic() {
        musicManager.resume();
    }
    
    /**
     * 播放音效
     * @param soundPath 音效文件路径
     * @param volume 音量 (0.0-1.0)
     * @return 音效播放ID
     */
    public long playSoundEffect(String soundPath, float volume) {
        return soundEffectManager.play(soundPath, volume);
    }
    
    /**
     * 播放3D空间音效
     * @param soundPath 音效文件路径
     * @param position 3D位置
     * @param volume 音量
     * @return 音效播放ID
     */
    public long playSpatialSoundEffect(String soundPath, Vector3 position, float volume) {
        return soundEffectManager.playSpatial(soundPath, position, volume);
    }
    
    /**
     * 停止音效
     * @param soundId 音效播放ID
     */
    public void stopSoundEffect(long soundId) {
        soundEffectManager.stop(soundId);
    }
    
    /**
     * 播放语音
     * @param text 要合成的文本
     * @param voiceConfig 语音配置
     */
    public CompletableFuture<Void> playVoice(String text, VoiceConfig voiceConfig) {
        return voiceManager.synthesizeAndPlay(text, voiceConfig);
    }
    
    /**
     * 停止语音播放
     */
    public void stopVoice() {
        voiceManager.stop();
    }
    
    /**
     * 设置主音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setMasterVolume(float volume) {
        audioEngine.setMasterVolume(volume);
    }
    
    /**
     * 设置音乐音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setMusicVolume(float volume) {
        musicManager.setVolume(volume);
    }
    
    /**
     * 设置音效音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setSoundEffectVolume(float volume) {
        soundEffectManager.setVolume(volume);
    }
    
    /**
     * 设置语音音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setVoiceVolume(float volume) {
        voiceManager.setVolume(volume);
    }
    
    /**
     * 设置监听器位置（用于3D音效）
     * @param position 监听器位置
     * @param orientation 监听器朝向
     */
    public void setListenerPosition(Vector3 position, Vector3 orientation) {
        spatialProcessor.setListenerPosition(position, orientation);
    }
    
    /**
     * 预加载音频资源
     * @param audioPaths 音频文件路径列表
     */
    public CompletableFuture<Void> preloadAudio(List<String> audioPaths) {
        return CompletableFuture.runAsync(() -> {
            audioPaths.forEach(path -> {
                try {
                    audioCache.preload(path);
                } catch (Exception e) {
                    System.err.println("预加载音频失败: " + path + " - " + e.getMessage());
                }
            });
        });
    }
    
    /**
     * 获取音频系统状态
     * @return 系统状态信息
     */
    public AudioSystemStatus getStatus() {
        return new AudioSystemStatus(
            musicManager.isPlaying(),
            musicManager.getCurrentTrack(),
            soundEffectManager.getActiveEffectsCount(),
            voiceManager.isSpeaking(),
            audioCache.getStats()
        );
    }
    
    /**
     * 释放音频系统资源
     */
    public void dispose() {
        musicManager.dispose();
        soundEffectManager.dispose();
        voiceManager.dispose();
        audioCache.dispose();
        spatialProcessor.dispose();
        audioEngine.dispose();
    }
}
```

### 3. 音效管理器

#### SoundEffectManager - 音效管理器
```java
/**
 * 音效管理器
 * 负责音效的播放、管理和3D空间音效处理
 */
public class SoundEffectManager {

    private final AudioEngine audioEngine;
    private final AudioCache audioCache;
    private final SpatialAudioProcessor spatialProcessor;
    private final Map<Long, SoundInstance> activeSounds;
    private final AtomicLong soundIdGenerator;
    private float volume;

    public SoundEffectManager(AudioEngine audioEngine) {
        this.audioEngine = audioEngine;
        this.audioCache = audioEngine.getAudioCache();
        this.spatialProcessor = audioEngine.getSpatialProcessor();
        this.activeSounds = new ConcurrentHashMap<>();
        this.soundIdGenerator = new AtomicLong(1);
        this.volume = 1.0f;
    }

    /**
     * 播放音效
     * @param soundPath 音效文件路径
     * @param volume 音量 (0.0-1.0)
     * @return 音效播放ID
     */
    public long play(String soundPath, float volume) {
        try {
            AudioData audioData = audioCache.getOrLoad(soundPath);
            Sound sound = audioEngine.createSound(audioData);

            long soundId = soundIdGenerator.getAndIncrement();
            float finalVolume = this.volume * volume;

            long instanceId = sound.play(finalVolume);
            SoundInstance instance = new SoundInstance(sound, instanceId, soundPath);
            activeSounds.put(soundId, instance);

            // 设置完成回调
            sound.setOnCompletionListener(() -> activeSounds.remove(soundId));

            return soundId;

        } catch (Exception e) {
            throw new AudioException("音效播放失败: " + soundPath, e);
        }
    }

    /**
     * 播放3D空间音效
     * @param soundPath 音效文件路径
     * @param position 3D位置
     * @param volume 音量
     * @return 音效播放ID
     */
    public long playSpatial(String soundPath, Vector3 position, float volume) {
        try {
            AudioData audioData = audioCache.getOrLoad(soundPath);
            Sound sound = audioEngine.createSound(audioData);

            long soundId = soundIdGenerator.getAndIncrement();

            // 应用3D空间音效处理
            SpatialAudioParams spatialParams = spatialProcessor.calculateSpatialParams(position);
            float finalVolume = this.volume * volume * spatialParams.getVolumeMultiplier();

            long instanceId = sound.play(finalVolume, spatialParams.getPitch(), spatialParams.getPan());
            SoundInstance instance = new SpatialSoundInstance(sound, instanceId, soundPath, position);
            activeSounds.put(soundId, instance);

            // 设置完成回调
            sound.setOnCompletionListener(() -> activeSounds.remove(soundId));

            return soundId;

        } catch (Exception e) {
            throw new AudioException("3D音效播放失败: " + soundPath, e);
        }
    }

    /**
     * 停止音效
     * @param soundId 音效播放ID
     */
    public void stop(long soundId) {
        SoundInstance instance = activeSounds.remove(soundId);
        if (instance != null) {
            instance.getSound().stop(instance.getInstanceId());
        }
    }

    /**
     * 停止所有音效
     */
    public void stopAll() {
        activeSounds.values().forEach(instance ->
            instance.getSound().stop(instance.getInstanceId()));
        activeSounds.clear();
    }

    /**
     * 设置音效音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setVolume(float volume) {
        this.volume = Math.max(0.0f, Math.min(1.0f, volume));

        // 更新所有活跃音效的音量
        activeSounds.values().forEach(instance -> {
            Sound sound = instance.getSound();
            sound.setVolume(instance.getInstanceId(), this.volume);
        });
    }

    /**
     * 更新3D音效位置
     * @param soundId 音效ID
     * @param newPosition 新位置
     */
    public void updateSpatialPosition(long soundId, Vector3 newPosition) {
        SoundInstance instance = activeSounds.get(soundId);
        if (instance instanceof SpatialSoundInstance spatialInstance) {
            spatialInstance.setPosition(newPosition);

            // 重新计算空间音效参数
            SpatialAudioParams params = spatialProcessor.calculateSpatialParams(newPosition);
            Sound sound = spatialInstance.getSound();
            long instanceId = spatialInstance.getInstanceId();

            sound.setVolume(instanceId, volume * params.getVolumeMultiplier());
            sound.setPitch(instanceId, params.getPitch());
            sound.setPan(instanceId, params.getPan());
        }
    }

    /**
     * 获取活跃音效数量
     */
    public int getActiveEffectsCount() {
        return activeSounds.size();
    }

    /**
     * 清理已完成的音效
     */
    public void cleanup() {
        activeSounds.entrySet().removeIf(entry -> {
            SoundInstance instance = entry.getValue();
            if (!instance.getSound().isPlaying(instance.getInstanceId())) {
                return true;
            }
            return false;
        });
    }

    /**
     * 释放资源
     */
    public void dispose() {
        stopAll();
    }

    private static class SoundInstance {
        private final Sound sound;
        private final long instanceId;
        private final String soundPath;

        public SoundInstance(Sound sound, long instanceId, String soundPath) {
            this.sound = sound;
            this.instanceId = instanceId;
            this.soundPath = soundPath;
        }

        public Sound getSound() { return sound; }
        public long getInstanceId() { return instanceId; }
        public String getSoundPath() { return soundPath; }
    }

    private static class SpatialSoundInstance extends SoundInstance {
        private Vector3 position;

        public SpatialSoundInstance(Sound sound, long instanceId, String soundPath, Vector3 position) {
            super(sound, instanceId, soundPath);
            this.position = position;
        }

        public Vector3 getPosition() { return position; }
        public void setPosition(Vector3 position) { this.position = position; }
    }
}
```

### 4. 语音管理器

#### VoiceManager - 语音合成管理器
```java
/**
 * 语音管理器
 * 负责文本到语音的合成和播放
 */
public class VoiceManager {

    private final TTSEngine ttsEngine;
    private final AudioEngine audioEngine;
    private final VoiceCache voiceCache;
    private final ExecutorService synthesisExecutor;
    private volatile boolean isSpeaking;
    private float volume;

    public VoiceManager(VoiceConfig config) {
        this.ttsEngine = createTTSEngine(config);
        this.audioEngine = config.getAudioEngine();
        this.voiceCache = new VoiceCache(config.getCacheConfig());
        this.synthesisExecutor = Executors.newSingleThreadExecutor();
        this.volume = 1.0f;
        this.isSpeaking = false;
    }

    /**
     * 合成并播放语音
     * @param text 要合成的文本
     * @param voiceConfig 语音配置
     * @return 播放完成的Future
     */
    public CompletableFuture<Void> synthesizeAndPlay(String text, VoiceConfig voiceConfig) {
        return CompletableFuture.runAsync(() -> {
            try {
                isSpeaking = true;

                // 1. 检查缓存
                String cacheKey = generateCacheKey(text, voiceConfig);
                AudioData cachedAudio = voiceCache.get(cacheKey);

                AudioData audioData;
                if (cachedAudio != null) {
                    audioData = cachedAudio;
                } else {
                    // 2. 合成语音
                    audioData = ttsEngine.synthesize(text, voiceConfig);

                    // 3. 缓存结果
                    voiceCache.put(cacheKey, audioData);
                }

                // 4. 播放语音
                playAudioData(audioData);

            } catch (Exception e) {
                throw new VoiceException("语音合成播放失败: " + text, e);
            } finally {
                isSpeaking = false;
            }
        }, synthesisExecutor);
    }

    /**
     * 批量预合成语音
     * @param textList 文本列表
     * @param voiceConfig 语音配置
     */
    public CompletableFuture<Void> presynthesizeVoices(List<String> textList, VoiceConfig voiceConfig) {
        return CompletableFuture.runAsync(() -> {
            textList.forEach(text -> {
                try {
                    String cacheKey = generateCacheKey(text, voiceConfig);
                    if (!voiceCache.contains(cacheKey)) {
                        AudioData audioData = ttsEngine.synthesize(text, voiceConfig);
                        voiceCache.put(cacheKey, audioData);
                    }
                } catch (Exception e) {
                    System.err.println("预合成语音失败: " + text + " - " + e.getMessage());
                }
            });
        }, synthesisExecutor);
    }

    /**
     * 停止语音播放
     */
    public void stop() {
        if (isSpeaking) {
            ttsEngine.stop();
            isSpeaking = false;
        }
    }

    /**
     * 设置语音音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setVolume(float volume) {
        this.volume = Math.max(0.0f, Math.min(1.0f, volume));
    }

    /**
     * 获取可用的语音列表
     * @return 语音列表
     */
    public List<VoiceInfo> getAvailableVoices() {
        return ttsEngine.getAvailableVoices();
    }

    /**
     * 设置默认语音
     * @param voiceId 语音ID
     */
    public void setDefaultVoice(String voiceId) {
        ttsEngine.setDefaultVoice(voiceId);
    }

    /**
     * 检查是否正在播放语音
     */
    public boolean isSpeaking() {
        return isSpeaking;
    }

    /**
     * 释放资源
     */
    public void dispose() {
        stop();
        voiceCache.dispose();
        synthesisExecutor.shutdown();
        ttsEngine.dispose();
    }

    private void playAudioData(AudioData audioData) {
        Music voiceMusic = audioEngine.createMusic(audioData);
        voiceMusic.setVolume(volume);
        voiceMusic.play();

        // 等待播放完成
        while (voiceMusic.isPlaying()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        voiceMusic.dispose();
    }

    private String generateCacheKey(String text, VoiceConfig config) {
        return text.hashCode() + "_" + config.hashCode();
    }

    private TTSEngine createTTSEngine(VoiceConfig config) {
        return switch (config.getEngineType()) {
            case SYSTEM -> new SystemTTSEngine();
            case AZURE -> new AzureTTSEngine(config.getAzureConfig());
            case GOOGLE -> new GoogleTTSEngine(config.getGoogleConfig());
            case AMAZON -> new AmazonTTSEngine(config.getAmazonConfig());
            default -> new SystemTTSEngine();
        };
    }
}
```

## 使用示例

### 基本音频播放
```java
// 创建音频系统
AudioSystem audioSystem = new AudioSystem(config);
audioSystem.initialize();

// 播放背景音乐
audioSystem.playMusic("music/background.ogg", true);

// 播放音效
long soundId = audioSystem.playSoundEffect("sounds/click.wav", 0.8f);

// 播放3D音效
Vector3 position = new Vector3(10, 0, 5);
audioSystem.playSpatialSoundEffect("sounds/footstep.wav", position, 1.0f);

// 语音合成
VoiceConfig voiceConfig = new VoiceConfig()
    .setVoiceId("zh-CN-XiaoxiaoNeural")
    .setSpeed(1.0f)
    .setPitch(1.0f);

audioSystem.playVoice("博士，欢迎回到罗德岛！", voiceConfig);
```

### 高级音频控制
```java
// 音乐淡入淡出
MusicManager musicManager = audioSystem.getMusicManager();
musicManager.fadeIn("music/battle.ogg", 2.0f);
musicManager.crossfade("music/victory.ogg", 3.0f);

// 动态音量控制
audioSystem.setMasterVolume(0.7f);
audioSystem.setMusicVolume(0.5f);
audioSystem.setSoundEffectVolume(0.9f);

// 3D音效监听器设置
Vector3 listenerPos = new Vector3(0, 0, 0);
Vector3 listenerDir = new Vector3(0, 0, 1);
audioSystem.setListenerPosition(listenerPos, listenerDir);
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-01
**最后更新**: 2025-01-01
**维护团队**: Ark-Pets开发团队
**文档说明**: 完整的音频系统实现，支持音乐、音效、语音合成、3D空间音效等功能

### 2. 音乐管理器

#### MusicManager - 背景音乐管理器
```java
/**
 * 背景音乐管理器
 * 负责背景音乐的播放控制和管理
 */
public class MusicManager {
    
    private final AudioEngine audioEngine;
    private final AudioCache audioCache;
    private Music currentMusic;
    private String currentTrack;
    private float volume;
    private boolean isPlaying;
    private boolean isPaused;
    
    public MusicManager(AudioEngine audioEngine) {
        this.audioEngine = audioEngine;
        this.audioCache = audioEngine.getAudioCache();
        this.volume = 1.0f;
        this.isPlaying = false;
        this.isPaused = false;
    }
    
    /**
     * 播放背景音乐
     * @param musicPath 音乐文件路径
     * @param loop 是否循环播放
     */
    public void play(String musicPath, boolean loop) {
        try {
            // 停止当前音乐
            if (currentMusic != null) {
                currentMusic.stop();
                currentMusic.dispose();
            }
            
            // 加载新音乐
            AudioData audioData = audioCache.getOrLoad(musicPath);
            currentMusic = audioEngine.createMusic(audioData);
            
            // 设置播放参数
            currentMusic.setLooping(loop);
            currentMusic.setVolume(volume);
            
            // 开始播放
            currentMusic.play();
            
            currentTrack = musicPath;
            isPlaying = true;
            isPaused = false;
            
        } catch (Exception e) {
            throw new AudioException("背景音乐播放失败: " + musicPath, e);
        }
    }
    
    /**
     * 停止背景音乐
     */
    public void stop() {
        if (currentMusic != null) {
            currentMusic.stop();
            isPlaying = false;
            isPaused = false;
        }
    }
    
    /**
     * 暂停背景音乐
     */
    public void pause() {
        if (currentMusic != null && isPlaying) {
            currentMusic.pause();
            isPaused = true;
        }
    }
    
    /**
     * 恢复背景音乐
     */
    public void resume() {
        if (currentMusic != null && isPaused) {
            currentMusic.play();
            isPaused = false;
        }
    }
    
    /**
     * 设置音乐音量
     * @param volume 音量 (0.0-1.0)
     */
    public void setVolume(float volume) {
        this.volume = Math.max(0.0f, Math.min(1.0f, volume));
        if (currentMusic != null) {
            currentMusic.setVolume(this.volume);
        }
    }
    
    /**
     * 淡入播放
     * @param musicPath 音乐文件路径
     * @param fadeInDuration 淡入时长（秒）
     */
    public void fadeIn(String musicPath, float fadeInDuration) {
        play(musicPath, true);
        if (currentMusic != null) {
            currentMusic.setVolume(0.0f);
            fadeToVolume(volume, fadeInDuration);
        }
    }
    
    /**
     * 淡出停止
     * @param fadeOutDuration 淡出时长（秒）
     */
    public void fadeOut(float fadeOutDuration) {
        if (currentMusic != null && isPlaying) {
            fadeToVolume(0.0f, fadeOutDuration, this::stop);
        }
    }
    
    /**
     * 交叉淡入新音乐
     * @param newMusicPath 新音乐路径
     * @param crossfadeDuration 交叉淡入时长（秒）
     */
    public void crossfade(String newMusicPath, float crossfadeDuration) {
        if (currentMusic != null) {
            // 当前音乐淡出
            fadeOut(crossfadeDuration);
            
            // 延迟播放新音乐并淡入
            Timer timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    fadeIn(newMusicPath, crossfadeDuration);
                }
            }, (long) (crossfadeDuration * 500)); // 在淡出中途开始淡入
        } else {
            fadeIn(newMusicPath, crossfadeDuration);
        }
    }
    
    /**
     * 获取当前播放状态
     */
    public boolean isPlaying() { return isPlaying && !isPaused; }
    public boolean isPaused() { return isPaused; }
    public String getCurrentTrack() { return currentTrack; }
    public float getVolume() { return volume; }
    
    /**
     * 释放资源
     */
    public void dispose() {
        if (currentMusic != null) {
            currentMusic.stop();
            currentMusic.dispose();
        }
    }
    
    private void fadeToVolume(float targetVolume, float duration) {
        fadeToVolume(targetVolume, duration, null);
    }
    
    private void fadeToVolume(float targetVolume, float duration, Runnable onComplete) {
        if (currentMusic == null) return;
        
        float startVolume = currentMusic.getVolume();
        float volumeDelta = targetVolume - startVolume;
        int steps = (int) (duration * 60); // 60 FPS
        float stepDelta = volumeDelta / steps;
        
        Timer timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            private int currentStep = 0;
            
            @Override
            public void run() {
                if (currentMusic == null || currentStep >= steps) {
                    timer.cancel();
                    if (onComplete != null) {
                        onComplete.run();
                    }
                    return;
                }
                
                float newVolume = startVolume + (stepDelta * currentStep);
                currentMusic.setVolume(newVolume);
                currentStep++;
            }
        }, 0, 1000 / 60); // 60 FPS
    }
}
```
