package cn.harryh.arkpets;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Rectangle2D;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.StackPane;
import javafx.scene.paint.Color;
import javafx.stage.Screen;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.animation.AnimationTimer;
import javafx.animation.TranslateTransition;
import javafx.util.Duration;

import java.util.Random;

/**
 * 桌面宠物启动器
 * 简化版的桌面宠物应用程序，用于演示
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class DesktopLauncher extends Application {

    private static String petId;
    private static String petName;
    private static String petType;
    private static String petConfig;

    private Stage primaryStage;
    private double xOffset = 0;
    private double yOffset = 0;
    private Random random = new Random();
    private AnimationTimer behaviorTimer;

    public static void main(String[] args) {
        // 解析命令行参数
        parseArguments(args);
        
        // 启动JavaFX应用
        launch(args);
    }

    private static void parseArguments(String[] args) {
        // 从系统属性获取宠物信息
        petId = System.getProperty("pet.id", "demo-pet");
        petName = System.getProperty("pet.name", "演示宠物");
        petType = System.getProperty("pet.type", "CAT");
        petConfig = System.getProperty("pet.config", "");
        
        System.out.println("启动桌面宠物:");
        System.out.println("ID: " + petId);
        System.out.println("名称: " + petName);
        System.out.println("类型: " + petType);
        System.out.println("配置: " + petConfig);
    }

    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;

        // 创建宠物界面
        StackPane root = createPetUI();

        // 创建场景
        Scene scene = new Scene(root, 120, 120);
        scene.setFill(Color.TRANSPARENT);

        // 配置舞台
        primaryStage.setScene(scene);
        primaryStage.setTitle("Ark-Pets: " + petName);
        primaryStage.initStyle(StageStyle.TRANSPARENT);
        primaryStage.setAlwaysOnTop(true);
        primaryStage.setResizable(false);

        // 设置初始位置
        setInitialPosition();

        // 添加拖拽功能
        addDragFunctionality(root);

        // 启动行为AI
        startBehaviorAI();

        // 显示宠物
        primaryStage.show();

        System.out.println("桌面宠物 " + petName + " 启动成功！");
    }

    private StackPane createPetUI() {
        StackPane root = new StackPane();
        root.setStyle("-fx-background-color: transparent;");

        // 根据宠物类型选择emoji
        String petEmoji = getPetEmoji(petType);
        
        // 创建宠物显示
        Label petLabel = new Label(petEmoji);
        petLabel.setStyle("-fx-font-size: 48px; -fx-text-fill: black;");

        // 创建名称标签
        Label nameLabel = new Label(petName);
        nameLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666; -fx-background-color: rgba(255,255,255,0.8); -fx-padding: 2px 6px; -fx-background-radius: 10px;");
        nameLabel.setTranslateY(40);

        root.getChildren().addAll(petLabel, nameLabel);

        // 添加点击事件
        root.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                // 双击显示菜单或执行动作
                showInteractionMenu();
            } else {
                // 单击播放动画
                playClickAnimation(petLabel);
            }
        });

        return root;
    }

    private String getPetEmoji(String type) {
        return switch (type.toUpperCase()) {
            case "CAT" -> "🐱";
            case "DOG" -> "🐕";
            case "BIRD" -> "🐦";
            case "FISH" -> "🐠";
            case "RABBIT" -> "🐰";
            case "HAMSTER" -> "🐹";
            default -> "🐾";
        };
    }

    private void setInitialPosition() {
        Rectangle2D screenBounds = Screen.getPrimary().getVisualBounds();
        
        // 随机位置，但确保在屏幕内
        double x = random.nextDouble() * (screenBounds.getWidth() - 120);
        double y = random.nextDouble() * (screenBounds.getHeight() - 120);
        
        primaryStage.setX(x);
        primaryStage.setY(y);
    }

    private void addDragFunctionality(StackPane root) {
        root.setOnMousePressed(event -> {
            xOffset = event.getSceneX();
            yOffset = event.getSceneY();
        });

        root.setOnMouseDragged(event -> {
            primaryStage.setX(event.getScreenX() - xOffset);
            primaryStage.setY(event.getScreenY() - yOffset);
        });
    }

    private void startBehaviorAI() {
        behaviorTimer = new AnimationTimer() {
            private long lastUpdate = 0;
            private final long BEHAVIOR_INTERVAL = 5_000_000_000L; // 5秒

            @Override
            public void handle(long now) {
                if (now - lastUpdate >= BEHAVIOR_INTERVAL) {
                    performRandomBehavior();
                    lastUpdate = now;
                }
            }
        };
        behaviorTimer.start();
    }

    private void performRandomBehavior() {
        Platform.runLater(() -> {
            int behavior = random.nextInt(4);
            switch (behavior) {
                case 0 -> moveRandomly();
                case 1 -> bounce();
                case 2 -> spin();
                case 3 -> idle();
            }
        });
    }

    private void moveRandomly() {
        Rectangle2D screenBounds = Screen.getPrimary().getVisualBounds();
        
        double newX = Math.max(0, Math.min(screenBounds.getWidth() - 120, 
            primaryStage.getX() + (random.nextDouble() - 0.5) * 200));
        double newY = Math.max(0, Math.min(screenBounds.getHeight() - 120, 
            primaryStage.getY() + (random.nextDouble() - 0.5) * 200));

        TranslateTransition move = new TranslateTransition(Duration.seconds(2), primaryStage.getScene().getRoot());
        move.setToX(newX - primaryStage.getX());
        move.setToY(newY - primaryStage.getY());
        move.setOnFinished(e -> {
            primaryStage.setX(newX);
            primaryStage.setY(newY);
            primaryStage.getScene().getRoot().setTranslateX(0);
            primaryStage.getScene().getRoot().setTranslateY(0);
        });
        move.play();
    }

    private void bounce() {
        TranslateTransition bounce = new TranslateTransition(Duration.seconds(0.5), primaryStage.getScene().getRoot());
        bounce.setToY(-20);
        bounce.setAutoReverse(true);
        bounce.setCycleCount(2);
        bounce.play();
    }

    private void spin() {
        primaryStage.getScene().getRoot().setRotate(0);
        TranslateTransition spin = new TranslateTransition(Duration.seconds(1), primaryStage.getScene().getRoot());
        spin.setOnFinished(e -> primaryStage.getScene().getRoot().setRotate(0));
        
        // 简单的旋转效果
        new Thread(() -> {
            try {
                for (int i = 0; i < 360; i += 10) {
                    final int rotation = i;
                    Platform.runLater(() -> primaryStage.getScene().getRoot().setRotate(rotation));
                    Thread.sleep(20);
                }
                Platform.runLater(() -> primaryStage.getScene().getRoot().setRotate(0));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    private void idle() {
        // 什么都不做，只是等待
        System.out.println(petName + " 正在休息...");
    }

    private void playClickAnimation(Label petLabel) {
        // 点击时的缩放动画
        petLabel.setScaleX(1.2);
        petLabel.setScaleY(1.2);
        
        new Thread(() -> {
            try {
                Thread.sleep(200);
                Platform.runLater(() -> {
                    petLabel.setScaleX(1.0);
                    petLabel.setScaleY(1.0);
                });
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    private void showInteractionMenu() {
        System.out.println("显示 " + petName + " 的互动菜单");
        // 这里可以显示右键菜单或互动选项
        // 简化版本只在控制台输出
        
        String[] actions = {"喂食", "玩耍", "休息", "关闭"};
        String action = actions[random.nextInt(actions.length)];
        System.out.println(petName + " 执行动作: " + action);
        
        if ("关闭".equals(action)) {
            Platform.exit();
        }
    }

    @Override
    public void stop() {
        if (behaviorTimer != null) {
            behaviorTimer.stop();
        }
        System.out.println("桌面宠物 " + petName + " 已关闭");
    }
}
