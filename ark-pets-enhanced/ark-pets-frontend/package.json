{"name": "ark-pets-frontend", "version": "4.0.0", "description": "Ark-Pet<PERSON> Enhanced Frontend - Modern React Web Application", "private": true, "homepage": ".", "dependencies": {"@ant-design/icons": "^5.2.6", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.202", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/styled-components": "^5.1.34", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "recharts": "^2.8.0", "styled-components": "^6.1.6", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "prettier": "^3.1.1", "webpack-bundle-analyzer": "^4.10.1"}}