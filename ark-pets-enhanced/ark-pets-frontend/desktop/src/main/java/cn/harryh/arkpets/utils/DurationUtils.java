/** Copyright (c) 2022-2025, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.utils;

import cn.harryh.arkpets.Const;
import javafx.util.Duration;

/**
 * Utility class to convert between long milliseconds and JavaFX Duration. This bridges the gap
 * between core module (using long) and desktop module (using JavaFX Duration).
 */
public class DurationUtils {

  /** Convert long milliseconds to JavaFX Duration. */
  public static Duration toDuration(long millis) {
    return new Duration(millis);
  }

  /** Convert JavaFX Duration to long milliseconds. */
  public static long toMillis(Duration duration) {
    return (long) duration.toMillis();
  }

  // Convenience methods for common durations
  public static Duration getDurationFast() {
    return new Duration(Const.durationFast);
  }

  public static Duration getDurationNormal() {
    return new Duration(Const.durationNormal);
  }

  public static Duration getDurationLong() {
    return new Duration(Const.durationLong);
  }
}
