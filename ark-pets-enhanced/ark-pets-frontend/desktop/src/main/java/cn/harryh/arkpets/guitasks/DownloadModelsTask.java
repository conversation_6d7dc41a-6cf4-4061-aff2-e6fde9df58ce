/** Copyright (c) 2022-2025, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.guitasks;

import static cn.harryh.arkpets.Const.PathConfig;

import cn.harryh.arkpets.Const;
import cn.harryh.arkpets.utils.Logger;
import java.io.File;
import java.nio.file.Files;
import javafx.scene.layout.StackPane;

public class DownloadModelsTask extends FetchGitHubRemoteTask {
  public DownloadModelsTask(StackPane parent, GuiTaskStyle style) {
    super(
        parent,
        style,
        PathConfig.urlModelsZip,
        PathConfig.tempModelsZipCachePath,
        Const.isHttpsTrustAll,
        true);

    try {
      Files.createDirectories(new File(PathConfig.tempDirPath).toPath());
    } catch (Exception e) {
      Logger.warn("Task", "Failed to create temp dir.");
      throw new RuntimeException(e);
    }
  }

  @Override
  protected String getHeader() {
    return "正在下载模型资源文件...";
  }
}
