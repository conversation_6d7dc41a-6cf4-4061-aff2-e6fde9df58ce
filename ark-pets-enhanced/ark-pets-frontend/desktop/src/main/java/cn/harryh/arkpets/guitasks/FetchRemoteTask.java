/** Copyright (c) 2022-2024, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.guitasks;

import static cn.harryh.arkpets.Const.httpBufferSizeDefault;
import static cn.harryh.arkpets.Const.httpTimeoutDefault;

import cn.harryh.arkpets.network.Connections;
import cn.harryh.arkpets.network.NetworkUtils;
import cn.harryh.arkpets.utils.GuiPrefabs;
import cn.harryh.arkpets.utils.Logger;
import cn.harryh.arkpets.utils.StringUtils;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import javafx.concurrent.Task;
import javafx.scene.layout.StackPane;
import javax.net.ssl.HttpsURLConnection;

public abstract class FetchRemoteTask extends GuiTask {
  protected final String remotePath;
  protected final String destPath;
  protected final boolean isHttpsTrustAll;

  public FetchRemoteTask(
      StackPane parent,
      GuiTaskStyle style,
      String remotePath,
      String destPath,
      boolean isHttpTrustAll) {
    super(parent, style);
    this.remotePath = remotePath;
    this.destPath = destPath;
    this.isHttpsTrustAll = isHttpTrustAll;
  }

  @Override
  protected Task<Boolean> getTask() {
    return new Task<>() {
      @Override
      protected Boolean call() throws Exception {
        Logger.info("Network", "Fetching " + remotePath + " to " + destPath);
        this.updateMessage("正在尝试建立连接");

        NetworkUtils.BufferLog log = new NetworkUtils.BufferLog(httpBufferSizeDefault);
        HttpsURLConnection connection =
            Connections.createHttpsConnection(
                new URL(remotePath), httpTimeoutDefault, httpTimeoutDefault, isHttpsTrustAll);
        final InputStream is = connection.getInputStream();
        final OutputStream os = Files.newOutputStream(new File(destPath).toPath());
        final BufferedInputStream bis = new BufferedInputStream(is, httpBufferSizeDefault);
        final BufferedOutputStream bos = new BufferedOutputStream(os, httpBufferSizeDefault);

        try (bis;
            bos;
            is;
            os) {
          int len = httpBufferSizeDefault;
          long sum = 0;
          long max = connection.getContentLengthLong();
          byte[] bytes = new byte[len];
          while ((len = bis.read(bytes)) != -1) {
            bos.write(bytes, 0, len);
            sum += len;
            log.receive();
            long speed = log.getSpeedPerSecond(500);
            this.updateMessage(
                "当前已下载："
                    + StringUtils.getFormattedSizeString(sum)
                    + (speed != 0 ? " (" + StringUtils.getFormattedSizeString(speed) + "/s)" : ""));
            this.updateProgress(sum, max);
            if (this.isCancelled()) {
              this.updateMessage("下载进程已被取消");
              break;
            }
          }
          this.updateProgress(max, max);
          bos.flush();
          Logger.info("Network", "Fetched to " + destPath + " , size: " + sum);
        }
        return this.isDone() && !this.isCancelled();
      }
    };
  }

  @Override
  protected void onFailed(Throwable e) {
    if (style != GuiTaskStyle.HIDDEN) GuiPrefabs.Dialogs.createErrorDialog(parent, e).show();
  }
}
