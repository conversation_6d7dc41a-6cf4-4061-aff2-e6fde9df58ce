/** Copyright (c) 2022-2025, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.guitasks;

import static cn.harryh.arkpets.Const.PathConfig;
import static cn.harryh.arkpets.network.api.AppQueryAnnouncement.AnnounceItem;

import cn.harryh.arkpets.Const;
import cn.harryh.arkpets.network.api.AppQueryAnnouncement;
import cn.harryh.arkpets.utils.GuiPrefabs;
import cn.harryh.arkpets.utils.IOUtils;
import cn.harryh.arkpets.utils.Logger;
import com.alibaba.fastjson2.JSONObject;
import java.io.File;
import java.nio.file.Files;
import java.util.Objects;
import javafx.collections.ObservableList;
import javafx.scene.layout.StackPane;

public class FetchAnnounceTask extends FetchRemoteTask {
  protected ObservableList<AnnounceItem> acceptor;

  public FetchAnnounceTask(
      StackPane parent, GuiTaskStyle style, ObservableList<AnnounceItem> acceptor) {
    super(
        parent,
        style,
        PathConfig.urlApi + "?type=queryAnnouncement",
        PathConfig.tempQueryAnnounceCachePath,
        Const.isHttpsTrustAll);
    this.acceptor = acceptor;

    try {
      Files.createDirectories(new File(PathConfig.tempDirPath).toPath());
    } catch (Exception e) {
      Logger.warn("Task", "Failed to create temp dir.");
      throw new RuntimeException(e);
    }
  }

  @Override
  protected String getHeader() {
    return "正在获取公告数据...";
  }

  @Override
  protected void onSucceeded(boolean result) {
    // When finished downloading the latest app ver-info:
    try {
      // Try to parse the latest app ver-info
      AppQueryAnnouncement queryAnnounceResult =
          Objects.requireNonNull(
              JSONObject.parseObject(
                  new String(
                      IOUtils.FileUtil.readByte(new File(PathConfig.tempQueryAnnounceCachePath))),
                  AppQueryAnnouncement.class));
      if (queryAnnounceResult.code == 0) {
        acceptor.setAll(Objects.requireNonNull(queryAnnounceResult.data.contents));
      } else {
        // On API failed:
        Logger.warn("Announce", "Announcement fetching failed (api failed)");
        if (style != GuiTaskStyle.HIDDEN)
          GuiPrefabs.Dialogs.createCommonDialog(
                  parent,
                  GuiPrefabs.Icons.getIcon(GuiPrefabs.Icons.SVG_DANGER, GuiPrefabs.COLOR_DANGER),
                  "获取公告失败",
                  "服务器返回了无效的消息。",
                  "可能是兼容性问题或服务器不可用。",
                  null)
              .show();
      }
    } catch (Exception e) {
      // On parsing failed:
      Logger.error("Announce", "Announcement fetching failed unexpectedly, details see below.", e);
      if (style != GuiTaskStyle.HIDDEN) GuiPrefabs.Dialogs.createErrorDialog(parent, e).show();
    }
  }
}
