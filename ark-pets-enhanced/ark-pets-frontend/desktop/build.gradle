// 前端桌面模块构建配置
dependencies {
    // 前端核心模块依赖
    implementation project(':ark-pets-frontend:core')
    
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // JavaFX依赖
    implementation "org.openjfx:javafx-controls:${javaFXVersion}"
    implementation "org.openjfx:javafx-fxml:${javaFXVersion}"
    implementation "org.openjfx:javafx-base:${javaFXVersion}"
    implementation "org.openjfx:javafx-graphics:${javaFXVersion}"
    implementation "org.openjfx:javafx-media:${javaFXVersion}"
    implementation "org.openjfx:javafx-web:${javaFXVersion}"
    
    // JFoenix UI库
    implementation 'com.jfoenix:jfoenix:9.0.10'
    
    // LibGDX桌面后端
    implementation "com.badlogicgames.gdx:gdx-backend-lwjgl3:${gdxVersion}"
    
    // HTTP客户端
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
}

// 应用程序插件配置
apply plugin: 'application'

mainClassName = 'cn.harryh.arkpets.desktop.DesktopLauncher'

// 运行配置
run {
    workingDir = project.rootDir
    jvmArgs = [
        '--add-opens', 'java.base/java.lang=ALL-UNNAMED',
        '--add-opens', 'java.desktop/sun.awt=ALL-UNNAMED',
        '--add-opens', 'java.desktop/sun.java2d=ALL-UNNAMED'
    ]
}
