// 前端桌面模块构建配置
plugins {
    id 'org.openjfx.javafxplugin' version '0.1.0'
}

javafx {
    version = "${javaFXVersion}"
    modules = ['javafx.controls', 'javafx.fxml', 'javafx.graphics', 'javafx.base']
}

dependencies {
    // 前端核心模块依赖
    implementation project(':ark-pets-frontend:core')
    
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // JavaFX依赖 - 使用compileOnly和runtimeOnly分离编译时和运行时依赖
    compileOnly "org.openjfx:javafx-controls:${javaFXVersion}"
    compileOnly "org.openjfx:javafx-fxml:${javaFXVersion}"
    compileOnly "org.openjfx:javafx-base:${javaFXVersion}"
    compileOnly "org.openjfx:javafx-graphics:${javaFXVersion}"

    runtimeOnly "org.openjfx:javafx-controls:${javaFXVersion}:win"
    runtimeOnly "org.openjfx:javafx-controls:${javaFXVersion}:linux"
    runtimeOnly "org.openjfx:javafx-controls:${javaFXVersion}:mac"
    runtimeOnly "org.openjfx:javafx-fxml:${javaFXVersion}:win"
    runtimeOnly "org.openjfx:javafx-fxml:${javaFXVersion}:linux"
    runtimeOnly "org.openjfx:javafx-fxml:${javaFXVersion}:mac"
    runtimeOnly "org.openjfx:javafx-base:${javaFXVersion}:win"
    runtimeOnly "org.openjfx:javafx-base:${javaFXVersion}:linux"
    runtimeOnly "org.openjfx:javafx-base:${javaFXVersion}:mac"
    runtimeOnly "org.openjfx:javafx-graphics:${javaFXVersion}:win"
    runtimeOnly "org.openjfx:javafx-graphics:${javaFXVersion}:linux"
    runtimeOnly "org.openjfx:javafx-graphics:${javaFXVersion}:mac"
    
    // JFoenix UI库
    implementation 'com.jfoenix:jfoenix:9.0.10'
    
    // LibGDX桌面后端
    implementation "com.badlogicgames.gdx:gdx-backend-lwjgl3:${gdxVersion}"
    
    // HTTP客户端
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    // JSON处理
    implementation 'com.alibaba.fastjson2:fastjson2:2.0.57'

    // 日志框架
    implementation 'org.apache.logging.log4j:log4j-core:2.24.3'
    implementation 'org.apache.logging.log4j:log4j-api:2.24.3'

    // 平台相关 (Windows)
    implementation 'net.java.dev.jna:jna:5.17.0'
    implementation 'net.java.dev.jna:jna-platform:5.17.0'

    // Markdown处理
    implementation 'org.commonmark:commonmark:0.24.0'
    implementation 'org.commonmark:commonmark-ext-gfm-tables:0.24.0'
    implementation 'org.commonmark:commonmark-ext-gfm-strikethrough:0.24.0'
    implementation 'org.commonmark:commonmark-ext-autolink:0.24.0'
}

// 应用程序插件配置
apply plugin: 'application'

mainClassName = 'cn.harryh.arkpets.desktop.DesktopLauncher'

// 运行配置
run {
    workingDir = project.rootDir
    jvmArgs = [
        '--add-opens', 'java.base/java.lang=ALL-UNNAMED',
        '--add-opens', 'java.desktop/sun.awt=ALL-UNNAMED',
        '--add-opens', 'java.desktop/sun.java2d=ALL-UNNAMED'
    ]
}
