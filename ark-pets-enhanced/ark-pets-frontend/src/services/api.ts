import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import { 
  LoginRequest, 
  LoginResponse, 
  User, 
  Pet, 
  ChatRequest, 
  ChatResponse, 
  SystemMetrics, 
  ApplicationMetrics,
  ApiResponse 
} from '../types';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加请求ID用于追踪
    const requestId = Math.random().toString(36).substring(2, 15);
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId;
    }
    
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      requestId,
      data: config.data,
      params: config.params,
    });
    
    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    const requestId = response.config.headers?.['X-Request-ID'];
    console.log(`[API Response] ${response.status} ${response.config.url}`, {
      requestId,
      data: response.data,
    });
    
    return response;
  },
  (error) => {
    const requestId = error.config?.headers?.['X-Request-ID'];
    console.error(`[API Error] ${error.response?.status || 'Network'} ${error.config?.url}`, {
      requestId,
      error: error.response?.data || error.message,
    });
    
    // 统一错误处理
    if (error.response?.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');
      window.location.href = '/login';
      toast.error('登录已过期，请重新登录');
    } else if (error.response?.status === 403) {
      toast.error('权限不足');
    } else if (error.response?.status === 429) {
      toast.error('请求过于频繁，请稍后重试');
    } else if (error.response?.status >= 500) {
      toast.error('服务器错误，请稍后重试');
    } else if (error.code === 'NETWORK_ERROR') {
      toast.error('网络连接失败，请检查网络');
    }
    
    return Promise.reject(error);
  }
);

// 认证相关API
export const authApi = {
  // 用户登录
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/api/v1/auth/login', data);
    return response.data;
  },
  
  // 用户注册
  register: async (data: any): Promise<ApiResponse> => {
    const response = await api.post<ApiResponse>('/api/v1/auth/register', data);
    return response.data;
  },
  
  // 刷新token
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/api/v1/auth/refresh');
    return response.data;
  },
  
  // 获取用户信息
  getUserInfo: async (): Promise<User> => {
    const response = await api.get<ApiResponse<User>>('/api/v1/auth/me');
    return response.data.data!;
  },
  
  // 更新用户信息
  updateUserInfo: async (data: Partial<User>): Promise<User> => {
    const response = await api.put<ApiResponse<User>>('/api/v1/auth/profile', data);
    return response.data.data!;
  },
  
  // 修改密码
  changePassword: async (data: { oldPassword: string; newPassword: string }): Promise<ApiResponse> => {
    const response = await api.post<ApiResponse>('/api/v1/auth/change-password', data);
    return response.data;
  },
  
  // 用户登出
  logout: async (): Promise<ApiResponse> => {
    const response = await api.post<ApiResponse>('/api/v1/auth/logout');
    return response.data;
  },
};

// 宠物相关API
export const petApi = {
  // 获取用户的宠物列表
  getPets: async (): Promise<Pet[]> => {
    const response = await api.get<ApiResponse<Pet[]>>('/api/v1/pets');
    return response.data.data || [];
  },
  
  // 获取宠物详情
  getPet: async (petId: string): Promise<Pet> => {
    const response = await api.get<ApiResponse<Pet>>(`/api/v1/pets/${petId}`);
    return response.data.data!;
  },
  
  // 创建宠物
  createPet: async (data: Partial<Pet>): Promise<Pet> => {
    const response = await api.post<ApiResponse<Pet>>('/api/v1/pets', data);
    return response.data.data!;
  },
  
  // 更新宠物信息
  updatePet: async (petId: string, data: Partial<Pet>): Promise<Pet> => {
    const response = await api.put<ApiResponse<Pet>>(`/api/v1/pets/${petId}`, data);
    return response.data.data!;
  },
  
  // 删除宠物
  deletePet: async (petId: string): Promise<ApiResponse> => {
    const response = await api.delete<ApiResponse>(`/api/v1/pets/${petId}`);
    return response.data;
  },
  
  // 宠物互动
  interactWithPet: async (petId: string, action: string): Promise<Pet> => {
    const response = await api.post<ApiResponse<Pet>>(`/api/v1/pets/${petId}/interact`, { action });
    return response.data.data!;
  },
};

// AI聊天相关API
export const chatApi = {
  // 发送聊天消息
  sendMessage: async (data: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post<ChatResponse>('/api/v1/ai/chat', data);
    return response.data;
  },
  
  // 获取聊天历史
  getChatHistory: async (sessionId?: string): Promise<any> => {
    const url = sessionId ? `/api/v1/ai/history/${sessionId}` : '/api/v1/ai/history';
    const response = await api.get<ApiResponse>(url);
    return response.data;
  },
  
  // 获取可用模型列表
  getModels: async (): Promise<string[]> => {
    const response = await api.get<ApiResponse<string[]>>('/api/v1/ai/models');
    return response.data.data || [];
  },
  
  // 清除聊天历史
  clearHistory: async (sessionId?: string): Promise<ApiResponse> => {
    const url = sessionId ? `/api/v1/ai/history/${sessionId}` : '/api/v1/ai/history';
    const response = await api.delete<ApiResponse>(url);
    return response.data;
  },
};

// 监控相关API
export const monitorApi = {
  // 获取系统监控信息
  getSystemMetrics: async (): Promise<SystemMetrics> => {
    const response = await api.get<ApiResponse<SystemMetrics>>('/api/v1/monitor/system');
    return response.data.data!;
  },
  
  // 获取应用监控信息
  getApplicationMetrics: async (): Promise<ApplicationMetrics> => {
    const response = await api.get<ApiResponse<ApplicationMetrics>>('/api/v1/monitor/application');
    return response.data.data!;
  },
  
  // 获取监控概览
  getMonitorOverview: async (): Promise<any> => {
    const response = await api.get<ApiResponse>('/api/v1/monitor/overview');
    return response.data.data;
  },
  
  // 健康检查
  healthCheck: async (): Promise<any> => {
    const response = await api.get<any>('/api/v1/monitor/health');
    return response.data;
  },
};

// 网关相关API
export const gatewayApi = {
  // 获取路由信息
  getRoutes: async (): Promise<any> => {
    const response = await api.get<ApiResponse>('/api/v1/gateway/routes');
    return response.data;
  },
  
  // 获取服务列表
  getServices: async (): Promise<any> => {
    const response = await api.get<ApiResponse>('/api/v1/gateway/services');
    return response.data;
  },
  
  // 获取网关统计
  getGatewayStats: async (): Promise<any> => {
    const response = await api.get<ApiResponse>('/api/v1/gateway/stats');
    return response.data;
  },
};

// 导出默认api实例
export default api;
