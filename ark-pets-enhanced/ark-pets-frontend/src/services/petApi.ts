import api from './api';

// 宠物相关类型定义
export interface Pet {
  id: string;
  name: string;
  type: 'CAT' | 'DOG' | 'BIRD' | 'FISH' | 'RABBIT' | 'HAMSTER' | 'OTHER';
  breed?: string;
  age?: number;
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
  color?: string;
  personality?: string[];
  avatar?: string;
  status: 'ACTIVE' | 'SLEEPING' | 'PLAYING' | 'EATING' | 'SICK' | 'INACTIVE';
  mood: 'HAPPY' | 'SAD' | 'EXCITED' | 'CALM' | 'ANGRY' | 'TIRED' | 'PLAYFUL';
  energy: number;
  hunger: number;
  health: number;
  experience: number;
  level: number;
  userId: string;
  lastInteraction?: string;
  lastFeed?: string;
  lastPlay?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePetRequest {
  name: string;
  type: Pet['type'];
  breed?: string;
  age?: number;
  gender?: Pet['gender'];
  color?: string;
  personality?: string[];
}

export interface PetInteractionRequest {
  action: 'FEED' | 'PLAY' | 'HEAL' | 'REST' | 'CLEAN' | 'TRAIN';
}

export interface PetAction {
  id: string;
  petId: string;
  actionType: string;
  description: string;
  energyChange: number;
  hungerChange: number;
  healthChange: number;
  moodChange?: string;
  experienceGain: number;
  timestamp: string;
  userId: string;
}

export interface PetAIAnalysis {
  petId: string;
  currentMood: string;
  recommendations: string[];
  healthStatus: string;
  behaviorPattern: string;
  nextAction: string;
  timestamp: string;
}

export interface DesktopPetConfig {
  scale: number;
  opacity: number;
  enableSound: boolean;
  enableInteraction: boolean;
  animationSpeed: number;
  position: 'random' | 'center' | 'corner';
  behavior: 'normal' | 'active' | 'quiet';
}

// 宠物API服务类
class PetApiService {
  private baseUrl = '/api/v1/pets';

  /**
   * 获取用户的宠物列表
   */
  async getUserPets(page = 0, size = 10): Promise<{
    pets: Pet[];
    total: number;
    page: number;
    size: number;
  }> {
    try {
      const response = await api.get(`${this.baseUrl}?page=${page}&size=${size}`);
      return {
        pets: response.data.data.content || [],
        total: response.data.data.totalElements || 0,
        page: response.data.data.number || 0,
        size: response.data.data.size || 10,
      };
    } catch (error) {
      console.error('Failed to fetch user pets:', error);
      throw new Error('获取宠物列表失败');
    }
  }

  /**
   * 根据ID获取宠物详情
   */
  async getPetById(petId: string): Promise<Pet> {
    try {
      const response = await api.get(`${this.baseUrl}/${petId}`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch pet details:', error);
      throw new Error('获取宠物详情失败');
    }
  }

  /**
   * 创建新宠物
   */
  async createPet(petData: CreatePetRequest): Promise<Pet> {
    try {
      const response = await api.post(this.baseUrl, petData);
      return response.data.data;
    } catch (error) {
      console.error('Failed to create pet:', error);
      throw new Error('创建宠物失败');
    }
  }

  /**
   * 更新宠物信息
   */
  async updatePet(petId: string, petData: Partial<CreatePetRequest>): Promise<Pet> {
    try {
      const response = await api.put(`${this.baseUrl}/${petId}`, petData);
      return response.data.data;
    } catch (error) {
      console.error('Failed to update pet:', error);
      throw new Error('更新宠物信息失败');
    }
  }

  /**
   * 删除宠物
   */
  async deletePet(petId: string): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/${petId}`);
    } catch (error) {
      console.error('Failed to delete pet:', error);
      throw new Error('删除宠物失败');
    }
  }

  /**
   * 与宠物互动
   */
  async interactWithPet(petId: string, action: PetInteractionRequest['action']): Promise<Pet> {
    try {
      const response = await api.post(`${this.baseUrl}/${petId}/interact`, { action });
      return response.data.data;
    } catch (error) {
      console.error('Failed to interact with pet:', error);
      throw new Error('宠物互动失败');
    }
  }

  /**
   * 获取宠物行为历史
   */
  async getPetActions(petId: string, page = 0, size = 20): Promise<{
    actions: PetAction[];
    total: number;
  }> {
    try {
      const response = await api.get(`${this.baseUrl}/${petId}/actions?page=${page}&size=${size}`);
      return {
        actions: response.data.data.content || [],
        total: response.data.data.totalElements || 0,
      };
    } catch (error) {
      console.error('Failed to fetch pet actions:', error);
      throw new Error('获取宠物行为历史失败');
    }
  }

  /**
   * 获取宠物AI分析
   */
  async getPetAIAnalysis(petId: string): Promise<PetAIAnalysis> {
    try {
      const response = await api.get(`${this.baseUrl}/${petId}/ai-analysis`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch pet AI analysis:', error);
      throw new Error('获取宠物AI分析失败');
    }
  }

  /**
   * 生成宠物个性化回复
   */
  async generatePetResponse(petId: string, message: string): Promise<string> {
    try {
      const response = await api.post(`${this.baseUrl}/${petId}/chat`, { message });
      return response.data.data.response;
    } catch (error) {
      console.error('Failed to generate pet response:', error);
      throw new Error('生成宠物回复失败');
    }
  }

  /**
   * 获取需要关注的宠物
   */
  async getPetsNeedingAttention(): Promise<Pet[]> {
    try {
      const response = await api.get(`${this.baseUrl}/attention`);
      return response.data.data || [];
    } catch (error) {
      console.error('Failed to fetch pets needing attention:', error);
      throw new Error('获取需要关注的宠物失败');
    }
  }

  /**
   * 启动桌面宠物
   */
  async launchDesktopPet(petId: string, config: DesktopPetConfig): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/${petId}/launch`, config);
    } catch (error) {
      console.error('Failed to launch desktop pet:', error);
      throw new Error('启动桌面宠物失败');
    }
  }

  /**
   * 停止桌面宠物
   */
  async stopDesktopPet(petId: string): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/${petId}/stop`);
    } catch (error) {
      console.error('Failed to stop desktop pet:', error);
      throw new Error('停止桌面宠物失败');
    }
  }

  /**
   * 获取桌面宠物状态
   */
  async getDesktopPetStatus(petId: string): Promise<{
    isRunning: boolean;
    config?: DesktopPetConfig;
    startTime?: string;
  }> {
    try {
      const response = await api.get(`${this.baseUrl}/${petId}/desktop-status`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch desktop pet status:', error);
      throw new Error('获取桌面宠物状态失败');
    }
  }
}

// 导出单例实例
export const petApi = new PetApiService();
