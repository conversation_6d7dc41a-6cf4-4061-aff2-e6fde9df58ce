// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  nickname?: string;
  avatar?: string;
  role: 'USER' | 'ADMIN';
  createdAt: string;
  lastLoginAt?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token: string;
  user: User;
  expiresIn: number;
}

// 宠物相关类型
export interface Pet {
  id: string;
  name: string;
  type: 'cat' | 'dog' | 'bird' | 'fish' | 'other';
  breed?: string;
  age?: number;
  gender?: 'male' | 'female';
  color?: string;
  personality: string[];
  avatar?: string;
  status: 'active' | 'sleeping' | 'playing' | 'eating';
  mood: 'happy' | 'sad' | 'excited' | 'calm' | 'angry';
  energy: number; // 0-100
  hunger: number; // 0-100
  health: number; // 0-100
  experience: number;
  level: number;
  createdAt: string;
  updatedAt: string;
}

export interface PetAction {
  id: string;
  type: 'feed' | 'play' | 'sleep' | 'exercise' | 'treat';
  name: string;
  description: string;
  energyCost: number;
  hungerChange: number;
  moodChange: number;
  experienceGain: number;
  cooldown: number; // 冷却时间（秒）
  icon: string;
}

// AI聊天相关类型
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  model?: string;
  tokens?: number;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  model: string;
  createdAt: string;
  updatedAt: string;
}

export interface ChatRequest {
  message: string;
  model?: string;
  sessionId?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface ChatResponse {
  success: boolean;
  message: string;
  response: string;
  model: string;
  tokens: number;
  sessionId: string;
  timestamp: number;
}

// 系统监控相关类型
export interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_bytes_received: number;
  network_bytes_sent: number;
  jvm_max_memory: number;
  jvm_total_memory: number;
  jvm_free_memory: number;
}

export interface ApplicationMetrics {
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  error_rate: number;
  active_sessions: number;
  database_connections: number;
  average_response_times: Record<string, number>;
}

// 设置相关类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  notifications: {
    petStatus: boolean;
    systemAlerts: boolean;
    chatMessages: boolean;
  };
  petDefaults: {
    autoFeed: boolean;
    autoPlay: boolean;
    sleepSchedule: boolean;
  };
  aiSettings: {
    defaultModel: string;
    temperature: number;
    maxTokens: number;
    autoSave: boolean;
  };
}

// API响应通用类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: number;
  errors?: string[];
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  size: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// 路由相关类型
export interface RouteInfo {
  id: string;
  uri: string;
  predicates: string;
  filters: string;
  order: number;
  metadata: Record<string, any>;
}

export interface ServiceInfo {
  serviceName: string;
  serviceId: string;
  host: string;
  port: number;
  healthy: boolean;
  metadata?: Record<string, any>;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// 主题相关类型
export interface ThemeConfig {
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  shadowColor: string;
}

// 动画相关类型
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
}

// 表单相关类型
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'password' | 'email' | 'number' | 'select' | 'textarea' | 'switch' | 'slider';
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[];
  min?: number;
  max?: number;
  step?: number;
  rules?: any[];
}
