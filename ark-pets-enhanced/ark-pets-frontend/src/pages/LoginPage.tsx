import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Typography, Space, Divider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { useAuthStore } from '../hooks/useAuthStore';
import { LoginRequest } from '../types';

const { Title, Text, Link } = Typography;

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: none;
  
  .ant-card-body {
    padding: 40px;
  }
`;

const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

const Logo = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  border-radius: 20px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  font-weight: bold;
`;

const StyledForm = styled(Form)`
  .ant-form-item {
    margin-bottom: 20px;
  }
  
  .ant-input-affix-wrapper {
    height: 48px;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    
    &:hover, &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .ant-btn-primary {
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #40a9ff, #9254de);
    }
  }
`;

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading } = useAuthStore();
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(false);

  const handleSubmit = async (values: any) => {
    const loginData: LoginRequest = {
      username: values.username,
      password: values.password,
    };

    try {
      const success = await login(loginData);
      if (success) {
        navigate('/dashboard', { replace: true });
      }
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const handleDemoLogin = () => {
    form.setFieldsValue({
      username: 'demo',
      password: 'demo123',
    });
  };

  return (
    <LoginContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <LoginCard>
          <LogoContainer>
            <Logo>🐾</Logo>
            <Title level={2} style={{ margin: 0, color: '#262626' }}>
              Ark-Pets Enhanced
            </Title>
            <Text type="secondary">现代化桌宠管理平台</Text>
          </LogoContainer>

          <StyledForm
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item>
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Checkbox
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                >
                  记住我
                </Checkbox>
                <Link href="#" style={{ color: '#1890ff' }}>
                  忘记密码？
                </Link>
              </Space>
            </Form.Item>

            <Form.Item style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                block
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>

            <Divider>
              <Text type="secondary">快速体验</Text>
            </Divider>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="default"
                onClick={handleDemoLogin}
                block
                style={{
                  height: '40px',
                  borderRadius: '8px',
                  borderColor: '#d9d9d9',
                }}
              >
                使用演示账号
              </Button>
            </Form.Item>
          </StyledForm>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Text type="secondary">
              还没有账号？{' '}
              <Link href="#" style={{ color: '#1890ff' }}>
                立即注册
              </Link>
            </Text>
          </div>
        </LoginCard>
      </motion.div>
    </LoginContainer>
  );
};

export default LoginPage;
