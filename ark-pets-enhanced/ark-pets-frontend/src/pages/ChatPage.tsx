import React from 'react';
import { Typography, Card, Empty, Button } from 'antd';
import { MessageOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Title } = Typography;

const ChatPage: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Title level={2} style={{ marginBottom: 24 }}>
        AI聊天
      </Title>
      
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="AI聊天功能开发中..."
        >
          <Button type="primary" icon={<MessageOutlined />}>
            开始聊天
          </Button>
        </Empty>
      </Card>
    </motion.div>
  );
};

export default ChatPage;
