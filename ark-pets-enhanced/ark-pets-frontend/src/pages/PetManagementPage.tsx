import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, But<PERSON>, Empty } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Title } = Typography;

const PetManagementPage: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Title level={2} style={{ marginBottom: 24 }}>
        宠物管理
      </Title>
      
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="宠物管理功能开发中..."
        >
          <Button type="primary" icon={<PlusOutlined />}>
            创建宠物
          </Button>
        </Empty>
      </Card>
    </motion.div>
  );
};

export default PetManagementPage;
