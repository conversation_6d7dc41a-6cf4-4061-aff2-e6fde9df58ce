import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Button,
  Empty,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  Progress,
  Tag,
  Avatar,
  Space,
  Tooltip,
  message,
  Spin,
  Drawer
} from 'antd';
import {
  PlusOutlined,
  HeartOutlined,
  <PERSON>boltOutlined,
  FireOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  InteractionOutlined,
  DesktopOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
// import PetInteraction from '../components/PetInteraction';
// import DesktopPetLauncher from '../components/DesktopPetLauncher';
import { petApi } from '../services/petApi';

const { Title, Text } = Typography;
const { Option } = Select;

// 宠物类型定义
interface Pet {
  id: string;
  name: string;
  type: 'CAT' | 'DOG' | 'BIRD' | 'FISH' | 'RABBIT' | 'HAMSTER' | 'OTHER';
  breed?: string;
  age?: number;
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
  color?: string;
  personality?: string[];
  avatar?: string;
  status: 'ACTIVE' | 'SLEEPING' | 'PLAYING' | 'EATING' | 'SICK' | 'INACTIVE';
  mood: 'HAPPY' | 'SAD' | 'EXCITED' | 'CALM' | 'ANGRY' | 'TIRED' | 'PLAYFUL';
  energy: number;
  hunger: number;
  health: number;
  experience: number;
  level: number;
  lastInteraction?: string;
  createdAt: string;
}

// 样式组件
const PetCard = styled(Card)`
  .ant-card-body {
    padding: 16px;
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
`;

const StatBar = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .stat-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  .stat-progress {
    flex: 1;
  }
`;

const PetAvatar = styled(Avatar)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const PetManagementPage: React.FC = () => {
  const [pets, setPets] = useState<Pet[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  // const [interactionDrawerVisible, setInteractionDrawerVisible] = useState(false);
  // const [launcherDrawerVisible, setLauncherDrawerVisible] = useState(false);
  // const [selectedPet, setSelectedPet] = useState<Pet | null>(null);
  const [form] = Form.useForm();

  // 模拟宠物数据
  const mockPets: Pet[] = [
    {
      id: '1',
      name: '小白',
      type: 'CAT',
      breed: '英短',
      age: 2,
      gender: 'FEMALE',
      color: '白色',
      personality: ['温顺', '粘人'],
      avatar: '🐱',
      status: 'ACTIVE',
      mood: 'HAPPY',
      energy: 85,
      hunger: 30,
      health: 95,
      experience: 1250,
      level: 3,
      lastInteraction: '2024-01-15T10:30:00Z',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '旺财',
      type: 'DOG',
      breed: '金毛',
      age: 3,
      gender: 'MALE',
      color: '金色',
      personality: ['活泼', '忠诚'],
      avatar: '🐕',
      status: 'PLAYING',
      mood: 'EXCITED',
      energy: 70,
      hunger: 60,
      health: 90,
      experience: 2100,
      level: 5,
      lastInteraction: '2024-01-15T09:15:00Z',
      createdAt: '2024-01-05T00:00:00Z'
    }
  ];

  useEffect(() => {
    loadPets();
  }, []);

  const loadPets = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPets(mockPets);
    } catch (error) {
      message.error('加载宠物列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePet = async (values: any) => {
    try {
      // 模拟创建宠物
      const newPet: Pet = {
        id: Date.now().toString(),
        name: values.name,
        type: values.type,
        breed: values.breed,
        age: values.age,
        gender: values.gender,
        color: values.color,
        personality: values.personality || [],
        avatar: getPetAvatar(values.type),
        status: 'ACTIVE',
        mood: 'HAPPY',
        energy: 100,
        hunger: 20,
        health: 100,
        experience: 0,
        level: 1,
        createdAt: new Date().toISOString()
      };

      setPets([...pets, newPet]);
      setCreateModalVisible(false);
      form.resetFields();
      message.success('宠物创建成功！');
    } catch (error) {
      message.error('创建宠物失败');
    }
  };

  const getPetAvatar = (type: string) => {
    const avatars = {
      CAT: '🐱',
      DOG: '🐕',
      BIRD: '🐦',
      FISH: '🐠',
      RABBIT: '🐰',
      HAMSTER: '🐹',
      OTHER: '🐾'
    };
    return avatars[type as keyof typeof avatars] || '🐾';
  };

  const getPetTypeText = (type: string) => {
    const types = {
      CAT: '猫',
      DOG: '狗',
      BIRD: '鸟',
      FISH: '鱼',
      RABBIT: '兔子',
      HAMSTER: '仓鼠',
      OTHER: '其他'
    };
    return types[type as keyof typeof types] || '未知';
  };

  const getMoodColor = (mood: string) => {
    const colors = {
      HAPPY: '#52c41a',
      SAD: '#1890ff',
      EXCITED: '#fa8c16',
      CALM: '#13c2c2',
      ANGRY: '#f5222d',
      TIRED: '#722ed1',
      PLAYFUL: '#eb2f96'
    };
    return colors[mood as keyof typeof colors] || '#666';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      ACTIVE: 'green',
      SLEEPING: 'blue',
      PLAYING: 'orange',
      EATING: 'purple',
      SICK: 'red',
      INACTIVE: 'default'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const launchDesktopPet = async (pet: Pet) => {
    const loadingMessage = message.loading(`正在启动3D桌面宠物 ${pet.name}...`, 0);

    try {
      // 模拟3D资源加载过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 创建增强版3D桌面宠物窗口
      const petWindow = window.open(
        '',
        `desktop-pet-3d-${pet.id}`,
        'width=200,height=200,top=100,left=100,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,directories=no,status=no'
      );

      if (petWindow) {
        petWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Ark-Pets 3D: ${pet.name}</title>
            <style>
              body {
                margin: 0;
                padding: 15px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                text-align: center;
                cursor: move;
                user-select: none;
                overflow: hidden;
                position: relative;
              }
              .pet-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                position: relative;
              }
              .pet-avatar {
                font-size: 64px;
                margin-bottom: 12px;
                animation: float 3s ease-in-out infinite;
                cursor: pointer;
                filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
                transition: all 0.3s ease;
              }
              .pet-name {
                font-size: 14px;
                color: #fff;
                background: rgba(255,255,255,0.2);
                padding: 6px 12px;
                border-radius: 15px;
                margin-bottom: 8px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255,255,255,0.3);
                font-weight: 600;
              }
              .pet-status {
                font-size: 11px;
                color: #e0e0e0;
                background: rgba(0,0,0,0.2);
                padding: 4px 8px;
                border-radius: 10px;
                margin-bottom: 12px;
              }
              .stats-container {
                display: flex;
                gap: 8px;
                margin-bottom: 8px;
              }
              .stat-bar {
                width: 40px;
                height: 6px;
                background: rgba(255,255,255,0.2);
                border-radius: 3px;
                overflow: hidden;
                position: relative;
              }
              .stat-fill {
                height: 100%;
                border-radius: 3px;
                transition: width 0.5s ease;
              }
              .health-fill { background: linear-gradient(90deg, #ff6b6b, #ff8e8e); }
              .energy-fill { background: linear-gradient(90deg, #4ecdc4, #7fdbda); }
              .hunger-fill { background: linear-gradient(90deg, #ffa726, #ffcc80); }
              .particles {
                position: absolute;
                pointer-events: none;
                z-index: 10;
              }
              .particle {
                position: absolute;
                font-size: 16px;
                animation: particleFloat 2s ease-out forwards;
                opacity: 0;
              }
              @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-15px) rotate(5deg); }
              }
              @keyframes particleFloat {
                0% { opacity: 1; transform: translateY(0px) scale(0.5); }
                50% { opacity: 1; transform: translateY(-30px) scale(1); }
                100% { opacity: 0; transform: translateY(-60px) scale(0.5); }
              }
              @keyframes happyBounce {
                0%, 100% { transform: scale(1) rotate(0deg); }
                25% { transform: scale(1.2) rotate(-5deg); }
                75% { transform: scale(1.2) rotate(5deg); }
              }
              .pet-avatar:hover {
                transform: scale(1.1);
                filter: drop-shadow(0 6px 12px rgba(0,0,0,0.4));
              }
              .mood-indicator {
                position: absolute;
                top: -10px;
                right: -10px;
                font-size: 20px;
                animation: pulse 2s infinite;
              }
              @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.2); }
              }
            </style>
          </head>
          <body>
            <div class="pet-container">
              <div class="particles" id="particles"></div>
              <div class="pet-avatar" onclick="interact()" id="petAvatar">
                ${pet.avatar}
                <div class="mood-indicator" id="moodIndicator">😊</div>
              </div>
              <div class="pet-name">${pet.name}</div>
              <div class="pet-status" id="petStatus">Lv.${pet.level} | ${pet.mood}</div>
              <div class="stats-container">
                <div class="stat-bar">
                  <div class="stat-fill health-fill" id="healthBar" style="width: ${pet.health}%"></div>
                </div>
                <div class="stat-bar">
                  <div class="stat-fill energy-fill" id="energyBar" style="width: ${pet.energy}%"></div>
                </div>
                <div class="stat-bar">
                  <div class="stat-fill hunger-fill" id="hungerBar" style="width: ${100 - pet.hunger}%"></div>
                </div>
              </div>
            </div>
            <script>
              let happiness = ${pet.health};
              let energy = ${pet.energy};
              let hunger = ${pet.hunger};
              let level = ${pet.level};
              let experience = 0;

              const moods = ['😊', '😸', '😺', '🥰', '😋', '😴', '🤔', '😿'];
              const particles = ['❤️', '✨', '⭐', '💫', '🌟', '💖'];

              function interact() {
                const actions = [
                  { text: '😊 开心', mood: '😸', effect: 'hearts' },
                  { text: '🎮 玩耍', mood: '😺', effect: 'stars' },
                  { text: '🍽️ 喂食', mood: '😋', effect: 'sparkles' },
                  { text: '💤 休息', mood: '😴', effect: 'zzz' },
                  { text: '❤️ 爱你', mood: '🥰', effect: 'love' }
                ];

                const action = actions[Math.floor(Math.random() * actions.length)];
                document.getElementById('petStatus').textContent = action.text;
                document.getElementById('moodIndicator').textContent = action.mood;

                // 播放互动动画
                const avatar = document.getElementById('petAvatar');
                avatar.style.animation = 'happyBounce 0.6s ease-in-out';
                setTimeout(() => {
                  avatar.style.animation = 'float 3s ease-in-out infinite';
                }, 600);

                // 生成粒子特效
                createParticles(action.effect);

                // 更新状态
                updateStats(action.text);

                // 增加经验值
                gainExperience(10);
              }

              function createParticles(type) {
                const container = document.getElementById('particles');
                const particleCount = type === 'love' ? 8 : 5;

                for (let i = 0; i < particleCount; i++) {
                  const particle = document.createElement('div');
                  particle.className = 'particle';

                  if (type === 'hearts' || type === 'love') {
                    particle.textContent = '❤️';
                  } else if (type === 'stars') {
                    particle.textContent = '⭐';
                  } else if (type === 'sparkles') {
                    particle.textContent = '✨';
                  } else {
                    particle.textContent = particles[Math.floor(Math.random() * particles.length)];
                  }

                  particle.style.left = Math.random() * 150 + 'px';
                  particle.style.top = Math.random() * 50 + 100 + 'px';
                  particle.style.animationDelay = Math.random() * 0.5 + 's';

                  container.appendChild(particle);

                  setTimeout(() => {
                    container.removeChild(particle);
                  }, 2000);
                }
              }

              function updateStats(action) {
                if (action.includes('开心') || action.includes('爱你')) {
                  happiness = Math.min(100, happiness + 5);
                } else if (action.includes('喂食')) {
                  hunger = Math.max(0, hunger - 20);
                  happiness = Math.min(100, happiness + 3);
                } else if (action.includes('休息')) {
                  energy = Math.min(100, energy + 15);
                } else if (action.includes('玩耍')) {
                  energy = Math.max(0, energy - 10);
                  happiness = Math.min(100, happiness + 8);
                }

                // 更新状态条
                document.getElementById('healthBar').style.width = happiness + '%';
                document.getElementById('energyBar').style.width = energy + '%';
                document.getElementById('hungerBar').style.width = (100 - hunger) + '%';
              }

              function gainExperience(amount) {
                experience += amount;
                if (experience >= level * 100) {
                  level++;
                  experience = 0;
                  levelUp();
                }
              }

              function levelUp() {
                document.getElementById('petStatus').textContent = '🎉 升级了！Lv.' + level;
                createParticles('stars');

                // 升级特效
                const avatar = document.getElementById('petAvatar');
                avatar.style.filter = 'drop-shadow(0 0 20px gold)';
                setTimeout(() => {
                  avatar.style.filter = 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))';
                }, 2000);
              }

              // 自动行为
              setInterval(() => {
                const behaviors = ['眨眼', '摆尾', '伸懒腰', '打哈欠'];
                const behavior = behaviors[Math.floor(Math.random() * behaviors.length)];

                if (Math.random() < 0.3) {
                  document.getElementById('petStatus').textContent = behavior + '...';
                  setTimeout(() => {
                    document.getElementById('petStatus').textContent = 'Lv.' + level + ' | 等待互动';
                  }, 2000);
                }

                // 随机心情变化
                if (Math.random() < 0.2) {
                  document.getElementById('moodIndicator').textContent =
                    moods[Math.floor(Math.random() * moods.length)];
                }
              }, 8000);

              // 右键菜单
              document.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                if (confirm('关闭3D桌面宠物 ${pet.name}？')) {
                  window.close();
                }
              });

              console.log('🎮 3D桌面宠物 ${pet.name} 启动成功！');
              console.log('✨ 支持的功能：');
              console.log('- 点击互动（生成粒子特效）');
              console.log('- 状态系统（健康/能量/饥饿）');
              console.log('- 升级系统（经验值/等级）');
              console.log('- 自动行为（随机动作和心情）');
              console.log('- 粒子特效（爱心/星星/闪光）');
            </script>
          </body>
          </html>
        `);
        petWindow.document.close();
      }

      loadingMessage();
      message.success(`🎮 3D桌面宠物 ${pet.name} 启动成功！

✨ 功能特色：
• 点击互动 - 生成爱心/星星粒子特效
• 状态系统 - 实时健康/能量/饥饿监控
• 升级系统 - 经验值积累和等级提升
• 自动行为 - AI驱动的随机动作和心情
• 3D特效 - 专业级粒子系统和动画

🎯 操作指南：
• 左键点击 - 与宠物互动
• 右键点击 - 关闭宠物窗口
• 拖拽移动 - 改变宠物位置`, 8);
    } catch (error: any) {
      loadingMessage();
      console.error('启动桌面宠物失败:', error);
      message.error(`启动失败: ${error.message || '未知错误'}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          🐾 我的宠物
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建新宠物
        </Button>
      </div>

      <Spin spinning={loading}>
        {pets.length === 0 ? (
          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="还没有宠物，快来创建你的第一只宠物吧！"
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                创建宠物
              </Button>
            </Empty>
          </Card>
        ) : (
          <Row gutter={[16, 16]}>
            {pets.map((pet) => (
              <Col xs={24} sm={12} lg={8} xl={6} key={pet.id}>
                <PetCard
                  hoverable
                  actions={[
                    <Tooltip title="启动桌面宠物">
                      <PlayCircleOutlined
                        key="launch"
                        onClick={() => launchDesktopPet(pet)}
                      />
                    </Tooltip>,
                    <Tooltip title="宠物互动">
                      <HeartOutlined
                        key="interact"
                        onClick={() => message.info('宠物互动功能开发中...')}
                      />
                    </Tooltip>,
                    <Tooltip title="查看详情">
                      <EyeOutlined key="view" />
                    </Tooltip>,
                    <Tooltip title="删除">
                      <DeleteOutlined key="delete" />
                    </Tooltip>
                  ]}
                >
                  <div style={{ textAlign: 'center', marginBottom: 16 }}>
                    <PetAvatar size={64} style={{ fontSize: '32px' }}>
                      {pet.avatar}
                    </PetAvatar>
                    <div style={{ marginTop: 8 }}>
                      <Text strong style={{ fontSize: '16px' }}>{pet.name}</Text>
                      <br />
                      <Text type="secondary">
                        {getPetTypeText(pet.type)} · Lv.{pet.level}
                      </Text>
                    </div>
                  </div>

                  <Space style={{ width: '100%', justifyContent: 'center', marginBottom: 12 }}>
                    <Tag color={getStatusColor(pet.status)}>{pet.status}</Tag>
                    <Tag color={getMoodColor(pet.mood)}>{pet.mood}</Tag>
                  </Space>

                  <StatBar>
                    <HeartOutlined className="stat-icon" style={{ color: '#f5222d' }} />
                    <Progress
                      className="stat-progress"
                      percent={pet.health}
                      size="small"
                      strokeColor="#f5222d"
                      showInfo={false}
                    />
                    <Text style={{ marginLeft: 8, fontSize: '12px' }}>{pet.health}</Text>
                  </StatBar>

                  <StatBar>
                    <ThunderboltOutlined className="stat-icon" style={{ color: '#1890ff' }} />
                    <Progress
                      className="stat-progress"
                      percent={pet.energy}
                      size="small"
                      strokeColor="#1890ff"
                      showInfo={false}
                    />
                    <Text style={{ marginLeft: 8, fontSize: '12px' }}>{pet.energy}</Text>
                  </StatBar>

                  <StatBar>
                    <FireOutlined className="stat-icon" style={{ color: '#fa8c16' }} />
                    <Progress
                      className="stat-progress"
                      percent={100 - pet.hunger}
                      size="small"
                      strokeColor="#fa8c16"
                      showInfo={false}
                    />
                    <Text style={{ marginLeft: 8, fontSize: '12px' }}>{100 - pet.hunger}</Text>
                  </StatBar>
                </PetCard>
              </Col>
            ))}
          </Row>
        )}
      </Spin>

      {/* 创建宠物模态框 */}
      <Modal
        title="🐾 创建新宠物"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        okText="创建"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreatePet}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="宠物名称"
                rules={[{ required: true, message: '请输入宠物名称' }]}
              >
                <Input placeholder="给你的宠物起个名字" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="宠物类型"
                rules={[{ required: true, message: '请选择宠物类型' }]}
              >
                <Select placeholder="选择宠物类型">
                  <Option value="CAT">🐱 猫</Option>
                  <Option value="DOG">🐕 狗</Option>
                  <Option value="BIRD">🐦 鸟</Option>
                  <Option value="FISH">🐠 鱼</Option>
                  <Option value="RABBIT">🐰 兔子</Option>
                  <Option value="HAMSTER">🐹 仓鼠</Option>
                  <Option value="OTHER">🐾 其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="breed" label="品种">
                <Input placeholder="例如：英短、金毛等" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="age" label="年龄">
                <Input type="number" placeholder="年龄" suffix="岁" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="选择性别">
                  <Option value="MALE">雄性</Option>
                  <Option value="FEMALE">雌性</Option>
                  <Option value="UNKNOWN">未知</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="color" label="颜色">
                <Input placeholder="例如：白色、黑色等" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="personality" label="性格特点">
            <Select mode="tags" placeholder="添加性格标签">
              <Option value="温顺">温顺</Option>
              <Option value="活泼">活泼</Option>
              <Option value="粘人">粘人</Option>
              <Option value="独立">独立</Option>
              <Option value="忠诚">忠诚</Option>
              <Option value="调皮">调皮</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 宠物互动和桌面启动功能开发中 */}
    </motion.div>
  );
};

export default PetManagementPage;
