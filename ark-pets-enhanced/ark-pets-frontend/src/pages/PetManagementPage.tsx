import React, { useState, useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  Card,
  Button,
  Empty,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  Progress,
  Tag,
  Avatar,
  Space,
  Tooltip,
  message,
  Spin,
  Drawer
} from 'antd';
import {
  PlusOutlined,
  HeartOutlined,
  <PERSON>boltOutlined,
  FireOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  InteractionOutlined,
  DesktopOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
// import PetInteraction from '../components/PetInteraction';
// import DesktopPetLauncher from '../components/DesktopPetLauncher';
// import { petApi, Pet as PetType, DesktopPetConfig } from '../services/petApi';

const { Title, Text } = Typography;
const { Option } = Select;

// 宠物类型定义
interface Pet {
  id: string;
  name: string;
  type: 'CAT' | 'DOG' | 'BIRD' | 'FISH' | 'RABBIT' | 'HAMSTER' | 'OTHER';
  breed?: string;
  age?: number;
  gender?: 'MALE' | 'FEMALE' | 'UNKNOWN';
  color?: string;
  personality?: string[];
  avatar?: string;
  status: 'ACTIVE' | 'SLEEPING' | 'PLAYING' | 'EATING' | 'SICK' | 'INACTIVE';
  mood: 'HAPPY' | 'SAD' | 'EXCITED' | 'CALM' | 'ANGRY' | 'TIRED' | 'PLAYFUL';
  energy: number;
  hunger: number;
  health: number;
  experience: number;
  level: number;
  lastInteraction?: string;
  createdAt: string;
}

// 样式组件
const PetCard = styled(Card)`
  .ant-card-body {
    padding: 16px;
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
`;

const StatBar = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .stat-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  .stat-progress {
    flex: 1;
  }
`;

const PetAvatar = styled(Avatar)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const PetManagementPage: React.FC = () => {
  const [pets, setPets] = useState<Pet[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  // const [interactionDrawerVisible, setInteractionDrawerVisible] = useState(false);
  // const [launcherDrawerVisible, setLauncherDrawerVisible] = useState(false);
  // const [selectedPet, setSelectedPet] = useState<Pet | null>(null);
  const [form] = Form.useForm();

  // 模拟宠物数据
  const mockPets: Pet[] = [
    {
      id: '1',
      name: '小白',
      type: 'CAT',
      breed: '英短',
      age: 2,
      gender: 'FEMALE',
      color: '白色',
      personality: ['温顺', '粘人'],
      avatar: '🐱',
      status: 'ACTIVE',
      mood: 'HAPPY',
      energy: 85,
      hunger: 30,
      health: 95,
      experience: 1250,
      level: 3,
      lastInteraction: '2024-01-15T10:30:00Z',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '旺财',
      type: 'DOG',
      breed: '金毛',
      age: 3,
      gender: 'MALE',
      color: '金色',
      personality: ['活泼', '忠诚'],
      avatar: '🐕',
      status: 'PLAYING',
      mood: 'EXCITED',
      energy: 70,
      hunger: 60,
      health: 90,
      experience: 2100,
      level: 5,
      lastInteraction: '2024-01-15T09:15:00Z',
      createdAt: '2024-01-05T00:00:00Z'
    }
  ];

  useEffect(() => {
    loadPets();
  }, []);

  const loadPets = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPets(mockPets);
    } catch (error) {
      message.error('加载宠物列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePet = async (values: any) => {
    try {
      // 模拟创建宠物
      const newPet: Pet = {
        id: Date.now().toString(),
        name: values.name,
        type: values.type,
        breed: values.breed,
        age: values.age,
        gender: values.gender,
        color: values.color,
        personality: values.personality || [],
        avatar: getPetAvatar(values.type),
        status: 'ACTIVE',
        mood: 'HAPPY',
        energy: 100,
        hunger: 20,
        health: 100,
        experience: 0,
        level: 1,
        createdAt: new Date().toISOString()
      };

      setPets([...pets, newPet]);
      setCreateModalVisible(false);
      form.resetFields();
      message.success('宠物创建成功！');
    } catch (error) {
      message.error('创建宠物失败');
    }
  };

  const getPetAvatar = (type: string) => {
    const avatars = {
      CAT: '🐱',
      DOG: '🐕',
      BIRD: '🐦',
      FISH: '🐠',
      RABBIT: '🐰',
      HAMSTER: '🐹',
      OTHER: '🐾'
    };
    return avatars[type as keyof typeof avatars] || '🐾';
  };

  const getPetTypeText = (type: string) => {
    const types = {
      CAT: '猫',
      DOG: '狗',
      BIRD: '鸟',
      FISH: '鱼',
      RABBIT: '兔子',
      HAMSTER: '仓鼠',
      OTHER: '其他'
    };
    return types[type as keyof typeof types] || '未知';
  };

  const getMoodColor = (mood: string) => {
    const colors = {
      HAPPY: '#52c41a',
      SAD: '#1890ff',
      EXCITED: '#fa8c16',
      CALM: '#13c2c2',
      ANGRY: '#f5222d',
      TIRED: '#722ed1',
      PLAYFUL: '#eb2f96'
    };
    return colors[mood as keyof typeof colors] || '#666';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      ACTIVE: 'green',
      SLEEPING: 'blue',
      PLAYING: 'orange',
      EATING: 'purple',
      SICK: 'red',
      INACTIVE: 'default'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const launchDesktopPet = (pet: Pet) => {
    message.info(`正在启动桌面宠物 ${pet.name}...`);
    // 这里将调用桌面应用程序
    // 实际实现中会通过API调用后端服务来启动JavaFX应用
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          🐾 我的宠物
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建新宠物
        </Button>
      </div>

      <Spin spinning={loading}>
        {pets.length === 0 ? (
          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="还没有宠物，快来创建你的第一只宠物吧！"
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                创建宠物
              </Button>
            </Empty>
          </Card>
        ) : (
          <Row gutter={[16, 16]}>
            {pets.map((pet) => (
              <Col xs={24} sm={12} lg={8} xl={6} key={pet.id}>
                <PetCard
                  hoverable
                  actions={[
                    <Tooltip title="启动桌面宠物">
                      <PlayCircleOutlined
                        key="launch"
                        onClick={() => launchDesktopPet(pet)}
                      />
                    </Tooltip>,
                    <Tooltip title="宠物互动">
                      <HeartOutlined
                        key="interact"
                        onClick={() => message.info('宠物互动功能开发中...')}
                      />
                    </Tooltip>,
                    <Tooltip title="查看详情">
                      <EyeOutlined key="view" />
                    </Tooltip>,
                    <Tooltip title="删除">
                      <DeleteOutlined key="delete" />
                    </Tooltip>
                  ]}
                >
                  <div style={{ textAlign: 'center', marginBottom: 16 }}>
                    <PetAvatar size={64} style={{ fontSize: '32px' }}>
                      {pet.avatar}
                    </PetAvatar>
                    <div style={{ marginTop: 8 }}>
                      <Text strong style={{ fontSize: '16px' }}>{pet.name}</Text>
                      <br />
                      <Text type="secondary">
                        {getPetTypeText(pet.type)} · Lv.{pet.level}
                      </Text>
                    </div>
                  </div>

                  <Space style={{ width: '100%', justifyContent: 'center', marginBottom: 12 }}>
                    <Tag color={getStatusColor(pet.status)}>{pet.status}</Tag>
                    <Tag color={getMoodColor(pet.mood)}>{pet.mood}</Tag>
                  </Space>

                  <StatBar>
                    <HeartOutlined className="stat-icon" style={{ color: '#f5222d' }} />
                    <Progress
                      className="stat-progress"
                      percent={pet.health}
                      size="small"
                      strokeColor="#f5222d"
                      showInfo={false}
                    />
                    <Text style={{ marginLeft: 8, fontSize: '12px' }}>{pet.health}</Text>
                  </StatBar>

                  <StatBar>
                    <ThunderboltOutlined className="stat-icon" style={{ color: '#1890ff' }} />
                    <Progress
                      className="stat-progress"
                      percent={pet.energy}
                      size="small"
                      strokeColor="#1890ff"
                      showInfo={false}
                    />
                    <Text style={{ marginLeft: 8, fontSize: '12px' }}>{pet.energy}</Text>
                  </StatBar>

                  <StatBar>
                    <FireOutlined className="stat-icon" style={{ color: '#fa8c16' }} />
                    <Progress
                      className="stat-progress"
                      percent={100 - pet.hunger}
                      size="small"
                      strokeColor="#fa8c16"
                      showInfo={false}
                    />
                    <Text style={{ marginLeft: 8, fontSize: '12px' }}>{100 - pet.hunger}</Text>
                  </StatBar>
                </PetCard>
              </Col>
            ))}
          </Row>
        )}
      </Spin>

      {/* 创建宠物模态框 */}
      <Modal
        title="🐾 创建新宠物"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        okText="创建"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreatePet}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="宠物名称"
                rules={[{ required: true, message: '请输入宠物名称' }]}
              >
                <Input placeholder="给你的宠物起个名字" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="宠物类型"
                rules={[{ required: true, message: '请选择宠物类型' }]}
              >
                <Select placeholder="选择宠物类型">
                  <Option value="CAT">🐱 猫</Option>
                  <Option value="DOG">🐕 狗</Option>
                  <Option value="BIRD">🐦 鸟</Option>
                  <Option value="FISH">🐠 鱼</Option>
                  <Option value="RABBIT">🐰 兔子</Option>
                  <Option value="HAMSTER">🐹 仓鼠</Option>
                  <Option value="OTHER">🐾 其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="breed" label="品种">
                <Input placeholder="例如：英短、金毛等" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="age" label="年龄">
                <Input type="number" placeholder="年龄" suffix="岁" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="选择性别">
                  <Option value="MALE">雄性</Option>
                  <Option value="FEMALE">雌性</Option>
                  <Option value="UNKNOWN">未知</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="color" label="颜色">
                <Input placeholder="例如：白色、黑色等" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="personality" label="性格特点">
            <Select mode="tags" placeholder="添加性格标签">
              <Option value="温顺">温顺</Option>
              <Option value="活泼">活泼</Option>
              <Option value="粘人">粘人</Option>
              <Option value="独立">独立</Option>
              <Option value="忠诚">忠诚</Option>
              <Option value="调皮">调皮</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 宠物互动和桌面启动功能开发中 */}
    </motion.div>
  );
};

export default PetManagementPage;
