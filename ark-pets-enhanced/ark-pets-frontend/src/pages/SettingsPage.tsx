import React from 'react';
import { Typography, Card, Empty, Button } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Title } = Typography;

const SettingsPage: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Title level={2} style={{ marginBottom: 24 }}>
        设置
      </Title>
      
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="设置功能开发中..."
        >
          <Button type="primary" icon={<SettingOutlined />}>
            配置设置
          </Button>
        </Empty>
      </Card>
    </motion.div>
  );
};

export default SettingsPage;
