import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Typography,
  Space,
  Button,
  Avatar,
  List,
  Tag,
} from 'antd';
import {
  HeartOutlined,
  MessageOutlined,
  SettingOutlined,
  FireOutlined,
  ThunderboltOutlined,
  SmileOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { useQuery } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { petApi, monitorApi } from '../services/api';
import { Pet, SystemMetrics } from '../types';

const { Title, Text } = Typography;

const DashboardContainer = styled.div`
  .dashboard-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }
  }
  
  .stat-card {
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .ant-statistic-title {
      color: rgba(255, 255, 255, 0.8);
    }
    
    .ant-statistic-content {
      color: white;
    }
  }
  
  .pet-card {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.02);
    }
  }
`;

const PetStatusBar = styled.div`
  margin-top: 12px;
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .status-label {
      font-size: 12px;
      color: #666;
    }
  }
`;

const ActivityItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 0;
  
  .activity-icon {
    margin-right: 12px;
    font-size: 16px;
  }
  
  .activity-content {
    flex: 1;
    
    .activity-title {
      font-weight: 500;
      margin-bottom: 2px;
    }
    
    .activity-time {
      font-size: 12px;
      color: #999;
    }
  }
`;

// 模拟数据
const mockSystemData = [
  { time: '00:00', cpu: 20, memory: 45, requests: 120 },
  { time: '04:00', cpu: 15, memory: 42, requests: 80 },
  { time: '08:00', cpu: 35, memory: 55, requests: 200 },
  { time: '12:00', cpu: 45, memory: 60, requests: 350 },
  { time: '16:00', cpu: 40, memory: 58, requests: 280 },
  { time: '20:00', cpu: 25, memory: 50, requests: 150 },
];

const mockActivities = [
  { id: 1, type: 'feed', title: '小橘猫吃了美味的猫粮', time: '2分钟前', icon: '🍽️' },
  { id: 2, type: 'play', title: '小白狗完成了飞盘游戏', time: '15分钟前', icon: '🎾' },
  { id: 3, type: 'level', title: '小花猫升级到了5级', time: '1小时前', icon: '⭐' },
  { id: 4, type: 'chat', title: 'AI助手回答了关于宠物护理的问题', time: '2小时前', icon: '💬' },
];

const DashboardPage: React.FC = () => {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);

  // 获取宠物列表
  const { data: pets = [], isLoading: petsLoading } = useQuery<Pet[]>(
    'pets',
    petApi.getPets,
    {
      refetchInterval: 30000, // 30秒刷新一次
    }
  );

  // 获取系统监控数据
  useEffect(() => {
    const fetchSystemMetrics = async () => {
      try {
        const metrics = await monitorApi.getSystemMetrics();
        setSystemMetrics(metrics);
      } catch (error) {
        console.error('Failed to fetch system metrics:', error);
      }
    };

    fetchSystemMetrics();
    const interval = setInterval(fetchSystemMetrics, 10000); // 10秒刷新一次

    return () => clearInterval(interval);
  }, []);

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'happy': return '#52c41a';
      case 'excited': return '#faad14';
      case 'calm': return '#1890ff';
      case 'sad': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  const getStatusColor = (value: number) => {
    if (value >= 80) return '#52c41a';
    if (value >= 50) return '#faad14';
    return '#f5222d';
  };

  return (
    <DashboardContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Title level={2} style={{ marginBottom: 24 }}>
          仪表盘
        </Title>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={12} sm={6}>
            <Card className="dashboard-card stat-card">
              <Statistic
                title="宠物总数"
                value={pets.length}
                prefix={<HeartOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card className="dashboard-card stat-card">
              <Statistic
                title="今日互动"
                value={23}
                prefix={<SmileOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card className="dashboard-card stat-card">
              <Statistic
                title="AI对话"
                value={15}
                prefix={<MessageOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card className="dashboard-card stat-card">
              <Statistic
                title="系统状态"
                value={systemMetrics ? Math.round(100 - systemMetrics.cpu_usage) : 95}
                suffix="%"
                prefix={<ThunderboltOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* 宠物状态 */}
          <Col xs={24} lg={12}>
            <Card
              title={
                <Space>
                  <HeartOutlined />
                  <span>我的宠物</span>
                </Space>
              }
              className="dashboard-card"
              extra={
                <Button type="link" href="/pets">
                  查看全部
                </Button>
              }
            >
              {petsLoading ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  加载中...
                </div>
              ) : pets.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Text type="secondary">还没有宠物，快去创建一个吧！</Text>
                  <br />
                  <Button type="primary" style={{ marginTop: 16 }} href="/pets">
                    创建宠物
                  </Button>
                </div>
              ) : (
                <Row gutter={[12, 12]}>
                  {pets.slice(0, 4).map((pet) => (
                    <Col xs={12} key={pet.id}>
                      <Card
                        size="small"
                        className="pet-card"
                        bodyStyle={{ padding: 12 }}
                      >
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Space>
                            <Avatar
                              src={pet.avatar}
                              size={40}
                              style={{ backgroundColor: getMoodColor(pet.mood) }}
                            >
                              {pet.name[0]}
                            </Avatar>
                            <div>
                              <Text strong>{pet.name}</Text>
                              <br />
                              <Tag color={getMoodColor(pet.mood)}>
                                {pet.mood}
                              </Tag>
                            </div>
                          </Space>
                          
                          <PetStatusBar>
                            <div className="status-item">
                              <span className="status-label">能量</span>
                              <Progress
                                percent={pet.energy}
                                size="small"
                                strokeColor={getStatusColor(pet.energy)}
                                style={{ width: 60 }}
                              />
                            </div>
                            <div className="status-item">
                              <span className="status-label">饥饿</span>
                              <Progress
                                percent={100 - pet.hunger}
                                size="small"
                                strokeColor={getStatusColor(100 - pet.hunger)}
                                style={{ width: 60 }}
                              />
                            </div>
                            <div className="status-item">
                              <span className="status-label">健康</span>
                              <Progress
                                percent={pet.health}
                                size="small"
                                strokeColor={getStatusColor(pet.health)}
                                style={{ width: 60 }}
                              />
                            </div>
                          </PetStatusBar>
                        </Space>
                      </Card>
                    </Col>
                  ))}
                </Row>
              )}
            </Card>
          </Col>

          {/* 系统监控 */}
          <Col xs={24} lg={12}>
            <Card
              title={
                <Space>
                  <SettingOutlined />
                  <span>系统监控</span>
                </Space>
              }
              className="dashboard-card"
            >
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={mockSystemData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="cpu"
                    stroke="#1890ff"
                    strokeWidth={2}
                    name="CPU使用率"
                  />
                  <Line
                    type="monotone"
                    dataKey="memory"
                    stroke="#52c41a"
                    strokeWidth={2}
                    name="内存使用率"
                  />
                </LineChart>
              </ResponsiveContainer>
              
              {systemMetrics && (
                <Row gutter={16} style={{ marginTop: 16 }}>
                  <Col span={8}>
                    <Statistic
                      title="CPU"
                      value={systemMetrics.cpu_usage}
                      precision={1}
                      suffix="%"
                      valueStyle={{ fontSize: 14 }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="内存"
                      value={systemMetrics.memory_usage}
                      precision={1}
                      suffix="%"
                      valueStyle={{ fontSize: 14 }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="磁盘"
                      value={systemMetrics.disk_usage}
                      precision={1}
                      suffix="%"
                      valueStyle={{ fontSize: 14 }}
                    />
                  </Col>
                </Row>
              )}
            </Card>
          </Col>

          {/* 最近活动 */}
          <Col xs={24}>
            <Card
              title={
                <Space>
                  <FireOutlined />
                  <span>最近活动</span>
                </Space>
              }
              className="dashboard-card"
            >
              <List
                dataSource={mockActivities}
                renderItem={(activity) => (
                  <ActivityItem key={activity.id}>
                    <span className="activity-icon">{activity.icon}</span>
                    <div className="activity-content">
                      <div className="activity-title">{activity.title}</div>
                      <div className="activity-time">{activity.time}</div>
                    </div>
                  </ActivityItem>
                )}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>
    </DashboardContainer>
  );
};

export default DashboardPage;
