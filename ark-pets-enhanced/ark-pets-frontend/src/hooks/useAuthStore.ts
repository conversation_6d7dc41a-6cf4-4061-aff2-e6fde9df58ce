import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest, LoginResponse } from '../types';
import { authApi } from '../services/api';
import toast from 'react-hot-toast';

interface AuthState {
  // 状态
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // 操作
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
  updateUser: (userData: Partial<User>) => void;
  checkAuth: () => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      
      // 登录
      login: async (credentials: LoginRequest): Promise<boolean> => {
        set({ isLoading: true });
        
        try {
          const response: LoginResponse = await authApi.login(credentials);
          
          if (response.success) {
            // 保存认证信息
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
            });
            
            // 保存到localStorage
            localStorage.setItem('auth_token', response.token);
            localStorage.setItem('user_info', JSON.stringify(response.user));
            
            toast.success(`欢迎回来，${response.user.nickname || response.user.username}！`);
            return true;
          } else {
            toast.error(response.message || '登录失败');
            set({ isLoading: false });
            return false;
          }
        } catch (error: any) {
          console.error('Login error:', error);
          const message = error.response?.data?.message || '登录失败，请检查网络连接';
          toast.error(message);
          set({ isLoading: false });
          return false;
        }
      },
      
      // 登出
      logout: () => {
        try {
          // 调用后端登出API（可选）
          authApi.logout().catch(console.error);
        } catch (error) {
          console.error('Logout API error:', error);
        }
        
        // 清除状态
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
        
        // 清除localStorage
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
        
        toast.success('已安全退出');
      },
      
      // 刷新token
      refreshToken: async (): Promise<boolean> => {
        try {
          const response: LoginResponse = await authApi.refreshToken();
          
          if (response.success) {
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
            });
            
            localStorage.setItem('auth_token', response.token);
            localStorage.setItem('user_info', JSON.stringify(response.user));
            
            return true;
          } else {
            // 刷新失败，清除认证状态
            get().logout();
            return false;
          }
        } catch (error) {
          console.error('Refresh token error:', error);
          get().logout();
          return false;
        }
      },
      
      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData };
          set({ user: updatedUser });
          localStorage.setItem('user_info', JSON.stringify(updatedUser));
        }
      },
      
      // 检查认证状态
      checkAuth: async (): Promise<boolean> => {
        const token = localStorage.getItem('auth_token');
        const userInfo = localStorage.getItem('user_info');
        
        if (!token || !userInfo) {
          return false;
        }
        
        try {
          // 验证token是否有效
          const user = await authApi.getUserInfo();
          
          set({
            user,
            token,
            isAuthenticated: true,
          });
          
          return true;
        } catch (error) {
          console.error('Auth check error:', error);
          // token无效，清除认证状态
          get().logout();
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
