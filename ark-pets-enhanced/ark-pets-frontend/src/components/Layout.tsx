import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Typography,
  Space,
  Badge,
  But<PERSON>,
  Drawer,
} from 'antd';
import {
  DashboardOutlined,
  HeartOutlined,
  MessageOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { useAuthStore } from '../hooks/useAuthStore';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

const StyledLayout = styled(AntLayout)`
  min-height: 100vh;
  
  .ant-layout-header {
    background: #fff;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    z-index: 10;
    position: relative;
  }
  
  .ant-layout-sider {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
    z-index: 5;
  }
  
  .ant-layout-content {
    margin: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
`;

const Logo = styled.div`
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  background: #001529;
  
  .logo-icon {
    font-size: 24px;
    margin-right: 12px;
  }
  
  .logo-text {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
  }
`;

const HeaderContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: '仪表盘',
  },
  {
    key: '/pets',
    icon: <HeartOutlined />,
    label: '宠物管理',
  },
  {
    key: '/chat',
    icon: <MessageOutlined />,
    label: 'AI聊天',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '设置',
  },
];

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/settings');
        break;
      case 'logout':
        logout();
        navigate('/login');
        break;
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const siderContent = (
    <>
      <Logo>
        <span className="logo-icon">🐾</span>
        {!collapsed && <span className="logo-text">Ark-Pets</span>}
      </Logo>
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ borderRight: 0 }}
      />
    </>
  );

  return (
    <StyledLayout>
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        breakpoint="lg"
        onBreakpoint={(broken) => {
          if (broken) {
            setCollapsed(true);
          }
        }}
        style={{
          display: window.innerWidth < 768 ? 'none' : 'block',
        }}
      >
        {siderContent}
      </Sider>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <Space>
            <span style={{ fontSize: '20px' }}>🐾</span>
            <span>Ark-Pets</span>
          </Space>
        }
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        bodyStyle={{ padding: 0 }}
        width={250}
      >
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Drawer>

      <AntLayout>
        <Header>
          <HeaderContent>
            <Space>
              {/* 移动端菜单按钮 */}
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setMobileMenuVisible(true)}
                style={{
                  display: window.innerWidth < 768 ? 'inline-flex' : 'none',
                }}
              />
              
              {/* 桌面端折叠按钮 */}
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{
                  display: window.innerWidth >= 768 ? 'inline-flex' : 'none',
                }}
              />
            </Space>

            <UserInfo>
              {/* 通知铃铛 */}
              <Badge count={3} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  style={{ fontSize: '16px' }}
                />
              </Badge>

              {/* 用户信息 */}
              <Dropdown
                menu={{
                  items: userMenuItems,
                  onClick: handleUserMenuClick,
                }}
                placement="bottomRight"
                arrow
              >
                <Space style={{ cursor: 'pointer' }}>
                  <Avatar
                    src={user?.avatar}
                    icon={<UserOutlined />}
                    size="default"
                  />
                  <div style={{ display: window.innerWidth < 480 ? 'none' : 'block' }}>
                    <Text strong>{user?.nickname || user?.username}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {user?.role === 'ADMIN' ? '管理员' : '用户'}
                    </Text>
                  </div>
                </Space>
              </Dropdown>
            </UserInfo>
          </HeaderContent>
        </Header>

        <Content>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Outlet />
          </motion.div>
        </Content>
      </AntLayout>
    </StyledLayout>
  );
};

export default Layout;
