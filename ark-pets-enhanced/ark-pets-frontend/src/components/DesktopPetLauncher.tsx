import React, { useState } from 'react';
import { Card, Button, Space, Typography, Avatar, Tag, Modal, Form, Select, Slider, Switch, message } from 'antd';
import { 
  PlayCircleOutlined, 
  SettingOutlined, 
  StopOutlined,
  DesktopOutlined,
  EyeOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const { Text, Title } = Typography;
const { Option } = Select;

interface Pet {
  id: string;
  name: string;
  avatar: string;
  type: string;
  mood: string;
  level: number;
}

interface DesktopPetLauncherProps {
  pet: Pet;
  onLaunch: (petId: string, config: DesktopPetConfig) => void;
  onStop: (petId: string) => void;
  isRunning?: boolean;
}

interface DesktopPetConfig {
  scale: number;
  opacity: number;
  enableSound: boolean;
  enableInteraction: boolean;
  animationSpeed: number;
  position: 'random' | 'center' | 'corner';
  behavior: 'normal' | 'active' | 'quiet';
}

const LauncherCard = styled(Card)`
  .ant-card-body {
    padding: 20px;
  }
`;

const PetPreview = styled.div`
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border-radius: 8px;
  margin-bottom: 16px;
`;

const ConfigSection = styled.div`
  margin-bottom: 16px;
  
  .config-label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
  }
`;

const DesktopPetLauncher: React.FC<DesktopPetLauncherProps> = ({ 
  pet, 
  onLaunch, 
  onStop, 
  isRunning = false 
}) => {
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [launching, setLaunching] = useState(false);
  const [config, setConfig] = useState<DesktopPetConfig>({
    scale: 1.0,
    opacity: 0.9,
    enableSound: true,
    enableInteraction: true,
    animationSpeed: 1.0,
    position: 'random',
    behavior: 'normal'
  });

  const handleLaunch = async () => {
    setLaunching(true);
    try {
      await onLaunch(pet.id, config);
      message.success(`桌面宠物 ${pet.name} 启动成功！`);
      setConfigModalVisible(false);
    } catch (error) {
      message.error('启动失败，请检查系统环境');
    } finally {
      setLaunching(false);
    }
  };

  const handleStop = async () => {
    try {
      await onStop(pet.id);
      message.success(`桌面宠物 ${pet.name} 已停止`);
    } catch (error) {
      message.error('停止失败');
    }
  };

  const handleQuickLaunch = async () => {
    setLaunching(true);
    try {
      await onLaunch(pet.id, config);
      message.success(`桌面宠物 ${pet.name} 启动成功！`);
    } catch (error) {
      message.error('启动失败，请检查系统环境');
    } finally {
      setLaunching(false);
    }
  };

  const getPetTypeText = (type: string) => {
    const types = {
      CAT: '猫',
      DOG: '狗',
      BIRD: '鸟',
      FISH: '鱼',
      RABBIT: '兔子',
      HAMSTER: '仓鼠',
      OTHER: '其他'
    };
    return types[type as keyof typeof types] || '未知';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <LauncherCard
        title={
          <Space>
            <DesktopOutlined />
            桌面宠物启动器
          </Space>
        }
        extra={
          isRunning ? (
            <Tag color="green">运行中</Tag>
          ) : (
            <Tag color="default">未启动</Tag>
          )
        }
      >
        {/* 宠物预览 */}
        <PetPreview>
          <Avatar size={64} style={{ fontSize: '32px', marginBottom: 8 }}>
            {pet.avatar}
          </Avatar>
          <div>
            <Title level={4} style={{ margin: '4px 0' }}>
              {pet.name}
            </Title>
            <Text type="secondary">
              {getPetTypeText(pet.type)} · Lv.{pet.level}
            </Text>
          </div>
        </PetPreview>

        {/* 控制按钮 */}
        <Space style={{ width: '100%', justifyContent: 'center' }} size="middle">
          {!isRunning ? (
            <>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={launching}
                onClick={handleQuickLaunch}
              >
                快速启动
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={() => setConfigModalVisible(true)}
              >
                高级设置
              </Button>
            </>
          ) : (
            <Button
              type="primary"
              danger
              icon={<StopOutlined />}
              onClick={handleStop}
            >
              停止宠物
            </Button>
          )}
        </Space>

        {/* 功能说明 */}
        <div style={{ marginTop: 16, padding: 12, background: '#f9f9f9', borderRadius: 6 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            💡 桌面宠物将在您的桌面上自由活动，支持拖拽、互动和多种动画效果
          </Text>
        </div>
      </LauncherCard>

      {/* 高级配置模态框 */}
      <Modal
        title="🎛️ 桌面宠物配置"
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        onOk={handleLaunch}
        okText="启动宠物"
        cancelText="取消"
        confirmLoading={launching}
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <ConfigSection>
            <Text className="config-label">🔍 显示设置</Text>
            <div style={{ marginBottom: 12 }}>
              <Text>大小缩放: {config.scale}x</Text>
              <Slider
                min={0.5}
                max={2.0}
                step={0.1}
                value={config.scale}
                onChange={(value) => setConfig({ ...config, scale: value })}
                style={{ marginTop: 8 }}
              />
            </div>
            <div style={{ marginBottom: 12 }}>
              <Text>透明度: {Math.round(config.opacity * 100)}%</Text>
              <Slider
                min={0.3}
                max={1.0}
                step={0.1}
                value={config.opacity}
                onChange={(value) => setConfig({ ...config, opacity: value })}
                style={{ marginTop: 8 }}
              />
            </div>
          </ConfigSection>

          <ConfigSection>
            <Text className="config-label">🎭 行为设置</Text>
            <div style={{ marginBottom: 12 }}>
              <Text>动画速度: {config.animationSpeed}x</Text>
              <Slider
                min={0.5}
                max={2.0}
                step={0.1}
                value={config.animationSpeed}
                onChange={(value) => setConfig({ ...config, animationSpeed: value })}
                style={{ marginTop: 8 }}
              />
            </div>
            <div style={{ marginBottom: 12 }}>
              <Text style={{ marginRight: 12 }}>初始位置:</Text>
              <Select
                value={config.position}
                onChange={(value) => setConfig({ ...config, position: value })}
                style={{ width: 120 }}
              >
                <Option value="random">随机位置</Option>
                <Option value="center">屏幕中央</Option>
                <Option value="corner">屏幕角落</Option>
              </Select>
            </div>
            <div style={{ marginBottom: 12 }}>
              <Text style={{ marginRight: 12 }}>行为模式:</Text>
              <Select
                value={config.behavior}
                onChange={(value) => setConfig({ ...config, behavior: value })}
                style={{ width: 120 }}
              >
                <Option value="normal">正常</Option>
                <Option value="active">活跃</Option>
                <Option value="quiet">安静</Option>
              </Select>
            </div>
          </ConfigSection>

          <ConfigSection>
            <Text className="config-label">🔊 交互设置</Text>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Text>启用声音效果</Text>
              <Switch
                checked={config.enableSound}
                onChange={(checked) => setConfig({ ...config, enableSound: checked })}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>启用鼠标交互</Text>
              <Switch
                checked={config.enableInteraction}
                onChange={(checked) => setConfig({ ...config, enableInteraction: checked })}
              />
            </div>
          </ConfigSection>

          <div style={{ padding: 12, background: '#fff7e6', borderRadius: 6, border: '1px solid #ffd591' }}>
            <Text type="warning" style={{ fontSize: '12px' }}>
              ⚠️ 请确保已安装Java运行环境，桌面宠物需要JavaFX支持
            </Text>
          </div>
        </div>
      </Modal>
    </motion.div>
  );
};

export default DesktopPetLauncher;
