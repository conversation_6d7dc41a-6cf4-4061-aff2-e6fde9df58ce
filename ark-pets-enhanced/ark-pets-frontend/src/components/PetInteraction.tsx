import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Progress, Typography, Avatar, Tag, Modal, message } from 'antd';
import { 
  HeartOutlined, 
  ThunderboltOutlined, 
  FireOutlined,
  PlayCircleOutlined,
  GiftOutlined,
  MedicineBoxOutlined,
  HomeOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const { Text, Title } = Typography;

interface Pet {
  id: string;
  name: string;
  avatar: string;
  mood: string;
  energy: number;
  hunger: number;
  health: number;
  level: number;
  status: string;
}

interface PetInteractionProps {
  pet: Pet;
  onInteraction: (petId: string, action: string) => void;
}

const InteractionCard = styled(Card)`
  .ant-card-body {
    padding: 20px;
  }
`;

const PetDisplay = styled.div`
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  margin-bottom: 20px;
`;

const ActionButton = styled(Button)`
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .anticon {
    font-size: 20px;
    margin-bottom: 4px;
  }
`;

const StatBar = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  .stat-icon {
    margin-right: 12px;
    font-size: 18px;
  }
  
  .stat-progress {
    flex: 1;
  }
  
  .stat-value {
    margin-left: 12px;
    font-weight: 500;
    min-width: 30px;
  }
`;

const PetInteraction: React.FC<PetInteractionProps> = ({ pet, onInteraction }) => {
  const [isInteracting, setIsInteracting] = useState(false);
  const [lastAction, setLastAction] = useState<string>('');

  const handleAction = async (action: string, actionName: string) => {
    setIsInteracting(true);
    setLastAction(actionName);
    
    try {
      await onInteraction(pet.id, action);
      message.success(`${actionName}成功！${pet.name}很开心！`);
    } catch (error) {
      message.error(`${actionName}失败，请稍后重试`);
    } finally {
      setIsInteracting(false);
      setLastAction('');
    }
  };

  const getMoodEmoji = (mood: string) => {
    const moods = {
      HAPPY: '😊',
      SAD: '😢',
      EXCITED: '🤩',
      CALM: '😌',
      ANGRY: '😠',
      TIRED: '😴',
      PLAYFUL: '😄'
    };
    return moods[mood as keyof typeof moods] || '😐';
  };

  const getMoodColor = (mood: string) => {
    const colors = {
      HAPPY: '#52c41a',
      SAD: '#1890ff',
      EXCITED: '#fa8c16',
      CALM: '#13c2c2',
      ANGRY: '#f5222d',
      TIRED: '#722ed1',
      PLAYFUL: '#eb2f96'
    };
    return colors[mood as keyof typeof colors] || '#666';
  };

  const getHealthColor = (value: number) => {
    if (value >= 80) return '#52c41a';
    if (value >= 50) return '#fa8c16';
    return '#f5222d';
  };

  const getEnergyColor = (value: number) => {
    if (value >= 70) return '#1890ff';
    if (value >= 30) return '#fa8c16';
    return '#f5222d';
  };

  const getHungerColor = (value: number) => {
    const satiety = 100 - value;
    if (satiety >= 70) return '#52c41a';
    if (satiety >= 30) return '#fa8c16';
    return '#f5222d';
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <InteractionCard title={`与 ${pet.name} 互动`}>
        {/* 宠物展示区域 */}
        <PetDisplay>
          <Avatar size={80} style={{ fontSize: '40px', marginBottom: 12 }}>
            {pet.avatar}
          </Avatar>
          <div>
            <Title level={3} style={{ margin: '8px 0 4px 0' }}>
              {pet.name}
            </Title>
            <Space>
              <Tag color={getMoodColor(pet.mood)}>
                {getMoodEmoji(pet.mood)} {pet.mood}
              </Tag>
              <Tag>Lv.{pet.level}</Tag>
            </Space>
          </div>
        </PetDisplay>

        {/* 状态栏 */}
        <div style={{ marginBottom: 24 }}>
          <StatBar>
            <HeartOutlined className="stat-icon" style={{ color: getHealthColor(pet.health) }} />
            <Text style={{ marginRight: 12, minWidth: 40 }}>健康</Text>
            <Progress 
              className="stat-progress"
              percent={pet.health} 
              strokeColor={getHealthColor(pet.health)}
              showInfo={false}
            />
            <Text className="stat-value">{pet.health}</Text>
          </StatBar>

          <StatBar>
            <ThunderboltOutlined className="stat-icon" style={{ color: getEnergyColor(pet.energy) }} />
            <Text style={{ marginRight: 12, minWidth: 40 }}>能量</Text>
            <Progress 
              className="stat-progress"
              percent={pet.energy} 
              strokeColor={getEnergyColor(pet.energy)}
              showInfo={false}
            />
            <Text className="stat-value">{pet.energy}</Text>
          </StatBar>

          <StatBar>
            <FireOutlined className="stat-icon" style={{ color: getHungerColor(pet.hunger) }} />
            <Text style={{ marginRight: 12, minWidth: 40 }}>饱食</Text>
            <Progress 
              className="stat-progress"
              percent={100 - pet.hunger} 
              strokeColor={getHungerColor(pet.hunger)}
              showInfo={false}
            />
            <Text className="stat-value">{100 - pet.hunger}</Text>
          </StatBar>
        </div>

        {/* 互动按钮 */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 12 }}>
          <ActionButton
            type="primary"
            loading={isInteracting && lastAction === '喂食'}
            onClick={() => handleAction('FEED', '喂食')}
            disabled={pet.hunger < 20}
          >
            <GiftOutlined />
            喂食
          </ActionButton>

          <ActionButton
            type="primary"
            loading={isInteracting && lastAction === '玩耍'}
            onClick={() => handleAction('PLAY', '玩耍')}
            disabled={pet.energy < 30}
          >
            <PlayCircleOutlined />
            玩耍
          </ActionButton>

          <ActionButton
            type="default"
            loading={isInteracting && lastAction === '治疗'}
            onClick={() => handleAction('HEAL', '治疗')}
            disabled={pet.health > 80}
          >
            <MedicineBoxOutlined />
            治疗
          </ActionButton>

          <ActionButton
            type="default"
            loading={isInteracting && lastAction === '休息'}
            onClick={() => handleAction('REST', '休息')}
            disabled={pet.energy > 70}
          >
            <HomeOutlined />
            休息
          </ActionButton>
        </div>

        {/* 状态提示 */}
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          {pet.hunger > 80 && (
            <Text type="warning">🍽️ {pet.name} 很饿了，快给它喂食吧！</Text>
          )}
          {pet.energy < 20 && (
            <Text type="warning">😴 {pet.name} 很累了，让它休息一下吧！</Text>
          )}
          {pet.health < 30 && (
            <Text type="danger">🏥 {pet.name} 生病了，需要治疗！</Text>
          )}
          {pet.health > 80 && pet.energy > 70 && pet.hunger < 30 && (
            <Text type="success">✨ {pet.name} 状态很好，可以一起玩耍！</Text>
          )}
        </div>
      </InteractionCard>
    </motion.div>
  );
};

export default PetInteraction;
