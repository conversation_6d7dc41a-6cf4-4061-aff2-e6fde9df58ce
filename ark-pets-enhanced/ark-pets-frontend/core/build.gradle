// 前端核心模块构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // LibGDX依赖 (保留原有前端技术栈)
    implementation "com.badlogicgames.gdx:gdx:${gdxVersion}"
    implementation "com.badlogicgames.gdx:gdx-backend-lwjgl3:${gdxVersion}"
    
    // Spine动画
    implementation "com.esotericsoftware.spine:spine-libgdx:${spineVersion}"
    
    // HTTP客户端
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    
    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
}
