// 前端核心模块构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // LibGDX依赖 (保留原有前端技术栈)
    implementation "com.badlogicgames.gdx:gdx:${gdxVersion}"
    implementation "com.badlogicgames.gdx:gdx-backend-lwjgl3:${gdxVersion}"
    
    // Spine动画
    implementation "com.esotericsoftware.spine:spine-libgdx:${spineVersion}"

    // JavaFX
    implementation "org.openjfx:javafx-controls:${javaFXVersion}"
    implementation "org.openjfx:javafx-fxml:${javaFXVersion}"

    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    implementation 'com.alibaba:fastjson:2.0.43'
    implementation 'com.alibaba.fastjson2:fastjson2:2.0.43'

    // HTTP客户端
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    // 日志框架
    implementation 'org.apache.logging.log4j:log4j-core:2.21.1'
    implementation 'org.apache.logging.log4j:log4j-api:2.21.1'

    // 平台相关 (Windows)
    implementation 'net.java.dev.jna:jna:5.14.0'
    implementation 'net.java.dev.jna:jna-platform:5.14.0'

    // 中文处理
    implementation 'com.github.promeg:tinypinyin:2.0.3'
    implementation 'com.github.houbb:opencc4j:1.8.2'

    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
}
