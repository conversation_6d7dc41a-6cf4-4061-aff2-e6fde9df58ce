/** Copyright (c) 2022-2025, <PERSON>
 * At GPL-3.0 License
 */

/* JavaFX CSS Reference Guide: https://openjfx.cn/javadoc/17/javafx.graphics/javafx/scene/doc-files/cssref.html */
/* JFoenix Wiki: https://github.com/sshahine/JFoenix/wiki */

/*******************************************************************************
 *                                                                             *
 * 1. Global Styles and Effects                                                *
 *                                                                             *
 ******************************************************************************/

/* Global font face */
* {
    /* Note: Use `Font.loadFont()` in the `Application` to load additional font file */
    -fx-font-family: "Source Han Sans CN";
    -fx-font-smoothing-type: gray;
}

/* Drop shadow for Nodes */
.shadowed {
    /* Note: dropshadow(type, color, radius, spread, x, y) */
    -fx-effect: dropshadow(gaussian, #00000088, 10, 0.1, 0, 0.5);
}

/* Color constants */
.root-container {
    -text-normal: #242424;
    -theme-color: #2A528C;
    -theme-color-dim: #8492A5;
    -theme-color-light: #86ABDE;
}

/* Decoration for Root Container */
.root-container {
    -fx-background-color: transparent;
    -fx-effect: dropshadow(gaussian, #00000044, 10, 0.25, 2.5, 2.5);
}

/* Decoration for root */
.root {
    -fx-background-radius: 8px;
}

/*******************************************************************************
 *                                                                             *
 * 2. Common Attributes for Controls                                           *
 *                                                                             *
 ******************************************************************************/

/* Cursor style */
JFXButton,
JFXCheckBox,
JFXComboBox,
JFXSlider,
JFXToggleButton {
    -fx-cursor: hand;
}

/* Anchor panes that contains an SVG-path */
.btn-icon {
    -fx-pref-width: 0;
    -fx-pref-height: 0;
    -fx-scale-x: 0.525;
    -fx-scale-y: 0.525;
    -fx-translate-x: -21px;
    -fx-translate-y: 0.5px;
}

Label>SVGPath,
Label>AnchorPane,
Label>AnchorPane>SVGPath,
JFXButton>SVGPath,
JFXButton>AnchorPane,
JFXButton>AnchorPane>SVGPath {
    -fx-fill: inherit;
}

/* Buttons that contains a btn-icon on the left */
.btn-with-icon {
    -fx-label-padding: 0 0 0 10px;
    -fx-content-display: center;
}

.btn-primary,
.btn-secondary,
.btn-plain-light,
.btn-plain-dark {
    /* Regular */
    -fx-font-size: 12px;
    -fx-text-alignment: center;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-border-width: 1px;
    -fx-border-style: solid;
}

.btn-primary {
    /* Regular */
    -fx-text-fill: white;
    -fx-background-color: -theme-color;
    -fx-border-color: -theme-color;
}

.btn-secondary {
    /* Regular */
    -fx-text-fill: -theme-color;
    -fx-background-color: white;
    -fx-border-color: -theme-color;
}

.btn-plain-light {
    /* Regular */
    -fx-text-fill: white;
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.btn-plain-dark {
    /* Regular */
    -fx-text-fill: -theme-color;
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.btn-iconified,
.btn-iconified-secondary {
    /* Regular */
    -fx-border-radius: 38.2%;
    -fx-background-radius: 38.2%;
}

.btn-primary .btn-icon,
.btn-plain-light .btn-icon {
    /* Regular */
    -fx-fill: white;
}

.btn-secondary .btn-icon,
.btn-plain-dark .btn-icon,
.btn-iconified-secondary .btn-icon {
    /* Regular */
    -fx-fill: -theme-color;
}

.list {
    -fx-padding: 4px;
    -fx-background-radius: 3px;
}

.list-item {
    /* Regular */
    -fx-cursor: hand;
    -fx-padding: 4px 2px;
    -fx-text-fill: dimgray;
    -fx-background-color: transparent;
    -fx-focus-traversable: true;
}

.list-item-label {
    -fx-font-size: 12px;
    -fx-label-padding: 2px;
    -fx-text-fill: -text-normal;
    -fx-text-overrun: ellipsis;
}

.list-item-label-sub {
    -fx-font-size: 11px;
    -fx-text-fill: dimgray;
    -fx-text-overrun: ellipsis;
}

.list-item-active .list-item-label {
    -fx-font-size: 12.5px;
    -fx-text-fill: -text-normal;
}

.list-item-active .list-item-label-sub {
    -fx-font-size: 11.5px;
    -fx-text-fill: dimgray;
}

.list:focused .list-item-active .list-item-label {
    -fx-font-size: 12.5px;
    -fx-text-fill: white;
}

.list:focused .list-item-active .list-item-label-sub {
    -fx-font-size: 11.5px;
    -fx-text-fill: white;
}

JFXCheckBox {
    -jfx-checked-color: -theme-color;
    -jfx-unchecked-color: dimgray;
}

JFXSlider {
    -jfx-default-thumb: -theme-color;
    -jfx-default-track: -theme-color-light;
}

/*******************************************************************************
 *                                                                             *
 * 3. Sidebar and its Menu Buttons                                             *
 *                                                                             *
 ******************************************************************************/

#Sidebar {
    -fx-background-color: -theme-color-light;
    -fx-background-radius: 0 0 0 8px;
}

#Title {
    -fx-fill: -theme-color;
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-stroke: #80DFFF;
    -fx-stroke-type: outside;
    -fx-stroke-width: 0.5px;
}

.menu-btn {
    /* Regular */
    -fx-padding: 0 0 0 20px;
    -fx-font-size: 18px;
    -fx-font-weight: normal;
    -fx-fill: -theme-color;
    -fx-text-fill: -theme-color;
    -fx-text-alignment: left;
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: -theme-color;
    -fx-border-radius: 8px;
    -fx-border-width: 1.5px;
    -fx-border-style: solid;
}

.menu-btn-active {
    /* Active */
    -fx-fill: white;
    -fx-text-fill: white;
    -fx-background-color: -theme-color;
}

#Launch-btn {
    /* Regular */
    -fx-padding: 0 0 0 20px;
    -fx-font-size: 18px;
    -fx-font-weight: normal;
    -fx-fill: white;
    -fx-text-fill: white;
    -fx-text-alignment: left;
    -fx-background-color: -theme-color;
    -fx-background-radius: 18px;
    -fx-border-color: -theme-color;
    -fx-border-radius: 18px;
    -fx-border-width: 1.5px;
    -fx-border-style: solid;
}

/*******************************************************************************
 *                                                                             *
 * 4. Contents in wrappers                                                     *
 *                                                                             *
 ******************************************************************************/

.wrapper {
    -fx-padding: 15px;
}

.major-tool-bar-upper,
.major-tool-bar-lower {
    -fx-background-color: -theme-color-light;
    -fx-border-style: none;
    -fx-padding: 4px 8px;
}

.major-tool-bar-upper {
    -fx-background-radius: 8px 8px 8px 0;
}

.major-tool-bar-lower {
    -fx-background-radius: 0 0 8px 8px;
}

.info-pane-light {
    -fx-background-color: white;
    -fx-background-radius: 3px;
    -fx-border-color: derive(-theme-color-light, 60%);
    -fx-border-radius: 3px;
    -fx-border-width: 1.25px;
    -fx-border-style: solid;
}

.info-pane-light>VBox {
    -fx-padding: 8px;
}

.flow-wrapper {
    -fx-border-color: derive(-theme-color-light, 60%);
    -fx-border-width: 1.25px 0 0 0;
    -fx-border-style: solid;
    -fx-fit-to-width: true;
    -fx-fit-to-height: true;
}

.tag-flow {
    -fx-hgap: 4px;
    -fx-vgap: 6px;
    -fx-padding: 6px 4px;
}

.btn-flow {
    -fx-hgap: 2px;
    -fx-vgap: 4px;
    -fx-padding: 4px 0;
}

#Search-models-status {
    -fx-fill: -theme-color;
    -fx-font-size: 11px;
    -fx-text-fill: -theme-color;
    -fx-label-padding: 0 2px;
}

.Search-models-star {
    -fx-opacity: 0;
}

.Search-models-item-favorite .Search-models-star {
    -fx-opacity: 1;
}

#Info-filter-label {
    -fx-font-size: 12px;
    -fx-text-fill: -theme-color;
}

#Info-filter-label SVGPath {
    -fx-fill: -theme-color;
}

#Info-filter-box {
    -fx-font-size: 11px;
    -fx-text-fill: dimgray;
}

.info-primary {
    -fx-font-size: 18px;
    -fx-text-fill: -text-normal;
    -fx-font-weight: bold;
    -fx-label-padding: 3px 1px;
    -fx-wrap-text: true;
}

.info-secondary {
    -fx-font-size: 12px;
    -fx-text-fill: gray;
    -fx-font-weight: normal;
    -fx-label-padding: 2px 1px;
    -fx-wrap-text: false;
}

.info-type-badge {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-text-alignment: justify;
    -fx-background-color: derive(-theme-color, 20%);
    -fx-background-radius: 6px;
    -fx-label-padding: 2px 4px;
}

.info-tag-badge,
.info-tag-badge-active {
    -fx-font-size: 11px;
    -fx-text-fill: white;
    -fx-background-radius: 0;
    -fx-max-height: 14px;
    -fx-effect: dropshadow(gaussian, derive(black, 90%), 2.5, 0.25, 0.5, 1);
}

.info-tag-badge {
    -fx-background-color: -theme-color-dim;
}

.info-tag-badge-active {
    -fx-background-color: derive(-theme-color, 20%);
}

.config-group-title {
    -fx-font-size: 14px;
    -fx-text-fill: -theme-color;
    -fx-label-padding: 0 0 0 6px;
    -fx-border-width: 0 0 0 2px;
    -fx-border-style: solid;
    -fx-border-color: -theme-color;
}

.config-field JFXComboBox {
    -fx-font-size: 12px;
    -fx-text-fill: -text-normal;
}

.config-field JFXComboBox .list-cell {
    -fx-cursor: hand;
    -fx-padding: 4px 8px;
}

.config-field JFXCheckBox,
.config-field HBox Label {
    -fx-font-size: 12px;
    -fx-text-fill: -text-normal;
    -fx-label-padding: 4px;
}

.config-field HBox {
    -fx-padding: 8px 4px 4px 4px;
}

.config-field HBox JFXCheckBox,
.config-field HBox Label {
    -fx-pref-width: 175px;
}

.config-field HBox ChoiceBox {
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
}

.config-field HBox JFXSlider {
    -fx-show-tick-marks: true;
}

.config-field Separator {
    -fx-padding: 8px 0;
    -fx-opacity: 0.382;
}

.config-help-text {
    -fx-font-size: 12px;
    -fx-text-fill: dimgray;
    -fx-wrap-text: true;
}

.config-hyper-link {
    -fx-cursor: hand;
    -fx-text-fill: -theme-color;
    -fx-alignment: top-center;
}

.config-hyper-link:hover,
.config-hyper-link:focused {
    -fx-underline: true;
}

/*******************************************************************************
 *                                                                             *
 * 5. Extra components in dialogs                                              *
 *                                                                             *
 ******************************************************************************/

.dialog-title-bar {
    -fx-alignment: center-left;
    -fx-background-color: -theme-color;
    -fx-background-radius: 0 0 30px 0;
}

.dialog-title-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.dialog-oper-bar {
    -fx-alignment: center-left;
    -fx-background-color: -theme-color-light;
    -fx-background-radius: 0;
}

/* Expandable text area for popup dialogs */
.popup-detail-field {
    -fx-alignment: top-left;
    -fx-font-size: 12px;
    -fx-text-fill: dimgray;
    -fx-pref-row-count: 12;
}

#Anno-container,
#Anno-container .viewport {
    -fx-background-color: transparent;
}

.tool-bar-mask {
    -fx-background-color: #00000011;
    -fx-background-radius: 5px;
}

.tool-bar-mask Label {
    -fx-fill: white;
    -fx-text-fill: white;
}

/*******************************************************************************
 *                                                                             *
 * 6. Other components                                                         *
 *                                                                             *
 ******************************************************************************/

/* Progress bar's outer track */
ProgressBar>.track {
    -fx-background-color: #DDDDDDDD;
    -fx-background-radius: 3px;
    -fx-background-insets: 0;
}

/* Progress bar's inner bar */
ProgressBar>.bar,
ProgressBar:indeterminate>.bar {
    -fx-background-color: derive(-theme-color, 20%);
    -fx-background-radius: 3px;
    -fx-background-insets: 0;
}

#Loading-failure-tip,
#Loading-waiting-tip {
    -fx-background-color: #F4F4F4;
}

#Loading-failure-tip-text {
    -fx-font-weight: bold;
}

#Loading-failure-tip>* {
    -fx-fill: dimgray;
}

#Loading-waiting-tip>* {
    -fx-fill: derive(-theme-color, 20%);
    -fx-font-weight: bold;
}

#Loading-empty-tip>* {
    -fx-fill: dimgray;
}

#Title-bar {
    -fx-background-color: -theme-color;
    -fx-background-radius: 8px 8px 0 0;
}

#Title-bar Text {
    -fx-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

#Title-bar JFXButton {
    -fx-fill: white;
    -fx-background-color: -theme-color;
    -fx-background-radius: 0;
    -fx-border-style: none;
    -fx-min-width: 24px;
    -fx-min-height: 24px;
    -fx-max-width: 24px;
    -fx-max-height: 24px;
    -fx-focus-traversable: false;
}

#Title-minimize-btn:hover,
#Title-minimize-btn:focused {
    -fx-background-color: derive(-theme-color, 20%);
}

#Title-close-btn:hover,
#Title-close-btn:focused {
    -fx-background-color: #CC2211;
}

#Title-close-btn {
    -fx-background-radius: 0 8px 0 0;
}

#Toast {
    -fx-padding: 4px 8px;
    -fx-background-color: white;
    -fx-background-radius: 3px;
}

/*******************************************************************************
 *                                                                             *
 * 7. ScrollBar and ScrollPane                                                 *
 *                                                                             *
 ******************************************************************************/

.scroll-bar {
    -arrow-color: #9696A0;
    -thumb-color: #BCBCC2;
    -track-color: #F1F1F5;
}

.scroll-bar>.thumb {
    -fx-cursor: hand;
    -fx-background-color: -thumb-color;
    -fx-background-insets: 0;
    -fx-background-radius: 3px;
}

.scroll-bar:vertical>.track-background,
.scroll-bar:horizontal>.track-background {
    -fx-cursor: default;
    -fx-background-color: -track-color;
    -fx-background-insets: 0;
    -fx-background-radius: 3px;
}

.scroll-bar>.increment-button,
.scroll-bar>.decrement-button,
.scroll-bar:hover>.increment-button,
.scroll-bar:hover>.decrement-button {
    -fx-cursor: hand;
    -fx-background-color: transparent;
}

.scroll-bar:vertical>.increment-button,
.scroll-bar:vertical>.decrement-button {
    -fx-padding: 5px 2px;
}

.scroll-bar:horizontal>.increment-button,
.scroll-bar:horizontal>.decrement-button {
    -fx-padding: 2px 5px;
}

/* All Arrows */
.scroll-bar>.increment-button>.increment-arrow,
.scroll-bar>.decrement-button>.decrement-arrow {
    -fx-background-color: -arrow-color;
}

/* Up Arrow */
.scroll-bar:vertical>.increment-button>.increment-arrow {
    -fx-shape: "M298 426h428l-214 214z";
}

/* Down Arrow */
.scroll-bar:vertical>.decrement-button>.decrement-arrow {
    -fx-shape: "M298 598l214-214 214 214h-428z";
}

/* Right Arrow */
.scroll-bar:horizontal>.increment-button>.increment-arrow {
    -fx-shape: "M0 428l0 -428l214 214l-214 214z";
}

/* Left Arrow */
.scroll-bar:horizontal>.decrement-button>.decrement-arrow {
    -fx-shape: "M214 0l0 428l-214 -214l214 -214z";
}

.scroll-pane {
    -fx-background-insets: 0;
    -fx-padding: 0;
}

.scroll-pane:focused,
.scroll-pane .corner {
    -fx-background-insets: 0;
}

.scroll-v,
.flow-wrapper {
    -fx-hbar-policy: never;
    -fx-vbar-policy: as-needed;
}

/*******************************************************************************
 *                                                                             *
 * 8. TabPane                                                                  *
 *                                                                             *
 ******************************************************************************/

.jfx-tab-pane {
	-fx-tab-min-width: 25px;
	-fx-tab-min-height: 25px;
	-fx-padding: 10px;
}

.jfx-tab-pane .tab-header-area .control-buttons-tab .jfx-rippler {
    -jfx-rippler-fill: -theme-color-dim;
}

.jfx-tab-pane .tab-header-background {
    -fx-background-color: #00000011;
}

.jfx-tab-pane .headers-region .tab-selected-line {
    -fx-background-color: -theme-color;
    -fx-pref-height: 1.5px;
}

.jfx-tab-pane .headers-region .tab .jfx-rippler {
    -jfx-rippler-fill: -theme-color-light;
}

.jfx-tab-pane .headers-region .tab .tab-container .tab-label {
    -fx-text-fill: gray;
    -fx-font-size: 13px;
}

.jfx-tab-pane .headers-region .tab:closable {
    -fx-border-color: -theme-color-dim;
}

.jfx-tab-pane .headers-region .tab:selected .tab-container .tab-label {
    -fx-text-fill: -theme-color;
}
