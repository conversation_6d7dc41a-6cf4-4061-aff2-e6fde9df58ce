<?xml version="1.0" encoding="UTF-8"?>

<!--
    Copyright (c) 2022-2025, <PERSON>
    At GPL-3.0 License
-->

<!-- ********* Wrapper 3 ********* -->
<?import com.jfoenix.controls.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.SVGPath?>
<?import java.lang.String?>
<Pane prefHeight="376.0" prefWidth="460.0" styleClass="wrapper" stylesheets="@Main.css"
      xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="cn.harryh.arkpets.controllers.SettingsModule">
    <ScrollPane fx:id="moduleScroll" layoutX="10.0" layoutY="11.0" prefHeight="350.0" prefWidth="440.0"
                styleClass="scroll-v">
        <AnchorPane maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minHeight="0.0" minWidth="0.0"
                    prefWidth="425.0" styleClass="config-field">
            <VBox>
                <VBox fx:id="noticeBox"/>
                <Label styleClass="config-group-title" text="显示设置"/>
                <HBox>
                    <Label text="图像缩放"/>
                    <JFXComboBox fx:id="configDisplayScale" prefWidth="120.0"/>
                    <JFXButton fx:id="configDisplayScaleHelp"/>
                </HBox>
                <HBox>
                    <Label text="最大帧率"/>
                    <JFXComboBox fx:id="configDisplayFps" prefWidth="120.0"/>
                    <JFXButton fx:id="configDisplayFpsHelp"/>
                </HBox>
                <Separator/>
                <Label styleClass="config-group-title" text="渲染设置"/>
                <JFXTabPane fx:id="configRenderTabPane" maxWidth="430.0" tabClosingPolicy="UNAVAILABLE">
                    <Tab text="背景">
                        <VBox>
                            <HBox>
                                <Label text="背景颜色"/>
                                <JFXComboBox fx:id="configCanvasColor" prefWidth="120.0"/>
                            </HBox>
                        </VBox>
                    </Tab>
                    <Tab text="窗口边界">
                        <VBox>
                            <HBox>
                                <Label text="边界大小"/>
                                <JFXComboBox fx:id="configCanvasCoverage" prefWidth="120.0"/>
                                <JFXButton fx:id="configCanvasCoverageHelp"/>
                            </HBox>
                            <HBox>
                                <Label text="边界计算方式"/>
                                <JFXComboBox fx:id="configCanvasSamplingInterval" prefWidth="120.0"/>
                            </HBox>
                        </VBox>
                    </Tab>
                    <Tab text="高亮描边">
                        <VBox>
                            <HBox>
                                <Label text="开启条件"/>
                                <JFXComboBox fx:id="configRenderOutline" prefWidth="120.0"/>
                            </HBox>
                            <HBox>
                                <Label text="描边颜色"/>
                                <JFXComboBox fx:id="configRenderOutlineColor" prefWidth="120.0"/>
                            </HBox>
                            <HBox>
                                <Label text="描边宽度"/>
                                <JFXComboBox fx:id="configRenderOutlineWidth" prefWidth="120.0"/>
                            </HBox>
                        </VBox>
                    </Tab>
                    <Tab text="不透明度">
                        <VBox>
                            <HBox>
                                <Label text="正常模式下"/>
                                <JFXSlider fx:id="configRenderOpacityNormal"/>
                                <Label fx:id="configRenderOpacityNormalValue" text="0"/>
                            </HBox>
                            <HBox>
                                <Label text="透明模式下"/>
                                <JFXSlider fx:id="configRenderOpacityDim"/>
                                <Label fx:id="configRenderOpacityDimValue" text="0"/>
                            </HBox>
                        </VBox>
                    </Tab>
                    <Tab text="光照效果">
                        <VBox>
                            <HBox>
                                <Label text="阴影强度"/>
                                <JFXComboBox fx:id="configRenderShadowColor" prefWidth="120.0"/>
                            </HBox>
                        </VBox>
                    </Tab>
                    <Tab text="其他">
                        <VBox>
                            <HBox>
                                <JFXCheckBox fx:id="configEnableMipMap" mnemonicParsing="false" text="MipMap 抗锯齿"/>
                                <JFXButton fx:id="configEnableMipMapHelp"/>
                            </HBox>
                            <HBox>
                                <JFXCheckBox fx:id="configEnableAngle" mnemonicParsing="false" text="Angle 原生渲染"/>
                                <JFXButton fx:id="configEnableAngleHelp"/>
                            </HBox>
                        </VBox>
                    </Tab>
                </JFXTabPane>
                <Separator/>
                <Label styleClass="config-group-title" text="高级设置"/>
                <HBox>
                    <JFXCheckBox fx:id="configAutoStartup" mnemonicParsing="false" text="开机自动生成桌宠"/>
                    <JFXCheckBox fx:id="configSolidExit" mnemonicParsing="false" text="退出程序时也退出桌宠"/>
                </HBox>
                <HBox>
                    <JFXCheckBox fx:id="configWindowTopmost" mnemonicParsing="false" text="桌宠窗口置顶"/>
                    <JFXCheckBox fx:id="configWindowToolwindow" mnemonicParsing="false" text="桌宠作为后台程序启动"/>
                    <JFXButton fx:id="configWindowToolwindowHelp"/>
                </HBox>
                <HBox>
                    <JFXCheckBox fx:id="configEcoMode" mnemonicParsing="false" text="长时间未交互时降低帧率"/>
                </HBox>
                <Separator/>
                <HBox spacing="20.0">
                    <Label text="日志级别"/>
                    <JFXComboBox fx:id="configLoggingLevel" prefWidth="100.0"/>
                    <JFXButton fx:id="exportLog" alignment="TOP_CENTER" minHeight="-Infinity" minWidth="-Infinity"
                               mnemonicParsing="false" prefWidth="82.5" text="导出日志">
                        <graphic>
                            <AnchorPane prefHeight="25.0" prefWidth="16.0" styleClass="btn-icon">
                                <SVGPath
                                        content="M23 0v20h-8v-2h6v-16h-18v16h6v2h-8v-20h22zm-12 13h-4l5-6 5 6h-4v11h-2v-11z"
                                        scaleX="1.1" scaleY="1.1" AnchorPane.leftAnchor="-36.0"/>
                            </AnchorPane>
                        </graphic>
                        <styleClass>
                            <String fx:value="btn-secondary"/>
                            <String fx:value="btn-with-icon"/>
                        </styleClass>
                    </JFXButton>
                </HBox>
                <HBox spacing="20.0">
                    <Label text="网络代理（仅本次有效）"/>
                    <JFXTextField fx:id="configNetworkAgent" prefHeight="23.0" prefWidth="100.0"/>
                    <Label fx:id="configNetworkAgentStatus" text="-"/>
                </HBox>
                <Separator/>
                <Label styleClass="config-group-title" text="关于软件"/>
                <HBox spacing="20.0">
                    <Label fx:id="aboutQueryUpdate" styleClass="config-hyper-link" text="检查软件更新"/>
                    <Label fx:id="aboutVisitWebsite" styleClass="config-hyper-link" text="ArkPets 官网"/>
                </HBox>
                <HBox spacing="20.0">
                    <Label fx:id="aboutReadme" styleClass="config-hyper-link" text="使用指南"/>
                    <Label fx:id="aboutGitHub" styleClass="config-hyper-link" text="GitHub 项目仓库"/>
                </HBox>
                <Separator/>
            </VBox>
        </AnchorPane>
    </ScrollPane>
</Pane>
