<?xml version="1.0" encoding="UTF-8"?>

<!--
    Copyright (c) 2022-2025, <PERSON>
    At GPL-3.0 License
-->

<!-- ********* Wrapper 2 ********* -->
<?import com.jfoenix.controls.*?>
<?import javafx.scene.canvas.Canvas?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.SVGPath?>
<?import java.lang.String?>
<Pane prefHeight="376.0" prefWidth="460.0" styleClass="wrapper" stylesheets="@Main.css"
      xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="cn.harryh.arkpets.controllers.BehaviorModule">
    <ScrollPane fx:id="moduleScroll" layoutX="10.0" layoutY="11.0" prefHeight="350.0" prefWidth="440.0"
                styleClass="scroll-v">
        <AnchorPane maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minHeight="0.0" minWidth="0.0"
                    prefWidth="425.0" styleClass="config-field">
            <VBox>
                <Label styleClass="config-group-title" text="动作"/>
                <HBox>
                    <JFXCheckBox fx:id="configBehaviorAllowWalk" mnemonicParsing="false" text="允许行走"/>
                    <JFXCheckBox fx:id="configBehaviorAllowSit" mnemonicParsing="false" text="允许坐下"/>
                </HBox>
                <HBox>
                    <JFXCheckBox fx:id="configBehaviorAllowSleep" mnemonicParsing="false" text="允许躺下"/>
                    <JFXCheckBox fx:id="configBehaviorAllowSpecial" mnemonicParsing="false" text="允许特殊动作"/>
                </HBox>
                <HBox>
                    <Label text="动作活跃度"/>
                    <JFXSlider fx:id="configBehaviorAiActivation"/>
                    <Label fx:id="configBehaviorAiActivationValue" text="0"/>
                </HBox>
                <Separator/>
                <Label styleClass="config-group-title" text="交互"/>
                <HBox>
                    <JFXCheckBox fx:id="configBehaviorAllowInteract" mnemonicParsing="false" text="允许触发交互动画"/>
                    <JFXCheckBox fx:id="configBehaviorDoPeerRepulsion" mnemonicParsing="false"
                                 text="允许被其他桌宠排斥"/>
                </HBox>
                <Separator/>
                <Label styleClass="config-group-title" text="位置"/>
                <HBox>
                    <JFXCheckBox fx:id="configDeployMultiMonitors" mnemonicParsing="false" text="启用多显示屏"/>
                    <Label fx:id="configDeployMultiMonitorsStatus" text="-"/>
                </HBox>
                <HBox>
                    <Label text="任务栏高度（下边界距离）"/>
                    <JFXSlider fx:id="configDeployMarginBottom"/>
                    <Label fx:id="configDeployMarginBottomValue" text="0"/>
                </HBox>
                <HBox>
                    <Label text="初始部署位置"/>
                    <JFXButton fx:id="toggleConfigDeployPosition" minHeight="-Infinity" minWidth="-Infinity"
                               mnemonicParsing="false" prefHeight="28.0" prefWidth="60.0" text="配置">
                        <graphic>
                            <AnchorPane prefHeight="25.0" prefWidth="16.0" scaleX="0.0" styleClass="btn-icon">
                                <SVGPath
                                        content="m12 1c-3.148 0-6 2.553-6 5.702 0 3.148 2.602 6.907 6 12.298 3.398-5.391 6-9.15 6-12.298 0-3.149-2.851-5.702-6-5.702zm0 8c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2zm12 14h-24l4-8h3.135c.385.641.798 1.309 1.232 2h-3.131l-2 4h17.527l-2-4h-3.131c.435-.691.848-1.359 1.232-2h3.136l4 8z"
                                        scaleX="1.2" scaleY="1.2" AnchorPane.leftAnchor="-9.0"/>
                            </AnchorPane>
                        </graphic>
                        <styleClass>
                            <String fx:value="btn-secondary"/>
                            <String fx:value="btn-with-icon"/>
                        </styleClass>
                    </JFXButton>
                </HBox>
                <HBox fx:id="wrapperConfigDeployPosition" managed="false" visible="false">
                    <Label styleClass="config-help-text" text="在矩形区域内单击以选定桌宠生成时在屏幕中的初始位置"/>
                    <Canvas fx:id="configDeployPosition" height="150.0" width="150.0"/>
                </HBox>
                <Separator/>
                <Label styleClass="config-group-title" text="过渡"/>
                <HBox>
                    <Label fx:id="configTransitionAnimationLabel" text="动画交叉过渡"/>
                    <JFXComboBox fx:id="configTransitionAnimation" prefWidth="120.0"/>
                    <JFXButton fx:id="configTransitionAnimationHelp"/>
                </HBox>
                <HBox>
                    <Label fx:id="configTransitionDurationLabel" text="常规属性过渡"/>
                    <JFXComboBox fx:id="configTransitionDuration" prefWidth="120.0"/>
                    <JFXButton fx:id="configTransitionDurationHelp"/>
                </HBox>
                <HBox>
                    <Label fx:id="configTransitionFunctionLabel" text="缓动函数"/>
                    <JFXComboBox fx:id="configTransitionFunction" prefWidth="120.0"/>
                    <JFXButton fx:id="configTransitionFunctionHelp"/>
                </HBox>
                <Separator/>
                <Label styleClass="config-group-title" text="物理"/>
                <HBox>
                    <Label text="重力加速度"/>
                    <JFXSlider fx:id="configPhysicGravity"/>
                    <Label fx:id="configPhysicGravityValue" text="0"/>
                </HBox>
                <HBox>
                    <Label text="空气阻力系加速度"/>
                    <JFXSlider fx:id="configPhysicAirFriction"/>
                    <Label fx:id="configPhysicAirFrictionValue" text="0"/>
                </HBox>
                <HBox>
                    <Label text="地面摩擦系加速度"/>
                    <JFXSlider fx:id="configPhysicStaticFriction"/>
                    <Label fx:id="configPhysicStaticFrictionValue" text="0"/>
                </HBox>
                <HBox>
                    <Label text="最大水平运动速率"/>
                    <JFXSlider fx:id="configPhysicSpeedLimitX"/>
                    <Label fx:id="configPhysicSpeedLimitXValue" text="0"/>
                </HBox>
                <HBox>
                    <Label text="最大垂直运动速率"/>
                    <JFXSlider fx:id="configPhysicSpeedLimitY"/>
                    <Label fx:id="configPhysicSpeedLimitYValue" text="0"/>
                </HBox>
                <Separator/>
                <HBox spacing="22.5">
                    <Label fx:id="configPhysicRestore" styleClass="config-hyper-link" text="恢复默认物理设置"/>
                </HBox>
                <Separator/>
            </VBox>
        </AnchorPane>
    </ScrollPane>
</Pane>
