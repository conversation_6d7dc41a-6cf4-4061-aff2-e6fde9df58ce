<?xml version="1.0" encoding="UTF-8"?>

<!--
    Copyright (c) 2022-2025, <PERSON>
    At GPL-3.0 License
-->

<!-- ************ Root Container Node ************ -->

<?import com.jfoenix.controls.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.effect.DropShadow?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.paint.Color?>
<?import javafx.scene.shape.*?>
<?import javafx.scene.text.Text?>
<?import java.lang.String?>
<StackPane fx:id="rootContainer" prefHeight="410.0" prefWidth="620.0" styleClass="root-container"
           stylesheets="@Main.css" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1"
           fx:controller="cn.harryh.arkpets.controllers.RootModule">
    <!-- *************** Root Node *************** -->
    <StackPane fx:id="root" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" prefHeight="400.0"
               prefWidth="600.0" styleClass="root" StackPane.alignment="TOP_CENTER">

        <!-- ********** 1. Main Content ********** -->
        <StackPane fx:id="body" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" prefHeight="376.0"
                   prefWidth="600.0" StackPane.alignment="BOTTOM_CENTER">
            <AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity"
                        prefHeight="376.0" prefWidth="600.0" StackPane.alignment="BOTTOM_CENTER">
                <!-- ***** 1.1. Sidebar ***** -->
                <Pane id="Sidebar" fx:id="sidebar" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity"
                      minWidth="-Infinity" prefHeight="376.0" prefWidth="140.0" styleClass="shadowed"
                      AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0" StackPane.alignment="BOTTOM_LEFT">
                    <Text id="Title" layoutX="12.0" layoutY="48.0" text="ArkPets" textAlignment="CENTER"
                          wrappingWidth="117.0">
                    </Text>
                    <Line endX="128.0" layoutY="60.0" startX="12.0" stroke="#0000009e"/>
                    <JFXButton fx:id="annoEntrance" layoutX="40.0" layoutY="64.0" minHeight="-Infinity"
                               minWidth="-Infinity" mnemonicParsing="false" prefHeight="28.0" prefWidth="60.0"
                               text="公告">
                        <graphic>
                            <AnchorPane prefHeight="25.0" prefWidth="16.0" styleClass="btn-icon">
                                <SVGPath
                                        content="M12.399 2.276c-.208-.63.264-1.276.919-1.276.405 0 .783.257.918.664l5.691 14.286c.169.509-.106 1.057-.613 1.226-.507.169-1.055-.106-1.224-.614 0 0-3.593-1.203-7.854.211l2.801 4.354c.291.428.088 1.013-.396 1.173l-1.904.634c-.131.044-.267.066-.401.066-.357 0-.705-.152-.951-.429l-3.85-4.247c-2.096.661-4.468-.102-5.26-2.076-.182-.453-.275-.936-.275-1.421 0-1.407.786-2.842 2.492-3.68 8.541-4.194 9.907-8.871 9.907-8.871m6.067.913c1.518.641 2.789 1.865 3.459 3.516.669 1.651.607 3.42-.034 4.94l1.474.626c.415-.985.635-2.053.635-3.141 0-3.167-1.873-6.133-4.911-7.419l-.623 1.478zm-1.064 2.523c.874.368 1.608 1.073 1.992 2.024.386.951.351 1.968-.017 2.843l1.436.61c.524-1.246.576-2.691.028-4.042-.547-1.352-1.588-2.352-2.831-2.877l-.608 1.442z"
                                        scaleX="0.95" scaleY="0.95"/>
                            </AnchorPane>
                        </graphic>
                        <styleClass>
                            <String fx:value="btn-plain-dark"/>
                            <String fx:value="btn-with-icon"/>
                        </styleClass>
                    </JFXButton>
                    <GridPane alignment="CENTER" layoutY="95.0" prefHeight="180.0" prefWidth="140.0" styleClass="menu">
                        <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0"/>
                        </columnConstraints>
                        <rowConstraints>
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                        </rowConstraints>
                        <JFXButton fx:id="menuBtn1" mnemonicParsing="false" prefHeight="40.0" prefWidth="140.0"
                                   styleClass="menu-btn" text="模型" textAlignment="CENTER">
                            <GridPane.margin>
                                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                            </GridPane.margin>
                            <graphic>
                                <AnchorPane prefHeight="24.0" prefWidth="24.0" translateX="-10.0">
                                    <SVGPath
                                            content="m3.514 6.636c-.317.179-.514.519-.514.887v8.95c0 .37.197.708.514.887 1.597.901 6.456 3.639 8.005 4.512.152.085.319.128.487.128.164 0 .328-.041.477-.123 1.549-.855 6.39-3.523 7.994-4.408.323-.177.523-.519.523-.891v-9.055c0-.368-.197-.708-.515-.887-1.595-.899-6.444-3.632-7.999-4.508-.151-.085-.319-.128-.486-.128-.168 0-.335.043-.486.128-1.555.876-6.405 3.609-8 4.508zm15.986 2.115v7.525l-6.75 3.722v-7.578zm-14.264-1.344 6.764-3.813 6.801 3.834-6.801 3.716z"
                                            scaleX="0.95" scaleY="0.95" translateX="5.0"/>
                                </AnchorPane>
                            </graphic>
                        </JFXButton>
                        <JFXButton fx:id="menuBtn2" mnemonicParsing="false" prefHeight="40.0" prefWidth="140.0"
                                   styleClass="menu-btn" text="行为" textAlignment="CENTER" GridPane.rowIndex="1">
                            <GridPane.margin>
                                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                            </GridPane.margin>
                            <graphic>
                                <AnchorPane prefHeight="24.0" prefWidth="24.0" scaleX="0.95" scaleY="0.95"
                                            translateX="-10.0">
                                    <SVGPath
                                            content="m18.39 8.428c-.835.186-2.113.51-2.845.866-1.089.529-1.874 1.358-1.874 2.76 0 4.089 3.073 7.956 3.073 8.293 0 .131-.137.203-.227.113 0-.001-.001-.002-.001-.002-.673-.69-1.997-2.747-2.606-3.738v-.001c-.404-.653-.951-1.448-1.903-1.448h-.003c-.961 0-1.509.791-1.914 1.449-2.274 3.698-2.707 3.738-2.607 3.738-.094.095-.228.015-.228-.111 0-.285 3.073-4.285 3.073-8.293 0-1.336-.697-2.139-1.744-2.678-.833-.428-1.923-.669-2.956-.944-.009-.002-.017-.004-.026-.006-.138-.032-.138-.272.011-.299 1.098.25 3.412.923 6.387.923 2.94 0 5.295-.669 6.389-.923.145.029.152.265.001.301m-6.392-4.928c.858 0 1.552.7 1.552 1.562s-.694 1.563-1.552 1.563c-.856 0-1.55-.701-1.55-1.563s.694-1.562 1.55-1.562m6.367 3.125c-.427 0-2.112.584-4.474.821.699-.561 1.157-1.414 1.157-2.384 0-1.691-1.366-3.062-3.05-3.062-1.681 0-3.049 1.371-3.049 3.062 0 .97.458 1.824 1.158 2.385-2.361-.238-4.018-.822-4.472-.822-.897 0-1.635.738-1.635 1.653 0 .765.536 1.437 1.256 1.608 1.805.478 3.573.755 3.573 2.168 0 3.145-2.041 6.072-2.852 7.462-.002.003-.004.006-.005.009-.142.251-.216.536-.216.822 0 .916.737 1.653 1.635 1.653.437 0 .853-.174 1.165-.494.722-.741 2.157-2.937 2.811-3.999.12-.195.238-.383.371-.537.082-.096.151-.199.267-.199.113 0 .176.105.256.198.133.154.252.343.373.539.652 1.06 2.086 3.255 2.809 3.997.31.319.724.495 1.167.495.896 0 1.634-.737 1.634-1.653 0-.282-.07-.562-.226-.837-.002-.002-.003-.005-.005-.008-.83-1.426-2.843-4.3-2.843-7.448 0-1.516 2.067-1.772 3.567-2.167.728-.173 1.263-.846 1.263-1.609 0-.915-.739-1.653-1.635-1.653"
                                            translateX="5.0"/>
                                </AnchorPane>
                            </graphic>
                        </JFXButton>
                        <JFXButton fx:id="menuBtn3" mnemonicParsing="false" prefHeight="40.0" prefWidth="140.0"
                                   styleClass="menu-btn" text="选项" textAlignment="CENTER" GridPane.rowIndex="2">
                            <GridPane.margin>
                                <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                            </GridPane.margin>
                            <graphic>
                                <AnchorPane prefHeight="24.0" prefWidth="24.0" translateX="-10.0">
                                    <SVGPath
                                            content="m19 18c0 1.104-.896 2-2 2s-2-.896-2-2 .896-2 2-2 2 .896 2 2zm-14-3c-1.654 0-3 1.346-3 3s1.346 3 3 3h14c1.654 0 3-1.346 3-3s-1.346-3-3-3h-14zm19 3c0 2.761-2.239 5-5 5h-14c-2.761 0-5-2.239-5-5s2.239-5 5-5h14c2.761 0 5 2.239 5 5zm0-12c0 2.761-2.239 5-5 5h-14c-2.761 0-5-2.239-5-5s2.239-5 5-5h14c2.761 0 5 2.239 5 5zm-15 0c0-1.104-.896-2-2-2s-2 .896-2 2 .896 2 2 2 2-.896 2-2z"
                                            scaleX="0.8" scaleY="0.8" translateX="5.0"/>
                                </AnchorPane>
                            </graphic>
                        </JFXButton>
                    </GridPane>
                    <JFXButton id="Launch-btn" fx:id="launchBtn" layoutX="25.0" layoutY="310.0" mnemonicParsing="false"
                               prefHeight="36.0" prefWidth="90.0" text="启动" textAlignment="CENTER">
                        <graphic>
                            <AnchorPane minHeight="-Infinity" minWidth="-Infinity" prefHeight="24.0" prefWidth="2.0"
                                        translateX="-20.0" translateY="1.0">
                                <SVGPath
                                        content="m13 2.004c5.046.504 9 4.783 9 9.97 0 1.467-.324 2.856-.892 4.113l1.738 1.005c.732-1.553 1.154-3.284 1.154-5.117 0-6.304-4.842-11.464-11-11.975v2.004zm-10.109 14.083c-.568-1.257-.891-2.646-.891-4.112 0-5.188 3.954-9.466 9-9.97v-2.005c-6.158.511-11 5.671-11 11.975 0 1.833.421 3.563 1.153 5.118l1.738-1.006zm17.213 1.734c-1.817 2.523-4.769 4.174-8.104 4.174s-6.288-1.651-8.105-4.175l-1.746 1.01c2.167 3.123 5.768 5.17 9.851 5.17 4.082 0 7.683-2.047 9.851-5.168l-1.747-1.011zm-8.104-13.863c-4.419 0-8 3.589-8 8.017s3.581 8.017 8 8.017 8-3.589 8-8.017-3.581-8.017-8-8.017zm-2 11.023v-6.013l6 3.152-6 2.861z"
                                        scaleX="0.8" scaleY="0.8"/>
                            </AnchorPane>
                        </graphic>
                    </JFXButton>
                </Pane>

                <!-- ***** 1.2. Wrappers for modules ***** -->
                <AnchorPane fx:id="wrapper1" visible="false" AnchorPane.leftAnchor="140.0" AnchorPane.topAnchor="0.0"/>
                <AnchorPane fx:id="wrapper2" visible="false" AnchorPane.leftAnchor="140.0" AnchorPane.topAnchor="0.0"/>
                <AnchorPane fx:id="wrapper3" visible="false" AnchorPane.leftAnchor="140.0" AnchorPane.topAnchor="0.0"/>

                <!-- ***** 1.3. Mask for loading ***** -->
                <Pane fx:id="loadingMask" prefHeight="376.0" prefWidth="460.0" styleClass="wrapper" visible="false"
                      AnchorPane.leftAnchor="140.0" AnchorPane.topAnchor="0.0">
                    <VBox id="Loading-waiting-tip" alignment="CENTER" layoutX="1.0" prefHeight="370.0" prefWidth="458.0"
                          spacing="5.0">
                        <SVGPath
                                content="m18.513 7.119c.958-1.143 1.487-2.577 1.487-4.036v-3.083h-16v3.083c0 1.459.528 2.892 1.487 4.035l3.087 3.68c.566.677.57 1.625.009 2.306l-3.13 3.794c-.937 1.136-1.453 2.555-1.453 3.995v3.107h16v-3.107c0-1.44-.517-2.858-1.453-3.994l-3.13-3.794c-.562-.681-.558-1.629.009-2.306l3.087-3.68zm-.513-4.12c0 1.101-.363 2.05-1.02 2.834l-.978 1.167h-8.004l-.978-1.167c-.66-.785-1.02-1.736-1.02-2.834h12zm-.996 15.172c.652.791.996 1.725.996 2.829h-1.061c-1.939-2-4.939-2-4.939-2s-3 0-4.939 2h-1.061c0-1.104.344-2.039.996-2.829l3.129-3.793c.342-.415.571-.886.711-1.377h.164v1h2v-1h.163c.141.491.369.962.711 1.376l3.13 3.794zm-6.004-1.171h2v1h-2v-1zm0-2h2v1h-2v-1z"
                                scaleX="1.25" scaleY="1.25" translateY="-10.0"/>
                        <Text strokeType="OUTSIDE" strokeWidth="0.0" text="Loading"/>
                    </VBox>
                </Pane>
                <StackPane.margin>
                    <Insets top="20.0"/>
                </StackPane.margin>
            </AnchorPane>
        </StackPane>

        <!-- ********** 2. Splash Screen ********** -->
        <AnchorPane fx:id="splashScreen" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity"
                    minWidth="-Infinity" prefHeight="376.0" prefWidth="600.0" StackPane.alignment="BOTTOM_CENTER">
            <ImageView fx:id="splashScreenIcon" fitHeight="90.0" fitWidth="90.0" AnchorPane.leftAnchor="255.0"
                       AnchorPane.topAnchor="125.0">
                <Image url="@/icons/icon.png"/>
                <effect>
                    <DropShadow height="25.0" radius="12.0" spread="0.05" width="25.0">
                        <color>
                            <Color red="0.1255" green="0.2824" blue="0.5020"/>
                        </color>
                    </DropShadow>
                </effect>
            </ImageView>
            <StackPane.margin>
                <Insets top="20.0"/>
            </StackPane.margin>
        </AnchorPane>

        <!-- ********** 3. Title Bar ********** -->
        <AnchorPane id="Title-bar" fx:id="titleBar" maxHeight="-Infinity" minHeight="-Infinity"
                    onMouseDragged="#titleBarDragged" onMousePressed="#titleBarPressed" prefHeight="24.0"
                    styleClass="shadowed" StackPane.alignment="TOP_CENTER">
            <HBox prefHeight="24.0" prefWidth="200.0" AnchorPane.leftAnchor="3.0">
                <ImageView cache="true" fitHeight="18.0" fitWidth="18.0" pickOnBounds="true">
                    <HBox.margin>
                        <Insets bottom="3.0" left="3.0" right="3.0" top="3.0"/>
                    </HBox.margin>
                    <Image requestedHeight="512.0" requestedWidth="512.0" url="@/icons/icon.png"/>
                </ImageView>
                <Text fx:id="titleText" strokeType="OUTSIDE" strokeWidth="0.0" text="ArkPets Launcher">
                    <HBox.margin>
                        <Insets bottom="3.0" left="3.0" right="3.0" top="3.0"/>
                    </HBox.margin>
                </Text>
            </HBox>
            <HBox alignment="TOP_RIGHT" prefHeight="24.0" prefWidth="200.0" AnchorPane.rightAnchor="0.0">
                <JFXButton id="Title-minimize-btn" fx:id="titleMinimizeBtn" mnemonicParsing="false"
                           onMouseClicked="#windowMinimize" text=" ">
                    <graphic>
                        <SVGPath
                                content="m21 11.75c0-.414-.336-.75-.75-.75h-16.5c-.414 0-.75.336-.75.75s.336.75.75.75h16.5c.414 0 .75-.336.75-.75z"
                                scaleX="0.75" scaleY="0.75" translateX="-5.0"/>
                    </graphic>
                </JFXButton>
                <JFXButton id="Title-close-btn" fx:id="titleCloseBtn" mnemonicParsing="false"
                           onMouseClicked="#windowClose" text=" ">
                    <graphic>
                        <SVGPath
                                content="m12 10.93 5.719-5.72c.146-.146.339-.219.531-.219.404 0 .75.324.75.749 0 .193-.073.385-.219.532l-5.72 5.719 5.719 5.719c.147.147.22.339.22.531 0 .427-.349.75-.75.75-.192 0-.385-.073-.531-.219l-5.719-5.719-5.719 5.719c-.146.146-.339.219-.531.219-.401 0-.75-.323-.75-.75 0-.192.073-.384.22-.531l5.719-5.719-5.72-5.719c-.146-.147-.219-.339-.219-.532 0-.425.346-.749.75-.749.192 0 .385.073.531.219z"
                                scaleX="0.75" scaleY="0.75" translateX="-3"/>
                    </graphic>
                </JFXButton>
            </HBox>
        </AnchorPane>

        <!-- ********** 4. Toast ********** -->
        <HBox fx:id="toast" id="Toast" maxHeight="30.0" maxWidth="-Infinity" minWidth="-Infinity" minHeight="-Infinity"
              styleClass="shadowed" prefHeight="40.0" StackPane.alignment="BOTTOM_CENTER" spacing="6" alignment="CENTER"
              managed="false" visible="false">
            <StackPane.margin>
                <Insets bottom="20.0"/>
            </StackPane.margin>
        </HBox>

    </StackPane>
</StackPane>
