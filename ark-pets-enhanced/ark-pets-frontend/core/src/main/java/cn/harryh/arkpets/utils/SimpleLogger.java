/** Copyright (c) 2022-2025, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Simplified logger implementation to replace the complex Log4j-based Logger.
 * This provides basic logging functionality without external dependencies.
 */
public class SimpleLogger {
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
  private static boolean debugEnabled = true;
  
  public static final int ERROR = 40000;
  public static final int WARN = 30000;
  public static final int INFO = 20000;
  public static final int DEBUG = 10000;
  
  public static void initialize(String logPrefix, int maxFileCount) {
    // Simplified initialization - just enable console logging
    System.out.println("[INIT] Logger initialized with prefix: " + logPrefix);
  }
  
  public static void setLevel(int level) {
    debugEnabled = level <= DEBUG;
  }
  
  public static void setLevel(String level) {
    switch (level.toUpperCase()) {
      case "DEBUG": setLevel(DEBUG); break;
      case "INFO": setLevel(INFO); break;
      case "WARN": setLevel(WARN); break;
      case "ERROR": setLevel(ERROR); break;
      default: setLevel(INFO);
    }
  }
  
  public static void debug(String tag, String message) {
    if (debugEnabled) {
      log("DEBUG", tag, message);
    }
  }
  
  public static void info(String tag, String message) {
    log("INFO", tag, message);
  }
  
  public static void warn(String tag, String message) {
    log("WARN", tag, message);
  }
  
  public static void error(String tag, String message) {
    log("ERROR", tag, message);
  }
  
  public static void error(String tag, String message, Throwable error) {
    log("ERROR", tag, message + " - " + error.getMessage());
    if (debugEnabled) {
      error.printStackTrace();
    }
  }
  
  private static void log(String level, String tag, String message) {
    String timestamp = LocalDateTime.now().format(formatter);
    String logMessage = String.format("%s [%s] %s: %s", timestamp, level, tag, message);
    
    if ("ERROR".equals(level) || "WARN".equals(level)) {
      System.err.println(logMessage);
    } else {
      System.out.println(logMessage);
    }
  }
}
