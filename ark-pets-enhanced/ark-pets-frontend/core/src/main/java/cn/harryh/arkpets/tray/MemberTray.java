/** Copyright (c) 2022-2025, <PERSON>, Half Nothing At GPL-3.0 License */
package cn.harryh.arkpets.tray;

import cn.harryh.arkpets.Const;
import cn.harryh.arkpets.concurrent.SocketData;
import java.awt.*;
import java.util.UUID;
import javax.swing.*;

public abstract class MemberTray {
  protected JMenuItem optKeepAnimEn = new JMenuItem("行动模式");
  protected JMenuItem optKeepAnimDis = new JMenuItem("退出行动");
  protected JMenuItem optTransparentEn = new JMenuItem("透明模式");
  protected JMenuItem optTransparentDis = new JMenuItem("取消透明");
  protected JMenuItem optChangeStage = new JMenuItem("切换形态");
  protected JMenuItem optExit = new JMenuItem("退出");
  protected final UUID uuid;
  protected final String name;

  static {
    // Avoid AWT Thread problem.
    try {
      String laf = UIManager.getSystemLookAndFeelClassName();
      if (laf.contains("WindowsLookAndFeel")) {
        UIManager.put("MenuItem.margin", new Insets(0, -18, 0, 0));
        UIManager.put("Menu.margin", new Insets(0, -18, 0, 0));
      }
      UIManager.setLookAndFeel(laf);
    } catch (Exception ignored) {
    }
    Const.FontsConfig.loadFontsToSwing();
  }

  /**
   * Initializes a tray icon instance for a ArkPets.
   *
   * @param name The name to be displayed in the menu, in the icon tooltip, etc.
   */
  public MemberTray(String name) {
    this.uuid = UUID.randomUUID();
    this.name = name;

    optKeepAnimEn.addActionListener(e -> onKeepAnimEn());
    optKeepAnimDis.addActionListener(e -> onKeepAnimDis());
    optTransparentEn.addActionListener(e -> onTransparentEn());
    optTransparentDis.addActionListener(e -> onTransparentDis());
    optChangeStage.addActionListener(e -> onChangeStage());
    optExit.addActionListener(e -> onExit());

    optKeepAnimEn.addActionListener(e -> sendOperation(SocketData.Operation.KEEP_ACTION));
    optKeepAnimDis.addActionListener(e -> sendOperation(SocketData.Operation.NO_KEEP_ACTION));
    optTransparentEn.addActionListener(e -> sendOperation(SocketData.Operation.TRANSPARENT_MODE));
    optTransparentDis.addActionListener(
        e -> sendOperation(SocketData.Operation.NO_TRANSPARENT_MODE));
    optChangeStage.addActionListener(e -> sendOperation(SocketData.Operation.CHANGE_STAGE));
    optExit.addActionListener(e -> sendOperation(SocketData.Operation.LOGOUT));

    optKeepAnimEn.setIcon(null);
    optKeepAnimDis.setIcon(null);
    optTransparentEn.setIcon(null);
    optTransparentDis.setIcon(null);
    optChangeStage.setIcon(null);
    optExit.setIcon(null);
  }

  public abstract void onExit();

  public abstract void onChangeStage();

  public abstract void onTransparentDis();

  public abstract void onTransparentEn();

  public abstract void onKeepAnimDis();

  public abstract void onKeepAnimEn();

  public abstract void remove();

  public abstract void sendOperation(SocketData.Operation operation);
}
