/** Copyright (c) 2022-2025, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.utils;

import static cn.harryh.arkpets.Const.charsetDefault;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.ConsoleAppender;
import org.apache.logging.log4j.core.appender.FileAppender;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.ConfigurationFactory;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.builder.api.AppenderComponentBuilder;
import org.apache.logging.log4j.core.config.builder.api.ConfigurationBuilder;
import org.apache.logging.log4j.core.config.builder.api.LayoutComponentBuilder;
import org.apache.logging.log4j.core.config.builder.impl.BuiltConfiguration;
import org.apache.logging.log4j.core.layout.PatternLayout;

public class Logger {
  protected static final org.apache.logging.log4j.Logger rootLogger = LogManager.getRootLogger();
  protected static final org.apache.logging.log4j.Logger currentLogger = rootLogger;
  protected static final long pid = ProcessHandle.current().pid();
  protected static boolean isFileLoggerAvailable = false;
  protected static boolean isInitialized = false;
  protected static int maxFileCount = 256;
  protected static Level level = Level.INFO;

  public static final int ERROR = 40000;
  public static final int WARN = 30000;
  public static final int INFO = 20000;
  public static final int DEBUG = 10000;

  /**
   * Initializes the static logger for the app. The default log level is {@code INFO}.
   *
   * @param logPrefix The prefix of the log's path-and-basename, e.g.{@code "logs/myLog"}.
   * @param maxFileCount The maximum count of the logs that shares the same prefix, overmuch logs
   *     will be deleted.
   */
  public static void initialize(String logPrefix, int maxFileCount) {
    Logger.maxFileCount = Math.max(1, maxFileCount);

    // Create a programmatic configuration
    ConfigurationBuilder<BuiltConfiguration> builder = ConfigurationFactory.newConfigurationBuilder();

    // Create console appender
    AppenderComponentBuilder consoleAppender = builder.newAppender("Console", "CONSOLE")
        .addAttribute("target", ConsoleAppender.Target.SYSTEM_OUT);
    consoleAppender.add(builder.newLayout("PatternLayout")
        .addAttribute("pattern", "[%p] %m%n"));
    builder.add(consoleAppender);

    // Create file appender
    String logFilePath = "%s.%d.log".formatted(logPrefix, pid);
    try {
      Cleaner.cleanByModifiedTime(logPrefix, Logger.maxFileCount - 1);
      AppenderComponentBuilder fileAppender = builder.newAppender("File", "File")
          .addAttribute("fileName", logFilePath)
          .addAttribute("append", false);

      LayoutComponentBuilder fileLayout = builder.newLayout("PatternLayout")
          .addAttribute("pattern", "%d{ABSOLUTE} [%p] %m%n")
          .addAttribute("header", getLogHeader(logPrefix));
      fileAppender.add(fileLayout);
      builder.add(fileAppender);
      isFileLoggerAvailable = true;
    } catch (Exception e) {
      System.err.println("Failed to initialize the file logger: " + e.getMessage());
    }

    // Create root logger
    builder.add(builder.newRootLogger(level)
        .add(builder.newAppenderRef("Console"))
        .add(builder.newAppenderRef("File")));

    // Apply configuration
    LoggerContext context = (LoggerContext) LogManager.getContext(false);
    context.setConfiguration(builder.build());
    context.updateLoggers();

    isInitialized = true;
  }

  private static String getLogHeader(String logPrefix) {
    final String header = "ArkPets Log - " + new File(logPrefix).getName() + " (PID" + pid + ")";
    return "# *** " + header + " ***\n" +
           "# Created: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss,SSS")) + "\n" +
           "# OS: " + System.getProperty("os.name") + " (" + System.getProperty("os.arch") + ")\n" +
           "# Java version: " + System.getProperty("java.version") + "\n" +
           "# Working directory: " + System.getProperty("user.dir") + "\n\n";
  }

  /**
   * Sets a new log level.
   *
   * @param level The new level.
   */
  public static void setLevel(Level level) {
    Logger.level = level;
    LoggerContext context = (LoggerContext) LogManager.getContext(false);
    context.getConfiguration().getLoggerConfig(LogManager.ROOT_LOGGER_NAME).setLevel(level);
    context.updateLoggers();
  }

  /**
   * Sets a new log level.
   *
   * @param level The new level in int format.
   */
  public static void setLevel(int level) {
    Level newLevel = switch (level) {
      case DEBUG -> Level.DEBUG;
      case INFO -> Level.INFO;
      case WARN -> Level.WARN;
      case ERROR -> Level.ERROR;
      default -> Level.INFO;
    };
    setLevel(newLevel);
  }

  /**
   * Sets a new log level.
   *
   * @param level The new level in string format.
   */
  public static void setLevel(String level) {
    setLevel(Level.getLevel(level));
  }

  /**
   * Gets the level of root logger.
   *
   * @return The level object.
   */
  public static Level getLevel() {
    return level;
  }

  /** Logs a message with the level {@code DEBUG}. */
  public static void debug(String tag, String message) {
    if (isFileLoggerAvailable) currentLogger.debug(combine(tag, message));
  }

  /** Logs a message with the level {@code INFO}. */
  public static void info(String tag, String message) {
    if (isFileLoggerAvailable) currentLogger.info(combine(tag, message));
  }

  /** Logs a message with the level {@code WARN}. */
  public static void warn(String tag, String message) {
    if (isFileLoggerAvailable) currentLogger.warn(combine(tag, message));
  }

  /** Logs a message with the level {@code ERROR}. */
  public static void error(String tag, String message) {
    if (isFileLoggerAvailable) currentLogger.error(combine(tag, message));
  }

  /**
   * Logs a message with the level {@code ERROR}, together with the detailed information (such as
   * stacktrace).
   */
  public static void error(String tag, String message, Throwable error) {
    if (isFileLoggerAvailable) currentLogger.error(combine(tag, message), error);
  }

  protected static String combine(String tag, String message) {
    return tag + ": " + message;
  }

  protected static class Cleaner {
    public static void cleanByModifiedTime(String logPrefixName, int maxFileCount) {
      List<File> fileList = getAllLogs(logPrefixName);
      maxFileCount = Math.max(1, maxFileCount);
      if (fileList.size() >= maxFileCount) {
        sortByModifiedTime(fileList);
        deleteButKeep(fileList, maxFileCount);
      }
    }

    private static void deleteButKeep(List<File> fileList, int maxFileCount) {
      if (fileList.size() > maxFileCount) {
        for (int i = 0; i < fileList.size() - maxFileCount; i++) {
          try {
            //noinspection ResultOfMethodCallIgnored
            fileList.get(i).delete();
            System.out.println("Delete file: " + fileList.get(i));
          } catch (SecurityException e) {
            System.err.println("Cannot delete file: " + fileList.get(i) + " - " + e.getMessage());
          }
        }
      }
    }

    private static void sortByModifiedTime(List<File> fileList) {
      if (fileList.isEmpty()) return;
      fileList.sort(
          (o1, o2) -> {
            long t1 = o1.lastModified();
            long t2 = o2.lastModified();
            if (t1 != t2) return t1 > t2 ? 1 : -1;
            return 0;
          });
    }

    private static List<File> getAllLogs(String logPrefixName) {
      File file = new File(logPrefixName);
      File dir = file.getParentFile() == null ? new File(".") : file.getParentFile();
      String finalLogPrefixName = file.getName();
      File[] files = dir.listFiles(pathname -> pathname.getName().indexOf(finalLogPrefixName) == 0);
      if (files == null) return List.of();
      return Arrays.asList(files);
    }
  }
}
