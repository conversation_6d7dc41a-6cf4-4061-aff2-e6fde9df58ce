/** Copyright (c) 2022-2025, <PERSON> At GPL-3.0 License */
package cn.harryh.arkpets.utils;

/**
 * Logger wrapper that delegates to SimpleLogger for compatibility.
 */
public class Logger {
  public static final int ERROR = 40000;
  public static final int WARN = 30000;
  public static final int INFO = 20000;
  public static final int DEBUG = 10000;

  public static void initialize(String logPrefix, int maxFileCount) {
    SimpleLogger.initialize(logPrefix, maxFileCount);
  }

  public static void setLevel(int level) {
    SimpleLogger.setLevel(level);
  }

  public static void setLevel(String level) {
    SimpleLogger.setLevel(level);
  }

  public static void debug(String tag, String message) {
    SimpleLogger.debug(tag, message);
  }

  public static void info(String tag, String message) {
    SimpleLogger.info(tag, message);
  }

  public static void warn(String tag, String message) {
    SimpleLogger.warn(tag, message);
  }

  public static void error(String tag, String message) {
    SimpleLogger.error(tag, message);
  }

  public static void error(String tag, String message, Throwable error) {
    SimpleLogger.error(tag, message, error);
  }
}
