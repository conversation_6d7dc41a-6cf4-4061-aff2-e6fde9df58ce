# Ark-Pets Enhanced Frontend

现代化的React Web前端应用，为Ark-Pets Enhanced项目提供用户界面。

## 🚀 技术栈

- **React 18** - 现代化React框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - 企业级UI组件库
- **React Router** - 客户端路由
- **Zustand** - 轻量级状态管理
- **React Query** - 数据获取和缓存
- **Styled Components** - CSS-in-JS样式方案
- **Framer Motion** - 动画库
- **Axios** - HTTP客户端
- **Recharts** - 图表库

## 📁 项目结构

```
src/
├── components/          # 可复用组件
│   ├── Layout.tsx      # 主布局组件
│   └── ProtectedRoute.tsx # 路由保护组件
├── pages/              # 页面组件
│   ├── LoginPage.tsx   # 登录页面
│   ├── DashboardPage.tsx # 仪表盘
│   ├── PetManagementPage.tsx # 宠物管理
│   ├── ChatPage.tsx    # AI聊天
│   └── SettingsPage.tsx # 设置页面
├── hooks/              # 自定义Hooks
│   └── useAuthStore.ts # 认证状态管理
├── services/           # API服务
│   └── api.ts         # API接口封装
├── types/              # TypeScript类型定义
│   └── index.ts       # 通用类型
├── styles/             # 样式文件
│   └── global.css     # 全局样式
├── utils/              # 工具函数
├── assets/             # 静态资源
├── App.tsx            # 应用根组件
└── index.tsx          # 应用入口
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd ark-pets-frontend
npm install
```

### 开发模式

```bash
npm start
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
```

### 代码格式化

```bash
npm run format
```

### 类型检查

```bash
npm run type-check
```

## 🔧 配置说明

### 环境变量

创建 `.env` 文件配置环境变量：

```env
REACT_APP_API_BASE_URL=http://localhost:8080
REACT_APP_VERSION=4.0.0
```

### 代理配置

开发环境下，API请求会自动代理到后端服务：

```json
{
  "proxy": "http://localhost:8080"
}
```

## 📱 功能特性

### 🔐 用户认证
- JWT token认证
- 自动token刷新
- 路由保护
- 登录状态持久化

### 📊 仪表盘
- 宠物状态概览
- 系统监控图表
- 最近活动记录
- 实时数据更新

### 🐾 宠物管理
- 宠物列表展示
- 宠物状态监控
- 互动操作
- 等级经验系统

### 💬 AI聊天
- 多模型支持
- 聊天历史记录
- 实时对话
- 消息持久化

### ⚙️ 设置管理
- 用户偏好设置
- 主题切换
- 通知配置
- 账户管理

## 🎨 UI/UX设计

### 设计原则
- **简洁直观** - 清晰的信息架构
- **响应式设计** - 适配各种设备
- **一致性** - 统一的视觉语言
- **可访问性** - 符合无障碍标准

### 主题配置
- 支持亮色/暗色主题
- 自定义品牌色彩
- 响应式布局
- 动画效果

## 📱 响应式支持

- **桌面端** (>= 1200px) - 完整功能体验
- **平板端** (768px - 1199px) - 优化布局
- **移动端** (< 768px) - 移动优先设计

## 🔄 状态管理

### Zustand Store
- 用户认证状态
- 应用全局状态
- 持久化存储

### React Query
- API数据缓存
- 自动重新获取
- 乐观更新
- 错误处理

## 🚦 路由配置

```typescript
/login          # 登录页面
/dashboard      # 仪表盘 (默认)
/pets          # 宠物管理
/chat          # AI聊天
/settings      # 设置页面
```

## 🔌 API集成

### 认证API
- 用户登录/注册
- Token刷新
- 用户信息管理

### 业务API
- 宠物管理
- AI聊天
- 系统监控
- 配置管理

## 🧪 测试

```bash
npm test
```

## 📦 部署

### 构建优化
- 代码分割
- 懒加载
- 资源压缩
- 缓存策略

### 部署选项
- 静态文件服务器
- CDN部署
- Docker容器
- Nginx代理

## 🔍 性能优化

- React.memo优化渲染
- useMemo/useCallback缓存
- 虚拟滚动
- 图片懒加载
- Bundle分析

## 🐛 问题排查

### 常见问题

1. **API请求失败**
   - 检查后端服务状态
   - 验证API地址配置
   - 查看网络连接

2. **认证失效**
   - 清除浏览器缓存
   - 重新登录
   - 检查token有效期

3. **页面加载慢**
   - 检查网络状况
   - 优化图片资源
   - 启用缓存

## 📄 许可证

MIT License

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

---

**📞 技术支持**: 如有问题请联系开发团队
