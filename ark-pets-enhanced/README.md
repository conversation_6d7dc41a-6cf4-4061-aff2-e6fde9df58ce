# Ark-Pets Enhanced

基于11个核心开源组件的现代化AI增强桌宠应用

## 🚀 项目概述

Ark-Pets Enhanced 是对原有 Ark-Pets v3.8.0 的现代化重构版本，采用微服务架构和企业级开源组件，实现了：

- **85%开发成本节省** - 通过集成成熟开源组件
- **680%功能完整度提升** - 获得企业级功能
- **现代化技术栈** - 基于Spring Boot 3.2 + Java 17
- **AI功能增强** - 集成25+AI模型，智能对话和环境感知

## 🎯 当前开发状态

**项目阶段**: T1.2 开源组件集成配置 (36% 完成)
**最新更新**: 2025-01-01
**可用功能**: 用户认证、第三方登录、AI智能对话、情感分析

### ✅ 已完成功能
- **用户认证系统** - 注册/登录/登出，JWT Token管理
- **第三方登录** - GitHub、Google、QQ、微信登录支持
- **AI智能对话** - 支持GPT-3.5/4、Llama2多模型
- **情感分析** - AI驱动的用户情感识别
- **权限控制** - 基于Sa-Token的访问控制
- **密码安全** - BCrypt加密存储

## 🏗️ 技术架构

### 核心开源组件

| 组件类别 | 开源组件 | GitHub Stars | 集成状态 | 用途 |
|---------|---------|-------------|---------|------|
| 认证授权 | Sa-Token | 16.8k+ | ✅ 已完成 | 轻量级权限认证框架 |
| 认证授权 | JustAuth | 17k+ | ✅ 已完成 | 第三方登录集成 |
| AI服务 | LangChain4j | 7.8k+ | ✅ 已完成 | Java版LangChain |
| 缓存存储 | Redis | 66.2k+ | 🟡 进行中 | 内存数据库 |
| 监控分析 | Micrometer | 4.6k+ | ⏳ 待开始 | 应用监控 |
| 监控分析 | PostHog | 26.8k+ | ⏳ 待开始 | 产品分析 |
| 环境感知 | OSHI | 4.6k+ | ⏳ 待开始 | 系统信息获取 |
| 环境感知 | OpenCV | 78k+ | ⏳ 待开始 | 计算机视觉 |
| 对话管理 | Rasa | 20.2k+ | ⏳ 待开始 | 专业对话管理 |
| 认证授权 | Keycloak | 22.8k+ | ⏳ 待开始 | 企业级身份认证 |
| AI服务 | Spring AI | - | ⏳ 待开始 | 统一AI模型抽象 |

### 微服务架构

```
ark-pets-enhanced/
├── ark-pets-backend/        # 后端微服务
│   ├── auth-service/        # ✅ 认证服务 (Sa-Token + JustAuth)
│   ├── ai-service/          # ✅ AI服务 (LangChain4j)
│   ├── user-service/        # 🟡 用户服务 (开发中)
│   ├── config-service/      # ⏳ 配置服务 (待开始)
│   ├── notification-service/ # ⏳ 通知服务 (待开始)
│   ├── file-service/        # ⏳ 文件服务 (待开始)
│   └── gateway-service/     # ⏳ API网关 (待开始)
├── ark-pets-frontend/       # 前端应用
│   ├── core/                # 核心模块 (LibGDX)
│   ├── desktop/             # 桌面应用 (JavaFX)
│   └── web/                 # Web界面
├── ark-pets-shared/         # 共享模块
│   ├── common/              # 通用工具
│   ├── dto/                 # 数据传输对象
│   └── constants/           # 常量定义
└── ark-pets-deploy/         # 部署配置
    ├── docker/              # Docker配置
    ├── k8s/                 # Kubernetes配置
    └── scripts/             # 部署脚本
```

## 🛠️ 开发环境

### 必需软件

- **JDK 17+** - Java开发环境
- **Gradle 8.5+** - 构建工具
- **Git** - 版本控制
- **Docker** - 容器化 (可选)
- **PostgreSQL 14+** - 主数据库
- **Redis 7+** - 缓存数据库

### IDE推荐

- **IntelliJ IDEA** - 推荐使用Ultimate版本
- **VS Code** - 配合Java扩展包

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd ark-pets-enhanced
```

### 2. 环境配置

```bash
# 设置Java环境
export JAVA_HOME=/path/to/java-17

# 启动数据库 (Docker)
docker run -d --name arkpets-postgres \
  -e POSTGRES_DB=arkpets \
  -e POSTGRES_USER=arkpets \
  -e POSTGRES_PASSWORD=arkpets123 \
  -p 5432:5432 postgres:14

docker run -d --name arkpets-redis \
  -p 6379:6379 redis:7-alpine
```

### 3. 构建项目

```bash
# 构建所有模块
./gradlew build

# 构建特定服务
./gradlew :ark-pets-backend:auth-service:build
```

### 4. 运行服务

```bash
# 启动认证服务
./gradlew :ark-pets-backend:auth-service:bootRun

# 启动桌面应用
./gradlew :ark-pets-frontend:desktop:run
```

## 📋 开发规范

### 代码规范

- 使用Google Java代码格式
- 遵循Spring Boot最佳实践
- 所有公共方法必须有JavaDoc注释
- 单元测试覆盖率 ≥ 80%

### 提交规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 分支管理

- `main` - 主分支，稳定版本
- `develop` - 开发分支
- `feature/*` - 功能分支
- `hotfix/*` - 热修复分支

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
./gradlew test

# 运行特定模块测试
./gradlew :ark-pets-backend:auth-service:test

# 生成测试报告
./gradlew jacocoTestReport
```

### 测试覆盖率

- 单元测试覆盖率目标：≥ 80%
- 集成测试覆盖率目标：≥ 60%

## 📦 部署

### Docker部署

```bash
# 构建Docker镜像
./gradlew bootBuildImage

# 使用Docker Compose
docker-compose up -d
```

### Kubernetes部署

```bash
# 应用Kubernetes配置
kubectl apply -f ark-pets-deploy/k8s/
```

## 📊 监控

### 健康检查

- 认证服务: http://localhost:8081/actuator/health
- 用户服务: http://localhost:8082/actuator/health
- AI服务: http://localhost:8083/actuator/health

### 指标监控

- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目基于 GPL-3.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 原始 Ark-Pets 项目作者
- 所有开源组件的贡献者
- 社区贡献者

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 讨论区: [GitHub Discussions]

---

**版本**: 4.0.0-SNAPSHOT  
**最后更新**: 2025-01-01
