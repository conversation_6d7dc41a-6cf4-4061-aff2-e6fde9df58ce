package cn.harryh.arkpets.constants;

/**
 * 应用程序常量定义 基于原有Const.java重构，适配微服务架构
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public final class AppConstants {

  private AppConstants() {
    // 工具类，禁止实例化
  }

  // 应用版本信息
  public static final String APP_NAME = "Ark-Pets Enhanced";
  public static final String APP_VERSION = "4.0.0-SNAPSHOT";
  public static final int VERSION_MAJOR = 4;
  public static final int VERSION_MINOR = 0;
  public static final int VERSION_PATCH = 0;

  // API相关常量
  public static final String API_VERSION_PREFIX = "/api/v1";
  public static final int DEFAULT_PORT = 8080;
  public static final int GATEWAY_PORT = 8080;
  public static final int AUTH_SERVICE_PORT = 8081;
  public static final int USER_SERVICE_PORT = 8082;
  public static final int AI_SERVICE_PORT = 8083;
  public static final int CONFIG_SERVICE_PORT = 8084;
  public static final int NOTIFICATION_SERVICE_PORT = 8085;
  public static final int FILE_SERVICE_PORT = 8086;

  // 图形相关常量
  public static final int FPS_DEFAULT = 60;
  public static final int CORE_WIDTH_DEFAULT = 150;
  public static final int CORE_HEIGHT_DEFAULT = 150;
  public static final int CANVAS_RESERVE_LENGTH = 20;
  public static final int CANVAS_MAX_SIZE = 4320;
  public static final float SKEL_BASE_SCALE = 0.3f;

  // 行为相关常量
  public static final int BEHAVIOR_BASE_WEIGHT = 320;
  public static final float DROPPED_THRESHOLD = 10f;

  // IO相关常量
  public static final int ZIP_BUFFER_SIZE_DEFAULT = 16 * 1024;
  public static final int HTTP_BUFFER_SIZE_DEFAULT = 16 * 1024;
  public static final int HTTP_TIMEOUT_DEFAULT = 20 * 1000;
  public static final long DISK_FREE_SPACE_RECOMMENDED = 1024 * 1024 * 1024L;

  // 编码相关常量
  public static final String CHARSET_DEFAULT = "UTF-8";

  // 文件路径常量
  public static final String CONFIG_EXTERNAL = "ArkPetsConfig.json";
  public static final String CONFIG_INTERNAL = "/ArkPetsConfigDefault.json";
  public static final String ICON_FILE_PNG = "/icons/icon.png";
  public static final String TEMP_DIR_PATH = "temp/";
  public static final String LOG_DIR = "logs/";

  // Socket相关常量
  public static final String SERVER_HOST = "localhost";
  public static final int[] SERVER_PORTS = {8686, 8866, 8989, 8899, 8800};
  public static final int RECONNECT_DELAY_MILLIS = 5 * 1000;

  // 日志相关常量
  public static final int LOG_CORE_MAX_KEEP = 32;
  public static final int LOG_DESKTOP_MAX_KEEP = 8;
  public static final String LOG_CORE_PATH = LOG_DIR + "core";
  public static final String LOG_DESKTOP_PATH = LOG_DIR + "desktop";

  // 调试相关常量
  public static boolean IS_DEBUG_ENABLED = false;
  public static boolean IS_HTTPS_TRUST_ALL = false;
  public static boolean IS_UPDATE_AVAILABLE = false;
  public static boolean IS_NEWCOMER = false;
}
