package cn.harryh.arkpets.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 配置数据传输对象 基于原有ArkConfig.java重构，适配微服务架构
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class ConfigDTO implements Serializable {

  // 行为配置
  @JsonProperty("behavior_ai_activation")
  @Min(1)
  @Max(10)
  private Integer behaviorAiActivation = 4;

  @JsonProperty("behavior_allow_interact")
  @NotNull
  private Boolean behaviorAllowInteract = true;

  @JsonProperty("behavior_allow_sit")
  @NotNull
  private Boolean behaviorAllowSit = true;

  @JsonProperty("behavior_allow_sleep")
  @NotNull
  private Boolean behaviorAllowSleep = false;

  @JsonProperty("behavior_allow_special")
  @NotNull
  private Boolean behaviorAllowSpecial = true;

  @JsonProperty("behavior_allow_walk")
  @NotNull
  private Boolean behaviorAllowWalk = true;

  @JsonProperty("behavior_do_peer_repulsion")
  @NotNull
  private Boolean behaviorDoPeerRepulsion = true;

  // 画布配置
  @JsonProperty("canvas_color")
  @NotBlank
  private String canvasColor = "#00000000";

  @JsonProperty("canvas_coverage")
  @Min(0)
  @Max(1)
  private Float canvasCoverage = 0.8f;

  @JsonProperty("canvas_sampling_interval")
  @Min(1)
  @Max(10)
  private Integer canvasSamplingInterval = 4;

  // 角色配置
  @JsonProperty("character_asset")
  private String characterAsset;

  @JsonProperty("character_label")
  private String characterLabel;

  // 显示配置
  @JsonProperty("display_fps")
  @Min(1)
  @Max(120)
  private Integer displayFps = 60;

  @JsonProperty("display_margin_bottom")
  @Min(0)
  private Integer displayMarginBottom = 0;

  @JsonProperty("display_multi_monitors")
  @NotNull
  private Boolean displayMultiMonitors = true;

  @JsonProperty("display_scale")
  @Min(0)
  @Max(5)
  private Float displayScale = 1.0f;

  // 节能模式
  @JsonProperty("eco_mode")
  @NotNull
  private Boolean ecoMode = false;

  // 初始位置
  @JsonProperty("initial_position_x")
  @Min(0)
  @Max(1)
  private Float initialPositionX = 0.2f;

  @JsonProperty("initial_position_y")
  @Min(0)
  @Max(1)
  private Float initialPositionY = 0.2f;

  // 启动器配置
  @JsonProperty("launcher_solid_exit")
  @NotNull
  private Boolean launcherSolidExit = true;

  // 日志配置
  @JsonProperty("logging_level")
  @NotBlank
  private String loggingLevel = "INFO";

  // 透明度配置
  @JsonProperty("opacity_dim")
  @Min(0)
  @Max(1)
  private Float opacityDim = 0.75f;

  @JsonProperty("opacity_normal")
  @Min(0)
  @Max(1)
  private Float opacityNormal = 1.0f;

  // 物理配置
  @JsonProperty("physic_gravity_acc")
  @Min(0)
  private Float physicGravityAcc = 800.0f;

  @JsonProperty("physic_air_friction_acc")
  @Min(0)
  private Float physicAirFrictionAcc = 100.0f;

  @JsonProperty("physic_static_friction_acc")
  @Min(0)
  private Float physicStaticFrictionAcc = 500.0f;

  @JsonProperty("physic_speed_limit_x")
  @Min(0)
  private Float physicSpeedLimitX = 1000.0f;

  @JsonProperty("physic_speed_limit_y")
  @Min(0)
  private Float physicSpeedLimitY = 1000.0f;

  // 渲染配置
  @JsonProperty("render_animation_mixture")
  @Min(0)
  @Max(1)
  private Float renderAnimationMixture = 0.3f;

  @JsonProperty("render_enable_angle")
  @NotNull
  private Boolean renderEnableAngle = false;

  @JsonProperty("render_enable_mipmap")
  @NotNull
  private Boolean renderEnableMipmap = true;

  @JsonProperty("render_outline")
  @Min(0)
  @Max(5)
  private Integer renderOutline = 1;

  @JsonProperty("render_outline_color")
  @NotBlank
  private String renderOutlineColor = "#FFFF00FF";

  @JsonProperty("render_outline_width")
  @Min(0)
  private Float renderOutlineWidth = 2.0f;

  @JsonProperty("render_shadow_color")
  @NotBlank
  private String renderShadowColor = "#000000BB";

  // 过渡动画配置
  @JsonProperty("transition_duration")
  @Min(0)
  private Float transitionDuration = 0.3f;

  @JsonProperty("transition_type")
  @NotBlank
  private String transitionType = "EASE_OUT_CUBIC";

  // 窗口样式配置
  @JsonProperty("window_style_toolwindow")
  @NotNull
  private Boolean windowStyleToolwindow = true;

  @JsonProperty("window_style_topmost")
  @NotNull
  private Boolean windowStyleTopmost = true;

  // 构造函数
  public ConfigDTO() {}

  // Getter和Setter方法
  public Integer getBehaviorAiActivation() {
    return behaviorAiActivation;
  }

  public void setBehaviorAiActivation(Integer behaviorAiActivation) {
    this.behaviorAiActivation = behaviorAiActivation;
  }

  public Boolean getBehaviorAllowInteract() {
    return behaviorAllowInteract;
  }

  public void setBehaviorAllowInteract(Boolean behaviorAllowInteract) {
    this.behaviorAllowInteract = behaviorAllowInteract;
  }

  public Boolean getBehaviorAllowSit() {
    return behaviorAllowSit;
  }

  public void setBehaviorAllowSit(Boolean behaviorAllowSit) {
    this.behaviorAllowSit = behaviorAllowSit;
  }

  public Boolean getBehaviorAllowSleep() {
    return behaviorAllowSleep;
  }

  public void setBehaviorAllowSleep(Boolean behaviorAllowSleep) {
    this.behaviorAllowSleep = behaviorAllowSleep;
  }

  public Boolean getBehaviorAllowSpecial() {
    return behaviorAllowSpecial;
  }

  public void setBehaviorAllowSpecial(Boolean behaviorAllowSpecial) {
    this.behaviorAllowSpecial = behaviorAllowSpecial;
  }

  public Boolean getBehaviorAllowWalk() {
    return behaviorAllowWalk;
  }

  public void setBehaviorAllowWalk(Boolean behaviorAllowWalk) {
    this.behaviorAllowWalk = behaviorAllowWalk;
  }

  public Boolean getBehaviorDoPeerRepulsion() {
    return behaviorDoPeerRepulsion;
  }

  public void setBehaviorDoPeerRepulsion(Boolean behaviorDoPeerRepulsion) {
    this.behaviorDoPeerRepulsion = behaviorDoPeerRepulsion;
  }

  public String getCanvasColor() {
    return canvasColor;
  }

  public void setCanvasColor(String canvasColor) {
    this.canvasColor = canvasColor;
  }

  public Float getCanvasCoverage() {
    return canvasCoverage;
  }

  public void setCanvasCoverage(Float canvasCoverage) {
    this.canvasCoverage = canvasCoverage;
  }

  public Integer getCanvasSamplingInterval() {
    return canvasSamplingInterval;
  }

  public void setCanvasSamplingInterval(Integer canvasSamplingInterval) {
    this.canvasSamplingInterval = canvasSamplingInterval;
  }

  public String getCharacterAsset() {
    return characterAsset;
  }

  public void setCharacterAsset(String characterAsset) {
    this.characterAsset = characterAsset;
  }

  public String getCharacterLabel() {
    return characterLabel;
  }

  public void setCharacterLabel(String characterLabel) {
    this.characterLabel = characterLabel;
  }

  public Integer getDisplayFps() {
    return displayFps;
  }

  public void setDisplayFps(Integer displayFps) {
    this.displayFps = displayFps;
  }

  public Integer getDisplayMarginBottom() {
    return displayMarginBottom;
  }

  public void setDisplayMarginBottom(Integer displayMarginBottom) {
    this.displayMarginBottom = displayMarginBottom;
  }

  public Boolean getDisplayMultiMonitors() {
    return displayMultiMonitors;
  }

  public void setDisplayMultiMonitors(Boolean displayMultiMonitors) {
    this.displayMultiMonitors = displayMultiMonitors;
  }

  public Float getDisplayScale() {
    return displayScale;
  }

  public void setDisplayScale(Float displayScale) {
    this.displayScale = displayScale;
  }

  public Boolean getEcoMode() {
    return ecoMode;
  }

  public void setEcoMode(Boolean ecoMode) {
    this.ecoMode = ecoMode;
  }

  public Float getInitialPositionX() {
    return initialPositionX;
  }

  public void setInitialPositionX(Float initialPositionX) {
    this.initialPositionX = initialPositionX;
  }

  public Float getInitialPositionY() {
    return initialPositionY;
  }

  public void setInitialPositionY(Float initialPositionY) {
    this.initialPositionY = initialPositionY;
  }

  public Boolean getLauncherSolidExit() {
    return launcherSolidExit;
  }

  public void setLauncherSolidExit(Boolean launcherSolidExit) {
    this.launcherSolidExit = launcherSolidExit;
  }

  public String getLoggingLevel() {
    return loggingLevel;
  }

  public void setLoggingLevel(String loggingLevel) {
    this.loggingLevel = loggingLevel;
  }

  public Float getOpacityDim() {
    return opacityDim;
  }

  public void setOpacityDim(Float opacityDim) {
    this.opacityDim = opacityDim;
  }

  public Float getOpacityNormal() {
    return opacityNormal;
  }

  public void setOpacityNormal(Float opacityNormal) {
    this.opacityNormal = opacityNormal;
  }

  public Float getPhysicGravityAcc() {
    return physicGravityAcc;
  }

  public void setPhysicGravityAcc(Float physicGravityAcc) {
    this.physicGravityAcc = physicGravityAcc;
  }

  public Float getPhysicAirFrictionAcc() {
    return physicAirFrictionAcc;
  }

  public void setPhysicAirFrictionAcc(Float physicAirFrictionAcc) {
    this.physicAirFrictionAcc = physicAirFrictionAcc;
  }

  public Float getPhysicStaticFrictionAcc() {
    return physicStaticFrictionAcc;
  }

  public void setPhysicStaticFrictionAcc(Float physicStaticFrictionAcc) {
    this.physicStaticFrictionAcc = physicStaticFrictionAcc;
  }

  public Float getPhysicSpeedLimitX() {
    return physicSpeedLimitX;
  }

  public void setPhysicSpeedLimitX(Float physicSpeedLimitX) {
    this.physicSpeedLimitX = physicSpeedLimitX;
  }

  public Float getPhysicSpeedLimitY() {
    return physicSpeedLimitY;
  }

  public void setPhysicSpeedLimitY(Float physicSpeedLimitY) {
    this.physicSpeedLimitY = physicSpeedLimitY;
  }

  public Float getRenderAnimationMixture() {
    return renderAnimationMixture;
  }

  public void setRenderAnimationMixture(Float renderAnimationMixture) {
    this.renderAnimationMixture = renderAnimationMixture;
  }

  public Boolean getRenderEnableAngle() {
    return renderEnableAngle;
  }

  public void setRenderEnableAngle(Boolean renderEnableAngle) {
    this.renderEnableAngle = renderEnableAngle;
  }

  public Boolean getRenderEnableMipmap() {
    return renderEnableMipmap;
  }

  public void setRenderEnableMipmap(Boolean renderEnableMipmap) {
    this.renderEnableMipmap = renderEnableMipmap;
  }

  public Integer getRenderOutline() {
    return renderOutline;
  }

  public void setRenderOutline(Integer renderOutline) {
    this.renderOutline = renderOutline;
  }

  public String getRenderOutlineColor() {
    return renderOutlineColor;
  }

  public void setRenderOutlineColor(String renderOutlineColor) {
    this.renderOutlineColor = renderOutlineColor;
  }

  public Float getRenderOutlineWidth() {
    return renderOutlineWidth;
  }

  public void setRenderOutlineWidth(Float renderOutlineWidth) {
    this.renderOutlineWidth = renderOutlineWidth;
  }

  public String getRenderShadowColor() {
    return renderShadowColor;
  }

  public void setRenderShadowColor(String renderShadowColor) {
    this.renderShadowColor = renderShadowColor;
  }

  public Float getTransitionDuration() {
    return transitionDuration;
  }

  public void setTransitionDuration(Float transitionDuration) {
    this.transitionDuration = transitionDuration;
  }

  public String getTransitionType() {
    return transitionType;
  }

  public void setTransitionType(String transitionType) {
    this.transitionType = transitionType;
  }

  public Boolean getWindowStyleToolwindow() {
    return windowStyleToolwindow;
  }

  public void setWindowStyleToolwindow(Boolean windowStyleToolwindow) {
    this.windowStyleToolwindow = windowStyleToolwindow;
  }

  public Boolean getWindowStyleTopmost() {
    return windowStyleTopmost;
  }

  public void setWindowStyleTopmost(Boolean windowStyleTopmost) {
    this.windowStyleTopmost = windowStyleTopmost;
  }
}
