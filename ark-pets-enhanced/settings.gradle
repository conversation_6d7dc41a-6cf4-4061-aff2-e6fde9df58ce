rootProject.name = 'ark-pets-enhanced'

// 后端微服务模块
include 'ark-pets-backend:auth-service'
include 'ark-pets-backend:user-service'
include 'ark-pets-backend:ai-service'
include 'ark-pets-backend:cache-service'
include 'ark-pets-backend:monitor-service'
include 'ark-pets-backend:pet-service'
include 'ark-pets-backend:notification-service'
include 'ark-pets-backend:data-service'
include 'ark-pets-backend:gateway-service'
include 'ark-pets-backend:config-service'
include 'ark-pets-backend:file-service'

// 前端模块
include 'ark-pets-frontend:core'
include 'ark-pets-frontend:desktop'
include 'ark-pets-frontend:web'

// 共享模块
include 'ark-pets-shared:common'
include 'ark-pets-shared:dto'
include 'ark-pets-shared:constants'

// 设置项目目录
project(':ark-pets-backend:auth-service').projectDir = file('ark-pets-backend/auth-service')
project(':ark-pets-backend:user-service').projectDir = file('ark-pets-backend/user-service')
project(':ark-pets-backend:ai-service').projectDir = file('ark-pets-backend/ai-service')
project(':ark-pets-backend:cache-service').projectDir = file('ark-pets-backend/cache-service')
project(':ark-pets-backend:monitor-service').projectDir = file('ark-pets-backend/monitor-service')
project(':ark-pets-backend:pet-service').projectDir = file('ark-pets-backend/pet-service')
project(':ark-pets-backend:notification-service').projectDir = file('ark-pets-backend/notification-service')
project(':ark-pets-backend:data-service').projectDir = file('ark-pets-backend/data-service')
project(':ark-pets-backend:gateway-service').projectDir = file('ark-pets-backend/gateway-service')
project(':ark-pets-backend:config-service').projectDir = file('ark-pets-backend/config-service')
project(':ark-pets-backend:notification-service').projectDir = file('ark-pets-backend/notification-service')
project(':ark-pets-backend:file-service').projectDir = file('ark-pets-backend/file-service')

project(':ark-pets-frontend:core').projectDir = file('ark-pets-frontend/core')
project(':ark-pets-frontend:desktop').projectDir = file('ark-pets-frontend/desktop')
project(':ark-pets-frontend:web').projectDir = file('ark-pets-frontend/web')

project(':ark-pets-shared:common').projectDir = file('ark-pets-shared/common')
project(':ark-pets-shared:dto').projectDir = file('ark-pets-shared/dto')
project(':ark-pets-shared:constants').projectDir = file('ark-pets-shared/constants')
