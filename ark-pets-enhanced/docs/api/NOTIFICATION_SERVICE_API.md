# 通知服务 API 文档

**服务名称**: ark-pets-notification-service  
**服务端口**: 8087  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

通知服务提供实时通知推送、WebSocket通信、消息管理等功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8087`
- **API 前缀**: `/api/v1/notifications`
- **WebSocket URL**: `ws://localhost:8087/ws/notifications`
- **认证方式**: JWT Bearer Token (通过网关)
- **数据格式**: JSON

---

## 📱 通知管理 API

### 1. 获取用户通知列表
**GET** `/api/v1/notifications`

获取用户的通知列表。

#### 查询参数
- `offset` (int): 偏移量，默认0
- `limit` (int): 每页大小，默认20

#### 响应示例
```json
{
  "success": true,
  "message": "通知列表获取成功",
  "data": [
    {
      "id": "notification-123",
      "title": "宠物升级啦！",
      "content": "恭喜！小橘猫 升级到了 5 级！",
      "type": "PET_LEVEL_UP",
      "priority": "HIGH",
      "isRead": false,
      "createdAt": "2025-01-01T10:00:00",
      "metadata": {
        "petId": "pet-123",
        "petName": "小橘猫",
        "newLevel": "5"
      }
    }
  ],
  "unreadCount": 3,
  "offset": 0,
  "limit": 20,
  "timestamp": 1704067200000
}
```

### 2. 获取未读通知数量
**GET** `/api/v1/notifications/unread-count`

获取用户的未读通知数量。

#### 响应示例
```json
{
  "success": true,
  "message": "未读通知数量获取成功",
  "unreadCount": 5,
  "timestamp": 1704067200000
}
```

### 3. 标记通知为已读
**POST** `/api/v1/notifications/{notificationId}/read`

标记指定通知为已读状态。

#### 路径参数
- `notificationId` (string): 通知ID

#### 响应示例
```json
{
  "success": true,
  "message": "通知已标记为已读",
  "notificationId": "notification-123",
  "timestamp": 1704067200000
}
```

### 4. 发送通知 (内部API)
**POST** `/api/v1/notifications/send`

发送通知给指定用户。

#### 请求体
```json
{
  "userId": "user123",
  "title": "通知标题",
  "content": "通知内容",
  "type": "SYSTEM",
  "priority": "NORMAL",
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "通知发送成功",
  "timestamp": 1704067200000
}
```

### 5. 发送系统通知 (管理员API)
**POST** `/api/v1/notifications/system`

发送系统通知给所有用户。

#### 请求体
```json
{
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-24:00进行维护，请提前保存数据。",
  "priority": "HIGH"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "系统通知发送成功",
  "timestamp": 1704067200000
}
```

### 6. 发送频道消息
**POST** `/api/v1/notifications/channel/{channel}`

向指定频道发送消息。

#### 路径参数
- `channel` (string): 频道名称

#### 请求体
```json
{
  "title": "频道消息标题",
  "content": "频道消息内容",
  "messageType": "info"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "频道消息发送成功",
  "channel": "pet_status_updates",
  "timestamp": 1704067200000
}
```

### 7. 获取在线统计信息
**GET** `/api/v1/notifications/stats`

获取在线用户和会话统计信息。

#### 响应示例
```json
{
  "success": true,
  "message": "在线统计信息获取成功",
  "data": {
    "onlineUsers": 25,
    "totalSessions": 32,
    "timestamp": 1704067200000
  },
  "timestamp": 1704067200000
}
```

---

## 🔌 WebSocket 实时通信

### WebSocket 连接

#### 连接URL
```
ws://localhost:8087/ws/notifications?token=your-jwt-token
```

#### 连接参数
- `token` (string): JWT认证token

#### 连接示例
```javascript
// 原生WebSocket连接
const ws = new WebSocket('ws://localhost:8087/ws/notifications?token=' + jwtToken);

// 使用SockJS连接
const socket = new SockJS('http://localhost:8087/ws/notifications/sockjs');
```

### WebSocket 消息格式

#### 1. 连接成功消息
```json
{
  "type": "connection",
  "status": "connected",
  "message": "WebSocket连接成功",
  "timestamp": 1704067200000
}
```

#### 2. 实时通知消息
```json
{
  "type": "notification",
  "id": "notification-123",
  "title": "宠物需要关注",
  "content": "小橘猫 饥饿值较高，请及时照顾！",
  "notificationType": "PET_NEEDS_ATTENTION",
  "timestamp": 1704067200000,
  "read": false
}
```

#### 3. 系统通知消息
```json
{
  "type": "system_notification",
  "id": "system-456",
  "title": "系统维护通知",
  "content": "系统将于今晚进行维护",
  "priority": "HIGH",
  "timestamp": 1704067200000
}
```

#### 4. 频道消息
```json
{
  "type": "channel_message",
  "channel": "pet_status_updates",
  "messageType": "info",
  "title": "宠物状态更新",
  "content": "您的宠物状态已更新",
  "timestamp": 1704067200000
}
```

#### 5. 未读数量更新
```json
{
  "type": "unread_count",
  "count": 3,
  "timestamp": 1704067200000
}
```

### 客户端发送消息

#### 1. 心跳检测
```json
{
  "type": "ping"
}
```

#### 2. 订阅频道
```json
{
  "type": "subscribe",
  "channel": "pet_status_updates"
}
```

#### 3. 取消订阅
```json
{
  "type": "unsubscribe",
  "channel": "pet_status_updates"
}
```

#### 4. 标记已读
```json
{
  "type": "mark_read",
  "notificationId": "notification-123"
}
```

#### 5. 获取通知列表
```json
{
  "type": "get_notifications",
  "limit": 10,
  "offset": 0
}
```

---

## 📊 通知类型和优先级

### 通知类型 (NotificationType)
- `SYSTEM` - 系统通知
- `PET_STATUS` - 宠物状态
- `PET_LEVEL_UP` - 宠物升级
- `PET_NEEDS_ATTENTION` - 宠物需要关注
- `AI_CHAT` - AI对话
- `USER_ACTION` - 用户操作
- `MAINTENANCE` - 系统维护
- `SECURITY` - 安全提醒
- `PROMOTION` - 推广信息

### 通知优先级 (NotificationPriority)
- `LOW` - 低优先级
- `NORMAL` - 普通优先级
- `HIGH` - 高优先级
- `URGENT` - 紧急优先级

### 频道列表
- `system_notifications` - 系统通知频道
- `pet_status_updates` - 宠物状态更新频道
- `user_action_logs` - 用户操作日志频道

---

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | WebSocket认证失败 | 检查JWT token |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 通知不存在 | 确认通知ID正确 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系技术支持 |

### WebSocket错误消息
```json
{
  "type": "error",
  "message": "认证失败",
  "timestamp": 1704067200000
}
```

---

## 🔧 使用示例

### JavaScript WebSocket客户端
```javascript
class NotificationClient {
  constructor(token) {
    this.token = token;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    this.ws = new WebSocket(`ws://localhost:8087/ws/notifications?token=${this.token}`);
    
    this.ws.onopen = () => {
      console.log('WebSocket连接成功');
      this.reconnectAttempts = 0;
      
      // 订阅频道
      this.subscribe('pet_status_updates');
      
      // 开始心跳
      this.startHeartbeat();
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = () => {
      console.log('WebSocket连接关闭');
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }

  handleMessage(message) {
    switch (message.type) {
      case 'notification':
        this.showNotification(message);
        break;
      case 'system_notification':
        this.showSystemNotification(message);
        break;
      case 'unread_count':
        this.updateUnreadCount(message.count);
        break;
      case 'pong':
        console.log('收到心跳响应');
        break;
    }
  }

  subscribe(channel) {
    this.send({
      type: 'subscribe',
      channel: channel
    });
  }

  markAsRead(notificationId) {
    this.send({
      type: 'mark_read',
      notificationId: notificationId
    });
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  startHeartbeat() {
    setInterval(() => {
      this.send({ type: 'ping' });
    }, 30000); // 30秒心跳
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, 5000 * this.reconnectAttempts);
    }
  }

  showNotification(notification) {
    // 显示通知的逻辑
    console.log('收到通知:', notification);
  }

  updateUnreadCount(count) {
    // 更新未读数量的逻辑
    console.log('未读通知数量:', count);
  }
}

// 使用示例
const client = new NotificationClient('your-jwt-token');
client.connect();
```

### Java 客户端示例
```java
// 发送通知
Map<String, Object> notification = Map.of(
    "userId", "user123",
    "title", "测试通知",
    "content", "这是一条测试通知",
    "type", "SYSTEM"
);

ResponseEntity<Map> response = restTemplate.postForEntity(
    "http://localhost:8087/api/v1/notifications/send",
    notification, Map.class);

// 获取通知列表
ResponseEntity<Map> notifications = restTemplate.getForEntity(
    "http://localhost:8087/api/v1/notifications?limit=10&offset=0",
    Map.class);
```

---

## 📈 监控指标

通知服务暴露以下Prometheus指标：
- `websocket_connections_total`: WebSocket连接总数
- `notifications_sent_total`: 发送通知总数
- `notifications_read_total`: 已读通知总数
- `online_users_current`: 当前在线用户数

访问监控端点：`http://localhost:8087/actuator/prometheus`

---

**📞 技术支持**: 如有问题请联系开发团队
