# 缓存服务 API 文档

**服务名称**: ark-pets-cache-service  
**服务端口**: 8084  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

缓存服务提供分布式缓存、限流控制、分布式锁等功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8084`
- **API 前缀**: `/api/v1/cache`
- **认证方式**: 无需认证（内部服务）
- **数据格式**: JSON

---

## 📚 API 接口列表

### 1. 设置缓存
**POST** `/api/v1/cache/set`

设置缓存键值对。

#### 请求参数
```json
{
  "key": "user:123",
  "value": {"name": "张三", "age": 25},
  "ttl": 3600,
  "category": "user"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "缓存设置成功",
  "key": "user:123",
  "remaining": 95,
  "timestamp": 1704067200000
}
```

### 2. 获取缓存
**GET** `/api/v1/cache/get/{key}`

获取指定键的缓存值。

#### 路径参数
- `key` (string): 缓存键

#### 响应示例
```json
{
  "success": true,
  "message": "缓存获取成功",
  "key": "user:123",
  "value": {"name": "张三", "age": 25},
  "exists": true,
  "remaining": 94,
  "timestamp": 1704067200000
}
```

### 3. 删除缓存
**DELETE** `/api/v1/cache/delete/{key}`

删除指定键的缓存。

#### 路径参数
- `key` (string): 缓存键

#### 响应示例
```json
{
  "success": true,
  "message": "缓存删除成功",
  "key": "user:123",
  "remaining": 93,
  "timestamp": 1704067200000
}
```

### 4. 检查缓存存在
**GET** `/api/v1/cache/exists/{key}`

检查指定键的缓存是否存在。

#### 路径参数
- `key` (string): 缓存键

#### 响应示例
```json
{
  "success": true,
  "message": "缓存检查完成",
  "key": "user:123",
  "exists": false,
  "timestamp": 1704067200000
}
```

### 5. 设置过期时间
**POST** `/api/v1/cache/expire/{key}`

设置缓存的过期时间。

#### 路径参数
- `key` (string): 缓存键

#### 查询参数
- `ttl` (long): 过期时间（秒）

#### 响应示例
```json
{
  "success": true,
  "message": "过期时间设置成功",
  "key": "user:123",
  "ttl": 1800,
  "timestamp": 1704067200000
}
```

### 6. 搜索缓存键
**GET** `/api/v1/cache/keys`

根据模式搜索缓存键。

#### 查询参数
- `pattern` (string): 搜索模式（支持通配符*）

#### 响应示例
```json
{
  "success": true,
  "message": "键搜索完成",
  "value": ["user:123", "user:456", "user:789"],
  "count": 3,
  "timestamp": 1704067200000
}
```

### 7. 清空缓存
**DELETE** `/api/v1/cache/clear`

根据模式清空缓存。

#### 查询参数
- `pattern` (string): 清空模式（支持通配符*）

#### 响应示例
```json
{
  "success": true,
  "message": "缓存清理完成",
  "count": 5,
  "timestamp": 1704067200000
}
```

### 8. 获取统计信息
**GET** `/api/v1/cache/stats`

获取缓存系统统计信息。

#### 响应示例
```json
{
  "success": true,
  "message": "统计信息获取成功",
  "value": {
    "connected_clients": "2",
    "used_memory_human": "1.2M",
    "keyspace_hits": "1000",
    "keyspace_misses": "50",
    "total_commands_processed": "5000"
  },
  "timestamp": 1704067200000
}
```

### 9. 健康检查
**GET** `/api/v1/cache/health`

检查缓存服务健康状态。

#### 响应示例
```json
{
  "status": "UP",
  "service": "cache-service",
  "timestamp": 1704067200000,
  "redis": "connected"
}
```

---

## 🚦 限流说明

缓存服务实现了API限流功能：

### 限流规则
- `/api/v1/cache/set`: 每分钟200次
- `/api/v1/cache/get`: 每分钟500次
- `/api/v1/cache/delete`: 每分钟100次
- 其他接口: 每分钟100次

### 限流响应
当触发限流时，返回HTTP 429状态码：
```json
{
  "success": false,
  "message": "请求频率过高，请稍后重试",
  "remaining": 0
}
```

---

## 📊 缓存策略

### 缓存类型
1. **AI响应缓存**: TTL 1小时，本地缓存启用
2. **用户会话缓存**: TTL 30分钟，仅分布式缓存
3. **用户状态缓存**: TTL 5分钟，本地缓存启用
4. **API限流缓存**: TTL 1分钟，仅分布式缓存

### 缓存键命名规范
- 用户相关: `user:{userId}:{type}`
- AI相关: `ai:{model}:{hash}`
- 会话相关: `session:{sessionId}`
- 限流相关: `rate_limit:{identifier}:{api}`

---

## ⚠️ 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 429 | 请求频率过高 | 降低请求频率 |
| 500 | 服务内部错误 | 检查Redis连接状态 |
| 503 | 服务不可用 | 检查服务健康状态 |

---

## 🔧 使用示例

### Java 客户端示例
```java
// 设置缓存
CacheRequest request = new CacheRequest("user:123", userData, 3600L);
ResponseEntity<CacheResponse> response = restTemplate.postForEntity(
    "http://localhost:8084/api/v1/cache/set", request, CacheResponse.class);

// 获取缓存
ResponseEntity<CacheResponse> response = restTemplate.getForEntity(
    "http://localhost:8084/api/v1/cache/get/user:123", CacheResponse.class);
```

### curl 示例
```bash
# 设置缓存
curl -X POST http://localhost:8084/api/v1/cache/set \
  -H "Content-Type: application/json" \
  -d '{"key":"test:key","value":"test value","ttl":3600}'

# 获取缓存
curl http://localhost:8084/api/v1/cache/get/test:key

# 删除缓存
curl -X DELETE http://localhost:8084/api/v1/cache/delete/test:key
```

---

**📞 技术支持**: 如有问题请联系开发团队
