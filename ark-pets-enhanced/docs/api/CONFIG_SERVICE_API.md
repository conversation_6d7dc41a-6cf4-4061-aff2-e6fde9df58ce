# 配置服务 API 文档

**服务名称**: ark-pets-config-service  
**服务端口**: 8086  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

配置服务提供动态配置管理和服务发现功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8086`
- **API 前缀**: `/api/v1/config` 和 `/api/v1/discovery`
- **认证方式**: Basic Auth (admin/ark-pets-config-2024)
- **数据格式**: JSON

---

## 📚 配置管理 API

### 1. 获取应用配置
**GET** `/api/v1/config/{application}/{profile}`

获取指定应用和环境的配置。

#### 路径参数
- `application` (string): 应用名称
- `profile` (string): 环境配置（development/testing/staging/production）

#### 查询参数
- `label` (string): 配置标签，默认为 "main"

#### 响应示例
```json
{
  "success": true,
  "message": "配置获取成功",
  "application": "ark-pets-auth-service",
  "profile": "development",
  "label": "main",
  "config": {
    "server.port": 8081,
    "spring.application.name": "ark-pets-auth-service",
    "spring.profiles.active": "development",
    "jwt.secret": "ark-pets-auth-secret-2024",
    "jwt.expiration": 86400,
    "management.endpoints.web.exposure.include": "*",
    "logging.level.cn.harryh.arkpets": "DEBUG"
  },
  "timestamp": 1704067200000
}
```

### 2. 更新应用配置
**POST** `/api/v1/config/{application}/{profile}`

更新指定应用和环境的配置。

#### 路径参数
- `application` (string): 应用名称
- `profile` (string): 环境配置

#### 查询参数
- `label` (string): 配置标签，默认为 "main"

#### 请求体
```json
{
  "server.port": 8081,
  "jwt.secret": "new-secret-key",
  "jwt.expiration": 7200,
  "custom.feature.enabled": true
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "配置更新成功",
  "application": "ark-pets-auth-service",
  "profile": "development",
  "label": "main",
  "timestamp": 1704067200000
}
```

### 3. 删除应用配置
**DELETE** `/api/v1/config/{application}/{profile}`

删除指定应用和环境的配置。

#### 路径参数
- `application` (string): 应用名称
- `profile` (string): 环境配置

#### 查询参数
- `label` (string): 配置标签，默认为 "main"

#### 响应示例
```json
{
  "success": true,
  "message": "配置删除成功",
  "application": "ark-pets-auth-service",
  "profile": "development",
  "label": "main",
  "timestamp": 1704067200000
}
```

### 4. 获取配置历史
**GET** `/api/v1/config/{application}/{profile}/history`

获取配置变更历史记录。

#### 路径参数
- `application` (string): 应用名称
- `profile` (string): 环境配置

#### 查询参数
- `label` (string): 配置标签，默认为 "main"

#### 响应示例
```json
{
  "success": true,
  "message": "配置历史获取成功",
  "application": "ark-pets-auth-service",
  "profile": "development",
  "label": "main",
  "history": [
    {
      "timestamp": "2025-01-01T10:00:00",
      "action": "UPDATE",
      "config": {
        "jwt.secret": "new-secret-key"
      }
    },
    {
      "timestamp": "2025-01-01T09:00:00",
      "action": "UPDATE",
      "config": {
        "server.port": 8081
      }
    }
  ],
  "count": 2,
  "timestamp": 1704067200000
}
```

### 5. 刷新配置缓存
**POST** `/api/v1/config/{application}/{profile}/refresh`

刷新指定应用的配置缓存。

#### 路径参数
- `application` (string): 应用名称
- `profile` (string): 环境配置

#### 查询参数
- `label` (string): 配置标签，默认为 "main"

#### 响应示例
```json
{
  "success": true,
  "message": "配置刷新成功",
  "application": "ark-pets-auth-service",
  "profile": "development",
  "label": "main",
  "timestamp": 1704067200000
}
```

### 6. 获取所有应用列表
**GET** `/api/v1/config/applications`

获取所有已配置的应用列表。

#### 响应示例
```json
{
  "success": true,
  "message": "应用列表获取成功",
  "applications": [
    "ark-pets-auth-service",
    "ark-pets-ai-service",
    "ark-pets-cache-service",
    "ark-pets-monitor-service"
  ],
  "count": 4,
  "timestamp": 1704067200000
}
```

### 7. 验证配置
**POST** `/api/v1/config/{application}/{profile}/validate`

验证配置的格式和内容。

#### 路径参数
- `application` (string): 应用名称
- `profile` (string): 环境配置

#### 查询参数
- `label` (string): 配置标签，默认为 "main"

#### 请求体
```json
{
  "server.port": 8081,
  "jwt.secret": "test-secret",
  "invalid.config": null
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "配置验证完成",
  "application": "ark-pets-auth-service",
  "profile": "development",
  "label": "main",
  "validation": {
    "valid": true,
    "errors": [],
    "warnings": ["建议使用更强的JWT密钥"]
  },
  "timestamp": 1704067200000
}
```

### 8. 配置服务健康检查
**GET** `/api/v1/config/health`

检查配置服务健康状态。

#### 响应示例
```json
{
  "status": "UP",
  "service": "config-service",
  "timestamp": 1704067200000,
  "redis": "connected"
}
```

---

## 🔍 服务发现 API

### 1. 注册服务
**POST** `/api/v1/discovery/register`

注册服务实例。

#### 查询参数
- `serviceName` (string): 服务名称
- `serviceId` (string): 服务实例ID
- `host` (string): 服务主机
- `port` (int): 服务端口

#### 请求体（可选）
```json
{
  "version": "4.0.0",
  "environment": "development",
  "tags": ["auth", "security"]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "服务注册成功",
  "serviceName": "ark-pets-auth-service",
  "serviceId": "auth-001",
  "endpoint": "localhost:8081",
  "timestamp": 1704067200000
}
```

### 2. 注销服务
**POST** `/api/v1/discovery/deregister`

注销服务实例。

#### 查询参数
- `serviceName` (string): 服务名称
- `serviceId` (string): 服务实例ID

#### 响应示例
```json
{
  "success": true,
  "message": "服务注销成功",
  "serviceName": "ark-pets-auth-service",
  "serviceId": "auth-001",
  "timestamp": 1704067200000
}
```

### 3. 发现服务实例
**GET** `/api/v1/discovery/services/{serviceName}`

发现指定服务的所有实例。

#### 路径参数
- `serviceName` (string): 服务名称

#### 响应示例
```json
{
  "success": true,
  "message": "服务发现成功",
  "serviceName": "ark-pets-auth-service",
  "services": [
    {
      "serviceName": "ark-pets-auth-service",
      "serviceId": "auth-001",
      "host": "localhost",
      "port": 8081,
      "metadata": {
        "version": "4.0.0",
        "environment": "development"
      },
      "registrationTime": "2025-01-01T10:00:00",
      "lastHeartbeat": "2025-01-01T10:05:00",
      "status": "UP",
      "healthy": true
    }
  ],
  "count": 1,
  "timestamp": 1704067200000
}
```

### 4. 获取健康服务实例
**GET** `/api/v1/discovery/services/{serviceName}/healthy`

获取指定服务的健康实例。

#### 路径参数
- `serviceName` (string): 服务名称

#### 响应示例
```json
{
  "success": true,
  "message": "健康服务获取成功",
  "serviceName": "ark-pets-auth-service",
  "services": [
    {
      "serviceName": "ark-pets-auth-service",
      "serviceId": "auth-001",
      "host": "localhost",
      "port": 8081,
      "healthy": true
    }
  ],
  "count": 1,
  "timestamp": 1704067200000
}
```

### 5. 服务心跳
**POST** `/api/v1/discovery/heartbeat`

发送服务心跳。

#### 查询参数
- `serviceName` (string): 服务名称
- `serviceId` (string): 服务实例ID

#### 响应示例
```json
{
  "success": true,
  "message": "心跳成功",
  "serviceName": "ark-pets-auth-service",
  "serviceId": "auth-001",
  "timestamp": 1704067200000
}
```

### 6. 检查服务健康状态
**GET** `/api/v1/discovery/services/{serviceName}/{serviceId}/health`

检查特定服务实例的健康状态。

#### 路径参数
- `serviceName` (string): 服务名称
- `serviceId` (string): 服务实例ID

#### 响应示例
```json
{
  "success": true,
  "message": "健康检查完成",
  "serviceName": "ark-pets-auth-service",
  "serviceId": "auth-001",
  "healthy": true,
  "status": "UP",
  "timestamp": 1704067200000
}
```

### 7. 获取所有注册服务
**GET** `/api/v1/discovery/services`

获取所有注册的服务。

#### 响应示例
```json
{
  "success": true,
  "message": "服务列表获取成功",
  "services": {
    "ark-pets-auth-service": [
      {
        "serviceId": "auth-001",
        "host": "localhost",
        "port": 8081,
        "healthy": true
      }
    ],
    "ark-pets-ai-service": [
      {
        "serviceId": "ai-001",
        "host": "localhost",
        "port": 8082,
        "healthy": true
      }
    ]
  },
  "serviceTypes": 2,
  "timestamp": 1704067200000
}
```

### 8. 获取服务统计信息
**GET** `/api/v1/discovery/stats`

获取服务发现统计信息。

#### 响应示例
```json
{
  "success": true,
  "message": "服务统计获取成功",
  "stats": {
    "totalServiceTypes": 4,
    "totalServiceInstances": 5,
    "healthyInstances": 4,
    "unhealthyInstances": 1,
    "healthRate": 80.0,
    "timestamp": "2025-01-01T10:00:00"
  },
  "timestamp": 1704067200000
}
```

---

## 🔧 使用示例

### Java 客户端示例
```java
// 获取配置
ResponseEntity<Map> response = restTemplate.getForEntity(
    "http://localhost:8086/api/v1/config/ark-pets-auth-service/development", Map.class);

// 注册服务
Map<String, String> metadata = Map.of("version", "4.0.0");
restTemplate.postForEntity(
    "http://localhost:8086/api/v1/discovery/register?serviceName=test-service&serviceId=test-001&host=localhost&port=8080",
    metadata, Map.class);
```

### curl 示例
```bash
# 获取配置
curl -u admin:ark-pets-config-2024 \
  http://localhost:8086/api/v1/config/ark-pets-auth-service/development

# 注册服务
curl -X POST -u admin:ark-pets-config-2024 \
  "http://localhost:8086/api/v1/discovery/register?serviceName=test-service&serviceId=test-001&host=localhost&port=8080" \
  -H "Content-Type: application/json" \
  -d '{"version":"4.0.0"}'
```

---

**📞 技术支持**: 如有问题请联系开发团队
