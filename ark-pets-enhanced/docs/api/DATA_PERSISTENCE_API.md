# 数据持久化增强 API 文档

**服务名称**: ark-pets-data-service  
**服务端口**: 8088  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

数据持久化增强服务提供数据备份恢复、数据导出和数据库管理等功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8088`
- **API 前缀**: `/api/v1/data`
- **认证方式**: JWT Bearer <PERSON>ken (通过网关)
- **数据格式**: JSON

---

## 💾 数据备份 API

### 1. 创建数据备份
**POST** `/api/v1/data/backup`

创建数据库备份任务。

#### 请求体
```json
{
  "name": "daily_backup_20250101",
  "description": "每日自动备份",
  "type": "FULL",
  "createdBy": "admin",
  "retentionDays": 30
}
```

#### 备份类型
- `FULL` - 全量备份
- `INCREMENTAL` - 增量备份
- `DIFFERENTIAL` - 差异备份
- `SCHEMA_ONLY` - 仅结构
- `DATA_ONLY` - 仅数据

#### 响应示例
```json
{
  "success": true,
  "message": "备份任务已启动",
  "backupId": "backup-123",
  "status": "PENDING",
  "timestamp": 1704067200000
}
```

### 2. 恢复数据备份
**POST** `/api/v1/data/backup/{backupId}/restore`

恢复指定的数据备份。

#### 路径参数
- `backupId` (string): 备份ID

#### 请求体
```json
{
  "confirmRestore": true,
  "targetDatabase": "ark_pets_db"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "恢复任务已启动",
  "backupId": "backup-123",
  "timestamp": 1704067200000
}
```

---

## 📊 数据导出 API

### 3. 导出表数据
**POST** `/api/v1/data/export/table`

导出指定表的数据。

#### 请求体
```json
{
  "name": "users_export",
  "tableName": "users",
  "format": "CSV",
  "createdBy": "admin",
  "parameters": {
    "limit": "10000",
    "orderBy": "created_at DESC"
  }
}
```

#### 导出格式
- `CSV` - CSV格式
- `EXCEL` - Excel格式
- `JSON` - JSON格式
- `XML` - XML格式
- `PDF` - PDF格式

#### 响应示例
```json
{
  "success": true,
  "message": "导出任务已启动",
  "exportId": "export-456",
  "status": "PENDING",
  "timestamp": 1704067200000
}
```

### 4. 导出查询结果
**POST** `/api/v1/data/export/query`

导出自定义SQL查询的结果。

#### 请求体
```json
{
  "name": "user_activity_report",
  "querySql": "SELECT user_id, COUNT(*) as action_count FROM user_behavior_events WHERE created_at >= CURRENT_DATE - INTERVAL '30 days' GROUP BY user_id ORDER BY action_count DESC LIMIT 100",
  "format": "EXCEL",
  "createdBy": "admin"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "导出任务已启动",
  "exportId": "export-789",
  "status": "PENDING",
  "timestamp": 1704067200000
}
```

### 5. 导出报表数据
**POST** `/api/v1/data/export/report`

导出预定义的报表数据。

#### 请求体
```json
{
  "name": "monthly_report",
  "format": "EXCEL",
  "createdBy": "admin",
  "parameters": {
    "reportType": "user_activity",
    "startDate": "2025-01-01",
    "endDate": "2025-01-31"
  }
}
```

#### 报表类型
- `user_activity` - 用户活动报表
- `pet_statistics` - 宠物统计报表
- `system_performance` - 系统性能报表

#### 响应示例
```json
{
  "success": true,
  "message": "报表导出任务已启动",
  "exportId": "export-101",
  "status": "PENDING",
  "timestamp": 1704067200000
}
```

### 6. 下载导出文件
**GET** `/api/v1/data/export/{exportId}/download`

下载导出的文件。

#### 路径参数
- `exportId` (string): 导出ID

#### 响应
返回文件流，浏览器会自动下载文件。

---

## 📈 数据库管理 API

### 7. 获取数据库统计信息
**GET** `/api/v1/data/statistics`

获取数据库的统计信息。

#### 响应示例
```json
{
  "success": true,
  "message": "数据库统计信息获取成功",
  "data": {
    "totalTables": 8,
    "totalRecords": 50000,
    "databaseSize": "256MB",
    "lastBackup": "2025-01-01 10:00:00",
    "backupCount": 15,
    "exportCount": 25
  },
  "timestamp": 1704067200000
}
```

### 8. 清理过期文件
**POST** `/api/v1/data/cleanup`

清理过期的备份和导出文件。

#### 响应示例
```json
{
  "success": true,
  "message": "过期文件清理完成",
  "timestamp": 1704067200000
}
```

---

## 📋 任务状态和类型

### 备份状态
- `PENDING` - 等待中
- `RUNNING` - 进行中
- `COMPLETED` - 已完成
- `FAILED` - 失败
- `CANCELLED` - 已取消
- `EXPIRED` - 已过期

### 导出状态
- `PENDING` - 等待中
- `RUNNING` - 进行中
- `COMPLETED` - 已完成
- `FAILED` - 失败
- `CANCELLED` - 已取消
- `EXPIRED` - 已过期

### 备份类型详解
- **全量备份 (FULL)**: 备份所有数据和结构
- **增量备份 (INCREMENTAL)**: 只备份自上次备份以来的变更
- **差异备份 (DIFFERENTIAL)**: 备份自上次全量备份以来的变更
- **仅结构 (SCHEMA_ONLY)**: 只备份数据库结构
- **仅数据 (DATA_ONLY)**: 只备份数据内容

---

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查JWT token |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 确认资源ID正确 |
| 413 | 文件过大 | 减少导出数据量 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 业务错误示例
```json
{
  "success": false,
  "message": "备份文件校验和不匹配，文件可能已损坏",
  "errorCode": "BACKUP_CORRUPTED",
  "timestamp": 1704067200000
}
```

---

## 🔧 使用示例

### JavaScript 客户端示例
```javascript
// 创建全量备份
const createBackup = async () => {
  const response = await fetch('/api/v1/data/backup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      name: 'daily_backup_' + new Date().toISOString().split('T')[0],
      description: '每日自动备份',
      type: 'FULL',
      createdBy: 'admin',
      retentionDays: 30
    })
  });
  return response.json();
};

// 导出表数据
const exportTable = async (tableName) => {
  const response = await fetch('/api/v1/data/export/table', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      name: `${tableName}_export`,
      tableName: tableName,
      format: 'CSV',
      createdBy: 'admin'
    })
  });
  return response.json();
};

// 下载导出文件
const downloadExport = (exportId) => {
  window.open(`/api/v1/data/export/${exportId}/download`);
};
```

### Java 客户端示例
```java
// 创建备份
Map<String, Object> backupRequest = Map.of(
    "name", "daily_backup_" + LocalDate.now(),
    "description", "每日自动备份",
    "type", "FULL",
    "createdBy", "admin",
    "retentionDays", 30
);

ResponseEntity<Map> response = restTemplate.postForEntity(
    "http://localhost:8088/api/v1/data/backup",
    backupRequest, Map.class);

// 导出查询结果
Map<String, Object> exportRequest = Map.of(
    "name", "user_report",
    "querySql", "SELECT * FROM users WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'",
    "format", "EXCEL",
    "createdBy", "admin"
);

ResponseEntity<Map> exportResponse = restTemplate.postForEntity(
    "http://localhost:8088/api/v1/data/export/query",
    exportRequest, Map.class);
```

---

## 📊 配置说明

### 备份配置
```yaml
data-service:
  backup:
    directory: ./data/backups
    compression: true
    retention-days: 30
    auto-backup:
      enabled: true
      daily-time: "02:00"
      weekly-full: true
```

### 导出配置
```yaml
data-service:
  export:
    directory: ./data/exports
    max-records: 100000
    retention-days: 7
    limits:
      max-file-size: 500
      max-concurrent-exports: 5
```

---

## 📈 监控指标

数据服务暴露以下Prometheus指标：
- `backup_tasks_total`: 备份任务总数
- `backup_success_total`: 备份成功总数
- `backup_failure_total`: 备份失败总数
- `export_tasks_total`: 导出任务总数
- `export_success_total`: 导出成功总数
- `database_size_bytes`: 数据库大小

访问监控端点：`http://localhost:8088/actuator/prometheus`

---

**📞 技术支持**: 如有问题请联系开发团队
