# 宠物服务 API 文档

**服务名称**: ark-pets-pet-service  
**服务端口**: 8083  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

宠物服务提供智能宠物管理、AI交互、行为分析等核心功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8083`
- **API 前缀**: `/api/v1/pets`
- **认证方式**: JWT Bearer <PERSON> (通过网关)
- **数据格式**: JSON

---

## 🐾 宠物管理 API

### 1. 创建宠物
**POST** `/api/v1/pets`

创建新的宠物。

#### 请求体
```json
{
  "name": "小橘猫",
  "type": "CAT",
  "breed": "橘猫",
  "age": 2,
  "gender": "MALE",
  "color": "橘色",
  "personality": ["活泼", "友好", "贪吃"]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "宠物创建成功",
  "data": {
    "id": "pet-123",
    "name": "小橘猫",
    "type": "CAT",
    "breed": "橘猫",
    "age": 2,
    "gender": "MALE",
    "color": "橘色",
    "personality": ["活泼", "友好", "贪吃"],
    "status": "ACTIVE",
    "mood": "HAPPY",
    "energy": 100,
    "hunger": 20,
    "health": 100,
    "experience": 0,
    "level": 1,
    "createdAt": "2025-01-01T10:00:00",
    "updatedAt": "2025-01-01T10:00:00"
  },
  "timestamp": 1704067200000
}
```

### 2. 获取宠物列表
**GET** `/api/v1/pets`

获取用户的宠物列表。

#### 查询参数
- `page` (int): 页码，默认0
- `size` (int): 每页大小，默认10，设为0获取全部

#### 响应示例
```json
{
  "success": true,
  "message": "宠物列表获取成功",
  "data": [
    {
      "id": "pet-123",
      "name": "小橘猫",
      "type": "CAT",
      "status": "ACTIVE",
      "mood": "HAPPY",
      "energy": 85,
      "hunger": 30,
      "health": 95,
      "level": 3,
      "experience": 250
    }
  ],
  "total": 1,
  "page": 0,
  "size": 10,
  "totalPages": 1,
  "timestamp": 1704067200000
}
```

### 3. 获取宠物详情
**GET** `/api/v1/pets/{petId}`

获取指定宠物的详细信息。

#### 路径参数
- `petId` (string): 宠物ID

#### 响应示例
```json
{
  "success": true,
  "message": "宠物详情获取成功",
  "data": {
    "id": "pet-123",
    "name": "小橘猫",
    "type": "CAT",
    "breed": "橘猫",
    "age": 2,
    "gender": "MALE",
    "color": "橘色",
    "personality": ["活泼", "友好", "贪吃"],
    "status": "ACTIVE",
    "mood": "HAPPY",
    "energy": 85,
    "hunger": 30,
    "health": 95,
    "experience": 250,
    "level": 3,
    "lastInteraction": "2025-01-01T09:30:00",
    "lastFeed": "2025-01-01T08:00:00",
    "lastPlay": "2025-01-01T09:00:00",
    "createdAt": "2025-01-01T08:00:00",
    "updatedAt": "2025-01-01T09:30:00"
  },
  "timestamp": 1704067200000
}
```

### 4. 更新宠物信息
**PUT** `/api/v1/pets/{petId}`

更新宠物的基本信息。

#### 路径参数
- `petId` (string): 宠物ID

#### 请求体
```json
{
  "name": "小橘猫咪",
  "breed": "英短橘猫",
  "age": 3,
  "personality": ["活泼", "友好", "贪吃", "聪明"]
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "宠物信息更新成功",
  "data": {
    "id": "pet-123",
    "name": "小橘猫咪",
    "breed": "英短橘猫",
    "age": 3,
    "personality": ["活泼", "友好", "贪吃", "聪明"]
  },
  "timestamp": 1704067200000
}
```

### 5. 删除宠物
**DELETE** `/api/v1/pets/{petId}`

删除指定的宠物。

#### 路径参数
- `petId` (string): 宠物ID

#### 响应示例
```json
{
  "success": true,
  "message": "宠物删除成功",
  "timestamp": 1704067200000
}
```

---

## 🎮 宠物互动 API

### 6. 与宠物互动
**POST** `/api/v1/pets/{petId}/interact`

与宠物进行各种互动。

#### 路径参数
- `petId` (string): 宠物ID

#### 请求体
```json
{
  "action": "FEED"
}
```

#### 支持的互动类型
- `FEED` - 喂食
- `PLAY` - 玩耍
- `SLEEP` - 睡觉
- `EXERCISE` - 运动
- `TREAT` - 治疗
- `CLEAN` - 清洁
- `TRAIN` - 训练
- `PET` - 抚摸

#### 响应示例
```json
{
  "success": true,
  "message": "互动成功",
  "data": {
    "id": "pet-123",
    "name": "小橘猫",
    "energy": 85,
    "hunger": 0,
    "health": 100,
    "mood": "HAPPY",
    "experience": 260,
    "level": 3
  },
  "action": "FEED",
  "timestamp": 1704067200000
}
```

### 7. 与宠物对话
**POST** `/api/v1/pets/{petId}/chat`

与宠物进行AI对话。

#### 路径参数
- `petId` (string): 宠物ID

#### 请求体
```json
{
  "message": "你好，小橘猫！"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "对话成功",
  "userMessage": "你好，小橘猫！",
  "petResponse": "😊 喵~ 我很开心能和你聊天！",
  "timestamp": 1704067200000
}
```

---

## 🧠 AI分析 API

### 8. 获取宠物AI分析
**GET** `/api/v1/pets/{petId}/analysis`

获取宠物的AI智能分析报告。

#### 路径参数
- `petId` (string): 宠物ID

#### 响应示例
```json
{
  "success": true,
  "message": "AI分析获取成功",
  "data": {
    "petId": "pet-123",
    "analysisTime": "2025-01-01T10:00:00",
    "healthScore": 85.5,
    "alerts": [
      {
        "title": "有些饥饿",
        "message": "建议准备食物",
        "level": "WARNING"
      }
    ],
    "recommendations": [
      "🍽️ 立即喂食 - 宠物饥饿值较高",
      "🎾 进行游戏 - 宠物精力充沛"
    ],
    "behaviorPattern": {
      "FEED": 3,
      "PLAY": 2,
      "SLEEP": 1
    },
    "predictions": {
      "nextFeedTime": "2025-01-01T14:00:00",
      "energyDeclineRate": 2.0,
      "expToNextLevel": 40,
      "healthTrend": "良好"
    },
    "emotionalAnalysis": {
      "currentMood": "HAPPY",
      "moodStability": 75.0,
      "emotionalNeeds": []
    }
  },
  "timestamp": 1704067200000
}
```

### 9. 获取需要关注的宠物
**GET** `/api/v1/pets/attention`

获取需要特别关注的宠物列表。

#### 响应示例
```json
{
  "success": true,
  "message": "需要关注的宠物获取成功",
  "data": [
    {
      "id": "pet-456",
      "name": "小白狗",
      "type": "DOG",
      "energy": 15,
      "hunger": 85,
      "health": 25,
      "mood": "SAD",
      "lastInteraction": "2025-01-01T06:00:00"
    }
  ],
  "count": 1,
  "timestamp": 1704067200000
}
```

---

## 📊 宠物类型和状态

### 宠物类型 (PetType)
- `CAT` - 猫
- `DOG` - 狗
- `BIRD` - 鸟
- `FISH` - 鱼
- `RABBIT` - 兔子
- `HAMSTER` - 仓鼠
- `OTHER` - 其他

### 宠物状态 (PetStatus)
- `ACTIVE` - 活跃
- `SLEEPING` - 睡觉
- `PLAYING` - 玩耍
- `EATING` - 进食
- `SICK` - 生病
- `INACTIVE` - 不活跃

### 宠物心情 (Mood)
- `HAPPY` - 开心
- `SAD` - 伤心
- `EXCITED` - 兴奋
- `CALM` - 平静
- `ANGRY` - 愤怒
- `TIRED` - 疲惫
- `PLAYFUL` - 顽皮

### 性别 (Gender)
- `MALE` - 雄性
- `FEMALE` - 雌性
- `UNKNOWN` - 未知

---

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查JWT token |
| 404 | 宠物不存在 | 确认宠物ID正确 |
| 409 | 宠物名称冲突 | 使用不同的宠物名称 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误响应格式
```json
{
  "success": false,
  "message": "宠物不存在: pet-999",
  "timestamp": 1704067200000
}
```

---

## 🔧 使用示例

### Java 客户端示例
```java
// 创建宠物
Pet pet = new Pet("小橘猫", Pet.PetType.CAT, userId);
ResponseEntity<Map> response = restTemplate.postForEntity(
    "http://localhost:8083/api/v1/pets", pet, Map.class);

// 与宠物互动
Map<String, String> interaction = Map.of("action", "FEED");
restTemplate.postForEntity(
    "http://localhost:8083/api/v1/pets/pet-123/interact", 
    interaction, Map.class);

// 获取AI分析
ResponseEntity<Map> analysis = restTemplate.getForEntity(
    "http://localhost:8083/api/v1/pets/pet-123/analysis", Map.class);
```

### curl 示例
```bash
# 创建宠物
curl -X POST http://localhost:8083/api/v1/pets \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -d '{"name":"小橘猫","type":"CAT","breed":"橘猫"}'

# 喂食宠物
curl -X POST http://localhost:8083/api/v1/pets/pet-123/interact \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -d '{"action":"FEED"}'

# 与宠物对话
curl -X POST http://localhost:8083/api/v1/pets/pet-123/chat \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -d '{"message":"你好！"}'
```

---

## 📈 监控指标

宠物服务暴露以下Prometheus指标：
- `pet_total`: 宠物总数
- `pet_interactions_total`: 互动总次数
- `pet_ai_analysis_duration`: AI分析耗时
- `pet_health_score_avg`: 平均健康评分

访问监控端点：`http://localhost:8083/actuator/prometheus`

---

**📞 技术支持**: 如有问题请联系开发团队
