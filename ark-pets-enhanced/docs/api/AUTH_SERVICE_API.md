# 认证服务 API 文档

**服务名称**: auth-service  
**服务端口**: 8081  
**基础路径**: `/api/v1/auth`  
**版本**: 4.0.0  
**状态**: ✅ 已完成

## 📋 API 概览

认证服务提供完整的用户认证、授权和第三方登录功能。

### 🔐 认证方式
- **JWT Token**: 基于Sa-Token的JWT认证
- **第三方登录**: GitHub、Google、QQ、微信
- **密码加密**: BCrypt加密存储

---

## 🔑 基础认证接口

### 1. 用户登录
```http
POST /api/v1/auth/login
```

**请求体**:
```json
{
  "username": "string",
  "password": "string",
  "rememberMe": false
}
```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "token": "jwt-token-string",
  "tokenName": "ark-pets-token",
  "tokenTimeout": 2592000,
  "username": "user123"
}
```

### 2. 用户注册
```http
POST /api/v1/auth/register
```

**请求体**:
```json
{
  "username": "string",
  "password": "string",
  "confirmPassword": "string",
  "email": "<EMAIL>",
  "nickname": "string"
}
```

**响应**:
```json
{
  "success": true,
  "message": "注册成功，请登录"
}
```

### 3. 用户登出
```http
POST /api/v1/auth/logout
```

**响应**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 4. 刷新Token
```http
POST /api/v1/auth/refresh
```

**响应**:
```json
{
  "success": true,
  "message": "刷新成功",
  "token": "new-jwt-token",
  "tokenName": "ark-pets-token",
  "tokenTimeout": 2592000
}
```

### 5. 获取用户信息
```http
GET /api/v1/auth/userinfo
```

**响应**:
```json
{
  "success": true,
  "message": "获取用户信息成功",
  "username": "user123"
}
```

---

## 🌐 第三方登录接口

### GitHub登录

#### 1. 获取授权URL
```http
GET /api/v1/auth/oauth/github/authorize
```

**响应**:
```json
{
  "success": true,
  "message": "获取授权URL成功",
  "token": "https://github.com/login/oauth/authorize?..."
}
```

#### 2. 登录回调
```http
GET /api/v1/auth/oauth/github/callback?code=xxx&state=xxx
```

**响应**:
```json
{
  "success": true,
  "message": "GitHub登录成功",
  "token": "jwt-token-string",
  "tokenName": "ark-pets-token",
  "tokenTimeout": 2592000,
  "username": "github_username"
}
```

### Google登录

#### 1. 获取授权URL
```http
GET /api/v1/auth/oauth/google/authorize
```

#### 2. 登录回调
```http
GET /api/v1/auth/oauth/google/callback?code=xxx&state=xxx
```

### QQ登录

#### 1. 获取授权URL
```http
GET /api/v1/auth/oauth/qq/authorize
```

#### 2. 登录回调
```http
GET /api/v1/auth/oauth/qq/callback?code=xxx&state=xxx
```

### 微信登录

#### 1. 获取授权URL
```http
GET /api/v1/auth/oauth/wechat/authorize
```

#### 2. 登录回调
```http
GET /api/v1/auth/oauth/wechat/callback?code=xxx&state=xxx
```

---

## 📊 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

---

## 🔒 安全配置

### Token配置
- **Token名称**: `ark-pets-token`
- **有效期**: 30天 (2592000秒)
- **算法**: JWT + UUID
- **存储**: Redis缓存

### 密码安全
- **加密算法**: BCrypt
- **最小长度**: 6个字符
- **最大长度**: 100个字符

### 第三方登录配置
需要在`application.yml`中配置各平台的客户端ID和密钥：

```yaml
oauth:
  github:
    client-id: your-github-client-id
    client-secret: your-github-client-secret
  google:
    client-id: your-google-client-id
    client-secret: your-google-client-secret
  qq:
    client-id: your-qq-client-id
    client-secret: your-qq-client-secret
  wechat:
    client-id: your-wechat-client-id
    client-secret: your-wechat-client-secret
```

---

## 🧪 测试示例

### 使用curl测试登录
```bash
curl -X POST http://localhost:8081/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

### 使用Token访问受保护接口
```bash
curl -X GET http://localhost:8081/api/v1/auth/userinfo \
  -H "ark-pets-token: your-jwt-token"
```

---

## 📝 更新日志

### v4.0.0 (2025-01-01)
- ✅ 基础认证功能完成
- ✅ 第三方登录集成
- ✅ JWT Token管理
- ✅ 用户权限控制
- ✅ 密码安全加密
