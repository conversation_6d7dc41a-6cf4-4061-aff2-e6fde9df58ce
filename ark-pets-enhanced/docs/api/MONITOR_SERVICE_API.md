# 监控服务 API 文档

**服务名称**: ark-pets-monitor-service  
**服务端口**: 8085  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

监控服务提供系统监控、应用监控、用户行为分析等功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8085`
- **API 前缀**: `/api/v1/monitor`
- **认证方式**: 无需认证（内部服务）
- **数据格式**: JSON

---

## 📚 API 接口列表

### 1. 获取系统监控信息
**GET** `/api/v1/monitor/system`

获取系统级监控指标。

#### 响应示例
```json
{
  "success": true,
  "message": "系统监控信息获取成功",
  "data": {
    "os": "Linux 5.4.0",
    "architecture": "64-bit",
    "cpu_model": "Intel Core i7-9700K",
    "cpu_cores": 8,
    "cpu_usage": 25.6,
    "total_memory": 17179869184,
    "available_memory": **********,
    "memory_usage": 50.0,
    "disk_usage": 65.2,
    "network_bytes_received": **********,
    "network_bytes_sent": 524288000,
    "jvm_max_memory": **********,
    "jvm_total_memory": **********,
    "jvm_free_memory": 536870912,
    "jvm_processors": 8
  },
  "healthy": true,
  "timestamp": 1704067200000
}
```

### 2. 获取应用监控信息
**GET** `/api/v1/monitor/application`

获取应用级监控指标。

#### 响应示例
```json
{
  "success": true,
  "message": "应用监控信息获取成功",
  "data": {
    "total_requests": 10000,
    "successful_requests": 9800,
    "failed_requests": 200,
    "error_rate": 2.0,
    "active_sessions": 150,
    "database_connections": 10,
    "average_response_times": {
      "/api/v1/auth/login": 120.5,
      "/api/v1/ai/chat": 850.2
    },
    "error_counts": {
      "/api/v1/auth/login": 5,
      "/api/v1/ai/chat": 15
    },
    "jvm": {
      "max_memory": **********,
      "total_memory": **********,
      "free_memory": 536870912,
      "used_memory": 536870912
    }
  },
  "healthy": true,
  "timestamp": 1704067200000
}
```

### 3. 获取监控概览
**GET** `/api/v1/monitor/overview`

获取系统和应用的综合监控信息。

#### 响应示例
```json
{
  "success": true,
  "message": "监控概览获取成功",
  "data": {
    "system": {
      "cpu_usage": 25.6,
      "memory_usage": 50.0,
      "disk_usage": 65.2
    },
    "application": {
      "total_requests": 10000,
      "error_rate": 2.0,
      "active_sessions": 150
    },
    "system_healthy": true,
    "application_healthy": true,
    "overall_healthy": true
  },
  "timestamp": 1704067200000
}
```

### 4. 跟踪用户事件
**POST** `/api/v1/monitor/events/track`

记录用户行为事件。

#### 查询参数
- `userId` (string): 用户ID
- `eventName` (string): 事件名称

#### 请求体
```json
{
  "feature_name": "ai_chat",
  "model": "gpt-3.5-turbo",
  "response_time": 850
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "事件跟踪成功",
  "event": "ai_chat",
  "user_id": "user123",
  "timestamp": 1704067200000
}
```

### 5. 记录API调用
**POST** `/api/v1/monitor/api/record`

记录API调用统计。

#### 查询参数
- `userId` (string): 用户ID
- `endpoint` (string): API端点
- `method` (string): HTTP方法
- `statusCode` (int): 状态码
- `duration` (long): 响应时间（毫秒）

#### 响应示例
```json
{
  "success": true,
  "message": "API调用记录成功",
  "endpoint": "/api/v1/ai/chat",
  "user_id": "user123",
  "timestamp": 1704067200000
}
```

### 6. 更新用户属性
**POST** `/api/v1/monitor/users/{userId}/properties`

更新用户分析属性。

#### 路径参数
- `userId` (string): 用户ID

#### 请求体
```json
{
  "plan": "premium",
  "registration_date": "2024-01-01",
  "last_login": "2025-01-01"
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "用户属性更新成功",
  "user_id": "user123",
  "timestamp": 1704067200000
}
```

### 7. 重置应用统计
**POST** `/api/v1/monitor/application/reset`

重置应用监控统计数据。

#### 响应示例
```json
{
  "success": true,
  "message": "应用统计数据重置成功",
  "timestamp": 1704067200000
}
```

### 8. 健康检查
**GET** `/api/v1/monitor/health`

检查监控服务健康状态。

#### 响应示例
```json
{
  "status": "UP",
  "service": "monitor-service",
  "system_healthy": true,
  "application_healthy": true,
  "timestamp": 1704067200000,
  "cpu_usage": 25.6,
  "memory_usage": 50.0,
  "disk_usage": 65.2
}
```

---

## 📊 监控指标说明

### 系统指标
- **CPU使用率**: 当前CPU使用百分比
- **内存使用率**: 当前内存使用百分比
- **磁盘使用率**: 磁盘空间使用百分比
- **网络流量**: 网络接收和发送字节数

### 应用指标
- **请求统计**: 总请求数、成功数、失败数
- **错误率**: 失败请求占总请求的百分比
- **响应时间**: 各API端点的平均响应时间
- **活跃会话**: 当前活跃用户会话数

### JVM指标
- **内存使用**: 最大内存、总内存、空闲内存
- **线程数**: 活跃线程数量
- **GC信息**: 垃圾回收统计

---

## 🎯 用户行为分析

### 支持的事件类型
- `user_login`: 用户登录
- `user_registration`: 用户注册
- `ai_chat`: AI聊天
- `feature_usage`: 功能使用
- `error_occurred`: 错误发生
- `page_view`: 页面访问
- `api_call`: API调用
- `session_start`: 会话开始
- `session_end`: 会话结束

### 事件属性
每个事件可以包含以下属性：
- `timestamp`: 事件时间戳
- `user_id`: 用户ID
- `session_id`: 会话ID
- `platform`: 平台信息
- `version`: 应用版本
- 自定义属性

---

## ⚠️ 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 404 | 资源不存在 | 检查请求路径 |
| 500 | 服务内部错误 | 检查服务状态 |
| 503 | 服务不可用 | 检查系统健康状态 |

---

## 🔧 使用示例

### Java 客户端示例
```java
// 获取系统监控信息
ResponseEntity<Map> response = restTemplate.getForEntity(
    "http://localhost:8085/api/v1/monitor/system", Map.class);

// 跟踪用户事件
Map<String, Object> properties = Map.of("feature", "ai_chat");
restTemplate.postForEntity(
    "http://localhost:8085/api/v1/monitor/events/track?userId=user123&eventName=feature_usage",
    properties, Map.class);
```

### curl 示例
```bash
# 获取系统监控信息
curl http://localhost:8085/api/v1/monitor/system

# 跟踪用户事件
curl -X POST "http://localhost:8085/api/v1/monitor/events/track?userId=user123&eventName=ai_chat" \
  -H "Content-Type: application/json" \
  -d '{"model":"gpt-3.5-turbo","response_time":850}'

# 健康检查
curl http://localhost:8085/api/v1/monitor/health
```

---

**📞 技术支持**: 如有问题请联系开发团队
