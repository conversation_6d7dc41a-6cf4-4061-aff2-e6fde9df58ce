# 高级监控分析 API 文档

**服务名称**: ark-pets-monitor-service (Enhanced)  
**服务端口**: 8085  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

高级监控分析服务提供用户行为分析、性能监控优化和智能告警等功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8085`
- **API 前缀**: `/api/v1/monitor`
- **认证方式**: JWT <PERSON> (通过网关)
- **数据格式**: JSON

---

## 📊 用户行为分析 API

### 1. 记录用户行为事件
**POST** `/api/v1/monitor/behavior/track`

记录用户行为事件用于分析。

#### 请求体
```json
{
  "userId": "user123",
  "sessionId": "session456",
  "eventType": "PAGE_VIEW",
  "eventName": "dashboard_visit",
  "eventCategory": "navigation",
  "eventLabel": "main_dashboard",
  "eventValue": 1,
  "pageUrl": "/dashboard",
  "referrerUrl": "/login",
  "userAgent": "Mozilla/5.0...",
  "ipAddress": "*************",
  "deviceType": "desktop",
  "browser": "Chrome",
  "operatingSystem": "Windows 10",
  "screenResolution": "1920x1080",
  "durationMs": 5000,
  "properties": {
    "feature": "pet_management",
    "version": "4.0.0"
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "用户行为事件记录成功",
  "eventId": "event-123",
  "timestamp": 1704067200000
}
```

### 2. 获取用户行为分析
**GET** `/api/v1/monitor/analytics/behavior`

获取用户行为分析数据。

#### 查询参数
- `days` (int): 分析天数，默认7天

#### 响应示例
```json
{
  "success": true,
  "message": "用户行为分析获取成功",
  "data": {
    "dailyActiveUsers": {
      "2025-01-01": 150,
      "2025-01-02": 180,
      "2025-01-03": 165
    },
    "eventStatistics": {
      "2025-01-01": {
        "PAGE_VIEW": 500,
        "USER_ACTION": 300,
        "PET_INTERACTION": 200,
        "AI_CHAT": 100
      }
    },
    "onlineUserCount": 25,
    "retentionRate": {
      "2025-01-01": 75.5,
      "2025-01-02": 68.2,
      "2025-01-03": 72.1
    }
  },
  "timestamp": 1704067200000
}
```

### 3. 获取页面访问统计
**GET** `/api/v1/monitor/analytics/pages`

获取页面访问和设备统计。

#### 查询参数
- `date` (string): 日期，格式YYYY-MM-DD，默认今天

#### 响应示例
```json
{
  "success": true,
  "message": "页面访问统计获取成功",
  "data": {
    "pageViews": {
      "/dashboard": 1200,
      "/pets": 800,
      "/chat": 600,
      "/settings": 300
    },
    "deviceTypes": {
      "desktop": 1500,
      "mobile": 800,
      "tablet": 200
    },
    "date": "2025-01-01"
  },
  "timestamp": 1704067200000
}
```

---

## 🚨 智能告警 API

### 4. 获取告警历史
**GET** `/api/v1/monitor/alerts/history`

获取告警历史记录。

#### 查询参数
- `date` (string): 日期，格式YYYY-MM-DD，默认今天
- `limit` (int): 返回数量限制，默认50

#### 响应示例
```json
{
  "success": true,
  "message": "告警历史获取成功",
  "data": [
    {
      "id": "alert-123",
      "ruleId": "rule-456",
      "ruleName": "高CPU使用率",
      "metricName": "system.cpu.usage",
      "metricValue": 85.5,
      "threshold": 80.0,
      "operator": ">",
      "severity": "WARNING",
      "serviceName": "pet-service",
      "instanceId": "instance-1",
      "triggerCount": 3,
      "timestamp": "2025-01-01 10:30:00",
      "description": "服务 pet-service 的指标 system.cpu.usage 当前值 85.50 > 阈值 80.00，触发了告警规则 '高CPU使用率'"
    }
  ],
  "date": "2025-01-01",
  "count": 1,
  "timestamp": 1704067200000
}
```

### 5. 获取告警统计
**GET** `/api/v1/monitor/alerts/statistics`

获取告警统计信息。

#### 查询参数
- `days` (int): 统计天数，默认7天

#### 响应示例
```json
{
  "success": true,
  "message": "告警统计获取成功",
  "data": {
    "dailyAlertCounts": {
      "2025-01-01": 15,
      "2025-01-02": 12,
      "2025-01-03": 8
    },
    "severityCounts": {
      "CRITICAL": 5,
      "ERROR": 10,
      "WARNING": 15,
      "INFO": 5
    },
    "totalAlerts": 35
  },
  "timestamp": 1704067200000
}
```

---

## 📈 性能监控 API

### 6. 获取系统监控信息
**GET** `/api/v1/monitor/system`

获取系统级监控指标。

#### 响应示例
```json
{
  "success": true,
  "message": "系统监控信息获取成功",
  "data": {
    "cpu": {
      "usage": 45.2,
      "cores": 8,
      "loadAverage": [1.2, 1.5, 1.8]
    },
    "memory": {
      "total": 16384,
      "used": 8192,
      "free": 8192,
      "usage": 50.0
    },
    "disk": {
      "total": 500000,
      "used": 250000,
      "free": 250000,
      "usage": 50.0
    },
    "network": {
      "bytesReceived": 1024000,
      "bytesSent": 512000,
      "packetsReceived": 1000,
      "packetsSent": 800
    }
  },
  "timestamp": 1704067200000
}
```

### 7. 获取应用监控信息
**GET** `/api/v1/monitor/application`

获取应用级监控指标。

#### 响应示例
```json
{
  "success": true,
  "message": "应用监控信息获取成功",
  "data": {
    "jvm": {
      "heapUsed": 512,
      "heapMax": 1024,
      "heapUsage": 50.0,
      "nonHeapUsed": 256,
      "gcCount": 100,
      "gcTime": 5000
    },
    "threads": {
      "active": 25,
      "peak": 30,
      "daemon": 15
    },
    "database": {
      "activeConnections": 10,
      "maxConnections": 20,
      "connectionUsage": 50.0
    },
    "cache": {
      "hitRate": 85.5,
      "missRate": 14.5,
      "evictions": 50
    }
  },
  "timestamp": 1704067200000
}
```

### 8. 获取业务监控信息
**GET** `/api/v1/monitor/business`

获取业务级监控指标。

#### 响应示例
```json
{
  "success": true,
  "message": "业务监控信息获取成功",
  "data": {
    "users": {
      "activeUsers": 150,
      "newUsers": 25,
      "retentionRate": 75.5
    },
    "pets": {
      "totalPets": 500,
      "activePets": 400,
      "interactionsToday": 1200
    },
    "ai": {
      "chatSessions": 300,
      "messagesProcessed": 1500,
      "averageResponseTime": 250
    },
    "performance": {
      "averageResponseTime": 150,
      "errorRate": 0.5,
      "throughput": 100
    }
  },
  "timestamp": 1704067200000
}
```

---

## 📊 事件类型和指标

### 用户行为事件类型
- `PAGE_VIEW` - 页面访问
- `USER_ACTION` - 用户操作
- `PET_INTERACTION` - 宠物互动
- `AI_CHAT` - AI对话
- `SYSTEM_EVENT` - 系统事件
- `ERROR_EVENT` - 错误事件
- `PERFORMANCE` - 性能事件
- `CUSTOM` - 自定义事件

### 告警严重程度
- `INFO` - 信息级别
- `WARNING` - 警告级别
- `ERROR` - 错误级别
- `CRITICAL` - 严重级别

### 性能指标类型
- `COUNTER` - 计数器
- `GAUGE` - 仪表
- `HISTOGRAM` - 直方图
- `TIMER` - 计时器
- `SUMMARY` - 摘要

---

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查JWT token |
| 404 | 资源不存在 | 确认资源ID正确 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系技术支持 |

---

## 🔧 使用示例

### JavaScript 客户端示例
```javascript
// 记录用户行为事件
const trackEvent = async (eventData) => {
  const response = await fetch('/api/v1/monitor/behavior/track', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(eventData)
  });
  return response.json();
};

// 获取用户行为分析
const getBehaviorAnalytics = async (days = 7) => {
  const response = await fetch(`/api/v1/monitor/analytics/behavior?days=${days}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// 获取告警统计
const getAlertStatistics = async (days = 7) => {
  const response = await fetch(`/api/v1/monitor/alerts/statistics?days=${days}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```

### Java 客户端示例
```java
// 记录用户行为事件
UserBehaviorEvent event = new UserBehaviorEvent();
event.setUserId("user123");
event.setSessionId("session456");
event.setEventType(UserBehaviorEvent.EventType.PAGE_VIEW);
event.setEventName("dashboard_visit");

ResponseEntity<Map> response = restTemplate.postForEntity(
    "http://localhost:8085/api/v1/monitor/behavior/track",
    event, Map.class);

// 获取系统监控信息
ResponseEntity<Map> systemMetrics = restTemplate.getForEntity(
    "http://localhost:8085/api/v1/monitor/system",
    Map.class);
```

---

## 📈 监控指标

高级监控服务暴露以下Prometheus指标：
- `behavior_events_total`: 用户行为事件总数
- `alert_rules_triggered_total`: 告警规则触发总数
- `performance_metrics_collected_total`: 性能指标收集总数
- `analytics_queries_total`: 分析查询总数

访问监控端点：`http://localhost:8085/actuator/prometheus`

---

**📞 技术支持**: 如有问题请联系开发团队
