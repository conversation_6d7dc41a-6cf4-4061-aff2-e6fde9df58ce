# AI服务 API 文档

**服务名称**: ai-service  
**服务端口**: 8083  
**基础路径**: `/api/v1/ai`  
**版本**: 4.0.0  
**状态**: ✅ 已完成

## 📋 API 概览

AI服务提供智能对话、情感分析、创意生成等AI功能，支持多种AI模型。

### 🤖 支持的AI模型
- **GPT-3.5-turbo**: OpenAI的高效对话模型
- **GPT-4**: OpenAI的高级对话模型
- **Llama2**: Meta的开源大语言模型
- **CodeLlama**: 专门用于代码生成的模型

---

## 💬 智能对话接口

### 1. 基础聊天
```http
POST /api/v1/ai/chat
```

**请求体**:
```json
{
  "message": "你好，我想了解一下Ark-Pets",
  "username": "user123",
  "context": "用户首次使用",
  "sessionId": "session-uuid",
  "modelPreference": "gpt35"
}
```

**响应**:
```json
{
  "success": true,
  "message": "聊天成功",
  "response": "你好！我是Ark-Pets的AI助手，很高兴认识你！😊 Ark-Pets是一个可爱的桌面宠物应用...",
  "model": "default",
  "timestamp": 1704067200000,
  "sessionId": "session-uuid",
  "emotion": "positive"
}
```

### 2. 指定模型聊天
```http
POST /api/v1/ai/chat/{modelType}
```

**路径参数**:
- `modelType`: 模型类型 (`gpt35`, `gpt4`, `llama2`, `codellama`)

**请求体**:
```json
{
  "message": "帮我写一个Python函数",
  "username": "developer",
  "context": "编程助手"
}
```

**响应**:
```json
{
  "success": true,
  "message": "聊天成功",
  "response": "当然可以！这里是一个Python函数示例：\n\n```python\ndef hello_world():\n    return 'Hello, World!'\n```",
  "model": "codellama",
  "timestamp": 1704067200000
}
```

---

## 🎨 创意生成接口

### 1. 生成创意回复
```http
GET /api/v1/ai/creative?topic=春天&username=user123
```

**查询参数**:
- `topic`: 话题关键词 (必需)
- `username`: 用户名 (可选)

**响应**:
```json
{
  "success": true,
  "message": "创意生成成功",
  "response": "春天来了！🌸 就像小草破土而出一样，每个新的开始都充满了无限可能。让我们一起在这个美好的季节里，种下希望的种子吧！",
  "model": "creative",
  "timestamp": 1704067200000
}
```

---

## 😊 情感分析接口

### 1. 分析文本情感
```http
POST /api/v1/ai/emotion?message=今天心情不太好
```

**查询参数**:
- `message`: 要分析的文本 (必需)

**响应**:
```json
{
  "success": true,
  "message": "情感分析完成",
  "response": "{\"emotion\":\"negative\",\"confidence\":0.8,\"keywords\":[\"心情\",\"不太好\"],\"suggestion\":\"给予安慰和支持\"}",
  "model": "emotion-analyzer",
  "timestamp": 1704067200000
}
```

---

## 🔧 模型管理接口

### 1. 获取可用模型列表
```http
GET /api/v1/ai/models
```

**响应**:
```json
{
  "gpt35": true,
  "gpt4": true,
  "llama2": false,
  "codellama": false
}
```

### 2. 检查特定模型状态
```http
GET /api/v1/ai/models/{modelType}/status
```

**路径参数**:
- `modelType`: 模型类型

**响应**:
```json
{
  "model": "gpt35",
  "available": true,
  "timestamp": 1704067200000
}
```

### 3. 服务健康检查
```http
GET /api/v1/ai/health
```

**响应**:
```json
{
  "status": "UP",
  "service": "ai-service",
  "timestamp": 1704067200000,
  "models": {
    "gpt35": true,
    "gpt4": true,
    "llama2": false,
    "codellama": false
  }
}
```

---

## ⚙️ 配置说明

### AI模型配置
在`application.yml`中配置AI模型参数：

```yaml
# LangChain4j配置
langchain4j:
  open-ai:
    chat-model:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      model-name: gpt-3.5-turbo
      temperature: 0.7
      max-tokens: 1000
      timeout: 30s
  
  ollama:
    chat-model:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      model-name: llama2
      temperature: 0.7
      timeout: 30s
```

### 缓存配置
```yaml
ai-service:
  cache:
    enabled: true
    ttl: 3600 # 1小时
    max-size: 1000
```

### 限流配置
```yaml
ai-service:
  rate-limit:
    enabled: true
    requests-per-minute: 60
    requests-per-hour: 1000
```

---

## 📊 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |
| 503 | AI模型不可用 |

---

## 🧪 测试示例

### 测试基础聊天
```bash
curl -X POST http://localhost:8083/api/v1/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好",
    "username": "testuser"
  }'
```

### 测试情感分析
```bash
curl -X POST "http://localhost:8083/api/v1/ai/emotion?message=今天很开心"
```

### 测试创意生成
```bash
curl -X GET "http://localhost:8083/api/v1/ai/creative?topic=友谊&username=testuser"
```

---

## 🎯 使用建议

### 最佳实践
1. **模型选择**: 
   - 日常对话使用`gpt35`
   - 复杂问题使用`gpt4`
   - 代码相关使用`codellama`

2. **上下文管理**:
   - 提供相关上下文信息
   - 使用sessionId维护对话连续性

3. **错误处理**:
   - 检查模型可用性
   - 实现降级策略

### 性能优化
1. **缓存策略**: 相同问题会返回缓存结果
2. **限流控制**: 避免频繁请求
3. **异步处理**: 长时间任务使用异步接口

---

## 📝 更新日志

### v4.0.0 (2025-01-01)
- ✅ 多AI模型支持
- ✅ 智能对话功能
- ✅ 情感分析功能
- ✅ 创意生成功能
- ✅ 模型管理功能
- ✅ 缓存和限流
