# API网关服务 API 文档

**服务名称**: ark-pets-gateway-service  
**服务端口**: 8080  
**版本**: 4.0.0  
**最后更新**: 2025-01-01

## 📋 API 概览

API网关服务作为所有微服务的统一入口，提供路由、负载均衡、限流、认证等功能。

### 🔗 基础信息
- **Base URL**: `http://localhost:8080`
- **API 前缀**: `/api/v1/gateway`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON

---

## 🚪 路由配置

### 服务路由映射

| 服务 | 路由路径 | 目标地址 | 限流 |
|------|----------|----------|------|
| 认证服务 | `/api/v1/auth/**` | `http://localhost:8081` | 10/min |
| AI服务 | `/api/v1/ai/**` | `http://localhost:8082` | 5/min |
| 缓存服务 | `/api/v1/cache/**` | `http://localhost:8084` | 50/min |
| 监控服务 | `/api/v1/monitor/**` | `http://localhost:8085` | 20/min |
| 配置服务 | `/api/v1/config/**` | `http://localhost:8086` | 30/min |

### 路由示例
```bash
# 通过网关访问认证服务
curl http://localhost:8080/api/v1/auth/login

# 通过网关访问AI服务
curl http://localhost:8080/api/v1/ai/chat \
  -H "Authorization: Bearer your-jwt-token"

# 通过网关访问缓存服务
curl http://localhost:8080/api/v1/cache/get/user:123 \
  -H "Authorization: Bearer your-jwt-token"
```

---

## 📚 网关管理 API

### 1. 获取所有路由信息
**GET** `/api/v1/gateway/routes`

获取网关配置的所有路由信息。

#### 响应示例
```json
{
  "success": true,
  "message": "路由信息获取成功",
  "routes": [
    {
      "id": "auth-service",
      "uri": "http://localhost:8081",
      "predicates": "Path predicates",
      "filters": "Gateway filters",
      "order": 0,
      "metadata": {}
    },
    {
      "id": "ai-service",
      "uri": "http://localhost:8082",
      "predicates": "Path predicates",
      "filters": "Gateway filters",
      "order": 0,
      "metadata": {}
    }
  ],
  "count": 5,
  "timestamp": 1704067200000
}
```

### 2. 获取特定路由信息
**GET** `/api/v1/gateway/routes/{routeId}`

获取指定路由的详细信息。

#### 路径参数
- `routeId` (string): 路由ID

#### 响应示例
```json
{
  "success": true,
  "message": "路由信息获取成功",
  "route": {
    "id": "auth-service",
    "uri": "http://localhost:8081",
    "predicates": "Path predicates",
    "filters": "Gateway filters",
    "order": 0,
    "metadata": {}
  },
  "timestamp": 1704067200000
}
```

### 3. 获取网关统计信息
**GET** `/api/v1/gateway/stats`

获取网关运行统计信息。

#### 响应示例
```json
{
  "success": true,
  "message": "网关统计信息获取成功",
  "stats": {
    "total_routes": 5,
    "active_routes": 5,
    "gateway_version": "4.0.0",
    "uptime": "1h 30m",
    "service_routes": {
      "auth-service": 1,
      "ai-service": 1,
      "cache-service": 1,
      "monitor-service": 1,
      "config-service": 1
    }
  },
  "timestamp": 1704067200000
}
```

### 4. 获取服务列表
**GET** `/api/v1/gateway/services`

获取网关管理的所有服务列表。

#### 响应示例
```json
{
  "success": true,
  "message": "服务列表获取成功",
  "services": [
    "auth-service",
    "ai-service",
    "cache-service",
    "monitor-service",
    "config-service"
  ],
  "count": 5,
  "timestamp": 1704067200000
}
```

### 5. 健康检查
**GET** `/api/v1/gateway/health`

检查网关服务健康状态。

#### 响应示例
```json
{
  "status": "UP",
  "service": "gateway-service",
  "routes_count": 5,
  "timestamp": 1704067200000
}
```

---

## 🔐 认证与授权

### JWT认证
网关服务对所有请求进行JWT认证，除了以下排除路径：
- `/api/v1/auth/login`
- `/api/v1/auth/register`
- `/api/v1/auth/refresh`
- `/actuator/**`

### 认证流程
1. 客户端在请求头中包含JWT token：`Authorization: Bearer <token>`
2. 网关验证token的有效性
3. 验证成功后，添加用户信息到请求头：
   - `X-User-ID`: 用户ID
   - `X-User-Role`: 用户角色
4. 转发请求到目标服务

### 认证失败响应
```json
{
  "success": false,
  "message": "Missing or invalid Authorization header",
  "timestamp": 1704067200000
}
```

---

## 🚦 限流控制

### 限流规则
网关对不同服务实施不同的限流策略：

| 服务 | 限流速率 | 突发容量 |
|------|----------|----------|
| 认证服务 | 10/分钟 | 20 |
| AI服务 | 5/分钟 | 10 |
| 缓存服务 | 50/分钟 | 100 |
| 监控服务 | 20/分钟 | 40 |
| 配置服务 | 30/分钟 | 60 |

### 限流响应
当触发限流时，返回HTTP 429状态码：
```json
{
  "timestamp": "2025-01-01T10:00:00.000+00:00",
  "status": 429,
  "error": "Too Many Requests",
  "message": "Request rate limit exceeded",
  "path": "/api/v1/ai/chat"
}
```

---

## 📊 请求日志

### 日志格式
网关记录所有请求的详细日志：

```
REQUEST [12345678] GET /api/v1/auth/login from *************
RESPONSE [12345678] Status: 200 OK Time: 120ms
```

### 日志字段
- **Request ID**: 唯一请求标识符
- **HTTP Method**: 请求方法
- **Path**: 请求路径
- **Client IP**: 客户端IP地址
- **Response Status**: 响应状态码
- **Response Time**: 响应时间（毫秒）

---

## 🔧 负载均衡

### 负载均衡策略
- **Round Robin**: 轮询分发（默认）
- **Random**: 随机分发
- **Weighted**: 权重分发

### 健康检查
网关定期检查后端服务健康状态：
- **检查间隔**: 30秒
- **超时时间**: 5秒
- **健康端点**: `/actuator/health` 或服务特定健康检查端点

---

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | 未授权 | 检查JWT token |
| 403 | 禁止访问 | 检查用户权限 |
| 404 | 路由不存在 | 检查请求路径 |
| 429 | 请求过多 | 降低请求频率 |
| 502 | 网关错误 | 检查后端服务状态 |
| 503 | 服务不可用 | 检查服务健康状态 |
| 504 | 网关超时 | 检查网络连接 |

### 错误响应格式
```json
{
  "timestamp": "2025-01-01T10:00:00.000+00:00",
  "status": 500,
  "error": "Internal Server Error",
  "message": "Service temporarily unavailable",
  "path": "/api/v1/ai/chat"
}
```

---

## 🔧 使用示例

### 通过网关访问服务
```bash
# 1. 用户登录获取token
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"user","password":"password"}'

# 2. 使用token访问AI服务
curl -X POST http://localhost:8080/api/v1/ai/chat \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIs..." \
  -H "Content-Type: application/json" \
  -d '{"message":"Hello AI"}'

# 3. 访问缓存服务
curl http://localhost:8080/api/v1/cache/get/user:123 \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIs..."

# 4. 查看网关状态
curl http://localhost:8080/api/v1/gateway/health
```

### Java 客户端示例
```java
// 配置WebClient使用网关
WebClient webClient = WebClient.builder()
    .baseUrl("http://localhost:8080")
    .defaultHeader("Authorization", "Bearer " + jwtToken)
    .build();

// 调用AI服务
Mono<String> response = webClient.post()
    .uri("/api/v1/ai/chat")
    .bodyValue(Map.of("message", "Hello AI"))
    .retrieve()
    .bodyToMono(String.class);
```

---

## 📈 监控指标

网关暴露以下Prometheus指标：
- `gateway_requests_total`: 总请求数
- `gateway_request_duration_seconds`: 请求处理时间
- `gateway_requests_active`: 活跃请求数
- `gateway_route_requests_total`: 按路由统计的请求数

访问监控端点：`http://localhost:8080/actuator/prometheus`

---

**📞 技术支持**: 如有问题请联系开发团队
