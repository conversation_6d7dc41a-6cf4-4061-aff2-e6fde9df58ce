# 微服务间通信架构

**文档版本**: 4.0.0  
**最后更新**: 2025-01-01  
**负责团队**: Ark-Pets Team

## 📋 概览

本文档描述了Ark-Pets Enhanced项目中微服务间的通信架构、协议和最佳实践。

---

## 🏗️ 架构概览

### 通信拓扑图
```
┌─────────────────┐
│   客户端应用     │
└─────────┬───────┘
          │ HTTP/HTTPS
          ▼
┌─────────────────┐
│   API网关       │ ← 统一入口 (8080)
│  (Gateway)      │
└─────────┬───────┘
          │ 内部路由
          ▼
┌─────────────────────────────────────────────────────┐
│                微服务集群                            │
├─────────┬─────────┬─────────┬─────────┬─────────────┤
│ 认证服务 │ AI服务  │ 缓存服务 │ 监控服务 │ 配置服务     │
│ (8081)  │ (8082)  │ (8084)  │ (8085)  │ (8086)      │
└─────────┴─────────┴─────────┴─────────┴─────────────┘
          │         │         │         │
          └─────────┼─────────┼─────────┘
                    │         │
                    ▼         ▼
            ┌─────────────────────┐
            │   共享基础设施       │
            │ Redis + PostgreSQL  │
            └─────────────────────┘
```

---

## 🚪 API网关 (Gateway Service)

### 核心功能
- **统一入口**: 所有外部请求的单一入口点
- **路由管理**: 智能路由到后端微服务
- **负载均衡**: 多实例服务的负载分发
- **认证授权**: JWT token验证和用户身份管理
- **限流控制**: API调用频率限制
- **请求日志**: 统一的请求追踪和日志记录

### 路由配置
| 路径模式 | 目标服务 | 端口 | 限流 |
|----------|----------|------|------|
| `/api/v1/auth/**` | 认证服务 | 8081 | 10/min |
| `/api/v1/ai/**` | AI服务 | 8082 | 5/min |
| `/api/v1/cache/**` | 缓存服务 | 8084 | 50/min |
| `/api/v1/monitor/**` | 监控服务 | 8085 | 20/min |
| `/api/v1/config/**` | 配置服务 | 8086 | 30/min |

---

## 🔗 服务间通信模式

### 1. 同步通信 (HTTP/REST)
**使用场景**: 实时数据查询、用户认证验证

```java
// 示例：AI服务调用认证服务验证用户
@Service
public class UserValidationService {
    
    @Autowired
    private WebClient webClient;
    
    public Mono<Boolean> validateUser(String userId) {
        return webClient.get()
            .uri("http://localhost:8081/api/v1/auth/validate/{userId}", userId)
            .retrieve()
            .bodyToMono(Boolean.class);
    }
}
```

### 2. 异步通信 (事件驱动)
**使用场景**: 用户行为分析、监控数据收集

```java
// 示例：发布用户登录事件
@EventListener
public void handleUserLogin(UserLoginEvent event) {
    // 异步发送到监控服务
    monitorService.trackUserLogin(event.getUserId(), event.getTimestamp());
}
```

### 3. 缓存通信
**使用场景**: 高频数据访问、会话管理

```java
// 示例：通过缓存服务存储用户会话
@Service
public class SessionService {
    
    public void storeUserSession(String userId, UserSession session) {
        cacheService.set("session:" + userId, session, Duration.ofMinutes(30));
    }
}
```

---

## 🔐 认证与授权流程

### JWT认证流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant A as 认证服务
    participant S as 业务服务

    C->>G: 1. 登录请求
    G->>A: 2. 转发登录请求
    A->>A: 3. 验证用户凭据
    A->>G: 4. 返回JWT token
    G->>C: 5. 返回JWT token
    
    C->>G: 6. 业务请求 + JWT
    G->>G: 7. 验证JWT
    G->>S: 8. 转发请求 + 用户信息
    S->>G: 9. 返回响应
    G->>C: 10. 返回响应
```

### 服务间认证
- **网关认证**: 统一的JWT验证
- **服务标识**: 通过`X-Gateway-Source`头标识来源
- **用户传递**: 通过`X-User-ID`和`X-User-Role`头传递用户信息

---

## 📊 服务发现与注册

### 服务注册流程
```java
// 服务启动时自动注册
@PostConstruct
public void registerService() {
    ServiceRegistration registration = ServiceRegistration.builder()
        .serviceName("ai-service")
        .serviceId("ai-001")
        .host("localhost")
        .port(8082)
        .metadata(Map.of("version", "4.0.0"))
        .build();
    
    discoveryClient.register(registration);
}
```

### 健康检查
- **检查间隔**: 30秒
- **超时时间**: 5秒
- **健康端点**: `/actuator/health`
- **自动故障转移**: 不健康服务自动从负载均衡中移除

---

## 🚦 限流与熔断

### 限流策略
```yaml
# 网关限流配置
spring:
  cloud:
    gateway:
      routes:
        - id: ai-service
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 5    # 每秒补充5个令牌
                redis-rate-limiter.burstCapacity: 10   # 突发容量10个
```

### 熔断器配置
```yaml
# 熔断器配置
gateway-service:
  circuit-breaker:
    enabled: true
    failure-rate-threshold: 50      # 失败率阈值50%
    wait-duration-in-open-state: 60s # 熔断器打开状态持续时间
    sliding-window-size: 10         # 滑动窗口大小
```

---

## 📈 监控与追踪

### 请求追踪
每个请求都有唯一的追踪ID，在所有服务间传递：

```
X-Request-ID: 12345678-1234-1234-1234-123456789012
```

### 监控指标
- **请求量**: 每个服务的请求数量
- **响应时间**: 平均和P99响应时间
- **错误率**: 4xx和5xx错误比例
- **服务健康**: 服务可用性状态

### 日志聚合
```
[2025-01-01 10:00:00] [12345678] INFO  - REQUEST GET /api/v1/ai/chat from 192.168.1.100
[2025-01-01 10:00:01] [12345678] INFO  - RESPONSE 200 OK Time: 850ms
```

---

## 🔧 配置管理

### 动态配置
- **配置中心**: 统一的配置管理服务
- **热更新**: 配置变更无需重启服务
- **环境隔离**: 开发、测试、生产环境配置分离

### 配置优先级
1. 环境变量
2. 配置中心
3. 本地配置文件
4. 默认配置

---

## 🛠️ 开发最佳实践

### 1. API设计
- **RESTful**: 遵循REST设计原则
- **版本控制**: 使用`/api/v1/`前缀
- **统一响应**: 标准化的响应格式
- **错误处理**: 统一的错误码和消息

### 2. 服务间调用
```java
// 推荐：使用WebClient进行异步调用
@Service
public class ExternalServiceClient {
    
    private final WebClient webClient;
    
    public ExternalServiceClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
            .baseUrl("http://localhost:8081")
            .build();
    }
    
    public Mono<UserInfo> getUserInfo(String userId) {
        return webClient.get()
            .uri("/api/v1/users/{id}", userId)
            .retrieve()
            .bodyToMono(UserInfo.class)
            .timeout(Duration.ofSeconds(5))
            .retry(3);
    }
}
```

### 3. 错误处理
```java
// 统一错误处理
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ServiceUnavailableException.class)
    public ResponseEntity<ErrorResponse> handleServiceUnavailable(
            ServiceUnavailableException ex) {
        return ResponseEntity.status(503)
            .body(new ErrorResponse("SERVICE_UNAVAILABLE", ex.getMessage()));
    }
}
```

### 4. 超时配置
```yaml
# 推荐的超时配置
gateway-service:
  timeout:
    connect-timeout: 5s      # 连接超时
    response-timeout: 30s    # 响应超时
  
  retry:
    max-attempts: 3          # 最大重试次数
    wait-duration: 1s        # 重试间隔
```

---

## 🔍 故障排查

### 常见问题

#### 1. 服务不可达
**症状**: 502 Bad Gateway  
**排查步骤**:
1. 检查目标服务是否启动
2. 验证网络连接
3. 查看服务健康检查状态

#### 2. 认证失败
**症状**: 401 Unauthorized  
**排查步骤**:
1. 验证JWT token格式
2. 检查token是否过期
3. 确认认证服务状态

#### 3. 限流触发
**症状**: 429 Too Many Requests  
**排查步骤**:
1. 检查请求频率
2. 调整限流配置
3. 实施客户端限流

### 监控告警
- **服务下线**: 服务健康检查失败
- **高错误率**: 错误率超过5%
- **高延迟**: P99响应时间超过5秒
- **限流触发**: 限流次数异常增加

---

## 📚 相关文档

- [API网关服务文档](../api/GATEWAY_SERVICE_API.md)
- [认证服务文档](../api/AUTH_SERVICE_API.md)
- [AI服务文档](../api/AI_SERVICE_API.md)
- [缓存服务文档](../api/CACHE_SERVICE_API.md)
- [监控服务文档](../api/MONITOR_SERVICE_API.md)
- [配置服务文档](../api/CONFIG_SERVICE_API.md)

---

**📞 技术支持**: 如有问题请联系开发团队
