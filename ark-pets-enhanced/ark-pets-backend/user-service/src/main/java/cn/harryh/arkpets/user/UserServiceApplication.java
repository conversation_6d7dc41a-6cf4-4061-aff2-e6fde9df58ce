package cn.harryh.arkpets.user;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 用户服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication(scanBasePackages = {"cn.harryh.arkpets.user", "cn.harryh.arkpets.common"})
public class UserServiceApplication {

  private static final Logger logger = LoggerFactory.getLogger(UserServiceApplication.class);

  public static void main(String[] args) {
    try {
      logger.info("启动用户服务...");
      SpringApplication.run(UserServiceApplication.class, args);
      logger.info("用户服务启动成功！");
    } catch (Exception e) {
      logger.error("用户服务启动失败", e);
      System.exit(1);
    }
  }
}
