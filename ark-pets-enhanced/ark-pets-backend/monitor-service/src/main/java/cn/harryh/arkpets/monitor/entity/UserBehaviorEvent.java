package cn.harryh.arkpets.monitor.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;
import org.hibernate.annotations.CreationTimestamp;

/**
 * 用户行为事件实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "user_behavior_events")
public class UserBehaviorEvent {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "用户ID不能为空")
  @Column(name = "user_id", nullable = false)
  private String userId;

  @NotBlank(message = "会话ID不能为空")
  @Column(name = "session_id", nullable = false)
  private String sessionId;

  @NotNull(message = "事件类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private EventType eventType;

  @NotBlank(message = "事件名称不能为空")
  @Column(name = "event_name", nullable = false, length = 100)
  private String eventName;

  @Column(name = "event_category", length = 50)
  private String eventCategory;

  @Column(name = "event_label", length = 100)
  private String eventLabel;

  @Column(name = "event_value")
  private Long eventValue;

  @Column(name = "page_url", length = 500)
  private String pageUrl;

  @Column(name = "referrer_url", length = 500)
  private String referrerUrl;

  @Column(name = "user_agent", length = 500)
  private String userAgent;

  @Column(name = "ip_address", length = 45)
  private String ipAddress;

  @Column(name = "device_type", length = 20)
  private String deviceType;

  @Column(name = "browser", length = 50)
  private String browser;

  @Column(name = "operating_system", length = 50)
  private String operatingSystem;

  @Column(name = "screen_resolution", length = 20)
  private String screenResolution;

  @Column(name = "duration_ms")
  private Long durationMs;

  @ElementCollection
  @CollectionTable(
      name = "user_behavior_event_properties",
      joinColumns = @JoinColumn(name = "event_id"))
  @MapKeyColumn(name = "property_key")
  @Column(name = "property_value")
  private Map<String, String> properties;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  // 事件类型枚举
  public enum EventType {
    PAGE_VIEW("页面访问"),
    USER_ACTION("用户操作"),
    PET_INTERACTION("宠物互动"),
    AI_CHAT("AI对话"),
    SYSTEM_EVENT("系统事件"),
    ERROR_EVENT("错误事件"),
    PERFORMANCE("性能事件"),
    CUSTOM("自定义事件");

    private final String displayName;

    EventType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public UserBehaviorEvent() {}

  public UserBehaviorEvent(String userId, String sessionId, EventType eventType, String eventName) {
    this.userId = userId;
    this.sessionId = sessionId;
    this.eventType = eventType;
    this.eventName = eventName;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getSessionId() {
    return sessionId;
  }

  public void setSessionId(String sessionId) {
    this.sessionId = sessionId;
  }

  public EventType getEventType() {
    return eventType;
  }

  public void setEventType(EventType eventType) {
    this.eventType = eventType;
  }

  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  public String getEventCategory() {
    return eventCategory;
  }

  public void setEventCategory(String eventCategory) {
    this.eventCategory = eventCategory;
  }

  public String getEventLabel() {
    return eventLabel;
  }

  public void setEventLabel(String eventLabel) {
    this.eventLabel = eventLabel;
  }

  public Long getEventValue() {
    return eventValue;
  }

  public void setEventValue(Long eventValue) {
    this.eventValue = eventValue;
  }

  public String getPageUrl() {
    return pageUrl;
  }

  public void setPageUrl(String pageUrl) {
    this.pageUrl = pageUrl;
  }

  public String getReferrerUrl() {
    return referrerUrl;
  }

  public void setReferrerUrl(String referrerUrl) {
    this.referrerUrl = referrerUrl;
  }

  public String getUserAgent() {
    return userAgent;
  }

  public void setUserAgent(String userAgent) {
    this.userAgent = userAgent;
  }

  public String getIpAddress() {
    return ipAddress;
  }

  public void setIpAddress(String ipAddress) {
    this.ipAddress = ipAddress;
  }

  public String getDeviceType() {
    return deviceType;
  }

  public void setDeviceType(String deviceType) {
    this.deviceType = deviceType;
  }

  public String getBrowser() {
    return browser;
  }

  public void setBrowser(String browser) {
    this.browser = browser;
  }

  public String getOperatingSystem() {
    return operatingSystem;
  }

  public void setOperatingSystem(String operatingSystem) {
    this.operatingSystem = operatingSystem;
  }

  public String getScreenResolution() {
    return screenResolution;
  }

  public void setScreenResolution(String screenResolution) {
    this.screenResolution = screenResolution;
  }

  public Long getDurationMs() {
    return durationMs;
  }

  public void setDurationMs(Long durationMs) {
    this.durationMs = durationMs;
  }

  public Map<String, String> getProperties() {
    return properties;
  }

  public void setProperties(Map<String, String> properties) {
    this.properties = properties;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
}
