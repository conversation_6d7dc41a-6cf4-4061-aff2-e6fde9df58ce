package cn.harryh.arkpets.monitor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 分析服务配置
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class AnalyticsConfig {

  @Value("${analytics.api-key:}")
  private String apiKey;

  @Value("${analytics.host:https://app.posthog.com}")
  private String host;

  @Value("${analytics.enabled:true}")
  private boolean enabled;

  @Value("${analytics.timeout:5000}")
  private int timeout;

  /** 分析服务HTTP客户端配置 */
  @Bean
  public WebClient analyticsWebClient() {
    return WebClient.builder().baseUrl(host).build();
  }

  public String getApiKey() {
    return apiKey;
  }

  public String getHost() {
    return host;
  }

  public boolean isEnabled() {
    return enabled;
  }

  public int getTimeout() {
    return timeout;
  }
}
