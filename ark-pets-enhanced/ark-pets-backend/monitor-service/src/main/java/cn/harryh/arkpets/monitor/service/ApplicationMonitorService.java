package cn.harryh.arkpets.monitor.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 应用监控服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class ApplicationMonitorService {

  private static final Logger logger = LoggerFactory.getLogger(ApplicationMonitorService.class);

  @Autowired private MeterRegistry meterRegistry;

  // 应用指标计数器
  private final AtomicLong totalRequests = new AtomicLong(0);
  private final AtomicLong successfulRequests = new AtomicLong(0);
  private final AtomicLong failedRequests = new AtomicLong(0);
  private final AtomicLong activeSessions = new AtomicLong(0);
  private final AtomicLong databaseConnections = new AtomicLong(0);

  // 响应时间统计
  private final Map<String, Timer.Sample> activeRequests = new ConcurrentHashMap<>();
  private final Map<String, Double> averageResponseTimes = new ConcurrentHashMap<>();

  // 错误统计
  private final Map<String, AtomicLong> errorCounts = new ConcurrentHashMap<>();

  public ApplicationMonitorService() {
    initializeMetrics();
  }

  /** 初始化应用指标 */
  private void initializeMetrics() {
    // 请求总数
    Gauge.builder("arkpets.app.requests.total", totalRequests, ref -> (double) ref.get())
        .description("请求总数")
        .register(meterRegistry);

    // 成功请求数
    Gauge.builder("arkpets.app.requests.successful", successfulRequests, ref -> (double) ref.get())
        .description("成功请求数")
        .register(meterRegistry);

    // 失败请求数
    Gauge.builder("arkpets.app.requests.failed", failedRequests, ref -> (double) ref.get())
        .description("失败请求数")
        .register(meterRegistry);

    // 错误率
    Gauge.builder("arkpets.app.error.rate", this, ApplicationMonitorService::calculateErrorRate)
        .description("错误率")
        .register(meterRegistry);

    // 活跃会话数
    Gauge.builder("arkpets.app.sessions.active", activeSessions, ref -> (double) ref.get())
        .description("活跃会话数")
        .register(meterRegistry);

    // 数据库连接数
    Gauge.builder(
            "arkpets.app.database.connections", databaseConnections, ref -> (double) ref.get())
        .description("数据库连接数")
        .register(meterRegistry);
  }

  /** 记录请求开始 */
  public void recordRequestStart(String requestId, String endpoint) {
    try {
      Timer.Sample sample = Timer.start(meterRegistry);
      activeRequests.put(requestId, sample);
      totalRequests.incrementAndGet();

      logger.debug("Request started: {} - {}", requestId, endpoint);

    } catch (Exception e) {
      logger.error("Error recording request start: {}", requestId, e);
    }
  }

  /** 记录请求结束 */
  public void recordRequestEnd(String requestId, String endpoint, boolean success) {
    try {
      Timer.Sample sample = activeRequests.remove(requestId);
      if (sample != null) {
        Timer timer =
            Timer.builder("arkpets.app.request.duration")
                .description("请求处理时间")
                .tag("endpoint", endpoint)
                .tag("status", success ? "success" : "error")
                .register(meterRegistry);

        long durationNanos = sample.stop(timer);

        // 更新平均响应时间
        averageResponseTimes.put(endpoint, durationNanos / 1_000_000.0); // 转换为毫秒
      }

      if (success) {
        successfulRequests.incrementAndGet();
      } else {
        failedRequests.incrementAndGet();
        recordError(endpoint);
      }

      logger.debug("Request ended: {} - {} (success: {})", requestId, endpoint, success);

    } catch (Exception e) {
      logger.error("Error recording request end: {}", requestId, e);
    }
  }

  /** 记录错误 */
  public void recordError(String endpoint) {
    try {
      errorCounts.computeIfAbsent(endpoint, k -> new AtomicLong(0)).incrementAndGet();

      Counter.builder("arkpets.app.errors")
          .description("应用错误计数")
          .tag("endpoint", endpoint)
          .register(meterRegistry)
          .increment();

      logger.debug("Error recorded for endpoint: {}", endpoint);

    } catch (Exception e) {
      logger.error("Error recording error for endpoint: {}", endpoint, e);
    }
  }

  /** 更新活跃会话数 */
  public void updateActiveSessions(long count) {
    activeSessions.set(count);
    logger.debug("Active sessions updated: {}", count);
  }

  /** 更新数据库连接数 */
  public void updateDatabaseConnections(long count) {
    databaseConnections.set(count);
    logger.debug("Database connections updated: {}", count);
  }

  /** 计算错误率 */
  private double calculateErrorRate() {
    long total = totalRequests.get();
    long failed = failedRequests.get();

    if (total == 0) {
      return 0.0;
    }

    return (double) failed / total * 100.0;
  }

  /** 定时收集应用指标 */
  @Scheduled(fixedRateString = "${monitor-service.application.collection-interval:10000}")
  public void collectApplicationMetrics() {
    try {
      logger.debug("Collecting application metrics...");

      // 这里可以添加更多的应用指标收集逻辑
      collectJvmMetrics();
      collectThreadMetrics();

      logger.debug("Application metrics collected successfully");

    } catch (Exception e) {
      logger.error("Error collecting application metrics", e);
    }
  }

  /** 收集JVM指标 */
  private void collectJvmMetrics() {
    try {
      Runtime runtime = Runtime.getRuntime();

      // JVM内存指标
      Gauge.builder("arkpets.jvm.memory.max", runtime, r -> (double) r.maxMemory())
          .description("JVM最大内存")
          .register(meterRegistry);

      Gauge.builder("arkpets.jvm.memory.total", runtime, r -> (double) r.totalMemory())
          .description("JVM总内存")
          .register(meterRegistry);

      Gauge.builder("arkpets.jvm.memory.free", runtime, r -> (double) r.freeMemory())
          .description("JVM空闲内存")
          .register(meterRegistry);

    } catch (Exception e) {
      logger.error("Error collecting JVM metrics", e);
    }
  }

  /** 收集线程指标 */
  private void collectThreadMetrics() {
    try {
      ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
      while (rootGroup.getParent() != null) {
        rootGroup = rootGroup.getParent();
      }

      int activeThreads = rootGroup.activeCount();

      Gauge.builder("arkpets.jvm.threads.active", this, obj -> (double) activeThreads)
          .description("活跃线程数")
          .register(meterRegistry);

    } catch (Exception e) {
      logger.error("Error collecting thread metrics", e);
    }
  }

  /** 获取应用监控摘要 */
  public Map<String, Object> getApplicationSummary() {
    Map<String, Object> summary = new HashMap<>();

    try {
      summary.put("total_requests", totalRequests.get());
      summary.put("successful_requests", successfulRequests.get());
      summary.put("failed_requests", failedRequests.get());
      summary.put("error_rate", calculateErrorRate());
      summary.put("active_sessions", activeSessions.get());
      summary.put("database_connections", databaseConnections.get());
      summary.put("average_response_times", new HashMap<>(averageResponseTimes));
      summary.put(
          "error_counts",
          errorCounts.entrySet().stream()
              .collect(
                  HashMap::new,
                  (map, entry) -> map.put(entry.getKey(), entry.getValue().get()),
                  HashMap::putAll));

      // JVM信息
      Runtime runtime = Runtime.getRuntime();
      Map<String, Object> jvmInfo = new HashMap<>();
      jvmInfo.put("max_memory", runtime.maxMemory());
      jvmInfo.put("total_memory", runtime.totalMemory());
      jvmInfo.put("free_memory", runtime.freeMemory());
      jvmInfo.put("used_memory", runtime.totalMemory() - runtime.freeMemory());
      summary.put("jvm", jvmInfo);

    } catch (Exception e) {
      logger.error("Error getting application summary", e);
      summary.put("error", "Failed to retrieve application metrics");
    }

    return summary;
  }

  /** 检查应用健康状态 */
  public boolean isApplicationHealthy() {
    try {
      double errorRate = calculateErrorRate();
      long activeSessions = this.activeSessions.get();

      // 健康阈值检查
      boolean errorRateHealthy = errorRate < 5.0; // 错误率小于5%
      boolean sessionsHealthy = activeSessions < 10000; // 活跃会话数小于10000

      return errorRateHealthy && sessionsHealthy;

    } catch (Exception e) {
      logger.error("Error checking application health", e);
      return false;
    }
  }

  /** 重置统计数据 */
  public void resetStatistics() {
    totalRequests.set(0);
    successfulRequests.set(0);
    failedRequests.set(0);
    activeRequests.clear();
    averageResponseTimes.clear();
    errorCounts.clear();

    logger.info("Application statistics reset");
  }
}
