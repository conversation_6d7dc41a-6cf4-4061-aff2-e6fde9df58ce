package cn.harryh.arkpets.monitor.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;
import org.hibernate.annotations.CreationTimestamp;

/**
 * 性能指标实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "performance_metrics")
public class PerformanceMetric {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "服务名称不能为空")
  @Column(name = "service_name", nullable = false, length = 50)
  private String serviceName;

  @NotBlank(message = "指标名称不能为空")
  @Column(name = "metric_name", nullable = false, length = 100)
  private String metricName;

  @NotNull(message = "指标类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private MetricType metricType;

  @NotNull(message = "指标值不能为空")
  @Column(name = "metric_value", nullable = false)
  private Double metricValue;

  @Column(name = "metric_unit", length = 20)
  private String metricUnit;

  @Column(name = "instance_id", length = 50)
  private String instanceId;

  @Column(name = "endpoint", length = 200)
  private String endpoint;

  @Column(name = "method", length = 10)
  private String method;

  @Column(name = "status_code")
  private Integer statusCode;

  @Column(name = "response_time_ms")
  private Long responseTimeMs;

  @Column(name = "memory_usage_mb")
  private Double memoryUsageMb;

  @Column(name = "cpu_usage_percent")
  private Double cpuUsagePercent;

  @Column(name = "thread_count")
  private Integer threadCount;

  @Column(name = "gc_count")
  private Long gcCount;

  @Column(name = "gc_time_ms")
  private Long gcTimeMs;

  @ElementCollection
  @CollectionTable(name = "performance_metric_tags", joinColumns = @JoinColumn(name = "metric_id"))
  @MapKeyColumn(name = "tag_key")
  @Column(name = "tag_value")
  private Map<String, String> tags;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  // 指标类型枚举
  public enum MetricType {
    COUNTER("计数器"),
    GAUGE("仪表"),
    HISTOGRAM("直方图"),
    TIMER("计时器"),
    SUMMARY("摘要");

    private final String displayName;

    MetricType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public PerformanceMetric() {}

  public PerformanceMetric(
      String serviceName, String metricName, MetricType metricType, Double metricValue) {
    this.serviceName = serviceName;
    this.metricName = metricName;
    this.metricType = metricType;
    this.metricValue = metricValue;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getServiceName() {
    return serviceName;
  }

  public void setServiceName(String serviceName) {
    this.serviceName = serviceName;
  }

  public String getMetricName() {
    return metricName;
  }

  public void setMetricName(String metricName) {
    this.metricName = metricName;
  }

  public MetricType getMetricType() {
    return metricType;
  }

  public void setMetricType(MetricType metricType) {
    this.metricType = metricType;
  }

  public Double getMetricValue() {
    return metricValue;
  }

  public void setMetricValue(Double metricValue) {
    this.metricValue = metricValue;
  }

  public String getMetricUnit() {
    return metricUnit;
  }

  public void setMetricUnit(String metricUnit) {
    this.metricUnit = metricUnit;
  }

  public String getInstanceId() {
    return instanceId;
  }

  public void setInstanceId(String instanceId) {
    this.instanceId = instanceId;
  }

  public String getEndpoint() {
    return endpoint;
  }

  public void setEndpoint(String endpoint) {
    this.endpoint = endpoint;
  }

  public String getMethod() {
    return method;
  }

  public void setMethod(String method) {
    this.method = method;
  }

  public Integer getStatusCode() {
    return statusCode;
  }

  public void setStatusCode(Integer statusCode) {
    this.statusCode = statusCode;
  }

  public Long getResponseTimeMs() {
    return responseTimeMs;
  }

  public void setResponseTimeMs(Long responseTimeMs) {
    this.responseTimeMs = responseTimeMs;
  }

  public Double getMemoryUsageMb() {
    return memoryUsageMb;
  }

  public void setMemoryUsageMb(Double memoryUsageMb) {
    this.memoryUsageMb = memoryUsageMb;
  }

  public Double getCpuUsagePercent() {
    return cpuUsagePercent;
  }

  public void setCpuUsagePercent(Double cpuUsagePercent) {
    this.cpuUsagePercent = cpuUsagePercent;
  }

  public Integer getThreadCount() {
    return threadCount;
  }

  public void setThreadCount(Integer threadCount) {
    this.threadCount = threadCount;
  }

  public Long getGcCount() {
    return gcCount;
  }

  public void setGcCount(Long gcCount) {
    this.gcCount = gcCount;
  }

  public Long getGcTimeMs() {
    return gcTimeMs;
  }

  public void setGcTimeMs(Long gcTimeMs) {
    this.gcTimeMs = gcTimeMs;
  }

  public Map<String, String> getTags() {
    return tags;
  }

  public void setTags(Map<String, String> tags) {
    this.tags = tags;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
}
