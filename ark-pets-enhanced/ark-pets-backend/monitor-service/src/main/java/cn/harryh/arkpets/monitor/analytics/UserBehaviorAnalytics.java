package cn.harryh.arkpets.monitor.analytics;

import cn.harryh.arkpets.monitor.entity.UserBehaviorEvent;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 用户行为分析服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class UserBehaviorAnalytics {

  private static final Logger logger = LoggerFactory.getLogger(UserBehaviorAnalytics.class);

  @Autowired private RedisTemplate<String, Object> redisTemplate;

  private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
  private final DateTimeFormatter hourFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH");

  /** 记录用户行为事件 */
  public void recordEvent(UserBehaviorEvent event) {
    try {
      String today = LocalDateTime.now().format(dateFormatter);
      String currentHour = LocalDateTime.now().format(hourFormatter);

      // 1. 记录每日活跃用户
      String dailyActiveUsersKey = "analytics:daily_active_users:" + today;
      redisTemplate.opsForSet().add(dailyActiveUsersKey, event.getUserId());
      redisTemplate.expire(dailyActiveUsersKey, 30, TimeUnit.DAYS);

      // 2. 记录每小时活跃用户
      String hourlyActiveUsersKey = "analytics:hourly_active_users:" + currentHour;
      redisTemplate.opsForSet().add(hourlyActiveUsersKey, event.getUserId());
      redisTemplate.expire(hourlyActiveUsersKey, 7, TimeUnit.DAYS);

      // 3. 记录事件计数
      String eventCountKey = "analytics:event_count:" + today + ":" + event.getEventType();
      redisTemplate.opsForValue().increment(eventCountKey);
      redisTemplate.expire(eventCountKey, 30, TimeUnit.DAYS);

      // 4. 记录用户会话
      String sessionKey =
          "analytics:user_session:" + event.getUserId() + ":" + event.getSessionId();
      redisTemplate.opsForHash().put(sessionKey, "lastActivity", System.currentTimeMillis());
      redisTemplate
          .opsForHash()
          .put(
              sessionKey,
              "eventCount",
              redisTemplate.opsForHash().increment(sessionKey, "eventCount", 1));
      redisTemplate.expire(sessionKey, 24, TimeUnit.HOURS);

      // 5. 记录页面访问统计
      if (event.getEventType() == UserBehaviorEvent.EventType.PAGE_VIEW
          && event.getPageUrl() != null) {
        String pageViewKey = "analytics:page_views:" + today;
        redisTemplate.opsForHash().increment(pageViewKey, event.getPageUrl(), 1);
        redisTemplate.expire(pageViewKey, 30, TimeUnit.DAYS);
      }

      // 6. 记录设备类型统计
      if (event.getDeviceType() != null) {
        String deviceTypeKey = "analytics:device_types:" + today;
        redisTemplate.opsForHash().increment(deviceTypeKey, event.getDeviceType(), 1);
        redisTemplate.expire(deviceTypeKey, 30, TimeUnit.DAYS);
      }

      // 7. 记录用户路径分析
      recordUserPath(event);

      logger.debug(
          "记录用户行为事件: userId={}, eventType={}, eventName={}",
          event.getUserId(),
          event.getEventType(),
          event.getEventName());

    } catch (Exception e) {
      logger.error("记录用户行为事件失败", e);
    }
  }

  /** 记录用户路径分析 */
  private void recordUserPath(UserBehaviorEvent event) {
    if (event.getEventType() == UserBehaviorEvent.EventType.PAGE_VIEW
        && event.getPageUrl() != null) {
      String userPathKey = "analytics:user_path:" + event.getUserId() + ":" + event.getSessionId();

      // 获取当前路径
      List<Object> currentPath = redisTemplate.opsForList().range(userPathKey, 0, -1);

      // 添加新页面
      redisTemplate.opsForList().rightPush(userPathKey, event.getPageUrl());

      // 限制路径长度
      if (currentPath != null && currentPath.size() >= 50) {
        redisTemplate.opsForList().leftPop(userPathKey);
      }

      redisTemplate.expire(userPathKey, 24, TimeUnit.HOURS);
    }
  }

  /** 获取每日活跃用户数 */
  public Map<String, Long> getDailyActiveUsers(int days) {
    Map<String, Long> result = new LinkedHashMap<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = days - 1; i >= 0; i--) {
      String date = now.minusDays(i).format(dateFormatter);
      String key = "analytics:daily_active_users:" + date;
      Long count = redisTemplate.opsForSet().size(key);
      result.put(date, count != null ? count : 0L);
    }

    return result;
  }

  /** 获取每小时活跃用户数 */
  public Map<String, Long> getHourlyActiveUsers(int hours) {
    Map<String, Long> result = new LinkedHashMap<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = hours - 1; i >= 0; i--) {
      String hour = now.minusHours(i).format(hourFormatter);
      String key = "analytics:hourly_active_users:" + hour;
      Long count = redisTemplate.opsForSet().size(key);
      result.put(hour, count != null ? count : 0L);
    }

    return result;
  }

  /** 获取事件统计 */
  public Map<String, Map<String, Long>> getEventStatistics(int days) {
    Map<String, Map<String, Long>> result = new LinkedHashMap<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = days - 1; i >= 0; i--) {
      String date = now.minusDays(i).format(dateFormatter);
      Map<String, Long> dayStats = new HashMap<>();

      for (UserBehaviorEvent.EventType eventType : UserBehaviorEvent.EventType.values()) {
        String key = "analytics:event_count:" + date + ":" + eventType;
        Object count = redisTemplate.opsForValue().get(key);
        dayStats.put(eventType.name(), count != null ? Long.valueOf(count.toString()) : 0L);
      }

      result.put(date, dayStats);
    }

    return result;
  }

  /** 获取页面访问统计 */
  public Map<String, Long> getPageViewStatistics(String date) {
    String key = "analytics:page_views:" + date;
    Map<Object, Object> pageViews = redisTemplate.opsForHash().entries(key);

    return pageViews.entrySet().stream()
        .collect(
            Collectors.toMap(
                entry -> entry.getKey().toString(),
                entry -> Long.valueOf(entry.getValue().toString()),
                (e1, e2) -> e1,
                LinkedHashMap::new));
  }

  /** 获取设备类型统计 */
  public Map<String, Long> getDeviceTypeStatistics(String date) {
    String key = "analytics:device_types:" + date;
    Map<Object, Object> deviceTypes = redisTemplate.opsForHash().entries(key);

    return deviceTypes.entrySet().stream()
        .collect(
            Collectors.toMap(
                entry -> entry.getKey().toString(),
                entry -> Long.valueOf(entry.getValue().toString()),
                (e1, e2) -> e1,
                LinkedHashMap::new));
  }

  /** 获取用户会话信息 */
  public Map<String, Object> getUserSessionInfo(String userId, String sessionId) {
    String key = "analytics:user_session:" + userId + ":" + sessionId;
    Map<Object, Object> sessionData = redisTemplate.opsForHash().entries(key);

    Map<String, Object> result = new HashMap<>();
    for (Map.Entry<Object, Object> entry : sessionData.entrySet()) {
      result.put(entry.getKey().toString(), entry.getValue());
    }

    return result;
  }

  /** 获取用户路径分析 */
  public List<String> getUserPath(String userId, String sessionId) {
    String key = "analytics:user_path:" + userId + ":" + sessionId;
    List<Object> path = redisTemplate.opsForList().range(key, 0, -1);

    return path != null
        ? path.stream().map(Object::toString).collect(Collectors.toList())
        : new ArrayList<>();
  }

  /** 获取实时在线用户数 */
  public long getOnlineUserCount() {
    String pattern = "analytics:user_session:*";
    Set<String> keys = redisTemplate.keys(pattern);

    if (keys == null) return 0;

    long onlineCount = 0;
    long currentTime = System.currentTimeMillis();
    long fiveMinutesAgo = currentTime - (5 * 60 * 1000); // 5分钟前

    for (String key : keys) {
      Object lastActivity = redisTemplate.opsForHash().get(key, "lastActivity");
      if (lastActivity != null) {
        long lastActivityTime = Long.parseLong(lastActivity.toString());
        if (lastActivityTime > fiveMinutesAgo) {
          onlineCount++;
        }
      }
    }

    return onlineCount;
  }

  /** 获取用户留存率 */
  public Map<String, Double> getUserRetentionRate(int days) {
    Map<String, Double> retentionRates = new LinkedHashMap<>();
    LocalDateTime now = LocalDateTime.now();

    for (int i = 1; i <= days; i++) {
      String baseDate = now.minusDays(i + 1).format(dateFormatter);
      String targetDate = now.minusDays(i).format(dateFormatter);

      String baseKey = "analytics:daily_active_users:" + baseDate;
      String targetKey = "analytics:daily_active_users:" + targetDate;

      Set<Object> baseUsers = redisTemplate.opsForSet().members(baseKey);
      Set<Object> targetUsers = redisTemplate.opsForSet().members(targetKey);

      if (baseUsers != null && !baseUsers.isEmpty() && targetUsers != null) {
        long retainedUsers =
            baseUsers.stream().mapToLong(user -> targetUsers.contains(user) ? 1 : 0).sum();

        double retentionRate = (double) retainedUsers / baseUsers.size() * 100;
        retentionRates.put(targetDate, retentionRate);
      } else {
        retentionRates.put(targetDate, 0.0);
      }
    }

    return retentionRates;
  }

  /** 清理过期数据 */
  public void cleanupExpiredData() {
    try {
      // 清理30天前的数据
      LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
      String expiredDate = thirtyDaysAgo.format(dateFormatter);

      String[] patterns = {
        "analytics:daily_active_users:" + expiredDate,
        "analytics:event_count:" + expiredDate + ":*",
        "analytics:page_views:" + expiredDate,
        "analytics:device_types:" + expiredDate
      };

      for (String pattern : patterns) {
        Set<String> keys = redisTemplate.keys(pattern);
        if (keys != null && !keys.isEmpty()) {
          redisTemplate.delete(keys);
          logger.info("清理过期分析数据: {} 个key", keys.size());
        }
      }

    } catch (Exception e) {
      logger.error("清理过期分析数据失败", e);
    }
  }
}
