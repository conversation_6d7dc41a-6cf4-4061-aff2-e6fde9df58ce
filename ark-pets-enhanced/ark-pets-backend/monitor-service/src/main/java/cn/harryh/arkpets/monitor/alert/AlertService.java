package cn.harryh.arkpets.monitor.alert;

import cn.harryh.arkpets.monitor.entity.AlertRule;
import cn.harryh.arkpets.monitor.entity.PerformanceMetric;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 智能告警服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class AlertService {

  private static final Logger logger = LoggerFactory.getLogger(AlertService.class);

  @Autowired private RedisTemplate<String, Object> redisTemplate;

  private final DateTimeFormatter timeFormatter =
      DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

  /** 评估告警规则 */
  public void evaluateAlertRules(List<AlertRule> rules, PerformanceMetric metric) {
    for (AlertRule rule : rules) {
      if (!rule.getIsEnabled() || !rule.getMetricName().equals(metric.getMetricName())) {
        continue;
      }

      try {
        evaluateRule(rule, metric);
      } catch (Exception e) {
        logger.error("评估告警规则失败: ruleId={}", rule.getId(), e);
      }
    }
  }

  /** 评估单个告警规则 */
  private void evaluateRule(AlertRule rule, PerformanceMetric metric) {
    String ruleKey = "alert:rule:" + rule.getId();
    String triggerCountKey = ruleKey + ":trigger_count";
    String lastEvaluationKey = ruleKey + ":last_evaluation";

    // 检查是否满足触发条件
    boolean shouldTrigger = rule.shouldTrigger(metric.getMetricValue());

    if (shouldTrigger) {
      // 增加触发计数
      Long currentTriggerCount = redisTemplate.opsForValue().increment(triggerCountKey);
      redisTemplate.expire(triggerCountKey, rule.getEvaluationWindowMinutes(), TimeUnit.MINUTES);

      // 检查是否达到触发阈值
      if (currentTriggerCount >= rule.getTriggerCount()) {
        triggerAlert(rule, metric, currentTriggerCount);
        // 重置计数器
        redisTemplate.delete(triggerCountKey);
      }
    } else {
      // 条件不满足，重置计数器
      redisTemplate.delete(triggerCountKey);
    }

    // 记录最后评估时间
    redisTemplate.opsForValue().set(lastEvaluationKey, System.currentTimeMillis());
    redisTemplate.expire(lastEvaluationKey, 24, TimeUnit.HOURS);
  }

  /** 触发告警 */
  private void triggerAlert(AlertRule rule, PerformanceMetric metric, Long triggerCount) {
    try {
      // 检查告警抑制
      if (isAlertSuppressed(rule)) {
        logger.debug("告警被抑制: ruleId={}", rule.getId());
        return;
      }

      // 创建告警事件
      Map<String, Object> alertEvent = createAlertEvent(rule, metric, triggerCount);

      // 发送告警通知
      sendAlertNotifications(rule, alertEvent);

      // 记录告警历史
      recordAlertHistory(rule, alertEvent);

      // 设置告警抑制
      setAlertSuppression(rule);

      logger.warn(
          "触发告警: rule={}, metric={}, value={}, threshold={}",
          rule.getName(),
          metric.getMetricName(),
          metric.getMetricValue(),
          rule.getThreshold());

    } catch (Exception e) {
      logger.error("触发告警失败: ruleId={}", rule.getId(), e);
    }
  }

  /** 创建告警事件 */
  private Map<String, Object> createAlertEvent(
      AlertRule rule, PerformanceMetric metric, Long triggerCount) {
    Map<String, Object> event = new HashMap<>();
    event.put("id", UUID.randomUUID().toString());
    event.put("ruleId", rule.getId());
    event.put("ruleName", rule.getName());
    event.put("metricName", metric.getMetricName());
    event.put("metricValue", metric.getMetricValue());
    event.put("threshold", rule.getThreshold());
    event.put("operator", rule.getOperator().getSymbol());
    event.put("severity", rule.getSeverity().name());
    event.put("serviceName", metric.getServiceName());
    event.put("instanceId", metric.getInstanceId());
    event.put("triggerCount", triggerCount);
    event.put("timestamp", LocalDateTime.now().format(timeFormatter));
    event.put("description", generateAlertDescription(rule, metric));

    return event;
  }

  /** 生成告警描述 */
  private String generateAlertDescription(AlertRule rule, PerformanceMetric metric) {
    return String.format(
        "服务 %s 的指标 %s 当前值 %.2f %s 阈值 %.2f，触发了告警规则 '%s'",
        metric.getServiceName(),
        metric.getMetricName(),
        metric.getMetricValue(),
        rule.getOperator().getSymbol(),
        rule.getThreshold(),
        rule.getName());
  }

  /** 发送告警通知 */
  private void sendAlertNotifications(AlertRule rule, Map<String, Object> alertEvent) {
    if (rule.getNotificationChannels() == null || rule.getNotificationChannels().isEmpty()) {
      return;
    }

    for (String channel : rule.getNotificationChannels()) {
      try {
        switch (channel.toLowerCase()) {
          case "email" -> sendEmailNotification(alertEvent);
          case "webhook" -> sendWebhookNotification(alertEvent);
          case "slack" -> sendSlackNotification(alertEvent);
          case "dingtalk" -> sendDingTalkNotification(alertEvent);
          default -> logger.warn("不支持的通知渠道: {}", channel);
        }
      } catch (Exception e) {
        logger.error("发送告警通知失败: channel={}", channel, e);
      }
    }
  }

  /** 发送邮件通知 */
  private void sendEmailNotification(Map<String, Object> alertEvent) {
    // 这里应该集成邮件发送服务
    logger.info("发送邮件告警通知: {}", alertEvent.get("description"));
  }

  /** 发送Webhook通知 */
  private void sendWebhookNotification(Map<String, Object> alertEvent) {
    // 这里应该发送HTTP请求到Webhook URL
    logger.info("发送Webhook告警通知: {}", alertEvent.get("description"));
  }

  /** 发送Slack通知 */
  private void sendSlackNotification(Map<String, Object> alertEvent) {
    // 这里应该集成Slack API
    logger.info("发送Slack告警通知: {}", alertEvent.get("description"));
  }

  /** 发送钉钉通知 */
  private void sendDingTalkNotification(Map<String, Object> alertEvent) {
    // 这里应该集成钉钉机器人API
    logger.info("发送钉钉告警通知: {}", alertEvent.get("description"));
  }

  /** 记录告警历史 */
  private void recordAlertHistory(AlertRule rule, Map<String, Object> alertEvent) {
    String historyKey =
        "alert:history:" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    redisTemplate.opsForList().leftPush(historyKey, alertEvent);

    // 限制历史记录数量
    redisTemplate.opsForList().trim(historyKey, 0, 999);
    redisTemplate.expire(historyKey, 30, TimeUnit.DAYS);

    // 更新规则统计
    rule.incrementTriggerCount();
  }

  /** 检查告警抑制 */
  private boolean isAlertSuppressed(AlertRule rule) {
    String suppressionKey = "alert:suppression:" + rule.getId();
    return Boolean.TRUE.equals(redisTemplate.hasKey(suppressionKey));
  }

  /** 设置告警抑制 */
  private void setAlertSuppression(AlertRule rule) {
    String suppressionKey = "alert:suppression:" + rule.getId();
    int suppressionMinutes = calculateSuppressionTime(rule.getSeverity());

    redisTemplate
        .opsForValue()
        .set(suppressionKey, "suppressed", suppressionMinutes, TimeUnit.MINUTES);
  }

  /** 计算抑制时间 */
  private int calculateSuppressionTime(AlertRule.Severity severity) {
    return switch (severity) {
      case CRITICAL -> 5; // 严重告警5分钟抑制
      case ERROR -> 10; // 错误告警10分钟抑制
      case WARNING -> 15; // 警告告警15分钟抑制
      case INFO -> 30; // 信息告警30分钟抑制
    };
  }

  /** 获取告警历史 */
  public List<Map<String, Object>> getAlertHistory(String date, int limit) {
    String historyKey = "alert:history:" + date;
    List<Object> history = redisTemplate.opsForList().range(historyKey, 0, limit - 1);

    List<Map<String, Object>> result = new ArrayList<>();
    if (history != null) {
      for (Object item : history) {
        if (item instanceof Map) {
          @SuppressWarnings("unchecked")
          Map<String, Object> alertEvent = (Map<String, Object>) item;
          result.add(alertEvent);
        }
      }
    }

    return result;
  }

  /** 获取告警统计 */
  public Map<String, Object> getAlertStatistics(int days) {
    Map<String, Object> stats = new HashMap<>();
    LocalDateTime now = LocalDateTime.now();

    Map<String, Long> dailyAlertCounts = new LinkedHashMap<>();
    Map<String, Long> severityCounts = new HashMap<>();

    for (int i = days - 1; i >= 0; i--) {
      String date = now.minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
      String historyKey = "alert:history:" + date;

      Long count = redisTemplate.opsForList().size(historyKey);
      dailyAlertCounts.put(date, count != null ? count : 0L);

      // 统计严重程度
      List<Object> alerts = redisTemplate.opsForList().range(historyKey, 0, -1);
      if (alerts != null) {
        for (Object alert : alerts) {
          if (alert instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> alertMap = (Map<String, Object>) alert;
            String severity = (String) alertMap.get("severity");
            severityCounts.merge(severity, 1L, Long::sum);
          }
        }
      }
    }

    stats.put("dailyAlertCounts", dailyAlertCounts);
    stats.put("severityCounts", severityCounts);
    stats.put("totalAlerts", dailyAlertCounts.values().stream().mapToLong(Long::longValue).sum());

    return stats;
  }

  /** 异常检测 */
  public boolean detectAnomaly(String metricName, Double currentValue, int windowSize) {
    String historyKey = "metric:history:" + metricName;

    // 获取历史数据
    List<Object> history = redisTemplate.opsForList().range(historyKey, 0, windowSize - 1);
    if (history == null || history.size() < windowSize / 2) {
      return false; // 数据不足，无法检测异常
    }

    // 计算统计指标
    List<Double> values = history.stream().map(obj -> Double.valueOf(obj.toString())).toList();

    double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    double variance = values.stream().mapToDouble(v -> Math.pow(v - mean, 2)).average().orElse(0.0);
    double stdDev = Math.sqrt(variance);

    // 使用3-sigma规则检测异常
    double threshold = 3.0;
    return Math.abs(currentValue - mean) > threshold * stdDev;
  }

  /** 更新指标历史 */
  public void updateMetricHistory(String metricName, Double value) {
    String historyKey = "metric:history:" + metricName;

    redisTemplate.opsForList().leftPush(historyKey, value);
    redisTemplate.opsForList().trim(historyKey, 0, 99); // 保留最近100个值
    redisTemplate.expire(historyKey, 24, TimeUnit.HOURS);
  }
}
