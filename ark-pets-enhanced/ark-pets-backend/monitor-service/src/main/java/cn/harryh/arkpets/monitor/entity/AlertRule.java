package cn.harryh.arkpets.monitor.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 告警规则实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "alert_rules")
public class AlertRule {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "规则名称不能为空")
  @Column(nullable = false, length = 100)
  private String name;

  @Column(columnDefinition = "TEXT")
  private String description;

  @NotBlank(message = "指标名称不能为空")
  @Column(name = "metric_name", nullable = false, length = 100)
  private String metricName;

  @NotNull(message = "条件类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private ConditionType conditionType;

  @NotNull(message = "阈值不能为空")
  @Column(nullable = false)
  private Double threshold;

  @NotNull(message = "比较操作符不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private ComparisonOperator operator;

  @NotNull(message = "严重程度不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private Severity severity;

  @Column(name = "evaluation_window_minutes", nullable = false)
  private Integer evaluationWindowMinutes = 5;

  @Column(name = "trigger_count", nullable = false)
  private Integer triggerCount = 1;

  @Column(name = "is_enabled", nullable = false)
  private Boolean isEnabled = true;

  @ElementCollection
  @CollectionTable(name = "alert_rule_tags", joinColumns = @JoinColumn(name = "rule_id"))
  @Column(name = "tag")
  private List<String> tags;

  @ElementCollection
  @CollectionTable(
      name = "alert_rule_notification_channels",
      joinColumns = @JoinColumn(name = "rule_id"))
  @Column(name = "channel")
  private List<String> notificationChannels;

  @Column(name = "last_triggered_at")
  private LocalDateTime lastTriggeredAt;

  @Column(name = "trigger_count_total")
  private Long triggerCountTotal = 0L;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  // 条件类型枚举
  public enum ConditionType {
    THRESHOLD("阈值"),
    RATE_OF_CHANGE("变化率"),
    ANOMALY("异常检测"),
    PATTERN("模式匹配");

    private final String displayName;

    ConditionType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 比较操作符枚举
  public enum ComparisonOperator {
    GREATER_THAN(">"),
    GREATER_THAN_OR_EQUAL(">="),
    LESS_THAN("<"),
    LESS_THAN_OR_EQUAL("<="),
    EQUAL("="),
    NOT_EQUAL("!=");

    private final String symbol;

    ComparisonOperator(String symbol) {
      this.symbol = symbol;
    }

    public String getSymbol() {
      return symbol;
    }
  }

  // 严重程度枚举
  public enum Severity {
    INFO("信息"),
    WARNING("警告"),
    ERROR("错误"),
    CRITICAL("严重");

    private final String displayName;

    Severity(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public AlertRule() {}

  public AlertRule(
      String name,
      String metricName,
      ConditionType conditionType,
      Double threshold,
      ComparisonOperator operator,
      Severity severity) {
    this.name = name;
    this.metricName = metricName;
    this.conditionType = conditionType;
    this.threshold = threshold;
    this.operator = operator;
    this.severity = severity;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getMetricName() {
    return metricName;
  }

  public void setMetricName(String metricName) {
    this.metricName = metricName;
  }

  public ConditionType getConditionType() {
    return conditionType;
  }

  public void setConditionType(ConditionType conditionType) {
    this.conditionType = conditionType;
  }

  public Double getThreshold() {
    return threshold;
  }

  public void setThreshold(Double threshold) {
    this.threshold = threshold;
  }

  public ComparisonOperator getOperator() {
    return operator;
  }

  public void setOperator(ComparisonOperator operator) {
    this.operator = operator;
  }

  public Severity getSeverity() {
    return severity;
  }

  public void setSeverity(Severity severity) {
    this.severity = severity;
  }

  public Integer getEvaluationWindowMinutes() {
    return evaluationWindowMinutes;
  }

  public void setEvaluationWindowMinutes(Integer evaluationWindowMinutes) {
    this.evaluationWindowMinutes = evaluationWindowMinutes;
  }

  public Integer getTriggerCount() {
    return triggerCount;
  }

  public void setTriggerCount(Integer triggerCount) {
    this.triggerCount = triggerCount;
  }

  public Boolean getIsEnabled() {
    return isEnabled;
  }

  public void setIsEnabled(Boolean isEnabled) {
    this.isEnabled = isEnabled;
  }

  public List<String> getTags() {
    return tags;
  }

  public void setTags(List<String> tags) {
    this.tags = tags;
  }

  public List<String> getNotificationChannels() {
    return notificationChannels;
  }

  public void setNotificationChannels(List<String> notificationChannels) {
    this.notificationChannels = notificationChannels;
  }

  public LocalDateTime getLastTriggeredAt() {
    return lastTriggeredAt;
  }

  public void setLastTriggeredAt(LocalDateTime lastTriggeredAt) {
    this.lastTriggeredAt = lastTriggeredAt;
  }

  public Long getTriggerCountTotal() {
    return triggerCountTotal;
  }

  public void setTriggerCountTotal(Long triggerCountTotal) {
    this.triggerCountTotal = triggerCountTotal;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  // 业务方法
  public void incrementTriggerCount() {
    this.triggerCountTotal++;
    this.lastTriggeredAt = LocalDateTime.now();
  }

  public boolean shouldTrigger(Double currentValue) {
    return switch (operator) {
      case GREATER_THAN -> currentValue > threshold;
      case GREATER_THAN_OR_EQUAL -> currentValue >= threshold;
      case LESS_THAN -> currentValue < threshold;
      case LESS_THAN_OR_EQUAL -> currentValue <= threshold;
      case EQUAL -> currentValue.equals(threshold);
      case NOT_EQUAL -> !currentValue.equals(threshold);
    };
  }
}
