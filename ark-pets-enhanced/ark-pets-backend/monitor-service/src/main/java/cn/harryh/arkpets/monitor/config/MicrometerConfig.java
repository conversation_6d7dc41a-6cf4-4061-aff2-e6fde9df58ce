package cn.harryh.arkpets.monitor.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import java.util.concurrent.atomic.AtomicLong;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Micrometer监控配置
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class MicrometerConfig {

  @Autowired private MeterRegistry meterRegistry;

  // 业务指标计数器
  private final AtomicLong userLoginCount = new AtomicLong(0);
  private final AtomicLong aiChatCount = new AtomicLong(0);
  private final AtomicLong cacheHitCount = new AtomicLong(0);
  private final AtomicLong cacheMissCount = new AtomicLong(0);

  /** 用户登录计数器 */
  @Bean
  public Counter userLoginCounter() {
    return Counter.builder("arkpets.user.login.count")
        .description("用户登录次数")
        .tag("service", "auth")
        .register(meterRegistry);
  }

  /** AI聊天计数器 */
  @Bean
  public Counter aiChatCounter() {
    return Counter.builder("arkpets.ai.chat.count")
        .description("AI聊天次数")
        .tag("service", "ai")
        .register(meterRegistry);
  }

  /** 缓存命中计数器 */
  @Bean
  public Counter cacheHitCounter() {
    return Counter.builder("arkpets.cache.hit.count")
        .description("缓存命中次数")
        .tag("service", "cache")
        .register(meterRegistry);
  }

  /** 缓存未命中计数器 */
  @Bean
  public Counter cacheMissCounter() {
    return Counter.builder("arkpets.cache.miss.count")
        .description("缓存未命中次数")
        .tag("service", "cache")
        .register(meterRegistry);
  }

  /** API响应时间计时器 */
  @Bean
  public Timer apiResponseTimer() {
    return Timer.builder("arkpets.api.response.time")
        .description("API响应时间")
        .register(meterRegistry);
  }

  /** 数据库连接池大小监控 */
  @Bean
  public Gauge databaseConnectionPoolGauge() {
    return Gauge.builder(
            "arkpets.database.connection.pool.size",
            this,
            MicrometerConfig::getCurrentConnectionPoolSize)
        .description("数据库连接池大小")
        .register(meterRegistry);
  }

  /** 活跃用户数监控 */
  @Bean
  public Gauge activeUsersGauge() {
    return Gauge.builder(
            "arkpets.users.active.count", this, MicrometerConfig::getCurrentActiveUsers)
        .description("当前活跃用户数")
        .register(meterRegistry);
  }

  /** 缓存命中率监控 */
  @Bean
  public Gauge cacheHitRateGauge() {
    return Gauge.builder("arkpets.cache.hit.rate", this, MicrometerConfig::calculateCacheHitRate)
        .description("缓存命中率")
        .register(meterRegistry);
  }

  /** JVM内存使用率监控 */
  @Bean
  public Gauge jvmMemoryUsageGauge() {
    return Gauge.builder(
            "arkpets.jvm.memory.usage.rate", this, MicrometerConfig::getJvmMemoryUsageRate)
        .description("JVM内存使用率")
        .register(meterRegistry);
  }

  /** 获取当前数据库连接池大小 */
  private double getCurrentConnectionPoolSize() {
    // 这里应该从实际的连接池获取数据
    // 暂时返回模拟数据
    return 10.0;
  }

  /** 获取当前活跃用户数 */
  private double getCurrentActiveUsers() {
    // 这里应该从Redis或数据库获取实际的活跃用户数
    // 暂时返回模拟数据
    return userLoginCount.get();
  }

  /** 计算缓存命中率 */
  private double calculateCacheHitRate() {
    long hits = cacheHitCount.get();
    long misses = cacheMissCount.get();
    long total = hits + misses;

    if (total == 0) {
      return 0.0;
    }

    return (double) hits / total * 100.0;
  }

  /** 获取JVM内存使用率 */
  private double getJvmMemoryUsageRate() {
    Runtime runtime = Runtime.getRuntime();
    long maxMemory = runtime.maxMemory();
    long totalMemory = runtime.totalMemory();
    long freeMemory = runtime.freeMemory();
    long usedMemory = totalMemory - freeMemory;

    return (double) usedMemory / maxMemory * 100.0;
  }

  /** 增加用户登录计数 */
  public void incrementUserLogin() {
    userLoginCount.incrementAndGet();
  }

  /** 增加AI聊天计数 */
  public void incrementAiChat() {
    aiChatCount.incrementAndGet();
  }

  /** 增加缓存命中计数 */
  public void incrementCacheHit() {
    cacheHitCount.incrementAndGet();
  }

  /** 增加缓存未命中计数 */
  public void incrementCacheMiss() {
    cacheMissCount.incrementAndGet();
  }
}
