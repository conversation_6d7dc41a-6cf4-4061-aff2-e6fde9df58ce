package cn.harryh.arkpets.monitor.service;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;

/**
 * 系统监控服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class SystemMonitorService {

  private static final Logger logger = LoggerFactory.getLogger(SystemMonitorService.class);

  @Autowired private MeterRegistry meterRegistry;

  private final SystemInfo systemInfo = new SystemInfo();
  private final HardwareAbstractionLayer hardware = systemInfo.getHardware();
  private final OperatingSystem os = systemInfo.getOperatingSystem();

  // 系统指标缓存
  private final AtomicReference<Double> cpuUsage = new AtomicReference<>(0.0);
  private final AtomicReference<Double> memoryUsage = new AtomicReference<>(0.0);
  private final AtomicReference<Double> diskUsage = new AtomicReference<>(0.0);
  private final AtomicReference<Long> networkBytesReceived = new AtomicReference<>(0L);
  private final AtomicReference<Long> networkBytesSent = new AtomicReference<>(0L);

  // CPU使用率历史记录（用于计算平均值）
  private long[] prevTicks;

  public SystemMonitorService() {
    initializeMetrics();
    initializeCpuTicks();
  }

  /** 初始化系统指标 */
  private void initializeMetrics() {
    // CPU使用率
    Gauge.builder("arkpets.system.cpu.usage", cpuUsage, AtomicReference::get)
        .description("CPU使用率")
        .register(meterRegistry);

    // 内存使用率
    Gauge.builder("arkpets.system.memory.usage", memoryUsage, AtomicReference::get)
        .description("内存使用率")
        .register(meterRegistry);

    // 磁盘使用率
    Gauge.builder("arkpets.system.disk.usage", diskUsage, AtomicReference::get)
        .description("磁盘使用率")
        .register(meterRegistry);

    // 网络接收字节数
    Gauge.builder(
            "arkpets.system.network.bytes.received",
            networkBytesReceived,
            ref -> ref.get().doubleValue())
        .description("网络接收字节数")
        .register(meterRegistry);

    // 网络发送字节数
    Gauge.builder(
            "arkpets.system.network.bytes.sent", networkBytesSent, ref -> ref.get().doubleValue())
        .description("网络发送字节数")
        .register(meterRegistry);
  }

  /** 初始化CPU时钟计数 */
  private void initializeCpuTicks() {
    CentralProcessor processor = hardware.getProcessor();
    prevTicks = processor.getSystemCpuLoadTicks();
  }

  /** 定时收集系统指标 */
  @Scheduled(fixedRateString = "${monitor-service.system.collection-interval:30000}")
  public void collectSystemMetrics() {
    try {
      logger.debug("Collecting system metrics...");

      collectCpuMetrics();
      collectMemoryMetrics();
      collectDiskMetrics();
      collectNetworkMetrics();

      logger.debug("System metrics collected successfully");

    } catch (Exception e) {
      logger.error("Error collecting system metrics", e);
    }
  }

  /** 收集CPU指标 */
  private void collectCpuMetrics() {
    try {
      CentralProcessor processor = hardware.getProcessor();
      double cpuLoad = processor.getSystemCpuLoadBetweenTicks(prevTicks) * 100.0;
      prevTicks = processor.getSystemCpuLoadTicks();

      cpuUsage.set(cpuLoad);
      logger.debug("CPU usage: {:.2f}%", cpuLoad);

    } catch (Exception e) {
      logger.error("Error collecting CPU metrics", e);
    }
  }

  /** 收集内存指标 */
  private void collectMemoryMetrics() {
    try {
      GlobalMemory memory = hardware.getMemory();
      long totalMemory = memory.getTotal();
      long availableMemory = memory.getAvailable();
      long usedMemory = totalMemory - availableMemory;

      double memoryUsagePercent = (double) usedMemory / totalMemory * 100.0;
      memoryUsage.set(memoryUsagePercent);

      logger.debug(
          "Memory usage: {:.2f}% ({} / {} bytes)", memoryUsagePercent, usedMemory, totalMemory);

    } catch (Exception e) {
      logger.error("Error collecting memory metrics", e);
    }
  }

  /** 收集磁盘指标 */
  private void collectDiskMetrics() {
    try {
      FileSystem fileSystem = os.getFileSystem();
      List<OSFileStore> fileStores = fileSystem.getFileStores();

      double totalDiskUsage = 0.0;
      int validStores = 0;

      for (OSFileStore store : fileStores) {
        long totalSpace = store.getTotalSpace();
        long usableSpace = store.getUsableSpace();

        if (totalSpace > 0) {
          long usedSpace = totalSpace - usableSpace;
          double usage = (double) usedSpace / totalSpace * 100.0;
          totalDiskUsage += usage;
          validStores++;

          logger.debug(
              "Disk {} usage: {:.2f}% ({} / {} bytes)",
              store.getMount(), usage, usedSpace, totalSpace);
        }
      }

      if (validStores > 0) {
        diskUsage.set(totalDiskUsage / validStores);
      }

    } catch (Exception e) {
      logger.error("Error collecting disk metrics", e);
    }
  }

  /** 收集网络指标 */
  private void collectNetworkMetrics() {
    try {
      List<NetworkIF> networkIFs = hardware.getNetworkIFs();

      long totalBytesReceived = 0;
      long totalBytesSent = 0;

      for (NetworkIF networkIF : networkIFs) {
        networkIF.updateAttributes();
        totalBytesReceived += networkIF.getBytesRecv();
        totalBytesSent += networkIF.getBytesSent();
      }

      networkBytesReceived.set(totalBytesReceived);
      networkBytesSent.set(totalBytesSent);

      logger.debug(
          "Network - Received: {} bytes, Sent: {} bytes", totalBytesReceived, totalBytesSent);

    } catch (Exception e) {
      logger.error("Error collecting network metrics", e);
    }
  }

  /** 获取系统信息摘要 */
  public Map<String, Object> getSystemSummary() {
    Map<String, Object> summary = new HashMap<>();

    try {
      // 基本系统信息
      summary.put("os", os.getFamily() + " " + os.getVersionInfo().getVersion());
      summary.put("architecture", os.getBitness() + "-bit");

      // CPU信息
      CentralProcessor processor = hardware.getProcessor();
      summary.put("cpu_model", processor.getProcessorIdentifier().getName());
      summary.put("cpu_cores", processor.getLogicalProcessorCount());
      summary.put("cpu_usage", cpuUsage.get());

      // 内存信息
      GlobalMemory memory = hardware.getMemory();
      summary.put("total_memory", memory.getTotal());
      summary.put("available_memory", memory.getAvailable());
      summary.put("memory_usage", memoryUsage.get());

      // 磁盘信息
      summary.put("disk_usage", diskUsage.get());

      // 网络信息
      summary.put("network_bytes_received", networkBytesReceived.get());
      summary.put("network_bytes_sent", networkBytesSent.get());

      // JVM信息
      Runtime runtime = Runtime.getRuntime();
      summary.put("jvm_max_memory", runtime.maxMemory());
      summary.put("jvm_total_memory", runtime.totalMemory());
      summary.put("jvm_free_memory", runtime.freeMemory());
      summary.put("jvm_processors", runtime.availableProcessors());

    } catch (Exception e) {
      logger.error("Error getting system summary", e);
      summary.put("error", "Failed to retrieve system information");
    }

    return summary;
  }

  /** 检查系统健康状态 */
  public boolean isSystemHealthy() {
    try {
      double cpu = cpuUsage.get();
      double memory = memoryUsage.get();
      double disk = diskUsage.get();

      // 健康阈值检查
      boolean cpuHealthy = cpu < 90.0;
      boolean memoryHealthy = memory < 90.0;
      boolean diskHealthy = disk < 95.0;

      return cpuHealthy && memoryHealthy && diskHealthy;

    } catch (Exception e) {
      logger.error("Error checking system health", e);
      return false;
    }
  }

  /** 获取当前CPU使用率 */
  public double getCpuUsage() {
    return cpuUsage.get();
  }

  /** 获取当前内存使用率 */
  public double getMemoryUsage() {
    return memoryUsage.get();
  }

  /** 获取当前磁盘使用率 */
  public double getDiskUsage() {
    return diskUsage.get();
  }
}
