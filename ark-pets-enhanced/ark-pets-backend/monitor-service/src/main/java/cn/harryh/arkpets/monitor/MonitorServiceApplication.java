package cn.harryh.arkpets.monitor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 监控服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication
@EnableFeignClients
@EnableAsync
@EnableScheduling
public class MonitorServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(MonitorServiceApplication.class, args);
  }
}
