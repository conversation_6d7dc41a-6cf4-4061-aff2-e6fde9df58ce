package cn.harryh.arkpets.monitor.controller;

import cn.harryh.arkpets.constants.AppConstants;
import cn.harryh.arkpets.monitor.alert.AlertService;
import cn.harryh.arkpets.monitor.analytics.UserBehaviorAnalytics;
import cn.harryh.arkpets.monitor.entity.UserBehaviorEvent;
import cn.harryh.arkpets.monitor.service.AnalyticsService;
import cn.harryh.arkpets.monitor.service.ApplicationMonitorService;
import cn.harryh.arkpets.monitor.service.SystemMonitorService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 监控控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/monitor")
@CrossOrigin(origins = "*")
public class MonitorController {

  private static final Logger logger = LoggerFactory.getLogger(MonitorController.class);

  @Autowired private SystemMonitorService systemMonitorService;

  @Autowired private ApplicationMonitorService applicationMonitorService;

  @Autowired private AnalyticsService analyticsService;

  @Autowired private UserBehaviorAnalytics behaviorAnalytics;

  @Autowired private AlertService alertService;

  /** 获取系统监控信息 */
  @GetMapping("/system")
  public ResponseEntity<Map<String, Object>> getSystemMetrics() {
    try {
      logger.debug("Getting system metrics");

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "系统监控信息获取成功");
      response.put("data", systemMonitorService.getSystemSummary());
      response.put("healthy", systemMonitorService.isSystemHealthy());
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("Error getting system metrics", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "系统监控信息获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取应用监控信息 */
  @GetMapping("/application")
  public ResponseEntity<Map<String, Object>> getApplicationMetrics() {
    try {
      logger.debug("Getting application metrics");

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "应用监控信息获取成功");
      response.put("data", applicationMonitorService.getApplicationSummary());
      response.put("healthy", applicationMonitorService.isApplicationHealthy());
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("Error getting application metrics", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "应用监控信息获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取综合监控信息 */
  @GetMapping("/overview")
  public ResponseEntity<Map<String, Object>> getMonitorOverview() {
    try {
      logger.debug("Getting monitor overview");

      Map<String, Object> overview = new HashMap<>();

      // 系统信息
      Map<String, Object> systemInfo = systemMonitorService.getSystemSummary();
      overview.put("system", systemInfo);
      overview.put("system_healthy", systemMonitorService.isSystemHealthy());

      // 应用信息
      Map<String, Object> appInfo = applicationMonitorService.getApplicationSummary();
      overview.put("application", appInfo);
      overview.put("application_healthy", applicationMonitorService.isApplicationHealthy());

      // 整体健康状态
      boolean overallHealthy =
          systemMonitorService.isSystemHealthy()
              && applicationMonitorService.isApplicationHealthy();
      overview.put("overall_healthy", overallHealthy);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "监控概览获取成功");
      response.put("data", overview);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("Error getting monitor overview", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "监控概览获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 记录用户事件 */
  @PostMapping("/events/track")
  public ResponseEntity<Map<String, Object>> trackEvent(
      @RequestParam String userId,
      @RequestParam String eventName,
      @RequestBody(required = false) Map<String, Object> properties) {
    try {
      logger.debug("Tracking event: {} for user: {}", eventName, userId);

      analyticsService.trackEvent(userId, eventName, properties);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "事件跟踪成功",
              "event",
              eventName,
              "user_id",
              userId,
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error tracking event: {} for user: {}", eventName, userId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "事件跟踪失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 记录API调用 */
  @PostMapping("/api/record")
  public ResponseEntity<Map<String, Object>> recordApiCall(
      @RequestParam String userId,
      @RequestParam String endpoint,
      @RequestParam String method,
      @RequestParam int statusCode,
      @RequestParam long duration) {
    try {
      logger.debug("Recording API call: {} {} for user: {}", method, endpoint, userId);

      analyticsService.trackApiCall(userId, endpoint, method, statusCode, duration);

      // 同时更新应用监控指标
      String requestId = userId + "_" + System.currentTimeMillis();
      applicationMonitorService.recordRequestStart(requestId, endpoint);
      applicationMonitorService.recordRequestEnd(requestId, endpoint, statusCode < 400);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "API调用记录成功",
              "endpoint",
              endpoint,
              "user_id",
              userId,
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error recording API call for user: {}", userId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "API调用记录失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 更新用户属性 */
  @PostMapping("/users/{userId}/properties")
  public ResponseEntity<Map<String, Object>> updateUserProperties(
      @PathVariable String userId, @RequestBody Map<String, Object> properties) {
    try {
      logger.debug("Updating user properties for: {}", userId);

      analyticsService.setUserProperties(userId, properties);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "用户属性更新成功",
              "user_id",
              userId,
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error updating user properties for: {}", userId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "用户属性更新失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 重置应用统计数据 */
  @PostMapping("/application/reset")
  public ResponseEntity<Map<String, Object>> resetApplicationStats() {
    try {
      logger.info("Resetting application statistics");

      applicationMonitorService.resetStatistics();

      return ResponseEntity.ok(
          Map.of(
              "success", true, "message", "应用统计数据重置成功", "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error resetting application statistics", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "应用统计数据重置失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 健康检查 */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    try {
      boolean systemHealthy = systemMonitorService.isSystemHealthy();
      boolean appHealthy = applicationMonitorService.isApplicationHealthy();
      boolean overallHealthy = systemHealthy && appHealthy;

      Map<String, Object> healthStatus = new HashMap<>();
      healthStatus.put("status", overallHealthy ? "UP" : "DOWN");
      healthStatus.put("service", "monitor-service");
      healthStatus.put("system_healthy", systemHealthy);
      healthStatus.put("application_healthy", appHealthy);
      healthStatus.put("timestamp", System.currentTimeMillis());

      // 添加关键指标
      healthStatus.put("cpu_usage", systemMonitorService.getCpuUsage());
      healthStatus.put("memory_usage", systemMonitorService.getMemoryUsage());
      healthStatus.put("disk_usage", systemMonitorService.getDiskUsage());

      return ResponseEntity.ok(healthStatus);

    } catch (Exception e) {
      logger.error("Health check failed", e);
      return ResponseEntity.status(503)
          .body(
              Map.of(
                  "status",
                  "DOWN",
                  "service",
                  "monitor-service",
                  "error",
                  e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 记录用户行为事件 */
  @PostMapping("/behavior/track")
  public ResponseEntity<Map<String, Object>> trackBehaviorEvent(
      @RequestBody UserBehaviorEvent event) {
    try {
      logger.debug(
          "Tracking behavior event: {} for user: {}", event.getEventName(), event.getUserId());

      behaviorAnalytics.recordEvent(event);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "用户行为事件记录成功");
      response.put("eventId", event.getId());
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("记录用户行为事件失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "记录用户行为事件失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 获取用户行为分析 */
  @GetMapping("/analytics/behavior")
  public ResponseEntity<Map<String, Object>> getBehaviorAnalytics(
      @RequestParam(defaultValue = "7") int days) {
    try {
      Map<String, Object> analytics = new HashMap<>();

      // 每日活跃用户
      analytics.put("dailyActiveUsers", behaviorAnalytics.getDailyActiveUsers(days));

      // 事件统计
      analytics.put("eventStatistics", behaviorAnalytics.getEventStatistics(days));

      // 实时在线用户数
      analytics.put("onlineUserCount", behaviorAnalytics.getOnlineUserCount());

      // 用户留存率
      analytics.put("retentionRate", behaviorAnalytics.getUserRetentionRate(days));

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "用户行为分析获取成功");
      response.put("data", analytics);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取用户行为分析失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取用户行为分析失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 获取页面访问统计 */
  @GetMapping("/analytics/pages")
  public ResponseEntity<Map<String, Object>> getPageAnalytics(
      @RequestParam(defaultValue = "") String date) {
    try {
      if (date.isEmpty()) {
        date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
      }

      Map<String, Object> analytics = new HashMap<>();
      analytics.put("pageViews", behaviorAnalytics.getPageViewStatistics(date));
      analytics.put("deviceTypes", behaviorAnalytics.getDeviceTypeStatistics(date));
      analytics.put("date", date);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "页面访问统计获取成功");
      response.put("data", analytics);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取页面访问统计失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取页面访问统计失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 获取告警历史 */
  @GetMapping("/alerts/history")
  public ResponseEntity<Map<String, Object>> getAlertHistory(
      @RequestParam(defaultValue = "") String date, @RequestParam(defaultValue = "50") int limit) {
    try {
      if (date.isEmpty()) {
        date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
      }

      List<Map<String, Object>> alerts = alertService.getAlertHistory(date, limit);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "告警历史获取成功");
      response.put("data", alerts);
      response.put("date", date);
      response.put("count", alerts.size());
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取告警历史失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取告警历史失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 获取告警统计 */
  @GetMapping("/alerts/statistics")
  public ResponseEntity<Map<String, Object>> getAlertStatistics(
      @RequestParam(defaultValue = "7") int days) {
    try {
      Map<String, Object> statistics = alertService.getAlertStatistics(days);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "告警统计获取成功");
      response.put("data", statistics);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取告警统计失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取告警统计失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }
}
