server:
  port: 8085

spring:
  application:
    name: ark-pets-monitor-service

  # 数据库配置
  datasource:
    url: ********************************************
    username: ark_pets_user
    password: ark_pets_password_2024
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password:
    database: 5
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # 数据库配置 (用于存储监控数据)
  datasource:
    url: ************************************************
    username: arkpets
    password: arkpets123
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  # Redis配置 (用于缓存监控数据)
  redis:
    host: localhost
    port: 6379
    password: 
    database: 3
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# Micrometer监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
    info:
      enabled: true
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
        descriptions: true
      jmx:
        enabled: true
    
    # 自定义指标配置
    distribution:
      percentiles-histogram:
        http.server.requests: true
        jvm.gc.pause: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        jvm.gc.pause: 0.5, 0.95, 0.99
    
    # 标签配置
    tags:
      application: ark-pets
      service: monitor-service
      environment: ${ENVIRONMENT:development}
      version: 4.0.0

# 分析服务配置
analytics:
  api-key: ${ANALYTICS_API_KEY:your-analytics-api-key}
  host: ${ANALYTICS_HOST:https://app.posthog.com}
  enabled: ${ANALYTICS_ENABLED:true}
  timeout: 5000 # 5秒

# 监控服务配置
monitor-service:
  # 系统监控配置
  system:
    enabled: true
    collection-interval: 30s # 30秒收集一次
    metrics:
      - cpu-usage
      - memory-usage
      - disk-usage
      - network-io
      - jvm-metrics
  
  # 应用监控配置
  application:
    enabled: true
    collection-interval: 10s # 10秒收集一次
    metrics:
      - request-count
      - response-time
      - error-rate
      - active-sessions
      - database-connections
  
  # 业务监控配置
  business:
    enabled: true
    collection-interval: 60s # 1分钟收集一次
    metrics:
      - user-activity
      - ai-chat-count
      - auth-success-rate
      - cache-hit-rate
  
  # 告警配置
  alerts:
    enabled: true
    rules:
      - name: "high-cpu-usage"
        condition: "cpu_usage > 80"
        duration: "5m"
        severity: "warning"
      - name: "high-memory-usage"
        condition: "memory_usage > 85"
        duration: "3m"
        severity: "critical"
      - name: "high-error-rate"
        condition: "error_rate > 5"
        duration: "2m"
        severity: "warning"
      - name: "low-cache-hit-rate"
        condition: "cache_hit_rate < 70"
        duration: "10m"
        severity: "info"
  
  # 用户行为分析配置
  behavior-analytics:
    enabled: true
    # 数据保留时间
    retention:
      daily-data: 30d
      hourly-data: 7d
      session-data: 24h
      path-data: 24h

    # 批量处理配置
    batch-size: 100
    batch-interval: 5s

    # 异常检测配置
    anomaly-detection:
      enabled: true
      window-size: 100
      threshold: 3.0

  # 智能告警配置
  intelligent-alerting:
    enabled: true

    # 评估间隔
    evaluation-interval: 60s

    # 默认抑制时间（分钟）
    suppression-time:
      critical: 5
      error: 10
      warning: 15
      info: 30

    # 通知渠道配置
    notification-channels:
      webhook:
        enabled: true
        url: http://localhost:8087/api/v1/notifications/send
        timeout: 5s

  # 数据保留配置
  retention:
    metrics: 30d # 指标数据保留30天
    events: 90d # 事件数据保留90天
    logs: 7d # 日志数据保留7天
    behavior-events: 30d # 用户行为事件保留30天
    performance-metrics: 30d # 性能指标保留30天
    alert-history: 30d # 告警历史保留30天
  
  # 报告配置
  reports:
    enabled: true
    daily-report:
      enabled: true
      time: "09:00"
      recipients: ["<EMAIL>"]
    weekly-report:
      enabled: true
      day: "monday"
      time: "10:00"
      recipients: ["<EMAIL>"]

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    io.micrometer: INFO
    com.posthog: INFO
    oshi: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/monitor-service.log
    max-size: 100MB
    max-history: 30
