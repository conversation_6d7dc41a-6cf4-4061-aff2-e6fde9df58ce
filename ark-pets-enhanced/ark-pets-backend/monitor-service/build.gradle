// 监控服务构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // Spring Boot依赖已在根build.gradle中配置

    // 数据库依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.postgresql:postgresql:42.7.1'

    // Redis依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // 验证依赖
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // Micrometer监控依赖
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-core:1.12.1'
    implementation 'io.micrometer:micrometer-registry-prometheus:1.12.1'
    implementation 'io.micrometer:micrometer-registry-jmx:1.12.1'
    
    // 分析依赖 (使用HTTP客户端实现)
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    
    // 系统信息获取 (OSHI)
    implementation 'com.github.oshi:oshi-core:6.4.8'
    
    // 数据库监控
    implementation 'net.ttddyy:datasource-proxy:1.9'
    
    // HTTP客户端监控
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.16.1'
    
    // 定时任务
    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    
    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.monitor.MonitorServiceApplication'
}
