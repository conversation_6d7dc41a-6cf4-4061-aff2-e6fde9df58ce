server:
  port: 8088

spring:
  application:
    name: ark-pets-data-service
  
  # 数据库配置
  datasource:
    url: ********************************************
    username: ark_pets_user
    password: ark_pets_password_2024
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 6
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # 安全配置
  security:
    user:
      name: admin
      password: ark-pets-data-2024

# 数据管理服务配置
data-service:
  # 备份配置
  backup:
    # 备份目录
    directory: ./data/backups
    
    # 启用压缩
    compression: true
    
    # 默认保留天数
    retention-days: 30
    
    # 自动备份配置
    auto-backup:
      enabled: true
      # 每日备份时间
      daily-time: "02:00"
      # 每周全量备份（周日）
      weekly-full: true
      # 增量备份间隔（小时）
      incremental-interval: 6
    
    # 备份验证
    verification:
      enabled: true
      # 校验和算法
      checksum-algorithm: "SHA-256"
      # 备份后验证
      verify-after-backup: true
  
  # 导出配置
  export:
    # 导出目录
    directory: ./data/exports
    
    # 最大记录数
    max-records: 100000
    
    # 文件保留天数
    retention-days: 7
    
    # 支持的格式
    supported-formats:
      - CSV
      - EXCEL
      - JSON
      - XML
    
    # 导出限制
    limits:
      # 单次导出最大文件大小（MB）
      max-file-size: 500
      # 并发导出任务数
      max-concurrent-exports: 5
      # 查询超时时间（秒）
      query-timeout: 300
  
  # 数据迁移配置
  migration:
    enabled: true
    # Flyway配置
    flyway:
      enabled: true
      locations: classpath:db/migration
      baseline-on-migrate: true
      validate-on-migrate: true
  
  # 数据清理配置
  cleanup:
    enabled: true
    # 清理间隔（小时）
    interval: 24
    
    # 清理策略
    strategies:
      # 过期备份清理
      expired-backups: true
      # 过期导出清理
      expired-exports: true
      # 临时文件清理
      temp-files: true
      # 日志文件清理
      log-files: true
    
    # 保留策略
    retention:
      # 备份文件保留天数
      backup-files: 30
      # 导出文件保留天数
      export-files: 7
      # 临时文件保留小时数
      temp-files: 24
      # 日志文件保留天数
      log-files: 30
  
  # 监控配置
  monitoring:
    enabled: true
    # 磁盘空间监控
    disk-space:
      # 警告阈值（百分比）
      warning-threshold: 80
      # 错误阈值（百分比）
      error-threshold: 90
    
    # 备份监控
    backup-monitoring:
      # 备份失败告警
      failure-alert: true
      # 备份超时时间（分钟）
      timeout-minutes: 120
    
    # 导出监控
    export-monitoring:
      # 导出失败告警
      failure-alert: true
      # 导出超时时间（分钟）
      timeout-minutes: 60

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    tags:
      application: ark-pets
      service: data-service
      environment: ${ENVIRONMENT:development}
      version: 4.0.0

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.data: INFO
    org.hibernate: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/data-service.log
    max-size: 100MB
    max-history: 30

# 定时任务配置
spring.quartz:
  job-store-type: memory
  properties:
    org:
      quartz:
        scheduler:
          instanceName: DataServiceScheduler
        threadPool:
          threadCount: 10
