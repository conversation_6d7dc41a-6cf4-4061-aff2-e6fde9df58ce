package cn.harryh.arkpets.data.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 数据备份实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "data_backups")
public class DataBackup {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "备份名称不能为空")
  @Column(nullable = false, length = 100)
  private String name;

  @Column(columnDefinition = "TEXT")
  private String description;

  @NotNull(message = "备份类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private BackupType backupType;

  @NotNull(message = "备份状态不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private BackupStatus status;

  @Column(name = "file_path", length = 500)
  private String filePath;

  @Column(name = "file_size")
  private Long fileSize;

  @Column(name = "compressed_size")
  private Long compressedSize;

  @Column(name = "checksum", length = 64)
  private String checksum;

  @Column(name = "backup_tables", columnDefinition = "TEXT")
  private String backupTables;

  @Column(name = "start_time")
  private LocalDateTime startTime;

  @Column(name = "end_time")
  private LocalDateTime endTime;

  @Column(name = "duration_ms")
  private Long durationMs;

  @Column(name = "error_message", columnDefinition = "TEXT")
  private String errorMessage;

  @Column(name = "created_by", length = 50)
  private String createdBy;

  @Column(name = "is_compressed", nullable = false)
  private Boolean isCompressed = false;

  @Column(name = "is_encrypted", nullable = false)
  private Boolean isEncrypted = false;

  @Column(name = "retention_days")
  private Integer retentionDays;

  @Column(name = "expires_at")
  private LocalDateTime expiresAt;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  // 备份类型枚举
  public enum BackupType {
    FULL("全量备份"),
    INCREMENTAL("增量备份"),
    DIFFERENTIAL("差异备份"),
    SCHEMA_ONLY("仅结构"),
    DATA_ONLY("仅数据");

    private final String displayName;

    BackupType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 备份状态枚举
  public enum BackupStatus {
    PENDING("等待中"),
    RUNNING("进行中"),
    COMPLETED("已完成"),
    FAILED("失败"),
    CANCELLED("已取消"),
    EXPIRED("已过期");

    private final String displayName;

    BackupStatus(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public DataBackup() {}

  public DataBackup(String name, BackupType backupType, String createdBy) {
    this.name = name;
    this.backupType = backupType;
    this.createdBy = createdBy;
    this.status = BackupStatus.PENDING;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public BackupType getBackupType() {
    return backupType;
  }

  public void setBackupType(BackupType backupType) {
    this.backupType = backupType;
  }

  public BackupStatus getStatus() {
    return status;
  }

  public void setStatus(BackupStatus status) {
    this.status = status;
  }

  public String getFilePath() {
    return filePath;
  }

  public void setFilePath(String filePath) {
    this.filePath = filePath;
  }

  public Long getFileSize() {
    return fileSize;
  }

  public void setFileSize(Long fileSize) {
    this.fileSize = fileSize;
  }

  public Long getCompressedSize() {
    return compressedSize;
  }

  public void setCompressedSize(Long compressedSize) {
    this.compressedSize = compressedSize;
  }

  public String getChecksum() {
    return checksum;
  }

  public void setChecksum(String checksum) {
    this.checksum = checksum;
  }

  public String getBackupTables() {
    return backupTables;
  }

  public void setBackupTables(String backupTables) {
    this.backupTables = backupTables;
  }

  public LocalDateTime getStartTime() {
    return startTime;
  }

  public void setStartTime(LocalDateTime startTime) {
    this.startTime = startTime;
  }

  public LocalDateTime getEndTime() {
    return endTime;
  }

  public void setEndTime(LocalDateTime endTime) {
    this.endTime = endTime;
  }

  public Long getDurationMs() {
    return durationMs;
  }

  public void setDurationMs(Long durationMs) {
    this.durationMs = durationMs;
  }

  public String getErrorMessage() {
    return errorMessage;
  }

  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Boolean getIsCompressed() {
    return isCompressed;
  }

  public void setIsCompressed(Boolean isCompressed) {
    this.isCompressed = isCompressed;
  }

  public Boolean getIsEncrypted() {
    return isEncrypted;
  }

  public void setIsEncrypted(Boolean isEncrypted) {
    this.isEncrypted = isEncrypted;
  }

  public Integer getRetentionDays() {
    return retentionDays;
  }

  public void setRetentionDays(Integer retentionDays) {
    this.retentionDays = retentionDays;
  }

  public LocalDateTime getExpiresAt() {
    return expiresAt;
  }

  public void setExpiresAt(LocalDateTime expiresAt) {
    this.expiresAt = expiresAt;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  // 业务方法
  public void markAsStarted() {
    this.status = BackupStatus.RUNNING;
    this.startTime = LocalDateTime.now();
  }

  public void markAsCompleted(String filePath, Long fileSize, String checksum) {
    this.status = BackupStatus.COMPLETED;
    this.endTime = LocalDateTime.now();
    this.filePath = filePath;
    this.fileSize = fileSize;
    this.checksum = checksum;

    if (this.startTime != null) {
      this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
    }
  }

  public void markAsFailed(String errorMessage) {
    this.status = BackupStatus.FAILED;
    this.endTime = LocalDateTime.now();
    this.errorMessage = errorMessage;

    if (this.startTime != null) {
      this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
    }
  }

  public boolean isExpired() {
    return this.expiresAt != null && LocalDateTime.now().isAfter(this.expiresAt);
  }

  public double getCompressionRatio() {
    if (fileSize != null && compressedSize != null && fileSize > 0) {
      return (double) compressedSize / fileSize;
    }
    return 1.0;
  }
}
