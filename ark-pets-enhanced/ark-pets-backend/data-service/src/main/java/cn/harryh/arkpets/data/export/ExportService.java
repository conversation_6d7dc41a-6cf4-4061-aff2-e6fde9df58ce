package cn.harryh.arkpets.data.export;

import cn.harryh.arkpets.data.entity.DataExport;
import com.opencsv.CSVWriter;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;
import javax.sql.DataSource;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 数据导出服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class ExportService {

  private static final Logger logger = LoggerFactory.getLogger(ExportService.class);

  @Autowired private DataSource dataSource;

  @Value("${data-service.export.directory:./exports}")
  private String exportDirectory;

  @Value("${data-service.export.max-records:100000}")
  private int maxRecords;

  /** 导出表数据 */
  @Async
  public CompletableFuture<DataExport> exportTableData(DataExport export) {
    logger.info("开始导出表数据: {} -> {}", export.getTableName(), export.getFormat());

    try {
      export.markAsStarted();

      // 创建导出目录
      Path exportDir = createExportDirectory();

      // 生成导出文件名
      String fileName = generateExportFileName(export);
      Path exportFile = exportDir.resolve(fileName);

      // 构建查询SQL
      String sql = "SELECT * FROM " + export.getTableName();
      if (export.getParameters() != null && export.getParameters().containsKey("limit")) {
        sql += " LIMIT " + export.getParameters().get("limit");
      }

      // 执行导出
      long recordCount = performDataExport(exportFile, sql, export.getFormat());
      long fileSize = Files.size(exportFile);

      // 设置过期时间（默认7天）
      export.setExpiresAt(LocalDateTime.now().plusDays(7));

      // 标记完成
      export.markAsCompleted(exportFile.toString(), fileSize, recordCount);

      logger.info("表数据导出成功: {} -> {}, 记录数: {}", export.getTableName(), exportFile, recordCount);
      return CompletableFuture.completedFuture(export);

    } catch (Exception e) {
      logger.error("导出表数据失败: {}", export.getTableName(), e);
      export.markAsFailed(e.getMessage());
      return CompletableFuture.completedFuture(export);
    }
  }

  /** 导出查询结果 */
  @Async
  public CompletableFuture<DataExport> exportQueryResult(DataExport export) {
    logger.info("开始导出查询结果: {}", export.getName());

    try {
      export.markAsStarted();

      // 创建导出目录
      Path exportDir = createExportDirectory();

      // 生成导出文件名
      String fileName = generateExportFileName(export);
      Path exportFile = exportDir.resolve(fileName);

      // 验证SQL安全性（简化实现）
      String sql = export.getQuerySql().trim();
      if (!sql.toLowerCase().startsWith("select")) {
        throw new IllegalArgumentException("只允许SELECT查询");
      }

      // 执行导出
      long recordCount = performDataExport(exportFile, sql, export.getFormat());
      long fileSize = Files.size(exportFile);

      // 设置过期时间（默认7天）
      export.setExpiresAt(LocalDateTime.now().plusDays(7));

      // 标记完成
      export.markAsCompleted(exportFile.toString(), fileSize, recordCount);

      logger.info("查询结果导出成功: {} -> {}, 记录数: {}", export.getName(), exportFile, recordCount);
      return CompletableFuture.completedFuture(export);

    } catch (Exception e) {
      logger.error("导出查询结果失败: {}", export.getName(), e);
      export.markAsFailed(e.getMessage());
      return CompletableFuture.completedFuture(export);
    }
  }

  /** 导出报表数据 */
  @Async
  public CompletableFuture<DataExport> exportReportData(DataExport export) {
    logger.info("开始导出报表数据: {}", export.getName());

    try {
      export.markAsStarted();

      // 创建导出目录
      Path exportDir = createExportDirectory();

      // 生成导出文件名
      String fileName = generateExportFileName(export);
      Path exportFile = exportDir.resolve(fileName);

      // 根据报表类型生成SQL
      String sql = generateReportSql(export);

      // 执行导出
      long recordCount = performDataExport(exportFile, sql, export.getFormat());
      long fileSize = Files.size(exportFile);

      // 设置过期时间（默认7天）
      export.setExpiresAt(LocalDateTime.now().plusDays(7));

      // 标记完成
      export.markAsCompleted(exportFile.toString(), fileSize, recordCount);

      logger.info("报表数据导出成功: {} -> {}, 记录数: {}", export.getName(), exportFile, recordCount);
      return CompletableFuture.completedFuture(export);

    } catch (Exception e) {
      logger.error("导出报表数据失败: {}", export.getName(), e);
      export.markAsFailed(e.getMessage());
      return CompletableFuture.completedFuture(export);
    }
  }

  /** 创建导出目录 */
  private Path createExportDirectory() throws IOException {
    Path dir = Paths.get(exportDirectory);
    if (!Files.exists(dir)) {
      Files.createDirectories(dir);
    }
    return dir;
  }

  /** 生成导出文件名 */
  private String generateExportFileName(DataExport export) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    String safeName = export.getName().replaceAll("[^a-zA-Z0-9_-]", "_");
    return String.format("%s_%s%s", safeName, timestamp, export.getFormat().getFileExtension());
  }

  /** 执行数据导出 */
  private long performDataExport(Path exportFile, String sql, DataExport.ExportFormat format)
      throws Exception {
    try (Connection conn = dataSource.getConnection();
        PreparedStatement stmt = conn.prepareStatement(sql);
        ResultSet rs = stmt.executeQuery()) {

      return switch (format) {
        case CSV -> exportToCSV(exportFile, rs);
        case EXCEL -> exportToExcel(exportFile, rs);
        case JSON -> exportToJSON(exportFile, rs);
        case XML -> exportToXML(exportFile, rs);
        default -> throw new UnsupportedOperationException("不支持的导出格式: " + format);
      };
    }
  }

  /** 导出为CSV格式 */
  private long exportToCSV(Path exportFile, ResultSet rs) throws Exception {
    long recordCount = 0;

    try (FileWriter writer = new FileWriter(exportFile.toFile());
        CSVWriter csvWriter = new CSVWriter(writer)) {

      ResultSetMetaData metaData = rs.getMetaData();
      int columnCount = metaData.getColumnCount();

      // 写入表头
      String[] headers = new String[columnCount];
      for (int i = 1; i <= columnCount; i++) {
        headers[i - 1] = metaData.getColumnName(i);
      }
      csvWriter.writeNext(headers);

      // 写入数据
      while (rs.next() && recordCount < maxRecords) {
        String[] row = new String[columnCount];
        for (int i = 1; i <= columnCount; i++) {
          Object value = rs.getObject(i);
          row[i - 1] = value != null ? value.toString() : "";
        }
        csvWriter.writeNext(row);
        recordCount++;
      }
    }

    return recordCount;
  }

  /** 导出为Excel格式 */
  private long exportToExcel(Path exportFile, ResultSet rs) throws Exception {
    long recordCount = 0;

    try (Workbook workbook = new XSSFWorkbook();
        FileOutputStream fos = new FileOutputStream(exportFile.toFile())) {

      Sheet sheet = workbook.createSheet("Data");

      ResultSetMetaData metaData = rs.getMetaData();
      int columnCount = metaData.getColumnCount();

      // 创建表头样式
      CellStyle headerStyle = workbook.createCellStyle();
      Font headerFont = workbook.createFont();
      headerFont.setBold(true);
      headerStyle.setFont(headerFont);

      // 写入表头
      Row headerRow = sheet.createRow(0);
      for (int i = 1; i <= columnCount; i++) {
        Cell cell = headerRow.createCell(i - 1);
        cell.setCellValue(metaData.getColumnName(i));
        cell.setCellStyle(headerStyle);
      }

      // 写入数据
      int rowIndex = 1;
      while (rs.next() && recordCount < maxRecords) {
        Row row = sheet.createRow(rowIndex++);
        for (int i = 1; i <= columnCount; i++) {
          Cell cell = row.createCell(i - 1);
          Object value = rs.getObject(i);
          if (value != null) {
            if (value instanceof Number) {
              cell.setCellValue(((Number) value).doubleValue());
            } else if (value instanceof Boolean) {
              cell.setCellValue((Boolean) value);
            } else {
              cell.setCellValue(value.toString());
            }
          }
        }
        recordCount++;
      }

      // 自动调整列宽
      for (int i = 0; i < columnCount; i++) {
        sheet.autoSizeColumn(i);
      }

      workbook.write(fos);
    }

    return recordCount;
  }

  /** 导出为JSON格式 */
  private long exportToJSON(Path exportFile, ResultSet rs) throws Exception {
    long recordCount = 0;

    try (FileWriter writer = new FileWriter(exportFile.toFile())) {
      ResultSetMetaData metaData = rs.getMetaData();
      int columnCount = metaData.getColumnCount();

      writer.write("[\n");

      boolean first = true;
      while (rs.next() && recordCount < maxRecords) {
        if (!first) {
          writer.write(",\n");
        }
        first = false;

        writer.write("  {\n");
        for (int i = 1; i <= columnCount; i++) {
          if (i > 1) {
            writer.write(",\n");
          }

          String columnName = metaData.getColumnName(i);
          Object value = rs.getObject(i);

          writer.write("    \"" + columnName + "\": ");
          if (value == null) {
            writer.write("null");
          } else if (value instanceof String) {
            writer.write("\"" + value.toString().replace("\"", "\\\"") + "\"");
          } else if (value instanceof Number || value instanceof Boolean) {
            writer.write(value.toString());
          } else {
            writer.write("\"" + value.toString().replace("\"", "\\\"") + "\"");
          }
        }
        writer.write("\n  }");
        recordCount++;
      }

      writer.write("\n]");
    }

    return recordCount;
  }

  /** 导出为XML格式 */
  private long exportToXML(Path exportFile, ResultSet rs) throws Exception {
    long recordCount = 0;

    try (FileWriter writer = new FileWriter(exportFile.toFile())) {
      ResultSetMetaData metaData = rs.getMetaData();
      int columnCount = metaData.getColumnCount();

      writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
      writer.write("<data>\n");

      while (rs.next() && recordCount < maxRecords) {
        writer.write("  <record>\n");

        for (int i = 1; i <= columnCount; i++) {
          String columnName = metaData.getColumnName(i);
          Object value = rs.getObject(i);

          writer.write("    <" + columnName + ">");
          if (value != null) {
            writer.write(escapeXml(value.toString()));
          }
          writer.write("</" + columnName + ">\n");
        }

        writer.write("  </record>\n");
        recordCount++;
      }

      writer.write("</data>");
    }

    return recordCount;
  }

  /** 生成报表SQL */
  private String generateReportSql(DataExport export) {
    // 根据参数生成不同的报表SQL
    String reportType = export.getParameters().get("reportType");

    return switch (reportType) {
      case "user_activity" -> """
          SELECT
            DATE(created_at) as date,
            COUNT(*) as user_count,
            COUNT(DISTINCT user_id) as unique_users
          FROM user_behavior_events
          WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY DATE(created_at)
          ORDER BY date DESC
          """;

      case "pet_statistics" -> """
          SELECT
            p.name as pet_name,
            p.level,
            COUNT(pa.id) as action_count,
            MAX(pa.created_at) as last_action
          FROM pets p
          LEFT JOIN pet_actions pa ON p.id = pa.pet_id
          GROUP BY p.id, p.name, p.level
          ORDER BY action_count DESC
          """;

      case "system_performance" -> """
          SELECT
            service_name,
            metric_name,
            AVG(metric_value) as avg_value,
            MAX(metric_value) as max_value,
            MIN(metric_value) as min_value,
            COUNT(*) as sample_count
          FROM performance_metrics
          WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
          GROUP BY service_name, metric_name
          ORDER BY service_name, metric_name
          """;

      default -> "SELECT 1 as dummy_data";
    };
  }

  /** XML转义 */
  private String escapeXml(String text) {
    return text.replace("&", "&amp;")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace("\"", "&quot;")
        .replace("'", "&apos;");
  }

  /** 清理过期导出文件 */
  public void cleanupExpiredExports() {
    try {
      Path exportDir = Paths.get(exportDirectory);
      if (!Files.exists(exportDir)) {
        return;
      }

      Files.walk(exportDir)
          .filter(Files::isRegularFile)
          .filter(
              path -> {
                try {
                  // 检查文件修改时间（默认保留7天）
                  LocalDateTime fileTime =
                      LocalDateTime.ofInstant(
                          Files.getLastModifiedTime(path).toInstant(),
                          java.time.ZoneId.systemDefault());
                  return fileTime.isBefore(LocalDateTime.now().minusDays(7));
                } catch (IOException e) {
                  return false;
                }
              })
          .forEach(
              path -> {
                try {
                  Files.deleteIfExists(path);
                  logger.info("删除过期导出文件: {}", path);
                } catch (IOException e) {
                  logger.warn("删除过期导出文件失败: {}", path, e);
                }
              });

    } catch (Exception e) {
      logger.error("清理过期导出文件失败", e);
    }
  }
}
