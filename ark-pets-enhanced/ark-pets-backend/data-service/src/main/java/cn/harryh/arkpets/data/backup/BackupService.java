package cn.harryh.arkpets.data.backup;

import cn.harryh.arkpets.data.entity.DataBackup;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.zip.GZIPOutputStream;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 数据备份服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class BackupService {

  private static final Logger logger = LoggerFactory.getLogger(BackupService.class);

  @Autowired private DataSource dataSource;

  @Value("${data-service.backup.directory:./backups}")
  private String backupDirectory;

  @Value("${data-service.backup.compression:true}")
  private boolean enableCompression;

  @Value("${data-service.backup.retention-days:30}")
  private int defaultRetentionDays;

  /** 创建全量备份 */
  @Async
  public CompletableFuture<DataBackup> createFullBackup(DataBackup backup) {
    logger.info("开始创建全量备份: {}", backup.getName());

    try {
      backup.markAsStarted();

      // 创建备份目录
      Path backupDir = createBackupDirectory();

      // 生成备份文件名
      String fileName = generateBackupFileName(backup.getName(), "full");
      Path backupFile = backupDir.resolve(fileName);

      // 获取所有表
      List<String> tables = getAllTables();
      backup.setBackupTables(String.join(",", tables));

      // 执行备份
      long fileSize = performDatabaseBackup(backupFile, tables, backup.getBackupType());

      // 压缩文件
      if (enableCompression) {
        Path compressedFile = compressBackupFile(backupFile);
        Files.deleteIfExists(backupFile);
        backupFile = compressedFile;
        backup.setIsCompressed(true);
      }

      // 计算校验和
      String checksum = calculateChecksum(backupFile);

      // 设置过期时间
      if (backup.getRetentionDays() == null) {
        backup.setRetentionDays(defaultRetentionDays);
      }
      backup.setExpiresAt(LocalDateTime.now().plusDays(backup.getRetentionDays()));

      // 标记完成
      backup.markAsCompleted(backupFile.toString(), fileSize, checksum);

      logger.info("全量备份创建成功: {} -> {}", backup.getName(), backupFile);
      return CompletableFuture.completedFuture(backup);

    } catch (Exception e) {
      logger.error("创建全量备份失败: {}", backup.getName(), e);
      backup.markAsFailed(e.getMessage());
      return CompletableFuture.completedFuture(backup);
    }
  }

  /** 创建增量备份 */
  @Async
  public CompletableFuture<DataBackup> createIncrementalBackup(
      DataBackup backup, LocalDateTime lastBackupTime) {
    logger.info("开始创建增量备份: {}", backup.getName());

    try {
      backup.markAsStarted();

      // 创建备份目录
      Path backupDir = createBackupDirectory();

      // 生成备份文件名
      String fileName = generateBackupFileName(backup.getName(), "incremental");
      Path backupFile = backupDir.resolve(fileName);

      // 获取有变更的表
      List<String> changedTables = getChangedTables(lastBackupTime);
      backup.setBackupTables(String.join(",", changedTables));

      if (changedTables.isEmpty()) {
        backup.markAsCompleted(null, 0L, null);
        logger.info("增量备份完成，无数据变更: {}", backup.getName());
        return CompletableFuture.completedFuture(backup);
      }

      // 执行增量备份
      long fileSize = performIncrementalBackup(backupFile, changedTables, lastBackupTime);

      // 压缩文件
      if (enableCompression) {
        Path compressedFile = compressBackupFile(backupFile);
        Files.deleteIfExists(backupFile);
        backupFile = compressedFile;
        backup.setIsCompressed(true);
      }

      // 计算校验和
      String checksum = calculateChecksum(backupFile);

      // 设置过期时间
      if (backup.getRetentionDays() == null) {
        backup.setRetentionDays(defaultRetentionDays);
      }
      backup.setExpiresAt(LocalDateTime.now().plusDays(backup.getRetentionDays()));

      // 标记完成
      backup.markAsCompleted(backupFile.toString(), fileSize, checksum);

      logger.info("增量备份创建成功: {} -> {}", backup.getName(), backupFile);
      return CompletableFuture.completedFuture(backup);

    } catch (Exception e) {
      logger.error("创建增量备份失败: {}", backup.getName(), e);
      backup.markAsFailed(e.getMessage());
      return CompletableFuture.completedFuture(backup);
    }
  }

  /** 恢复备份 */
  @Async
  public CompletableFuture<Boolean> restoreBackup(DataBackup backup) {
    logger.info("开始恢复备份: {}", backup.getName());

    try {
      Path backupFile = Paths.get(backup.getFilePath());

      if (!Files.exists(backupFile)) {
        throw new FileNotFoundException("备份文件不存在: " + backup.getFilePath());
      }

      // 验证校验和
      if (backup.getChecksum() != null) {
        String currentChecksum = calculateChecksum(backupFile);
        if (!backup.getChecksum().equals(currentChecksum)) {
          throw new RuntimeException("备份文件校验和不匹配，文件可能已损坏");
        }
      }

      // 解压文件
      Path restoreFile = backupFile;
      if (backup.getIsCompressed()) {
        restoreFile = decompressBackupFile(backupFile);
      }

      // 执行恢复
      performDatabaseRestore(restoreFile);

      // 清理临时文件
      if (backup.getIsCompressed() && !restoreFile.equals(backupFile)) {
        Files.deleteIfExists(restoreFile);
      }

      logger.info("备份恢复成功: {}", backup.getName());
      return CompletableFuture.completedFuture(true);

    } catch (Exception e) {
      logger.error("恢复备份失败: {}", backup.getName(), e);
      return CompletableFuture.completedFuture(false);
    }
  }

  /** 创建备份目录 */
  private Path createBackupDirectory() throws IOException {
    Path dir = Paths.get(backupDirectory);
    if (!Files.exists(dir)) {
      Files.createDirectories(dir);
    }
    return dir;
  }

  /** 生成备份文件名 */
  private String generateBackupFileName(String backupName, String type) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    String safeName = backupName.replaceAll("[^a-zA-Z0-9_-]", "_");
    return String.format("%s_%s_%s.sql", safeName, type, timestamp);
  }

  /** 获取所有表 */
  private List<String> getAllTables() throws Exception {
    List<String> tables = new ArrayList<>();

    try (Connection conn = dataSource.getConnection()) {
      DatabaseMetaData metaData = conn.getMetaData();
      try (ResultSet rs = metaData.getTables(null, "public", "%", new String[] {"TABLE"})) {
        while (rs.next()) {
          String tableName = rs.getString("TABLE_NAME");
          if (!tableName.startsWith("flyway_")) { // 排除Flyway表
            tables.add(tableName);
          }
        }
      }
    }

    return tables;
  }

  /** 获取有变更的表 */
  private List<String> getChangedTables(LocalDateTime lastBackupTime) throws Exception {
    // 这里简化实现，实际应该根据表的最后修改时间判断
    // PostgreSQL可以通过pg_stat_user_tables等系统表获取统计信息
    return getAllTables();
  }

  /** 执行数据库备份 */
  private long performDatabaseBackup(
      Path backupFile, List<String> tables, DataBackup.BackupType backupType) throws Exception {
    try (FileWriter writer = new FileWriter(backupFile.toFile());
        Connection conn = dataSource.getConnection()) {

      // 写入备份头信息
      writer.write("-- Ark-Pets Database Backup\n");
      writer.write("-- Created: " + LocalDateTime.now() + "\n");
      writer.write("-- Type: " + backupType + "\n");
      writer.write("-- Tables: " + String.join(", ", tables) + "\n\n");

      // 备份每个表
      for (String table : tables) {
        backupTable(writer, conn, table, backupType);
      }
    }

    return Files.size(backupFile);
  }

  /** 备份单个表 */
  private void backupTable(
      FileWriter writer, Connection conn, String table, DataBackup.BackupType backupType)
      throws Exception {
    try (Statement stmt = conn.createStatement()) {

      if (backupType == DataBackup.BackupType.SCHEMA_ONLY
          || backupType == DataBackup.BackupType.FULL) {
        // 备份表结构（简化实现）
        writer.write("-- Table structure for " + table + "\n");
        writer.write("DROP TABLE IF EXISTS " + table + " CASCADE;\n");
        // 这里应该获取完整的CREATE TABLE语句
        writer.write("-- CREATE TABLE " + table + " (...); -- 需要实现完整的DDL导出\n\n");
      }

      if (backupType == DataBackup.BackupType.DATA_ONLY
          || backupType == DataBackup.BackupType.FULL) {
        // 备份表数据
        writer.write("-- Data for table " + table + "\n");
        try (ResultSet rs = stmt.executeQuery("SELECT * FROM " + table)) {
          int columnCount = rs.getMetaData().getColumnCount();

          while (rs.next()) {
            StringBuilder insert = new StringBuilder("INSERT INTO " + table + " VALUES (");
            for (int i = 1; i <= columnCount; i++) {
              if (i > 1) insert.append(", ");
              Object value = rs.getObject(i);
              if (value == null) {
                insert.append("NULL");
              } else if (value instanceof String) {
                insert.append("'").append(value.toString().replace("'", "''")).append("'");
              } else {
                insert.append(value.toString());
              }
            }
            insert.append(");\n");
            writer.write(insert.toString());
          }
        }
        writer.write("\n");
      }
    }
  }

  /** 执行增量备份 */
  private long performIncrementalBackup(
      Path backupFile, List<String> tables, LocalDateTime lastBackupTime) throws Exception {
    // 简化实现，实际应该只备份变更的数据
    return performDatabaseBackup(backupFile, tables, DataBackup.BackupType.INCREMENTAL);
  }

  /** 执行数据库恢复 */
  private void performDatabaseRestore(Path restoreFile) throws Exception {
    try (Connection conn = dataSource.getConnection();
        Statement stmt = conn.createStatement();
        BufferedReader reader = Files.newBufferedReader(restoreFile)) {

      String line;
      StringBuilder sqlBuilder = new StringBuilder();

      while ((line = reader.readLine()) != null) {
        line = line.trim();

        // 跳过注释和空行
        if (line.isEmpty() || line.startsWith("--")) {
          continue;
        }

        sqlBuilder.append(line).append("\n");

        // 执行完整的SQL语句
        if (line.endsWith(";")) {
          String sql = sqlBuilder.toString().trim();
          if (!sql.isEmpty()) {
            stmt.execute(sql);
          }
          sqlBuilder.setLength(0);
        }
      }
    }
  }

  /** 压缩备份文件 */
  private Path compressBackupFile(Path backupFile) throws IOException {
    Path compressedFile = Paths.get(backupFile.toString() + ".gz");

    try (FileInputStream fis = new FileInputStream(backupFile.toFile());
        FileOutputStream fos = new FileOutputStream(compressedFile.toFile());
        GZIPOutputStream gzos = new GZIPOutputStream(fos)) {

      byte[] buffer = new byte[8192];
      int length;
      while ((length = fis.read(buffer)) > 0) {
        gzos.write(buffer, 0, length);
      }
    }

    return compressedFile;
  }

  /** 解压备份文件 */
  private Path decompressBackupFile(Path compressedFile) throws IOException {
    String originalName = compressedFile.toString();
    if (originalName.endsWith(".gz")) {
      originalName = originalName.substring(0, originalName.length() - 3);
    }
    Path decompressedFile = Paths.get(originalName + ".tmp");

    try (java.util.zip.GZIPInputStream gzis =
            new java.util.zip.GZIPInputStream(new FileInputStream(compressedFile.toFile()));
        FileOutputStream fos = new FileOutputStream(decompressedFile.toFile())) {

      byte[] buffer = new byte[8192];
      int length;
      while ((length = gzis.read(buffer)) > 0) {
        fos.write(buffer, 0, length);
      }
    }

    return decompressedFile;
  }

  /** 计算文件校验和 */
  private String calculateChecksum(Path file) throws Exception {
    MessageDigest md = MessageDigest.getInstance("SHA-256");

    try (FileInputStream fis = new FileInputStream(file.toFile())) {
      byte[] buffer = new byte[8192];
      int length;
      while ((length = fis.read(buffer)) > 0) {
        md.update(buffer, 0, length);
      }
    }

    byte[] digest = md.digest();
    StringBuilder sb = new StringBuilder();
    for (byte b : digest) {
      sb.append(String.format("%02x", b));
    }

    return sb.toString();
  }

  /** 清理过期备份 */
  public void cleanupExpiredBackups() {
    try {
      Path backupDir = Paths.get(backupDirectory);
      if (!Files.exists(backupDir)) {
        return;
      }

      Files.walk(backupDir)
          .filter(Files::isRegularFile)
          .filter(
              path -> {
                try {
                  // 检查文件修改时间
                  LocalDateTime fileTime =
                      LocalDateTime.ofInstant(
                          Files.getLastModifiedTime(path).toInstant(),
                          java.time.ZoneId.systemDefault());
                  return fileTime.isBefore(LocalDateTime.now().minusDays(defaultRetentionDays));
                } catch (IOException e) {
                  return false;
                }
              })
          .forEach(
              path -> {
                try {
                  Files.deleteIfExists(path);
                  logger.info("删除过期备份文件: {}", path);
                } catch (IOException e) {
                  logger.warn("删除过期备份文件失败: {}", path, e);
                }
              });

    } catch (Exception e) {
      logger.error("清理过期备份失败", e);
    }
  }
}
