package cn.harryh.arkpets.data.controller;

import cn.harryh.arkpets.constants.AppConstants;
import cn.harryh.arkpets.data.backup.BackupService;
import cn.harryh.arkpets.data.entity.DataBackup;
import cn.harryh.arkpets.data.entity.DataExport;
import cn.harryh.arkpets.data.export.ExportService;
import java.io.File;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 数据管理控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/data")
@CrossOrigin(origins = "*")
public class DataController {

  private static final Logger logger = LoggerFactory.getLogger(DataController.class);

  @Autowired private BackupService backupService;

  @Autowired private ExportService exportService;

  /** 创建数据备份 */
  @PostMapping("/backup")
  public ResponseEntity<Map<String, Object>> createBackup(
      @RequestBody Map<String, Object> request) {
    try {
      String name = (String) request.get("name");
      String description = (String) request.get("description");
      String typeStr = (String) request.getOrDefault("type", "FULL");
      String createdBy = (String) request.get("createdBy");
      Integer retentionDays = (Integer) request.get("retentionDays");

      if (name == null || createdBy == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      DataBackup.BackupType type = DataBackup.BackupType.valueOf(typeStr.toUpperCase());
      DataBackup backup = new DataBackup(name, type, createdBy);
      backup.setDescription(description);
      backup.setRetentionDays(retentionDays);

      // 异步执行备份
      CompletableFuture<DataBackup> future;
      if (type == DataBackup.BackupType.INCREMENTAL) {
        // 获取最后备份时间（简化实现）
        LocalDateTime lastBackupTime = LocalDateTime.now().minusDays(1);
        future = backupService.createIncrementalBackup(backup, lastBackupTime);
      } else {
        future = backupService.createFullBackup(backup);
      }

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "备份任务已启动");
      response.put("backupId", backup.getId());
      response.put("status", backup.getStatus());
      response.put("timestamp", System.currentTimeMillis());

      logger.info("创建备份任务: name={}, type={}, createdBy={}", name, type, createdBy);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("创建备份失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "创建备份失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 恢复数据备份 */
  @PostMapping("/backup/{backupId}/restore")
  public ResponseEntity<Map<String, Object>> restoreBackup(
      @PathVariable String backupId, @RequestBody Map<String, Object> request) {
    try {
      // 这里应该从数据库获取备份信息
      // 简化实现，创建一个示例备份对象
      DataBackup backup = new DataBackup();
      backup.setId(backupId);
      backup.setName("示例备份");
      backup.setFilePath("./backups/example_backup.sql.gz");
      backup.setChecksum("example_checksum");
      backup.setIsCompressed(true);

      // 异步执行恢复
      CompletableFuture<Boolean> future = backupService.restoreBackup(backup);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "恢复任务已启动");
      response.put("backupId", backupId);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("启动备份恢复: backupId={}", backupId);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("恢复备份失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "恢复备份失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 导出表数据 */
  @PostMapping("/export/table")
  public ResponseEntity<Map<String, Object>> exportTable(@RequestBody Map<String, Object> request) {
    try {
      String name = (String) request.get("name");
      String tableName = (String) request.get("tableName");
      String formatStr = (String) request.getOrDefault("format", "CSV");
      String createdBy = (String) request.get("createdBy");

      if (name == null || tableName == null || createdBy == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      DataExport.ExportFormat format = DataExport.ExportFormat.valueOf(formatStr.toUpperCase());
      DataExport export = new DataExport(name, DataExport.ExportType.TABLE, format, createdBy);
      export.setTableName(tableName);

      // 设置参数
      @SuppressWarnings("unchecked")
      Map<String, String> parameters = (Map<String, String>) request.get("parameters");
      export.setParameters(parameters);

      // 异步执行导出
      CompletableFuture<DataExport> future = exportService.exportTableData(export);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "导出任务已启动");
      response.put("exportId", export.getId());
      response.put("status", export.getStatus());
      response.put("timestamp", System.currentTimeMillis());

      logger.info("创建表导出任务: name={}, table={}, format={}", name, tableName, format);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("导出表数据失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "导出表数据失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 导出查询结果 */
  @PostMapping("/export/query")
  public ResponseEntity<Map<String, Object>> exportQuery(@RequestBody Map<String, Object> request) {
    try {
      String name = (String) request.get("name");
      String querySql = (String) request.get("querySql");
      String formatStr = (String) request.getOrDefault("format", "CSV");
      String createdBy = (String) request.get("createdBy");

      if (name == null || querySql == null || createdBy == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      DataExport.ExportFormat format = DataExport.ExportFormat.valueOf(formatStr.toUpperCase());
      DataExport export = new DataExport(name, DataExport.ExportType.QUERY, format, createdBy);
      export.setQuerySql(querySql);

      // 异步执行导出
      CompletableFuture<DataExport> future = exportService.exportQueryResult(export);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "导出任务已启动");
      response.put("exportId", export.getId());
      response.put("status", export.getStatus());
      response.put("timestamp", System.currentTimeMillis());

      logger.info("创建查询导出任务: name={}, format={}", name, format);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("导出查询结果失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "导出查询结果失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 导出报表数据 */
  @PostMapping("/export/report")
  public ResponseEntity<Map<String, Object>> exportReport(
      @RequestBody Map<String, Object> request) {
    try {
      String name = (String) request.get("name");
      String formatStr = (String) request.getOrDefault("format", "EXCEL");
      String createdBy = (String) request.get("createdBy");

      if (name == null || createdBy == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      DataExport.ExportFormat format = DataExport.ExportFormat.valueOf(formatStr.toUpperCase());
      DataExport export = new DataExport(name, DataExport.ExportType.REPORT, format, createdBy);

      // 设置报表参数
      @SuppressWarnings("unchecked")
      Map<String, String> parameters = (Map<String, String>) request.get("parameters");
      export.setParameters(parameters);

      // 异步执行导出
      CompletableFuture<DataExport> future = exportService.exportReportData(export);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "报表导出任务已启动");
      response.put("exportId", export.getId());
      response.put("status", export.getStatus());
      response.put("timestamp", System.currentTimeMillis());

      logger.info("创建报表导出任务: name={}, format={}", name, format);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("导出报表数据失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "导出报表数据失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 下载导出文件 */
  @GetMapping("/export/{exportId}/download")
  public ResponseEntity<Resource> downloadExport(@PathVariable String exportId) {
    try {
      // 这里应该从数据库获取导出信息
      // 简化实现，假设文件路径
      String filePath = "./exports/example_export.csv";
      File file = new File(filePath);

      if (!file.exists()) {
        return ResponseEntity.notFound().build();
      }

      Resource resource = new FileSystemResource(file);

      return ResponseEntity.ok()
          .header(
              HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"")
          .contentType(MediaType.APPLICATION_OCTET_STREAM)
          .body(resource);

    } catch (Exception e) {
      logger.error("下载导出文件失败: exportId={}", exportId, e);
      return ResponseEntity.internalServerError().build();
    }
  }

  /** 获取数据库统计信息 */
  @GetMapping("/statistics")
  public ResponseEntity<Map<String, Object>> getDatabaseStatistics() {
    try {
      Map<String, Object> statistics = new HashMap<>();

      // 这里应该查询实际的数据库统计信息
      statistics.put("totalTables", 8);
      statistics.put("totalRecords", 50000);
      statistics.put("databaseSize", "256MB");
      statistics.put("lastBackup", "2025-01-01 10:00:00");
      statistics.put("backupCount", 15);
      statistics.put("exportCount", 25);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "数据库统计信息获取成功");
      response.put("data", statistics);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取数据库统计信息失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取数据库统计信息失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 清理过期文件 */
  @PostMapping("/cleanup")
  public ResponseEntity<Map<String, Object>> cleanupExpiredFiles() {
    try {
      // 清理过期备份
      backupService.cleanupExpiredBackups();

      // 清理过期导出
      exportService.cleanupExpiredExports();

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "过期文件清理完成");
      response.put("timestamp", System.currentTimeMillis());

      logger.info("执行过期文件清理");
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("清理过期文件失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "清理过期文件失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 健康检查 */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    Map<String, Object> response = new HashMap<>();
    response.put("status", "UP");
    response.put("service", "data-service");
    response.put("backup", "enabled");
    response.put("export", "enabled");
    response.put("timestamp", System.currentTimeMillis());
    return ResponseEntity.ok(response);
  }
}
