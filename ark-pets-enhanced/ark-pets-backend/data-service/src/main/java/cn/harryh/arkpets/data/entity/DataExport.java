package cn.harryh.arkpets.data.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 数据导出实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "data_exports")
public class DataExport {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "导出名称不能为空")
  @Column(nullable = false, length = 100)
  private String name;

  @Column(columnDefinition = "TEXT")
  private String description;

  @NotNull(message = "导出类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private ExportType exportType;

  @NotNull(message = "导出格式不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private ExportFormat format;

  @NotNull(message = "导出状态不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private ExportStatus status;

  @Column(name = "table_name", length = 100)
  private String tableName;

  @Column(name = "query_sql", columnDefinition = "TEXT")
  private String querySql;

  @Column(name = "file_path", length = 500)
  private String filePath;

  @Column(name = "file_size")
  private Long fileSize;

  @Column(name = "record_count")
  private Long recordCount;

  @Column(name = "start_time")
  private LocalDateTime startTime;

  @Column(name = "end_time")
  private LocalDateTime endTime;

  @Column(name = "duration_ms")
  private Long durationMs;

  @Column(name = "error_message", columnDefinition = "TEXT")
  private String errorMessage;

  @Column(name = "created_by", length = 50)
  private String createdBy;

  @ElementCollection
  @CollectionTable(name = "data_export_parameters", joinColumns = @JoinColumn(name = "export_id"))
  @MapKeyColumn(name = "param_key")
  @Column(name = "param_value")
  private Map<String, String> parameters;

  @Column(name = "expires_at")
  private LocalDateTime expiresAt;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  // 导出类型枚举
  public enum ExportType {
    TABLE("表数据"),
    QUERY("查询结果"),
    REPORT("报表数据"),
    ANALYTICS("分析数据");

    private final String displayName;

    ExportType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 导出格式枚举
  public enum ExportFormat {
    CSV("CSV"),
    EXCEL("Excel"),
    JSON("JSON"),
    XML("XML"),
    PDF("PDF");

    private final String displayName;

    ExportFormat(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }

    public String getFileExtension() {
      return switch (this) {
        case CSV -> ".csv";
        case EXCEL -> ".xlsx";
        case JSON -> ".json";
        case XML -> ".xml";
        case PDF -> ".pdf";
      };
    }
  }

  // 导出状态枚举
  public enum ExportStatus {
    PENDING("等待中"),
    RUNNING("进行中"),
    COMPLETED("已完成"),
    FAILED("失败"),
    CANCELLED("已取消"),
    EXPIRED("已过期");

    private final String displayName;

    ExportStatus(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public DataExport() {}

  public DataExport(String name, ExportType exportType, ExportFormat format, String createdBy) {
    this.name = name;
    this.exportType = exportType;
    this.format = format;
    this.createdBy = createdBy;
    this.status = ExportStatus.PENDING;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public ExportType getExportType() {
    return exportType;
  }

  public void setExportType(ExportType exportType) {
    this.exportType = exportType;
  }

  public ExportFormat getFormat() {
    return format;
  }

  public void setFormat(ExportFormat format) {
    this.format = format;
  }

  public ExportStatus getStatus() {
    return status;
  }

  public void setStatus(ExportStatus status) {
    this.status = status;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public String getQuerySql() {
    return querySql;
  }

  public void setQuerySql(String querySql) {
    this.querySql = querySql;
  }

  public String getFilePath() {
    return filePath;
  }

  public void setFilePath(String filePath) {
    this.filePath = filePath;
  }

  public Long getFileSize() {
    return fileSize;
  }

  public void setFileSize(Long fileSize) {
    this.fileSize = fileSize;
  }

  public Long getRecordCount() {
    return recordCount;
  }

  public void setRecordCount(Long recordCount) {
    this.recordCount = recordCount;
  }

  public LocalDateTime getStartTime() {
    return startTime;
  }

  public void setStartTime(LocalDateTime startTime) {
    this.startTime = startTime;
  }

  public LocalDateTime getEndTime() {
    return endTime;
  }

  public void setEndTime(LocalDateTime endTime) {
    this.endTime = endTime;
  }

  public Long getDurationMs() {
    return durationMs;
  }

  public void setDurationMs(Long durationMs) {
    this.durationMs = durationMs;
  }

  public String getErrorMessage() {
    return errorMessage;
  }

  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Map<String, String> getParameters() {
    return parameters;
  }

  public void setParameters(Map<String, String> parameters) {
    this.parameters = parameters;
  }

  public LocalDateTime getExpiresAt() {
    return expiresAt;
  }

  public void setExpiresAt(LocalDateTime expiresAt) {
    this.expiresAt = expiresAt;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  // 业务方法
  public void markAsStarted() {
    this.status = ExportStatus.RUNNING;
    this.startTime = LocalDateTime.now();
  }

  public void markAsCompleted(String filePath, Long fileSize, Long recordCount) {
    this.status = ExportStatus.COMPLETED;
    this.endTime = LocalDateTime.now();
    this.filePath = filePath;
    this.fileSize = fileSize;
    this.recordCount = recordCount;

    if (this.startTime != null) {
      this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
    }
  }

  public void markAsFailed(String errorMessage) {
    this.status = ExportStatus.FAILED;
    this.endTime = LocalDateTime.now();
    this.errorMessage = errorMessage;

    if (this.startTime != null) {
      this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
    }
  }

  public boolean isExpired() {
    return this.expiresAt != null && LocalDateTime.now().isAfter(this.expiresAt);
  }

  public String getFileName() {
    if (this.name != null && this.format != null) {
      return this.name.replaceAll("[^a-zA-Z0-9_-]", "_") + this.format.getFileExtension();
    }
    return "export" + (this.format != null ? this.format.getFileExtension() : ".txt");
  }
}
