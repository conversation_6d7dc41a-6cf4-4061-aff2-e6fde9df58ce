package cn.harryh.arkpets.data;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据管理服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication(scanBasePackages = {"cn.harryh.arkpets.data", "cn.harryh.arkpets.common"})
@EntityScan(basePackages = {"cn.harryh.arkpets.data.entity", "cn.harryh.arkpets.common.entity"})
@EnableJpaRepositories(
    basePackages = {"cn.harryh.arkpets.data.repository", "cn.harryh.arkpets.common.repository"})
@EnableTransactionManagement
@EnableScheduling
@EnableAsync
public class DataServiceApplication {

  private static final Logger logger = LoggerFactory.getLogger(DataServiceApplication.class);

  public static void main(String[] args) {
    try {
      logger.info("启动数据管理服务...");
      SpringApplication.run(DataServiceApplication.class, args);
      logger.info("数据管理服务启动成功！");
    } catch (Exception e) {
      logger.error("数据管理服务启动失败", e);
      System.exit(1);
    }
  }
}
