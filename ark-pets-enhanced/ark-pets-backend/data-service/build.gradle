// 数据管理服务构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // Spring Boot依赖已在根build.gradle中配置
    
    // 数据库依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.postgresql:postgresql:42.7.1'
    
    // Redis依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    
    // 验证依赖
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    
    // 安全依赖
    implementation 'org.springframework.boot:spring-boot-starter-security'
    
    // 监控依赖
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus:1.12.1'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    
    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
    
    // 定时任务
    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    
    // 数据导出依赖
    implementation 'org.apache.poi:poi:5.2.5'
    implementation 'org.apache.poi:poi-ooxml:5.2.5'
    
    // CSV处理
    implementation 'com.opencsv:opencsv:5.9'
    
    // 数据库连接池
    implementation 'com.zaxxer:HikariCP:5.1.0'
    
    // 数据库迁移
    implementation 'org.flywaydb:flyway-core:10.4.1'
    implementation 'org.flywaydb:flyway-database-postgresql:10.4.1'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.data.DataServiceApplication'
}
