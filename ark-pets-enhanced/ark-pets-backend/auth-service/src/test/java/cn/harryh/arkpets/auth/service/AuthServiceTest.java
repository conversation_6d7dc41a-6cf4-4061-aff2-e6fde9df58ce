package cn.harryh.arkpets.auth.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import cn.harryh.arkpets.auth.dto.RegisterRequest;
import cn.harryh.arkpets.auth.entity.User;
import cn.harryh.arkpets.auth.repository.UserRepository;
import java.time.LocalDateTime;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

  @Mock private UserRepository userRepository;

  @InjectMocks private AuthService authService;

  private User testUser;
  private RegisterRequest registerRequest;

  @BeforeEach
  void setUp() {
    // 创建测试用户
    testUser = new User();
    testUser.setUsername("testuser");
    testUser.setPassword(new BCryptPasswordEncoder().encode("password123"));
    testUser.setEmail("<EMAIL>");
    testUser.setEnabled(true);
    testUser.setCreatedTime(LocalDateTime.now());

    // 创建注册请求
    registerRequest = new RegisterRequest();
    registerRequest.setUsername("newuser");
    registerRequest.setPassword("password123");
    registerRequest.setConfirmPassword("password123");
    registerRequest.setEmail("<EMAIL>");
  }

  @Test
  void validateUser_WithValidCredentials_ReturnsTrue() {
    // 配置模拟行为
    when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

    // 执行测试
    boolean result = authService.validateUser("testuser", "password123");

    // 验证结果
    assertTrue(result);
    verify(userRepository).findByUsername("testuser");
  }

  @Test
  void validateUser_WithInvalidPassword_ReturnsFalse() {
    // 配置模拟行为
    when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

    // 执行测试
    boolean result = authService.validateUser("testuser", "wrongpassword");

    // 验证结果
    assertFalse(result);
    verify(userRepository).findByUsername("testuser");
  }

  @Test
  void validateUser_WithNonExistentUser_ReturnsFalse() {
    // 配置模拟行为
    when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

    // 执行测试
    boolean result = authService.validateUser("nonexistent", "password123");

    // 验证结果
    assertFalse(result);
    verify(userRepository).findByUsername("nonexistent");
  }

  @Test
  void createUser_WithValidRequest_ReturnsTrue() {
    // 配置模拟行为
    when(userRepository.existsByUsername("newuser")).thenReturn(false);
    when(userRepository.save(any(User.class))).thenReturn(new User());

    // 执行测试
    boolean result = authService.createUser(registerRequest);

    // 验证结果
    assertTrue(result);
    verify(userRepository).existsByUsername("newuser");
    verify(userRepository).save(any(User.class));
  }

  @Test
  void createUser_WithExistingUsername_ReturnsFalse() {
    // 配置模拟行为
    when(userRepository.existsByUsername("newuser")).thenReturn(true);

    // 执行测试
    boolean result = authService.createUser(registerRequest);

    // 验证结果
    assertFalse(result);
    verify(userRepository).existsByUsername("newuser");
    verify(userRepository, never()).save(any(User.class));
  }

  @Test
  void disableUser_WithExistingUser_ReturnsTrue() {
    // 配置模拟行为
    when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
    when(userRepository.save(any(User.class))).thenReturn(testUser);

    // 执行测试
    boolean result = authService.disableUser("testuser");

    // 验证结果
    assertTrue(result);
    assertFalse(testUser.getEnabled());
    verify(userRepository).findByUsername("testuser");
    verify(userRepository).save(testUser);
  }

  @Test
  void enableUser_WithExistingUser_ReturnsTrue() {
    // 配置模拟行为
    testUser.setEnabled(false);
    when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
    when(userRepository.save(any(User.class))).thenReturn(testUser);

    // 执行测试
    boolean result = authService.enableUser("testuser");

    // 验证结果
    assertTrue(result);
    assertTrue(testUser.getEnabled());
    verify(userRepository).findByUsername("testuser");
    verify(userRepository).save(testUser);
  }
}
