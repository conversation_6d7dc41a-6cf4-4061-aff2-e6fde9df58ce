package cn.harryh.arkpets.auth.dto;

/**
 * 登录响应DTO
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class LoginResponse {

  private Boolean success;
  private String message;
  private String token;
  private String tokenName;
  private Long tokenTimeout;
  private String username;
  private String nickname;
  private String email;

  public LoginResponse() {}

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public String getTokenName() {
    return tokenName;
  }

  public void setTokenName(String tokenName) {
    this.tokenName = tokenName;
  }

  public Long getTokenTimeout() {
    return tokenTimeout;
  }

  public void setTokenTimeout(Long tokenTimeout) {
    this.tokenTimeout = tokenTimeout;
  }

  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public String getNickname() {
    return nickname;
  }

  public void setNickname(String nickname) {
    this.nickname = nickname;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    private LoginResponse response = new LoginResponse();

    public Builder success(Boolean success) {
      response.setSuccess(success);
      return this;
    }

    public Builder message(String message) {
      response.setMessage(message);
      return this;
    }

    public Builder token(String token) {
      response.setToken(token);
      return this;
    }

    public Builder tokenName(String tokenName) {
      response.setTokenName(tokenName);
      return this;
    }

    public Builder tokenTimeout(Long tokenTimeout) {
      response.setTokenTimeout(tokenTimeout);
      return this;
    }

    public Builder username(String username) {
      response.setUsername(username);
      return this;
    }

    public Builder nickname(String nickname) {
      response.setNickname(nickname);
      return this;
    }

    public Builder email(String email) {
      response.setEmail(email);
      return this;
    }

    public LoginResponse build() {
      return response;
    }
  }
}
