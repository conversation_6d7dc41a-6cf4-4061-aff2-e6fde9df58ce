package cn.harryh.arkpets.auth.repository;

import cn.harryh.arkpets.auth.entity.User;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 用户数据访问层
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

  /**
   * 根据用户名查找用户
   *
   * @param username 用户名
   * @return 用户信息
   */
  Optional<User> findByUsername(String username);

  /**
   * 根据邮箱查找用户
   *
   * @param email 邮箱
   * @return 用户信息
   */
  Optional<User> findByEmail(String email);

  /**
   * 检查用户名是否存在
   *
   * @param username 用户名
   * @return 是否存在
   */
  boolean existsByUsername(String username);

  /**
   * 检查邮箱是否存在
   *
   * @param email 邮箱
   * @return 是否存在
   */
  boolean existsByEmail(String email);

  /**
   * 查找启用的用户
   *
   * @param enabled 是否启用
   * @return 用户列表
   */
  List<User> findByEnabled(Boolean enabled);

  /**
   * 根据用户名和启用状态查找用户
   *
   * @param username 用户名
   * @param enabled 是否启用
   * @return 用户信息
   */
  Optional<User> findByUsernameAndEnabled(String username, Boolean enabled);

  /**
   * 查找指定时间之后创建的用户
   *
   * @param createdTime 创建时间
   * @return 用户列表
   */
  List<User> findByCreatedTimeAfter(LocalDateTime createdTime);

  /**
   * 查找指定时间之后最后登录的用户
   *
   * @param lastLoginTime 最后登录时间
   * @return 用户列表
   */
  List<User> findByLastLoginTimeAfter(LocalDateTime lastLoginTime);

  /**
   * 统计启用的用户数量
   *
   * @return 用户数量
   */
  @Query("SELECT COUNT(u) FROM User u WHERE u.enabled = true")
  long countEnabledUsers();

  /**
   * 统计指定时间之后注册的用户数量
   *
   * @param createdTime 创建时间
   * @return 用户数量
   */
  @Query("SELECT COUNT(u) FROM User u WHERE u.createdTime >= :createdTime")
  long countUsersByCreatedTimeAfter(@Param("createdTime") LocalDateTime createdTime);

  /**
   * 查找用户名包含指定关键字的用户
   *
   * @param keyword 关键字
   * @return 用户列表
   */
  @Query("SELECT u FROM User u WHERE u.username LIKE %:keyword% OR u.nickname LIKE %:keyword%")
  List<User> findByUsernameOrNicknameContaining(@Param("keyword") String keyword);

  /**
   * 更新用户最后登录时间
   *
   * @param username 用户名
   * @param lastLoginTime 最后登录时间
   */
  @Query("UPDATE User u SET u.lastLoginTime = :lastLoginTime WHERE u.username = :username")
  void updateLastLoginTime(
      @Param("username") String username, @Param("lastLoginTime") LocalDateTime lastLoginTime);
}
