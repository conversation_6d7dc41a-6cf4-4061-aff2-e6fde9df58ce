package cn.harryh.arkpets.auth.config;

import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.request.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JustAuth第三方登录配置
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class JustAuthConfig {

  @Value("${oauth.github.client-id:}")
  private String githubClientId;

  @Value("${oauth.github.client-secret:}")
  private String githubClientSecret;

  @Value("${oauth.github.redirect-uri:http://localhost:8081/api/v1/auth/oauth/github/callback}")
  private String githubRedirectUri;

  @Value("${oauth.google.client-id:}")
  private String googleClientId;

  @Value("${oauth.google.client-secret:}")
  private String googleClientSecret;

  @Value("${oauth.google.redirect-uri:http://localhost:8081/api/v1/auth/oauth/google/callback}")
  private String googleRedirectUri;

  @Value("${oauth.qq.client-id:}")
  private String qqClientId;

  @Value("${oauth.qq.client-secret:}")
  private String qqClientSecret;

  @Value("${oauth.qq.redirect-uri:http://localhost:8081/api/v1/auth/oauth/qq/callback}")
  private String qqRedirectUri;

  @Value("${oauth.wechat.client-id:}")
  private String wechatClientId;

  @Value("${oauth.wechat.client-secret:}")
  private String wechatClientSecret;

  @Value("${oauth.wechat.redirect-uri:http://localhost:8081/api/v1/auth/oauth/wechat/callback}")
  private String wechatRedirectUri;

  /** GitHub登录请求 */
  @Bean
  public AuthGithubRequest githubAuthRequest() {
    return new AuthGithubRequest(
        AuthConfig.builder()
            .clientId(githubClientId)
            .clientSecret(githubClientSecret)
            .redirectUri(githubRedirectUri)
            .build());
  }

  /** Google登录请求 */
  @Bean
  public AuthGoogleRequest googleAuthRequest() {
    return new AuthGoogleRequest(
        AuthConfig.builder()
            .clientId(googleClientId)
            .clientSecret(googleClientSecret)
            .redirectUri(googleRedirectUri)
            .build());
  }

  /** QQ登录请求 */
  @Bean
  public AuthQqRequest qqAuthRequest() {
    return new AuthQqRequest(
        AuthConfig.builder()
            .clientId(qqClientId)
            .clientSecret(qqClientSecret)
            .redirectUri(qqRedirectUri)
            .build());
  }

  /** 微信登录请求 */
  @Bean
  public AuthWeChatOpenRequest wechatAuthRequest() {
    return new AuthWeChatOpenRequest(
        AuthConfig.builder()
            .clientId(wechatClientId)
            .clientSecret(wechatClientSecret)
            .redirectUri(wechatRedirectUri)
            .build());
  }
}
