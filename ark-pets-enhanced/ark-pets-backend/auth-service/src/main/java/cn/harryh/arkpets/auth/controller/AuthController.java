package cn.harryh.arkpets.auth.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.harryh.arkpets.auth.dto.LoginRequest;
import cn.harryh.arkpets.auth.dto.LoginResponse;
import cn.harryh.arkpets.auth.dto.RegisterRequest;
import cn.harryh.arkpets.auth.service.AuthService;
import cn.harryh.arkpets.constants.AppConstants;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/auth")
@CrossOrigin(origins = "*")
public class AuthController {

  private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

  @Autowired private AuthService authService;

  /**
   * 用户登录
   *
   * @param request 登录请求
   * @return 登录响应
   */
  @PostMapping("/login")
  public ResponseEntity<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
    logger.info("User login attempt: {}", request.getUsername());

    try {
      // 验证用户凭据
      boolean isValid = authService.validateUser(request.getUsername(), request.getPassword());

      if (!isValid) {
        logger.warn("Login failed for user: {}", request.getUsername());
        return ResponseEntity.badRequest()
            .body(LoginResponse.builder().success(false).message("用户名或密码错误").build());
      }

      // 执行登录
      StpUtil.login(request.getUsername());

      // 获取token信息
      SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

      logger.info("User login successful: {}", request.getUsername());

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("登录成功")
              .token(tokenInfo.getTokenValue())
              .tokenName(tokenInfo.getTokenName())
              .tokenTimeout(tokenInfo.getTokenTimeout())
              .username(request.getUsername())
              .build());

    } catch (Exception e) {
      logger.error("Login error for user: {}", request.getUsername(), e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("登录失败，请稍后重试").build());
    }
  }

  /**
   * 用户注册
   *
   * @param request 注册请求
   * @return 注册响应
   */
  @PostMapping("/register")
  public ResponseEntity<LoginResponse> register(@Valid @RequestBody RegisterRequest request) {
    logger.info("User register attempt: {}", request.getUsername());

    try {
      // 检查用户是否已存在
      if (authService.userExists(request.getUsername())) {
        logger.warn("Register failed - user already exists: {}", request.getUsername());
        return ResponseEntity.badRequest()
            .body(LoginResponse.builder().success(false).message("用户名已存在").build());
      }

      // 创建用户
      boolean created = authService.createUser(request);

      if (!created) {
        logger.warn("Register failed for user: {}", request.getUsername());
        return ResponseEntity.badRequest()
            .body(LoginResponse.builder().success(false).message("注册失败").build());
      }

      logger.info("User register successful: {}", request.getUsername());

      return ResponseEntity.ok(LoginResponse.builder().success(true).message("注册成功，请登录").build());

    } catch (Exception e) {
      logger.error("Register error for user: {}", request.getUsername(), e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("注册失败，请稍后重试").build());
    }
  }

  /**
   * 用户登出
   *
   * @return 登出响应
   */
  @PostMapping("/logout")
  public ResponseEntity<LoginResponse> logout() {
    try {
      String username = StpUtil.getLoginIdAsString();
      StpUtil.logout();

      logger.info("User logout successful: {}", username);

      return ResponseEntity.ok(LoginResponse.builder().success(true).message("登出成功").build());

    } catch (Exception e) {
      logger.error("Logout error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("登出失败").build());
    }
  }

  /**
   * 刷新token
   *
   * @return token信息
   */
  @PostMapping("/refresh")
  public ResponseEntity<LoginResponse> refresh() {
    try {
      // 检查是否登录
      if (!StpUtil.isLogin()) {
        return ResponseEntity.badRequest()
            .body(LoginResponse.builder().success(false).message("未登录").build());
      }

      // 刷新token
      StpUtil.renewTimeout(StpUtil.getTokenTimeout());
      SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("刷新成功")
              .token(tokenInfo.getTokenValue())
              .tokenName(tokenInfo.getTokenName())
              .tokenTimeout(tokenInfo.getTokenTimeout())
              .build());

    } catch (Exception e) {
      logger.error("Refresh token error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("刷新失败").build());
    }
  }

  /**
   * 获取当前用户信息
   *
   * @return 用户信息
   */
  @GetMapping("/userinfo")
  public ResponseEntity<LoginResponse> getUserInfo() {
    try {
      if (!StpUtil.isLogin()) {
        return ResponseEntity.badRequest()
            .body(LoginResponse.builder().success(false).message("未登录").build());
      }

      String username = StpUtil.getLoginIdAsString();

      return ResponseEntity.ok(
          LoginResponse.builder().success(true).message("获取用户信息成功").username(username).build());

    } catch (Exception e) {
      logger.error("Get user info error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("获取用户信息失败").build());
    }
  }
}
