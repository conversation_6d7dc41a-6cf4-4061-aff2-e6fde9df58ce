package cn.harryh.arkpets.auth.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 注册请求DTO
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class RegisterRequest {

  @NotBlank(message = "用户名不能为空")
  @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
  private String username;

  @NotBlank(message = "密码不能为空")
  @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
  private String password;

  @NotBlank(message = "确认密码不能为空")
  private String confirmPassword;

  @Email(message = "邮箱格式不正确")
  private String email;

  @Size(max = 100, message = "昵称长度不能超过100个字符")
  private String nickname;

  public RegisterRequest() {}

  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public String getConfirmPassword() {
    return confirmPassword;
  }

  public void setConfirmPassword(String confirmPassword) {
    this.confirmPassword = confirmPassword;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getNickname() {
    return nickname;
  }

  public void setNickname(String nickname) {
    this.nickname = nickname;
  }

  /**
   * 验证密码是否一致
   *
   * @return 密码是否一致
   */
  public boolean isPasswordMatch() {
    return password != null && password.equals(confirmPassword);
  }

  @Override
  public String toString() {
    return "RegisterRequest{"
        + "username='"
        + username
        + '\''
        + ", email='"
        + email
        + '\''
        + ", nickname='"
        + nickname
        + '\''
        + '}';
  }
}
