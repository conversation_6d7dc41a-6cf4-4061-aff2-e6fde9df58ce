package cn.harryh.arkpets.auth.service;

import cn.harryh.arkpets.auth.dto.RegisterRequest;
import cn.harryh.arkpets.auth.entity.User;
import cn.harryh.arkpets.auth.repository.UserRepository;
import java.time.LocalDateTime;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 认证服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class AuthService {

  private static final Logger logger = LoggerFactory.getLogger(AuthService.class);

  @Autowired private UserRepository userRepository;

  private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

  /**
   * 验证用户凭据
   *
   * @param username 用户名
   * @param password 密码
   * @return 是否验证成功
   */
  public boolean validateUser(String username, String password) {
    try {
      Optional<User> userOpt = userRepository.findByUsername(username);

      if (userOpt.isEmpty()) {
        logger.debug("User not found: {}", username);
        return false;
      }

      User user = userOpt.get();

      // 检查用户是否被禁用
      if (!user.getEnabled()) {
        logger.warn("User is disabled: {}", username);
        return false;
      }

      // 验证密码
      boolean passwordMatch = passwordEncoder.matches(password, user.getPassword());

      if (passwordMatch) {
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
        logger.info("User validation successful: {}", username);
      } else {
        logger.debug("Password mismatch for user: {}", username);
      }

      return passwordMatch;

    } catch (Exception e) {
      logger.error("Error validating user: {}", username, e);
      return false;
    }
  }

  /**
   * 检查用户是否存在
   *
   * @param username 用户名
   * @return 用户是否存在
   */
  public boolean userExists(String username) {
    try {
      return userRepository.existsByUsername(username);
    } catch (Exception e) {
      logger.error("Error checking user existence: {}", username, e);
      return true; // 出错时返回true，避免重复创建
    }
  }

  /**
   * 创建新用户
   *
   * @param request 注册请求
   * @return 是否创建成功
   */
  public boolean createUser(RegisterRequest request) {
    try {
      // 验证密码一致性
      if (!request.isPasswordMatch()) {
        logger.warn("Password mismatch during registration: {}", request.getUsername());
        return false;
      }

      // 检查用户名是否已存在
      if (userExists(request.getUsername())) {
        logger.warn("Username already exists: {}", request.getUsername());
        return false;
      }

      // 创建新用户
      User user = new User();
      user.setUsername(request.getUsername());
      user.setPassword(passwordEncoder.encode(request.getPassword()));
      user.setEmail(request.getEmail());
      user.setNickname(
          request.getNickname() != null ? request.getNickname() : request.getUsername());
      user.setEnabled(true);
      user.setCreatedTime(LocalDateTime.now());
      user.setUpdatedTime(LocalDateTime.now());

      userRepository.save(user);

      logger.info("User created successfully: {}", request.getUsername());
      return true;

    } catch (Exception e) {
      logger.error("Error creating user: {}", request.getUsername(), e);
      return false;
    }
  }

  /**
   * 根据用户名获取用户信息
   *
   * @param username 用户名
   * @return 用户信息
   */
  public Optional<User> getUserByUsername(String username) {
    try {
      return userRepository.findByUsername(username);
    } catch (Exception e) {
      logger.error("Error getting user by username: {}", username, e);
      return Optional.empty();
    }
  }

  /**
   * 更新用户最后登录时间
   *
   * @param username 用户名
   */
  public void updateLastLoginTime(String username) {
    try {
      Optional<User> userOpt = userRepository.findByUsername(username);
      if (userOpt.isPresent()) {
        User user = userOpt.get();
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
      }
    } catch (Exception e) {
      logger.error("Error updating last login time for user: {}", username, e);
    }
  }

  /**
   * 禁用用户
   *
   * @param username 用户名
   * @return 是否操作成功
   */
  public boolean disableUser(String username) {
    try {
      Optional<User> userOpt = userRepository.findByUsername(username);
      if (userOpt.isPresent()) {
        User user = userOpt.get();
        user.setEnabled(false);
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.save(user);
        logger.info("User disabled: {}", username);
        return true;
      }
      return false;
    } catch (Exception e) {
      logger.error("Error disabling user: {}", username, e);
      return false;
    }
  }

  /**
   * 启用用户
   *
   * @param username 用户名
   * @return 是否操作成功
   */
  public boolean enableUser(String username) {
    try {
      Optional<User> userOpt = userRepository.findByUsername(username);
      if (userOpt.isPresent()) {
        User user = userOpt.get();
        user.setEnabled(true);
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.save(user);
        logger.info("User enabled: {}", username);
        return true;
      }
      return false;
    } catch (Exception e) {
      logger.error("Error enabling user: {}", username, e);
      return false;
    }
  }
}
