package cn.harryh.arkpets.auth.service;

import cn.harryh.arkpets.auth.entity.User;
import cn.harryh.arkpets.auth.repository.UserRepository;
import java.time.LocalDateTime;
import java.util.Optional;
import me.zhyd.oauth.model.AuthUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 第三方登录服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class OAuthService {

  private static final Logger logger = LoggerFactory.getLogger(OAuthService.class);

  @Autowired private UserRepository userRepository;

  /**
   * 处理第三方登录
   *
   * @param authUser 第三方用户信息
   * @param provider 登录提供商
   * @return 本地用户名
   */
  @Transactional
  public String handleOAuthLogin(AuthUser authUser, String provider) {
    try {
      // 生成唯一的用户名
      String username = generateUsername(authUser, provider);

      // 查找或创建用户
      Optional<User> existingUser = userRepository.findByUsername(username);

      if (existingUser.isPresent()) {
        // 用户已存在，更新登录时间
        User user = existingUser.get();
        user.setLastLoginTime(LocalDateTime.now());

        // 更新用户信息（如果第三方信息有更新）
        updateUserFromOAuth(user, authUser);

        userRepository.save(user);
        logger.info("Existing OAuth user login: {} via {}", username, provider);

        return username;
      } else {
        // 创建新用户
        User newUser = createUserFromOAuth(authUser, provider, username);
        userRepository.save(newUser);
        logger.info("New OAuth user created: {} via {}", username, provider);

        return username;
      }

    } catch (Exception e) {
      logger.error(
          "Error handling OAuth login for user: {} via {}", authUser.getUsername(), provider, e);
      throw new RuntimeException("第三方登录处理失败", e);
    }
  }

  /**
   * 生成唯一的用户名
   *
   * @param authUser 第三方用户信息
   * @param provider 登录提供商
   * @return 用户名
   */
  private String generateUsername(AuthUser authUser, String provider) {
    // 基础用户名：provider_原始用户名
    String baseUsername = provider + "_" + authUser.getUsername();

    // 如果用户名已存在，添加数字后缀
    String username = baseUsername;
    int counter = 1;

    while (userRepository.existsByUsername(username)) {
      username = baseUsername + "_" + counter;
      counter++;
    }

    return username;
  }

  /**
   * 从第三方用户信息创建本地用户
   *
   * @param authUser 第三方用户信息
   * @param provider 登录提供商
   * @param username 生成的用户名
   * @return 新用户
   */
  private User createUserFromOAuth(AuthUser authUser, String provider, String username) {
    User user = new User();

    user.setUsername(username);
    user.setPassword(""); // 第三方登录用户没有密码
    user.setNickname(
        authUser.getNickname() != null ? authUser.getNickname() : authUser.getUsername());
    user.setEmail(authUser.getEmail());
    user.setAvatar(authUser.getAvatar());
    user.setEnabled(true);
    user.setEmailVerified(authUser.getEmail() != null);
    user.setCreatedTime(LocalDateTime.now());
    user.setUpdatedTime(LocalDateTime.now());
    user.setLastLoginTime(LocalDateTime.now());

    return user;
  }

  /**
   * 从第三方用户信息更新本地用户
   *
   * @param user 本地用户
   * @param authUser 第三方用户信息
   */
  private void updateUserFromOAuth(User user, AuthUser authUser) {
    // 更新昵称（如果第三方有提供且本地为空）
    if (user.getNickname() == null && authUser.getNickname() != null) {
      user.setNickname(authUser.getNickname());
    }

    // 更新邮箱（如果第三方有提供且本地为空）
    if (user.getEmail() == null && authUser.getEmail() != null) {
      user.setEmail(authUser.getEmail());
      user.setEmailVerified(true);
    }

    // 更新头像（如果第三方有提供）
    if (authUser.getAvatar() != null) {
      user.setAvatar(authUser.getAvatar());
    }

    user.setUpdatedTime(LocalDateTime.now());
  }

  /**
   * 检查第三方用户是否已绑定本地账号
   *
   * @param authUser 第三方用户信息
   * @param provider 登录提供商
   * @return 是否已绑定
   */
  public boolean isOAuthUserBound(AuthUser authUser, String provider) {
    String username = generateUsername(authUser, provider);
    return userRepository.existsByUsername(username);
  }

  /**
   * 绑定第三方账号到现有本地账号
   *
   * @param localUsername 本地用户名
   * @param authUser 第三方用户信息
   * @param provider 登录提供商
   * @return 是否绑定成功
   */
  @Transactional
  public boolean bindOAuthToLocalUser(String localUsername, AuthUser authUser, String provider) {
    try {
      Optional<User> localUserOpt = userRepository.findByUsername(localUsername);

      if (localUserOpt.isEmpty()) {
        logger.warn("Local user not found for binding: {}", localUsername);
        return false;
      }

      // 检查第三方账号是否已被其他用户绑定
      if (isOAuthUserBound(authUser, provider)) {
        logger.warn("OAuth user already bound: {} via {}", authUser.getUsername(), provider);
        return false;
      }

      // 这里可以扩展为在数据库中记录绑定关系
      // 目前简化处理，直接更新用户信息
      User localUser = localUserOpt.get();
      updateUserFromOAuth(localUser, authUser);
      userRepository.save(localUser);

      logger.info(
          "OAuth account bound to local user: {} -> {}", authUser.getUsername(), localUsername);
      return true;

    } catch (Exception e) {
      logger.error(
          "Error binding OAuth account: {} to {}", authUser.getUsername(), localUsername, e);
      return false;
    }
  }

  /**
   * 解绑第三方账号
   *
   * @param username 用户名
   * @param provider 登录提供商
   * @return 是否解绑成功
   */
  @Transactional
  public boolean unbindOAuthAccount(String username, String provider) {
    try {
      // 这里可以扩展为删除绑定关系记录
      // 目前简化处理
      logger.info("OAuth account unbound: {} from {}", username, provider);
      return true;

    } catch (Exception e) {
      logger.error("Error unbinding OAuth account: {} from {}", username, provider, e);
      return false;
    }
  }
}
