package cn.harryh.arkpets.auth.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

  /** 注册Sa-Token拦截器，打开注解式鉴权功能 */
  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
    registry
        .addInterceptor(
            new SaInterceptor(
                handle -> {
                  SaRouter.match("/**") // 拦截所有路由
                      .notMatch("/auth/login") // 排除登录接口
                      .notMatch("/auth/register") // 排除注册接口
                      .notMatch("/auth/refresh") // 排除刷新token接口
                      .notMatch("/auth/logout") // 排除登出接口
                      .notMatch("/auth/oauth/**") // 排除第三方登录接口
                      .notMatch("/actuator/**") // 排除监控端点
                      .notMatch("/swagger-ui/**") // 排除Swagger UI
                      .notMatch("/v3/api-docs/**") // 排除API文档
                      .notMatch("/favicon.ico") // 排除图标
                      .check(r -> StpUtil.checkLogin()); // 执行登录校验
                }))
        .addPathPatterns("/**"); // 拦截所有路径
  }
}
