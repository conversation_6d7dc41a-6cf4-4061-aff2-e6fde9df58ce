package cn.harryh.arkpets.auth.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "users")
public class User {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(unique = true, nullable = false, length = 50)
  private String username;

  @Column(nullable = false, length = 255)
  private String password;

  @Column(length = 100)
  private String email;

  @Column(length = 100)
  private String nickname;

  @Column(nullable = false)
  private Boolean enabled = true;

  @Column(name = "created_time", nullable = false)
  private LocalDateTime createdTime;

  @Column(name = "updated_time", nullable = false)
  private LocalDateTime updatedTime;

  @Column(name = "last_login_time")
  private LocalDateTime lastLoginTime;

  @Column(length = 500)
  private String avatar;

  @Column(length = 20)
  private String phone;

  @Column(name = "email_verified")
  private Boolean emailVerified = false;

  @Column(name = "phone_verified")
  private Boolean phoneVerified = false;

  public User() {}

  public User(String username, String password) {
    this.username = username;
    this.password = password;
    this.createdTime = LocalDateTime.now();
    this.updatedTime = LocalDateTime.now();
  }

  // Getters and Setters
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getNickname() {
    return nickname;
  }

  public void setNickname(String nickname) {
    this.nickname = nickname;
  }

  public Boolean getEnabled() {
    return enabled;
  }

  public void setEnabled(Boolean enabled) {
    this.enabled = enabled;
  }

  public LocalDateTime getCreatedTime() {
    return createdTime;
  }

  public void setCreatedTime(LocalDateTime createdTime) {
    this.createdTime = createdTime;
  }

  public LocalDateTime getUpdatedTime() {
    return updatedTime;
  }

  public void setUpdatedTime(LocalDateTime updatedTime) {
    this.updatedTime = updatedTime;
  }

  public LocalDateTime getLastLoginTime() {
    return lastLoginTime;
  }

  public void setLastLoginTime(LocalDateTime lastLoginTime) {
    this.lastLoginTime = lastLoginTime;
  }

  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public Boolean getEmailVerified() {
    return emailVerified;
  }

  public void setEmailVerified(Boolean emailVerified) {
    this.emailVerified = emailVerified;
  }

  public Boolean getPhoneVerified() {
    return phoneVerified;
  }

  public void setPhoneVerified(Boolean phoneVerified) {
    this.phoneVerified = phoneVerified;
  }

  @PrePersist
  protected void onCreate() {
    createdTime = LocalDateTime.now();
    updatedTime = LocalDateTime.now();
  }

  @PreUpdate
  protected void onUpdate() {
    updatedTime = LocalDateTime.now();
  }

  @Override
  public String toString() {
    return "User{"
        + "id="
        + id
        + ", username='"
        + username
        + '\''
        + ", email='"
        + email
        + '\''
        + ", nickname='"
        + nickname
        + '\''
        + ", enabled="
        + enabled
        + ", createdTime="
        + createdTime
        + '}';
  }
}
