package cn.harryh.arkpets.auth.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.harryh.arkpets.auth.dto.LoginResponse;
import cn.harryh.arkpets.auth.service.OAuthService;
import cn.harryh.arkpets.constants.AppConstants;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 第三方登录控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/auth/oauth")
@CrossOrigin(origins = "*")
public class OAuthController {

  private static final Logger logger = LoggerFactory.getLogger(OAuthController.class);

  @Autowired private AuthGithubRequest githubAuthRequest;

  @Autowired private AuthGoogleRequest googleAuthRequest;

  @Autowired private AuthQqRequest qqAuthRequest;

  @Autowired private AuthWeChatOpenRequest wechatAuthRequest;

  @Autowired private OAuthService oAuthService;

  /** 获取GitHub授权URL */
  @GetMapping("/github/authorize")
  public ResponseEntity<LoginResponse> githubAuthorize() {
    try {
      String authorizeUrl = githubAuthRequest.authorize();
      logger.info("GitHub authorize URL generated: {}", authorizeUrl);

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("获取授权URL成功")
              .token(authorizeUrl) // 这里复用token字段传递授权URL
              .build());

    } catch (Exception e) {
      logger.error("Error generating GitHub authorize URL", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("获取授权URL失败").build());
    }
  }

  /** GitHub登录回调 */
  @GetMapping("/github/callback")
  public ResponseEntity<LoginResponse> githubCallback(AuthCallback callback) {
    try {
      logger.info("GitHub callback received: {}", callback);

      AuthResponse<AuthUser> response = githubAuthRequest.login(callback);

      if (!response.ok()) {
        logger.warn("GitHub login failed: {}", response.getMsg());
        return ResponseEntity.badRequest()
            .body(
                LoginResponse.builder()
                    .success(false)
                    .message("GitHub登录失败: " + response.getMsg())
                    .build());
      }

      AuthUser authUser = response.getData();
      logger.info("GitHub login successful: {}", authUser.getUsername());

      // 处理第三方用户登录
      String username = oAuthService.handleOAuthLogin(authUser, "github");

      // 执行Sa-Token登录
      StpUtil.login(username);
      SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("GitHub登录成功")
              .token(tokenInfo.getTokenValue())
              .tokenName(tokenInfo.getTokenName())
              .tokenTimeout(tokenInfo.getTokenTimeout())
              .username(username)
              .build());

    } catch (Exception e) {
      logger.error("GitHub callback error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("GitHub登录失败").build());
    }
  }

  /** 获取Google授权URL */
  @GetMapping("/google/authorize")
  public ResponseEntity<LoginResponse> googleAuthorize() {
    try {
      String authorizeUrl = googleAuthRequest.authorize();
      logger.info("Google authorize URL generated: {}", authorizeUrl);

      return ResponseEntity.ok(
          LoginResponse.builder().success(true).message("获取授权URL成功").token(authorizeUrl).build());

    } catch (Exception e) {
      logger.error("Error generating Google authorize URL", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("获取授权URL失败").build());
    }
  }

  /** Google登录回调 */
  @GetMapping("/google/callback")
  public ResponseEntity<LoginResponse> googleCallback(AuthCallback callback) {
    try {
      logger.info("Google callback received: {}", callback);

      AuthResponse<AuthUser> response = googleAuthRequest.login(callback);

      if (!response.ok()) {
        logger.warn("Google login failed: {}", response.getMsg());
        return ResponseEntity.badRequest()
            .body(
                LoginResponse.builder()
                    .success(false)
                    .message("Google登录失败: " + response.getMsg())
                    .build());
      }

      AuthUser authUser = response.getData();
      logger.info("Google login successful: {}", authUser.getUsername());

      String username = oAuthService.handleOAuthLogin(authUser, "google");

      StpUtil.login(username);
      SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("Google登录成功")
              .token(tokenInfo.getTokenValue())
              .tokenName(tokenInfo.getTokenName())
              .tokenTimeout(tokenInfo.getTokenTimeout())
              .username(username)
              .build());

    } catch (Exception e) {
      logger.error("Google callback error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("Google登录失败").build());
    }
  }

  /** 获取QQ授权URL */
  @GetMapping("/qq/authorize")
  public ResponseEntity<LoginResponse> qqAuthorize() {
    try {
      String authorizeUrl = qqAuthRequest.authorize();
      logger.info("QQ authorize URL generated: {}", authorizeUrl);

      return ResponseEntity.ok(
          LoginResponse.builder().success(true).message("获取授权URL成功").token(authorizeUrl).build());

    } catch (Exception e) {
      logger.error("Error generating QQ authorize URL", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("获取授权URL失败").build());
    }
  }

  /** QQ登录回调 */
  @GetMapping("/qq/callback")
  public ResponseEntity<LoginResponse> qqCallback(AuthCallback callback) {
    try {
      logger.info("QQ callback received: {}", callback);

      AuthResponse<AuthUser> response = qqAuthRequest.login(callback);

      if (!response.ok()) {
        logger.warn("QQ login failed: {}", response.getMsg());
        return ResponseEntity.badRequest()
            .body(
                LoginResponse.builder()
                    .success(false)
                    .message("QQ登录失败: " + response.getMsg())
                    .build());
      }

      AuthUser authUser = response.getData();
      logger.info("QQ login successful: {}", authUser.getUsername());

      String username = oAuthService.handleOAuthLogin(authUser, "qq");

      StpUtil.login(username);
      SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("QQ登录成功")
              .token(tokenInfo.getTokenValue())
              .tokenName(tokenInfo.getTokenName())
              .tokenTimeout(tokenInfo.getTokenTimeout())
              .username(username)
              .build());

    } catch (Exception e) {
      logger.error("QQ callback error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("QQ登录失败").build());
    }
  }

  /** 获取微信授权URL */
  @GetMapping("/wechat/authorize")
  public ResponseEntity<LoginResponse> wechatAuthorize() {
    try {
      String authorizeUrl = wechatAuthRequest.authorize();
      logger.info("WeChat authorize URL generated: {}", authorizeUrl);

      return ResponseEntity.ok(
          LoginResponse.builder().success(true).message("获取授权URL成功").token(authorizeUrl).build());

    } catch (Exception e) {
      logger.error("Error generating WeChat authorize URL", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("获取授权URL失败").build());
    }
  }

  /** 微信登录回调 */
  @GetMapping("/wechat/callback")
  public ResponseEntity<LoginResponse> wechatCallback(AuthCallback callback) {
    try {
      logger.info("WeChat callback received: {}", callback);

      AuthResponse<AuthUser> response = wechatAuthRequest.login(callback);

      if (!response.ok()) {
        logger.warn("WeChat login failed: {}", response.getMsg());
        return ResponseEntity.badRequest()
            .body(
                LoginResponse.builder()
                    .success(false)
                    .message("微信登录失败: " + response.getMsg())
                    .build());
      }

      AuthUser authUser = response.getData();
      logger.info("WeChat login successful: {}", authUser.getUsername());

      String username = oAuthService.handleOAuthLogin(authUser, "wechat");

      StpUtil.login(username);
      SaTokenInfo tokenInfo = StpUtil.getTokenInfo();

      return ResponseEntity.ok(
          LoginResponse.builder()
              .success(true)
              .message("微信登录成功")
              .token(tokenInfo.getTokenValue())
              .tokenName(tokenInfo.getTokenName())
              .tokenTimeout(tokenInfo.getTokenTimeout())
              .username(username)
              .build());

    } catch (Exception e) {
      logger.error("WeChat callback error", e);
      return ResponseEntity.internalServerError()
          .body(LoginResponse.builder().success(false).message("微信登录失败").build());
    }
  }
}
