// 认证服务构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // Spring Boot依赖已在根build.gradle中配置
    
    // Sa-Token认证框架
    implementation "cn.dev33:sa-token-spring-boot3-starter:${saTokenVersion}"
    implementation "cn.dev33:sa-token-redis-jackson:${saTokenVersion}"
    
    // JustAuth第三方登录
    implementation 'me.zhyd.oauth:JustAuth:1.16.6'

    // JWT支持
    implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
    implementation 'io.jsonwebtoken:jjwt-impl:0.12.3'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.12.3'

    // Spring Security (用于密码加密)
    implementation 'org.springframework.security:spring-security-crypto:6.2.1'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.auth.AuthServiceApplication'
}
