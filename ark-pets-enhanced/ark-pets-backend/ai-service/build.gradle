// AI服务构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // Spring Boot依赖已在根build.gradle中配置
    
    // LangChain4j (使用稳定版本)
    implementation 'dev.langchain4j:langchain4j:0.25.0'
    implementation 'dev.langchain4j:langchain4j-open-ai:0.25.0'
    implementation 'dev.langchain4j:langchain4j-ollama:0.25.0'
    implementation 'dev.langchain4j:langchain4j-embeddings:0.25.0'
    implementation 'dev.langchain4j:langchain4j-embeddings-all-minilm-l6-v2:0.25.0'

    // OpenAI Java客户端
    implementation 'com.theokanning.openai-gpt3-java:service:0.18.2'
    
    // HTTP客户端
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // JSON处理增强
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.16.1'
    
    // 文本处理
    implementation 'org.apache.commons:commons-text:1.11.0'
    
    // 缓存
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.ai.AiServiceApplication'
}
