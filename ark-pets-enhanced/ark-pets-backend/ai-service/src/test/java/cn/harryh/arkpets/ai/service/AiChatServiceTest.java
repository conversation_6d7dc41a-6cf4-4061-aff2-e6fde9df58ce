package cn.harryh.arkpets.ai.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import dev.langchain4j.model.chat.ChatLanguageModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AiChatServiceTest {

  @Mock private ChatLanguageModel openaiGpt35Model;

  @Mock private ChatLanguageModel openaiGpt4Model;

  @Mock private ChatLanguageModel ollamaLlama2Model;

  @InjectMocks private AiChatService aiChatService;

  @BeforeEach
  void setUp() {
    // 配置模拟响应 - 直接返回字符串而不是Response对象
    when(openaiGpt35Model.generate(anyString())).thenReturn("这是一个测试回复");
    when(openaiGpt4Model.generate(anyString())).thenReturn("这是一个测试回复");
    when(ollamaLlama2Model.generate(anyString())).thenReturn("这是一个测试回复");
  }

  @Test
  void chat_WithDefaultModel_ReturnsResponse() {
    // 执行测试
    String response = aiChatService.chat("你好", "testuser", "测试上下文");

    // 验证结果
    assertNotNull(response);
    assertEquals("这是一个测试回复", response);
    verify(openaiGpt35Model).generate(anyString());
  }

  @Test
  void chatWithModel_WithSpecificModel_ReturnsResponse() {
    // 执行测试
    String response = aiChatService.chatWithModel("你好", "testuser", "测试上下文", "gpt4");

    // 验证结果
    assertNotNull(response);
    assertEquals("这是一个测试回复", response);
    verify(openaiGpt4Model).generate(anyString());
  }

  @Test
  void generateCreativeResponse_ReturnsResponse() {
    // 执行测试
    String response = aiChatService.generateCreativeResponse("讲个故事", "testuser");

    // 验证结果
    assertNotNull(response);
    assertEquals("这是一个测试回复", response);
    verify(openaiGpt35Model).generate(anyString());
  }

  @Test
  void analyzeEmotion_ReturnsResponse() {
    // 执行测试
    String response = aiChatService.analyzeEmotion("我很开心");

    // 验证结果
    assertNotNull(response);
    assertEquals("这是一个测试回复", response);
    verify(openaiGpt35Model).generate(anyString());
  }

  @Test
  void chat_WithEmptyInput_ThrowsException() {
    // 执行测试并验证异常
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          aiChatService.chat("", "testuser", "测试上下文");
        });
  }

  @Test
  void chatWithModel_WithInvalidModel_ThrowsException() {
    // 执行测试并验证异常
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          aiChatService.chatWithModel("你好", "testuser", "测试上下文", "invalid_model");
        });
  }
}
