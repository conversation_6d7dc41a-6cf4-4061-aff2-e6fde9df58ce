package cn.harryh.arkpets.ai.service;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

/** AI聊天服务测试 简化版测试，主要验证服务能正常创建和基本功能 */
class AiChatServiceTest {

  @Test
  void testServiceCreation() {
    // 测试服务能正常创建
    AiChatService service = new AiChatService();
    assertNotNull(service);
  }

  @Test
  void testGetAvailableModels() {
    // 测试获取可用模型列表
    AiChatService service = new AiChatService();
    assertNotNull(service.getAvailableModels());
  }
}
