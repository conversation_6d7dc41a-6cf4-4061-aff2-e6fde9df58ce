server:
  port: 8083

spring:
  application:
    name: ark-pets-ai-service
  
  datasource:
    url: *******************************************
    username: arkpets
    password: arkpets123
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  # Spring AI配置
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 1000
    
    ollama:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: llama2
          temperature: 0.7

# LangChain4j配置
langchain4j:
  open-ai:
    chat-model:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      model-name: gpt-3.5-turbo
      temperature: 0.7
      max-tokens: 1000
      timeout: 30s
  
  ollama:
    chat-model:
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      model-name: llama2
      temperature: 0.7
      timeout: 30s

# AI服务配置
ai-service:
  # 默认模型提供商
  default-provider: openai
  
  # 模型配置
  models:
    openai:
      gpt-3.5-turbo:
        max-tokens: 1000
        temperature: 0.7
        top-p: 1.0
      gpt-4:
        max-tokens: 2000
        temperature: 0.7
        top-p: 1.0
    
    ollama:
      llama2:
        temperature: 0.7
        top-p: 0.9
      codellama:
        temperature: 0.3
        top-p: 0.9
  
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600 # 1小时
    max-size: 1000
  
  # 限流配置
  rate-limit:
    enabled: true
    requests-per-minute: 60
    requests-per-hour: 1000

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    dev.langchain4j: INFO
    org.springframework.ai: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
