package cn.harryh.arkpets.ai.controller;

import cn.harryh.arkpets.ai.dto.ChatRequest;
import cn.harryh.arkpets.ai.dto.ChatResponse;
import cn.harryh.arkpets.ai.service.AiChatService;
import cn.harryh.arkpets.constants.AppConstants;
import jakarta.validation.Valid;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * AI聊天控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/ai")
@CrossOrigin(origins = "*")
public class AiChatController {

  private static final Logger logger = LoggerFactory.getLogger(AiChatController.class);

  @Autowired private AiChatService aiChatService;

  /**
   * 发送聊天消息
   *
   * @param request 聊天请求
   * @return 聊天响应
   */
  @PostMapping("/chat")
  public ResponseEntity<ChatResponse> chat(@Valid @RequestBody ChatRequest request) {
    logger.info("Received chat request from user: {}", request.getUsername());

    try {
      String response =
          aiChatService.chat(request.getMessage(), request.getUsername(), request.getContext());

      return ResponseEntity.ok(
          ChatResponse.builder()
              .success(true)
              .message("聊天成功")
              .response(response)
              .model("default")
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error processing chat request from user: {}", request.getUsername(), e);
      return ResponseEntity.internalServerError()
          .body(
              ChatResponse.builder()
                  .success(false)
                  .message("聊天失败，请稍后重试")
                  .response("抱歉，我现在有点累了~ 😅")
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 使用指定模型聊天
   *
   * @param request 聊天请求
   * @param modelType 模型类型
   * @return 聊天响应
   */
  @PostMapping("/chat/{modelType}")
  public ResponseEntity<ChatResponse> chatWithModel(
      @Valid @RequestBody ChatRequest request, @PathVariable String modelType) {
    logger.info(
        "Received chat request with model {} from user: {}", modelType, request.getUsername());

    try {
      String response =
          aiChatService.chatWithModel(
              request.getMessage(), request.getUsername(), request.getContext(), modelType);

      return ResponseEntity.ok(
          ChatResponse.builder()
              .success(true)
              .message("聊天成功")
              .response(response)
              .model(modelType)
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error(
          "Error processing chat request with model {} from user: {}",
          modelType,
          request.getUsername(),
          e);
      return ResponseEntity.internalServerError()
          .body(
              ChatResponse.builder()
                  .success(false)
                  .message("聊天失败，请稍后重试")
                  .response("抱歉，我现在有点累了~ 😅")
                  .model(modelType)
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 生成创意回复
   *
   * @param topic 话题
   * @param username 用户名（可选）
   * @return 创意回复
   */
  @GetMapping("/creative")
  public ResponseEntity<ChatResponse> generateCreative(
      @RequestParam String topic, @RequestParam(required = false) String username) {
    logger.info("Generating creative response for topic: {} from user: {}", topic, username);

    try {
      String response = aiChatService.generateCreativeResponse(topic, username);

      return ResponseEntity.ok(
          ChatResponse.builder()
              .success(true)
              .message("创意生成成功")
              .response(response)
              .model("creative")
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error generating creative response for topic: {}", topic, e);
      return ResponseEntity.internalServerError()
          .body(
              ChatResponse.builder()
                  .success(false)
                  .message("创意生成失败")
                  .response("让我想想...这个话题很有趣呢！😊")
                  .model("creative")
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 情感分析
   *
   * @param message 要分析的消息
   * @return 情感分析结果
   */
  @PostMapping("/emotion")
  public ResponseEntity<ChatResponse> analyzeEmotion(@RequestParam String message) {
    logger.info("Analyzing emotion for message: {}", message);

    try {
      String analysis = aiChatService.analyzeEmotion(message);

      return ResponseEntity.ok(
          ChatResponse.builder()
              .success(true)
              .message("情感分析完成")
              .response(analysis)
              .model("emotion-analyzer")
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error analyzing emotion for message: {}", message, e);
      return ResponseEntity.internalServerError()
          .body(
              ChatResponse.builder()
                  .success(false)
                  .message("情感分析失败")
                  .response("{\"emotion\":\"neutral\",\"confidence\":0.5}")
                  .model("emotion-analyzer")
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 获取可用的AI模型列表
   *
   * @return 模型可用性状态
   */
  @GetMapping("/models")
  public ResponseEntity<Map<String, Boolean>> getAvailableModels() {
    logger.info("Getting available AI models");

    try {
      Map<String, Boolean> models = aiChatService.getAvailableModels();
      return ResponseEntity.ok(models);

    } catch (Exception e) {
      logger.error("Error getting available models", e);
      return ResponseEntity.internalServerError().body(Map.of());
    }
  }

  /**
   * 检查特定模型的可用性
   *
   * @param modelType 模型类型
   * @return 模型可用性
   */
  @GetMapping("/models/{modelType}/status")
  public ResponseEntity<Map<String, Object>> checkModelStatus(@PathVariable String modelType) {
    logger.info("Checking status for model: {}", modelType);

    try {
      boolean available = aiChatService.isModelAvailable(modelType);

      return ResponseEntity.ok(
          Map.of(
              "model", modelType,
              "available", available,
              "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error checking model status for: {}", modelType, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "model",
                  modelType,
                  "available",
                  false,
                  "error",
                  e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /**
   * 健康检查
   *
   * @return 服务状态
   */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    return ResponseEntity.ok(
        Map.of(
            "status",
            "UP",
            "service",
            "ai-service",
            "timestamp",
            System.currentTimeMillis(),
            "models",
            aiChatService.getAvailableModels()));
  }
}
