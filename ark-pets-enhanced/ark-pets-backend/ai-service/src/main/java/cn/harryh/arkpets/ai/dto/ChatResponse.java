package cn.harryh.arkpets.ai.dto;

/**
 * 聊天响应DTO
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class ChatResponse {

  private Boolean success;
  private String message;
  private String response;
  private String model;
  private Long timestamp;
  private String sessionId;
  private Integer tokenUsage;
  private String emotion;

  public ChatResponse() {}

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getResponse() {
    return response;
  }

  public void setResponse(String response) {
    this.response = response;
  }

  public String getModel() {
    return model;
  }

  public void setModel(String model) {
    this.model = model;
  }

  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }

  public String getSessionId() {
    return sessionId;
  }

  public void setSessionId(String sessionId) {
    this.sessionId = sessionId;
  }

  public Integer getTokenUsage() {
    return tokenUsage;
  }

  public void setTokenUsage(Integer tokenUsage) {
    this.tokenUsage = tokenUsage;
  }

  public String getEmotion() {
    return emotion;
  }

  public void setEmotion(String emotion) {
    this.emotion = emotion;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    private ChatResponse response = new ChatResponse();

    public Builder success(Boolean success) {
      response.setSuccess(success);
      return this;
    }

    public Builder message(String message) {
      response.setMessage(message);
      return this;
    }

    public Builder response(String responseText) {
      response.setResponse(responseText);
      return this;
    }

    public Builder model(String model) {
      response.setModel(model);
      return this;
    }

    public Builder timestamp(Long timestamp) {
      response.setTimestamp(timestamp);
      return this;
    }

    public Builder sessionId(String sessionId) {
      response.setSessionId(sessionId);
      return this;
    }

    public Builder tokenUsage(Integer tokenUsage) {
      response.setTokenUsage(tokenUsage);
      return this;
    }

    public Builder emotion(String emotion) {
      response.setEmotion(emotion);
      return this;
    }

    public ChatResponse build() {
      return response;
    }
  }

  @Override
  public String toString() {
    return "ChatResponse{"
        + "success="
        + success
        + ", message='"
        + message
        + '\''
        + ", model='"
        + model
        + '\''
        + ", timestamp="
        + timestamp
        + ", sessionId='"
        + sessionId
        + '\''
        + '}';
  }
}
