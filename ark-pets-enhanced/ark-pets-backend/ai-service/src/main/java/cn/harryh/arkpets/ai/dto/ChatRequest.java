package cn.harryh.arkpets.ai.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 聊天请求DTO
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class ChatRequest {

  @NotBlank(message = "消息内容不能为空")
  @Size(max = 2000, message = "消息长度不能超过2000个字符")
  private String message;

  @Size(max = 100, message = "用户名长度不能超过100个字符")
  private String username;

  @Size(max = 1000, message = "上下文长度不能超过1000个字符")
  private String context;

  private String sessionId;

  private String modelPreference;

  public ChatRequest() {}

  public ChatRequest(String message, String username) {
    this.message = message;
    this.username = username;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public String getContext() {
    return context;
  }

  public void setContext(String context) {
    this.context = context;
  }

  public String getSessionId() {
    return sessionId;
  }

  public void setSessionId(String sessionId) {
    this.sessionId = sessionId;
  }

  public String getModelPreference() {
    return modelPreference;
  }

  public void setModelPreference(String modelPreference) {
    this.modelPreference = modelPreference;
  }

  @Override
  public String toString() {
    return "ChatRequest{"
        + "message='"
        + message
        + '\''
        + ", username='"
        + username
        + '\''
        + ", sessionId='"
        + sessionId
        + '\''
        + ", modelPreference='"
        + modelPreference
        + '\''
        + '}';
  }
}
