package cn.harryh.arkpets.ai;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * AI服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication
@EnableFeignClients
@EnableAsync
public class AiServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(AiServiceApplication.class, args);
  }
}
