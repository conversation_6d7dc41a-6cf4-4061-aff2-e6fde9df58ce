package cn.harryh.arkpets.ai.config;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import java.time.Duration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * AI模型配置类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class AiModelConfig {

  @Value("${langchain4j.open-ai.chat-model.api-key:}")
  private String openaiApiKey;

  @Value("${langchain4j.open-ai.chat-model.base-url:https://api.openai.com}")
  private String openaiBaseUrl;

  @Value("${langchain4j.ollama.chat-model.base-url:http://localhost:11434}")
  private String ollamaBaseUrl;

  /** OpenAI GPT-3.5-turbo模型 */
  @Bean("openaiGpt35Model")
  @Primary
  public ChatLanguageModel openaiGpt35Model() {
    return OpenAiChatModel.builder()
        .apiKey(openaiApiKey)
        .baseUrl(openaiBaseUrl)
        .modelName("gpt-3.5-turbo")
        .temperature(0.7)
        .maxTokens(1000)
        .timeout(Duration.ofSeconds(30))
        .maxRetries(3)
        .build();
  }

  /** OpenAI GPT-4模型 */
  @Bean("openaiGpt4Model")
  public ChatLanguageModel openaiGpt4Model() {
    return OpenAiChatModel.builder()
        .apiKey(openaiApiKey)
        .baseUrl(openaiBaseUrl)
        .modelName("gpt-4")
        .temperature(0.7)
        .maxTokens(2000)
        .timeout(Duration.ofSeconds(60))
        .maxRetries(3)
        .build();
  }

  /** Ollama Llama2模型 */
  @Bean("ollamaLlama2Model")
  public ChatLanguageModel ollamaLlama2Model() {
    return OllamaChatModel.builder()
        .baseUrl(ollamaBaseUrl)
        .modelName("llama2")
        .temperature(0.7)
        .timeout(Duration.ofSeconds(30))
        .build();
  }

  /** Ollama CodeLlama模型 */
  @Bean("ollamaCodeLlamaModel")
  public ChatLanguageModel ollamaCodeLlamaModel() {
    return OllamaChatModel.builder()
        .baseUrl(ollamaBaseUrl)
        .modelName("codellama")
        .temperature(0.3)
        .timeout(Duration.ofSeconds(30))
        .build();
  }
}
