package cn.harryh.arkpets.ai.service;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * AI聊天服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class AiChatService {

  private static final Logger logger = LoggerFactory.getLogger(AiChatService.class);

  @Autowired
  @Qualifier("openaiGpt35Model")
  private ChatLanguageModel defaultChatModel;

  @Autowired
  @Qualifier("openaiGpt4Model")
  private ChatLanguageModel gpt4Model;

  @Autowired
  @Qualifier("ollamaLlama2Model")
  private ChatLanguageModel llama2Model;

  @Value("${ai-service.default-provider:openai}")
  private String defaultProvider;

  // 系统提示词模板
  private static final String SYSTEM_PROMPT_TEMPLATE =
      """
      你是Ark-Pets的AI助手，一个可爱的桌面宠物伙伴。你的特点：

      1. 性格：活泼、友善、乐于助人
      2. 语言风格：轻松愉快，偶尔使用可爱的表情符号
      3. 专长：帮助用户解决问题，提供建议，陪伴聊天
      4. 限制：不讨论政治敏感话题，不提供有害信息

      用户信息：
      - 用户名：{{username}}
      - 当前时间：{{currentTime}}
      - 上下文：{{context}}

      请根据用户的问题给出合适的回答。
      """;

  /**
   * 发送聊天消息
   *
   * @param message 用户消息
   * @param username 用户名
   * @param context 上下文信息
   * @return AI回复
   */
  @Cacheable(value = "chatResponses", key = "#message + '_' + #username")
  public String chat(String message, String username, String context) {
    try {
      logger.info("Processing chat message from user: {}", username);

      // 构建提示词
      PromptTemplate promptTemplate =
          PromptTemplate.from(SYSTEM_PROMPT_TEMPLATE + "\n\n用户问题：{{userMessage}}");

      Map<String, Object> variables = new HashMap<>();
      variables.put("username", username != null ? username : "用户");
      variables.put("currentTime", java.time.LocalDateTime.now().toString());
      variables.put("context", context != null ? context : "无特殊上下文");
      variables.put("userMessage", message);

      Prompt prompt = promptTemplate.apply(variables);

      // 使用默认模型生成回复
      String response = defaultChatModel.generate(prompt.text());

      logger.info("Generated chat response for user: {}", username);
      return response;

    } catch (Exception e) {
      logger.error("Error processing chat message from user: {}", username, e);
      return "抱歉，我现在有点累了，请稍后再试试吧~ 😅";
    }
  }

  /**
   * 使用指定模型发送聊天消息
   *
   * @param message 用户消息
   * @param username 用户名
   * @param context 上下文信息
   * @param modelType 模型类型 (gpt35, gpt4, llama2)
   * @return AI回复
   */
  public String chatWithModel(String message, String username, String context, String modelType) {
    try {
      logger.info("Processing chat message with model {} from user: {}", modelType, username);

      ChatLanguageModel model = selectModel(modelType);
      if (model == null) {
        return "抱歉，指定的AI模型暂时不可用~ 😔";
      }

      // 构建提示词
      PromptTemplate promptTemplate =
          PromptTemplate.from(SYSTEM_PROMPT_TEMPLATE + "\n\n用户问题：{{userMessage}}");

      Map<String, Object> variables = new HashMap<>();
      variables.put("username", username != null ? username : "用户");
      variables.put("currentTime", java.time.LocalDateTime.now().toString());
      variables.put("context", context != null ? context : "无特殊上下文");
      variables.put("userMessage", message);

      Prompt prompt = promptTemplate.apply(variables);

      // 生成回复
      String response = model.generate(prompt.text());

      logger.info("Generated chat response with model {} for user: {}", modelType, username);
      return response;

    } catch (Exception e) {
      logger.error(
          "Error processing chat message with model {} from user: {}", modelType, username, e);
      return "抱歉，我现在有点累了，请稍后再试试吧~ 😅";
    }
  }

  /**
   * 生成创意回复
   *
   * @param topic 话题
   * @param username 用户名
   * @return 创意回复
   */
  public String generateCreativeResponse(String topic, String username) {
    try {
      logger.info("Generating creative response for topic: {} from user: {}", topic, username);

      String creativePrompt =
          String.format(
              """
              作为Ark-Pets的AI助手，请围绕话题"%s"生成一个有趣、创意的回复。

              要求：
              1. 内容积极正面
              2. 富有想象力
              3. 适合作为桌面宠物的回复
              4. 长度控制在100字以内

              用户名：%s
              """,
              topic, username != null ? username : "用户");

      String response = defaultChatModel.generate(creativePrompt);

      logger.info("Generated creative response for user: {}", username);
      return response;

    } catch (Exception e) {
      logger.error("Error generating creative response for user: {}", username, e);
      return "让我想想...嗯，这个话题很有趣呢！不过我现在脑子有点转不过来~ 😊";
    }
  }

  /**
   * 情感分析
   *
   * @param message 用户消息
   * @return 情感分析结果
   */
  public String analyzeEmotion(String message) {
    try {
      logger.info("Analyzing emotion for message: {}", message);

      String emotionPrompt =
          String.format(
              """
              请分析以下文本的情感倾向，并返回JSON格式的结果：

              文本："%s"

              返回格式：
              {
                "emotion": "情感类型(positive/negative/neutral)",
                "confidence": "置信度(0-1)",
                "keywords": ["关键词1", "关键词2"],
                "suggestion": "回复建议"
              }
              """,
              message);

      String response = defaultChatModel.generate(emotionPrompt);

      logger.info("Completed emotion analysis");
      return response;

    } catch (Exception e) {
      logger.error("Error analyzing emotion", e);
      return "{\"emotion\":\"neutral\",\"confidence\":0.5,\"keywords\":[],\"suggestion\":\"正常回复即可\"}";
    }
  }

  /**
   * 选择AI模型
   *
   * @param modelType 模型类型
   * @return 对应的模型实例
   */
  private ChatLanguageModel selectModel(String modelType) {
    return switch (modelType.toLowerCase()) {
      case "gpt35", "gpt-3.5", "openai" -> defaultChatModel;
      case "gpt4", "gpt-4" -> gpt4Model;
      case "llama2", "llama" -> llama2Model;
      default -> defaultChatModel;
    };
  }

  /**
   * 检查模型可用性
   *
   * @param modelType 模型类型
   * @return 是否可用
   */
  public boolean isModelAvailable(String modelType) {
    try {
      ChatLanguageModel model = selectModel(modelType);
      if (model == null) {
        return false;
      }

      // 发送测试消息
      String testResponse = model.generate("Hello");
      return testResponse != null && !testResponse.trim().isEmpty();

    } catch (Exception e) {
      logger.warn("Model {} is not available: {}", modelType, e.getMessage());
      return false;
    }
  }

  /**
   * 获取可用的模型列表
   *
   * @return 可用模型列表
   */
  public Map<String, Boolean> getAvailableModels() {
    Map<String, Boolean> models = new HashMap<>();
    models.put("gpt35", isModelAvailable("gpt35"));
    models.put("gpt4", isModelAvailable("gpt4"));
    models.put("llama2", isModelAvailable("llama2"));
    return models;
  }
}
