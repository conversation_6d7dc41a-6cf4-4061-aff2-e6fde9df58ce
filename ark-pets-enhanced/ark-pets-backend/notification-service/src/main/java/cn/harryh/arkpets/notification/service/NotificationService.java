package cn.harryh.arkpets.notification.service;

import cn.harryh.arkpets.notification.entity.Notification;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 通知服务类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class NotificationService {

  private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

  @Autowired private WebSocketSessionManager sessionManager;

  @Autowired private RedisTemplate<String, Object> redisTemplate;

  /** 发送通知给指定用户 */
  public void sendNotification(
      String userId, String title, String content, Notification.NotificationType type) {
    sendNotification(userId, title, content, type, Notification.NotificationPriority.NORMAL, null);
  }

  /** 发送通知给指定用户（带优先级） */
  public void sendNotification(
      String userId,
      String title,
      String content,
      Notification.NotificationType type,
      Notification.NotificationPriority priority,
      Map<String, String> metadata) {
    try {
      // 创建通知对象
      Notification notification = new Notification(userId, title, content, type);
      notification.setPriority(priority);
      notification.setMetadata(metadata);
      notification.setCreatedAt(LocalDateTime.now());

      // 保存到Redis (简化版本，实际应该保存到数据库)
      String notificationId = UUID.randomUUID().toString();
      notification.setId(notificationId);

      String key = "notification:" + notificationId;
      redisTemplate.opsForHash().putAll(key, notificationToMap(notification));
      redisTemplate.expire(key, 30, TimeUnit.DAYS);

      // 添加到用户通知列表
      String userNotificationsKey = "user_notifications:" + userId;
      redisTemplate.opsForList().leftPush(userNotificationsKey, notificationId);
      redisTemplate.expire(userNotificationsKey, 30, TimeUnit.DAYS);

      // 通过WebSocket发送实时通知
      sessionManager.sendNotificationToUser(userId, title, content, type.name());

      logger.info("发送通知成功: userId={}, type={}, title={}", userId, type, title);

    } catch (Exception e) {
      logger.error("发送通知失败", e);
    }
  }

  /** 发送系统通知给所有用户 */
  public void sendSystemNotification(String title, String content) {
    sendSystemNotification(title, content, Notification.NotificationPriority.NORMAL);
  }

  /** 发送系统通知给所有用户（带优先级） */
  public void sendSystemNotification(
      String title, String content, Notification.NotificationPriority priority) {
    try {
      // 创建系统通知
      Map<String, Object> notification = new HashMap<>();
      notification.put("type", "system_notification");
      notification.put("id", UUID.randomUUID().toString());
      notification.put("title", title);
      notification.put("content", content);
      notification.put("priority", priority.name());
      notification.put("timestamp", System.currentTimeMillis());

      // 广播给所有在线用户
      sessionManager.broadcastMessage(notification);

      logger.info("发送系统通知: title={}, priority={}", title, priority);

    } catch (Exception e) {
      logger.error("发送系统通知失败", e);
    }
  }

  /** 发送宠物状态通知 */
  public void sendPetStatusNotification(
      String userId, String petId, String petName, String statusMessage) {
    Map<String, String> metadata = new HashMap<>();
    metadata.put("petId", petId);
    metadata.put("petName", petName);

    sendNotification(
        userId,
        "宠物状态提醒",
        petName + " " + statusMessage,
        Notification.NotificationType.PET_STATUS,
        Notification.NotificationPriority.NORMAL,
        metadata);
  }

  /** 发送宠物升级通知 */
  public void sendPetLevelUpNotification(
      String userId, String petId, String petName, int newLevel) {
    Map<String, String> metadata = new HashMap<>();
    metadata.put("petId", petId);
    metadata.put("petName", petName);
    metadata.put("newLevel", String.valueOf(newLevel));

    sendNotification(
        userId,
        "宠物升级啦！",
        "恭喜！" + petName + " 升级到了 " + newLevel + " 级！",
        Notification.NotificationType.PET_LEVEL_UP,
        Notification.NotificationPriority.HIGH,
        metadata);
  }

  /** 发送宠物需要关注通知 */
  public void sendPetNeedsAttentionNotification(
      String userId, String petId, String petName, String reason) {
    Map<String, String> metadata = new HashMap<>();
    metadata.put("petId", petId);
    metadata.put("petName", petName);
    metadata.put("reason", reason);

    sendNotification(
        userId,
        "宠物需要关注",
        petName + " " + reason + "，请及时照顾！",
        Notification.NotificationType.PET_NEEDS_ATTENTION,
        Notification.NotificationPriority.HIGH,
        metadata);
  }

  /** 发送频道消息 */
  public void sendChannelMessage(String channel, String title, String content, String messageType) {
    Map<String, Object> message = new HashMap<>();
    message.put("type", "channel_message");
    message.put("messageType", messageType);
    message.put("title", title);
    message.put("content", content);
    message.put("timestamp", System.currentTimeMillis());

    sessionManager.sendMessageToChannel(channel, message);
    logger.info("发送频道消息: channel={}, title={}", channel, title);
  }

  /** 获取用户的通知列表 */
  public List<Map<String, Object>> getUserNotifications(String userId, int limit, int offset) {
    try {
      String userNotificationsKey = "user_notifications:" + userId;
      List<Object> notificationIds =
          redisTemplate.opsForList().range(userNotificationsKey, offset, offset + limit - 1);

      if (notificationIds == null || notificationIds.isEmpty()) {
        return Collections.emptyList();
      }

      List<Map<String, Object>> notifications = new ArrayList<>();
      for (Object notificationId : notificationIds) {
        String key = "notification:" + notificationId;
        Map<Object, Object> notificationData = redisTemplate.opsForHash().entries(key);
        if (!notificationData.isEmpty()) {
          notifications.add(convertToStringMap(notificationData));
        }
      }

      return notifications;

    } catch (Exception e) {
      logger.error("获取用户通知列表失败: userId={}", userId, e);
      return Collections.emptyList();
    }
  }

  /** 标记通知为已读 */
  public void markNotificationAsRead(String userId, String notificationId) {
    try {
      String key = "notification:" + notificationId;
      redisTemplate.opsForHash().put(key, "isRead", "true");
      redisTemplate.opsForHash().put(key, "readAt", LocalDateTime.now().toString());

      // 更新WebSocket会话管理器
      sessionManager.markNotificationAsRead(userId, notificationId);

      logger.debug("标记通知为已读: userId={}, notificationId={}", userId, notificationId);

    } catch (Exception e) {
      logger.error("标记通知为已读失败", e);
    }
  }

  /** 获取用户未读通知数量 */
  public int getUnreadNotificationCount(String userId) {
    return sessionManager.getUnreadNotificationCount(userId);
  }

  /** 删除过期通知 */
  public void cleanupExpiredNotifications() {
    try {
      // 这里应该实现清理过期通知的逻辑
      // 简化版本：记录日志
      logger.info("清理过期通知任务执行");

    } catch (Exception e) {
      logger.error("清理过期通知失败", e);
    }
  }

  /** 获取在线统计信息 */
  public Map<String, Object> getOnlineStats() {
    Map<String, Object> stats = new HashMap<>();
    stats.put("onlineUsers", sessionManager.getOnlineUserCount());
    stats.put("totalSessions", sessionManager.getTotalSessionCount());
    stats.put("timestamp", System.currentTimeMillis());
    return stats;
  }

  /** 将通知对象转换为Map */
  private Map<String, Object> notificationToMap(Notification notification) {
    Map<String, Object> map = new HashMap<>();
    map.put("id", notification.getId());
    map.put("userId", notification.getUserId());
    map.put("title", notification.getTitle());
    map.put("content", notification.getContent());
    map.put("type", notification.getType().name());
    map.put("priority", notification.getPriority().name());
    map.put("isRead", notification.getIsRead().toString());
    map.put("createdAt", notification.getCreatedAt().toString());

    if (notification.getReadAt() != null) {
      map.put("readAt", notification.getReadAt().toString());
    }

    if (notification.getMetadata() != null) {
      map.put("metadata", notification.getMetadata());
    }

    return map;
  }

  /** 转换Map类型 */
  private Map<String, Object> convertToStringMap(Map<Object, Object> objectMap) {
    Map<String, Object> stringMap = new HashMap<>();
    for (Map.Entry<Object, Object> entry : objectMap.entrySet()) {
      stringMap.put(entry.getKey().toString(), entry.getValue());
    }
    return stringMap;
  }
}
