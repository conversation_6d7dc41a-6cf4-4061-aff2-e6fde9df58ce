package cn.harryh.arkpets.notification.websocket;

import cn.harryh.arkpets.notification.service.WebSocketSessionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

/**
 * WebSocket消息处理器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Component
public class WebSocketHandler implements org.springframework.web.socket.WebSocketHandler {

  private static final Logger logger = LoggerFactory.getLogger(WebSocketHandler.class);

  @Autowired private WebSocketSessionManager sessionManager;

  private final ObjectMapper objectMapper = new ObjectMapper();

  @Override
  public void afterConnectionEstablished(WebSocketSession session) throws Exception {
    String userId = (String) session.getAttributes().get("userId");
    logger.info("WebSocket连接建立: sessionId={}, userId={}", session.getId(), userId);

    // 注册会话
    sessionManager.addSession(userId, session);

    // 发送连接成功消息
    Map<String, Object> welcomeMessage = new HashMap<>();
    welcomeMessage.put("type", "connection");
    welcomeMessage.put("status", "connected");
    welcomeMessage.put("message", "WebSocket连接成功");
    welcomeMessage.put("timestamp", System.currentTimeMillis());

    sendMessage(session, welcomeMessage);

    // 发送未读通知数量
    int unreadCount = sessionManager.getUnreadNotificationCount(userId);
    if (unreadCount > 0) {
      Map<String, Object> unreadMessage = new HashMap<>();
      unreadMessage.put("type", "unread_count");
      unreadMessage.put("count", unreadCount);
      unreadMessage.put("timestamp", System.currentTimeMillis());
      sendMessage(session, unreadMessage);
    }
  }

  @Override
  public void handleMessage(WebSocketSession session, WebSocketMessage<?> message)
      throws Exception {
    String userId = (String) session.getAttributes().get("userId");
    String payload = message.getPayload().toString();

    logger.debug(
        "收到WebSocket消息: sessionId={}, userId={}, message={}", session.getId(), userId, payload);

    try {
      // 解析消息
      @SuppressWarnings("unchecked")
      Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
      String messageType = (String) messageData.get("type");

      // 处理不同类型的消息
      switch (messageType) {
        case "ping" -> handlePingMessage(session, messageData);
        case "subscribe" -> handleSubscribeMessage(session, userId, messageData);
        case "unsubscribe" -> handleUnsubscribeMessage(session, userId, messageData);
        case "mark_read" -> handleMarkReadMessage(session, userId, messageData);
        case "get_notifications" -> handleGetNotificationsMessage(session, userId, messageData);
        default -> {
          logger.warn("未知的消息类型: {}", messageType);
          sendErrorMessage(session, "未知的消息类型: " + messageType);
        }
      }

    } catch (Exception e) {
      logger.error("处理WebSocket消息时发生错误", e);
      sendErrorMessage(session, "消息处理失败: " + e.getMessage());
    }
  }

  @Override
  public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
    String userId = (String) session.getAttributes().get("userId");
    logger.error("WebSocket传输错误: sessionId={}, userId={}", session.getId(), userId, exception);
  }

  @Override
  public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus)
      throws Exception {
    String userId = (String) session.getAttributes().get("userId");
    logger.info(
        "WebSocket连接关闭: sessionId={}, userId={}, status={}", session.getId(), userId, closeStatus);

    // 移除会话
    sessionManager.removeSession(userId, session.getId());
  }

  @Override
  public boolean supportsPartialMessages() {
    return false;
  }

  /** 处理ping消息 */
  private void handlePingMessage(WebSocketSession session, Map<String, Object> messageData)
      throws Exception {
    Map<String, Object> pongMessage = new HashMap<>();
    pongMessage.put("type", "pong");
    pongMessage.put("timestamp", System.currentTimeMillis());
    sendMessage(session, pongMessage);
  }

  /** 处理订阅消息 */
  private void handleSubscribeMessage(
      WebSocketSession session, String userId, Map<String, Object> messageData) throws Exception {
    String channel = (String) messageData.get("channel");
    if (channel != null) {
      sessionManager.subscribeToChannel(userId, session.getId(), channel);

      Map<String, Object> response = new HashMap<>();
      response.put("type", "subscribe_success");
      response.put("channel", channel);
      response.put("timestamp", System.currentTimeMillis());
      sendMessage(session, response);

      logger.info("用户 {} 订阅频道: {}", userId, channel);
    }
  }

  /** 处理取消订阅消息 */
  private void handleUnsubscribeMessage(
      WebSocketSession session, String userId, Map<String, Object> messageData) throws Exception {
    String channel = (String) messageData.get("channel");
    if (channel != null) {
      sessionManager.unsubscribeFromChannel(userId, session.getId(), channel);

      Map<String, Object> response = new HashMap<>();
      response.put("type", "unsubscribe_success");
      response.put("channel", channel);
      response.put("timestamp", System.currentTimeMillis());
      sendMessage(session, response);

      logger.info("用户 {} 取消订阅频道: {}", userId, channel);
    }
  }

  /** 处理标记已读消息 */
  private void handleMarkReadMessage(
      WebSocketSession session, String userId, Map<String, Object> messageData) throws Exception {
    String notificationId = (String) messageData.get("notificationId");
    if (notificationId != null) {
      sessionManager.markNotificationAsRead(userId, notificationId);

      Map<String, Object> response = new HashMap<>();
      response.put("type", "mark_read_success");
      response.put("notificationId", notificationId);
      response.put("timestamp", System.currentTimeMillis());
      sendMessage(session, response);
    }
  }

  /** 处理获取通知消息 */
  private void handleGetNotificationsMessage(
      WebSocketSession session, String userId, Map<String, Object> messageData) throws Exception {
    int limit = (Integer) messageData.getOrDefault("limit", 10);
    int offset = (Integer) messageData.getOrDefault("offset", 0);

    // 这里应该从数据库获取通知列表
    Map<String, Object> response = new HashMap<>();
    response.put("type", "notifications");
    response.put("data", sessionManager.getRecentNotifications(userId, limit, offset));
    response.put("timestamp", System.currentTimeMillis());
    sendMessage(session, response);
  }

  /** 发送消息到WebSocket会话 */
  private void sendMessage(WebSocketSession session, Map<String, Object> message) {
    try {
      if (session.isOpen()) {
        String jsonMessage = objectMapper.writeValueAsString(message);
        session.sendMessage(new TextMessage(jsonMessage));
      }
    } catch (Exception e) {
      logger.error("发送WebSocket消息失败", e);
    }
  }

  /** 发送错误消息 */
  private void sendErrorMessage(WebSocketSession session, String errorMessage) {
    Map<String, Object> error = new HashMap<>();
    error.put("type", "error");
    error.put("message", errorMessage);
    error.put("timestamp", System.currentTimeMillis());
    sendMessage(session, error);
  }
}
