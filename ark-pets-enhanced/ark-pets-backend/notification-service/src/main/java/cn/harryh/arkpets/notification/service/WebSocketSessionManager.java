package cn.harryh.arkpets.notification.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

/**
 * WebSocket会话管理器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class WebSocketSessionManager {

  private static final Logger logger = LoggerFactory.getLogger(WebSocketSessionManager.class);

  // 用户ID -> WebSocket会话列表的映射
  private final Map<String, Set<WebSocketSession>> userSessions = new ConcurrentHashMap<>();

  // 会话ID -> 用户ID的映射
  private final Map<String, String> sessionUserMap = new ConcurrentHashMap<>();

  // 用户订阅的频道
  private final Map<String, Set<String>> userChannels = new ConcurrentHashMap<>();

  @Autowired private RedisTemplate<String, Object> redisTemplate;

  private final ObjectMapper objectMapper = new ObjectMapper();

  /** 添加WebSocket会话 */
  public void addSession(String userId, WebSocketSession session) {
    userSessions.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(session);
    sessionUserMap.put(session.getId(), userId);

    logger.info(
        "添加WebSocket会话: userId={}, sessionId={}, 当前会话数={}",
        userId,
        session.getId(),
        userSessions.get(userId).size());
  }

  /** 移除WebSocket会话 */
  public void removeSession(String userId, String sessionId) {
    Set<WebSocketSession> sessions = userSessions.get(userId);
    if (sessions != null) {
      sessions.removeIf(session -> session.getId().equals(sessionId));
      if (sessions.isEmpty()) {
        userSessions.remove(userId);
        userChannels.remove(userId);
      }
    }
    sessionUserMap.remove(sessionId);

    logger.info("移除WebSocket会话: userId={}, sessionId={}", userId, sessionId);
  }

  /** 获取用户的所有活跃会话 */
  public Set<WebSocketSession> getUserSessions(String userId) {
    Set<WebSocketSession> sessions = userSessions.get(userId);
    if (sessions == null) {
      return Collections.emptySet();
    }

    // 过滤掉已关闭的会话
    sessions.removeIf(session -> !session.isOpen());
    return new HashSet<>(sessions);
  }

  /** 向指定用户发送消息 */
  public void sendMessageToUser(String userId, Map<String, Object> message) {
    Set<WebSocketSession> sessions = getUserSessions(userId);
    if (sessions.isEmpty()) {
      logger.debug("用户 {} 没有活跃的WebSocket连接", userId);
      return;
    }

    String jsonMessage;
    try {
      jsonMessage = objectMapper.writeValueAsString(message);
    } catch (Exception e) {
      logger.error("序列化消息失败", e);
      return;
    }

    int successCount = 0;
    for (WebSocketSession session : sessions) {
      try {
        if (session.isOpen()) {
          session.sendMessage(new TextMessage(jsonMessage));
          successCount++;
        }
      } catch (Exception e) {
        logger.error("发送WebSocket消息失败: sessionId={}", session.getId(), e);
      }
    }

    logger.debug("向用户 {} 发送消息完成: 成功={}, 总数={}", userId, successCount, sessions.size());
  }

  /** 向多个用户发送消息 */
  public void sendMessageToUsers(Set<String> userIds, Map<String, Object> message) {
    for (String userId : userIds) {
      sendMessageToUser(userId, message);
    }
  }

  /** 广播消息给所有在线用户 */
  public void broadcastMessage(Map<String, Object> message) {
    Set<String> allUsers = new HashSet<>(userSessions.keySet());
    sendMessageToUsers(allUsers, message);
    logger.info("广播消息给 {} 个用户", allUsers.size());
  }

  /** 订阅频道 */
  public void subscribeToChannel(String userId, String sessionId, String channel) {
    userChannels.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(channel);

    // 将订阅信息存储到Redis
    String key = "user_channels:" + userId;
    redisTemplate.opsForSet().add(key, channel);
    redisTemplate.expire(key, 24, TimeUnit.HOURS);

    logger.info("用户 {} 订阅频道: {}", userId, channel);
  }

  /** 取消订阅频道 */
  public void unsubscribeFromChannel(String userId, String sessionId, String channel) {
    Set<String> channels = userChannels.get(userId);
    if (channels != null) {
      channels.remove(channel);
    }

    // 从Redis中移除订阅信息
    String key = "user_channels:" + userId;
    redisTemplate.opsForSet().remove(key, channel);

    logger.info("用户 {} 取消订阅频道: {}", userId, channel);
  }

  /** 向频道发送消息 */
  public void sendMessageToChannel(String channel, Map<String, Object> message) {
    Set<String> subscribedUsers = new HashSet<>();

    // 从内存中查找订阅用户
    for (Map.Entry<String, Set<String>> entry : userChannels.entrySet()) {
      if (entry.getValue().contains(channel)) {
        subscribedUsers.add(entry.getKey());
      }
    }

    // 从Redis中查找订阅用户
    Set<String> allUsers = userSessions.keySet();
    for (String userId : allUsers) {
      String key = "user_channels:" + userId;
      if (Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, channel))) {
        subscribedUsers.add(userId);
      }
    }

    if (!subscribedUsers.isEmpty()) {
      message.put("channel", channel);
      sendMessageToUsers(subscribedUsers, message);
      logger.info("向频道 {} 发送消息，影响 {} 个用户", channel, subscribedUsers.size());
    }
  }

  /** 获取在线用户数量 */
  public int getOnlineUserCount() {
    return userSessions.size();
  }

  /** 获取总会话数量 */
  public int getTotalSessionCount() {
    return userSessions.values().stream().mapToInt(Set::size).sum();
  }

  /** 获取用户的未读通知数量 */
  public int getUnreadNotificationCount(String userId) {
    try {
      String key = "unread_notifications:" + userId;
      Object count = redisTemplate.opsForValue().get(key);
      return count != null ? (Integer) count : 0;
    } catch (Exception e) {
      logger.error("获取未读通知数量失败: userId={}", userId, e);
      return 0;
    }
  }

  /** 标记通知为已读 */
  public void markNotificationAsRead(String userId, String notificationId) {
    try {
      // 从未读列表中移除
      String unreadKey = "unread_notifications_list:" + userId;
      redisTemplate.opsForSet().remove(unreadKey, notificationId);

      // 更新未读数量
      String countKey = "unread_notifications:" + userId;
      redisTemplate.opsForValue().decrement(countKey);

      // 添加到已读列表
      String readKey = "read_notifications:" + userId;
      redisTemplate.opsForSet().add(readKey, notificationId);
      redisTemplate.expire(readKey, 7, TimeUnit.DAYS);

      logger.debug("标记通知为已读: userId={}, notificationId={}", userId, notificationId);
    } catch (Exception e) {
      logger.error("标记通知为已读失败", e);
    }
  }

  /** 获取最近的通知列表 */
  public List<Map<String, Object>> getRecentNotifications(String userId, int limit, int offset) {
    try {
      // 这里应该从数据库获取通知列表
      // 简化版本：返回模拟数据
      List<Map<String, Object>> notifications = new ArrayList<>();

      for (int i = offset; i < offset + limit && i < 20; i++) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("id", "notification_" + i);
        notification.put("title", "通知标题 " + i);
        notification.put("content", "这是第 " + i + " 条通知内容");
        notification.put("type", "info");
        notification.put("timestamp", System.currentTimeMillis() - i * 60000);
        notification.put("read", i % 3 == 0);
        notifications.add(notification);
      }

      return notifications;
    } catch (Exception e) {
      logger.error("获取通知列表失败", e);
      return Collections.emptyList();
    }
  }

  /** 发送通知给用户 */
  public void sendNotificationToUser(String userId, String title, String content, String type) {
    // 创建通知消息
    Map<String, Object> notification = new HashMap<>();
    notification.put("type", "notification");
    notification.put("id", UUID.randomUUID().toString());
    notification.put("title", title);
    notification.put("content", content);
    notification.put("notificationType", type);
    notification.put("timestamp", System.currentTimeMillis());
    notification.put("read", false);

    // 发送实时通知
    sendMessageToUser(userId, notification);

    // 更新未读数量
    try {
      String countKey = "unread_notifications:" + userId;
      redisTemplate.opsForValue().increment(countKey);
      redisTemplate.expire(countKey, 30, TimeUnit.DAYS);

      String listKey = "unread_notifications_list:" + userId;
      redisTemplate.opsForSet().add(listKey, notification.get("id"));
      redisTemplate.expire(listKey, 30, TimeUnit.DAYS);
    } catch (Exception e) {
      logger.error("更新未读通知数量失败", e);
    }

    logger.info("发送通知给用户: userId={}, title={}", userId, title);
  }

  /** 清理过期会话 */
  public void cleanupExpiredSessions() {
    int removedCount = 0;
    Iterator<Map.Entry<String, Set<WebSocketSession>>> iterator =
        userSessions.entrySet().iterator();

    while (iterator.hasNext()) {
      Map.Entry<String, Set<WebSocketSession>> entry = iterator.next();
      Set<WebSocketSession> sessions = entry.getValue();

      sessions.removeIf(session -> !session.isOpen());

      if (sessions.isEmpty()) {
        iterator.remove();
        userChannels.remove(entry.getKey());
        removedCount++;
      }
    }

    if (removedCount > 0) {
      logger.info("清理过期会话完成，移除 {} 个用户的会话", removedCount);
    }
  }
}
