package cn.harryh.arkpets.notification.websocket;

import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

/**
 * WebSocket认证拦截器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Component
public class WebSocketAuthInterceptor implements HandshakeInterceptor {

  private static final Logger logger = LoggerFactory.getLogger(WebSocketAuthInterceptor.class);

  @Override
  public boolean beforeHandshake(
      ServerHttpRequest request,
      ServerHttpResponse response,
      WebSocketHandler wsHandler,
      Map<String, Object> attributes)
      throws Exception {

    logger.debug("WebSocket握手前验证: {}", request.getURI());

    try {
      // 从查询参数或头部获取token
      String token = extractToken(request);

      if (token == null || token.isEmpty()) {
        logger.warn("WebSocket连接缺少认证token: {}", request.getRemoteAddress());
        return false;
      }

      // 验证token (简化版本)
      String userId = validateTokenAndGetUserId(token);
      if (userId == null) {
        logger.warn("WebSocket连接token无效: {}", request.getRemoteAddress());
        return false;
      }

      // 将用户ID存储到WebSocket会话属性中
      attributes.put("userId", userId);
      attributes.put("token", token);
      attributes.put("connectTime", System.currentTimeMillis());

      logger.info("WebSocket连接认证成功: userId={}, remote={}", userId, request.getRemoteAddress());
      return true;

    } catch (Exception e) {
      logger.error("WebSocket认证过程中发生错误", e);
      return false;
    }
  }

  @Override
  public void afterHandshake(
      ServerHttpRequest request,
      ServerHttpResponse response,
      WebSocketHandler wsHandler,
      Exception exception) {

    if (exception != null) {
      logger.error("WebSocket握手后发生错误", exception);
    } else {
      logger.debug("WebSocket握手完成: {}", request.getURI());
    }
  }

  /** 从请求中提取token */
  private String extractToken(ServerHttpRequest request) {
    // 1. 从查询参数获取token
    String query = request.getURI().getQuery();
    if (query != null && query.contains("token=")) {
      String[] params = query.split("&");
      for (String param : params) {
        if (param.startsWith("token=")) {
          return param.substring(6); // "token=".length()
        }
      }
    }

    // 2. 从Authorization头获取token
    String authHeader = request.getHeaders().getFirst("Authorization");
    if (authHeader != null && authHeader.startsWith("Bearer ")) {
      return authHeader.substring(7);
    }

    // 3. 从Sec-WebSocket-Protocol头获取token (某些客户端使用)
    String protocolHeader = request.getHeaders().getFirst("Sec-WebSocket-Protocol");
    if (protocolHeader != null && protocolHeader.startsWith("access_token_")) {
      return protocolHeader.substring(13); // "access_token_".length()
    }

    return null;
  }

  /** 验证token并获取用户ID (简化版本) */
  private String validateTokenAndGetUserId(String token) {
    try {
      // 这里应该实现真正的JWT验证逻辑
      // 简化版本：检查token格式并返回模拟用户ID
      if (token != null && token.length() > 10) {
        // 模拟从token中解析用户ID
        return "user_" + Math.abs(token.hashCode() % 1000);
      }
      return null;
    } catch (Exception e) {
      logger.error("Token验证失败", e);
      return null;
    }
  }
}
