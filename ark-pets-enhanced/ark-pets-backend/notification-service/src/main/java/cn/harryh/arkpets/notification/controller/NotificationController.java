package cn.harryh.arkpets.notification.controller;

import cn.harryh.arkpets.constants.AppConstants;
import cn.harryh.arkpets.notification.entity.Notification;
import cn.harryh.arkpets.notification.service.NotificationService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 通知控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/notifications")
@CrossOrigin(origins = "*")
public class NotificationController {

  private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

  @Autowired private NotificationService notificationService;

  /** 获取用户通知列表 */
  @GetMapping
  public ResponseEntity<Map<String, Object>> getUserNotifications(
      @RequestHeader("X-User-ID") String userId,
      @RequestParam(defaultValue = "0") int offset,
      @RequestParam(defaultValue = "20") int limit) {

    try {
      List<Map<String, Object>> notifications =
          notificationService.getUserNotifications(userId, limit, offset);
      int unreadCount = notificationService.getUnreadNotificationCount(userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "通知列表获取成功");
      response.put("data", notifications);
      response.put("unreadCount", unreadCount);
      response.put("offset", offset);
      response.put("limit", limit);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取用户通知列表失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取通知列表失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 获取未读通知数量 */
  @GetMapping("/unread-count")
  public ResponseEntity<Map<String, Object>> getUnreadCount(
      @RequestHeader("X-User-ID") String userId) {

    try {
      int unreadCount = notificationService.getUnreadNotificationCount(userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "未读通知数量获取成功");
      response.put("unreadCount", unreadCount);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取未读通知数量失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取未读通知数量失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 标记通知为已读 */
  @PostMapping("/{notificationId}/read")
  public ResponseEntity<Map<String, Object>> markAsRead(
      @PathVariable String notificationId, @RequestHeader("X-User-ID") String userId) {

    try {
      notificationService.markNotificationAsRead(userId, notificationId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "通知已标记为已读");
      response.put("notificationId", notificationId);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("用户 {} 标记通知为已读: {}", userId, notificationId);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("标记通知为已读失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "标记通知为已读失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 发送通知 (内部API) */
  @PostMapping("/send")
  public ResponseEntity<Map<String, Object>> sendNotification(
      @RequestBody Map<String, Object> request) {

    try {
      String userId = (String) request.get("userId");
      String title = (String) request.get("title");
      String content = (String) request.get("content");
      String typeStr = (String) request.get("type");
      String priorityStr = (String) request.getOrDefault("priority", "NORMAL");

      if (userId == null || title == null || content == null || typeStr == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      Notification.NotificationType type =
          Notification.NotificationType.valueOf(typeStr.toUpperCase());
      Notification.NotificationPriority priority =
          Notification.NotificationPriority.valueOf(priorityStr.toUpperCase());

      @SuppressWarnings("unchecked")
      Map<String, String> metadata = (Map<String, String>) request.get("metadata");

      notificationService.sendNotification(userId, title, content, type, priority, metadata);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "通知发送成功");
      response.put("timestamp", System.currentTimeMillis());

      logger.info("发送通知成功: userId={}, type={}, title={}", userId, type, title);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("发送通知失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "发送通知失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 发送系统通知 (管理员API) */
  @PostMapping("/system")
  public ResponseEntity<Map<String, Object>> sendSystemNotification(
      @RequestBody Map<String, Object> request) {

    try {
      String title = (String) request.get("title");
      String content = (String) request.get("content");
      String priorityStr = (String) request.getOrDefault("priority", "NORMAL");

      if (title == null || content == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      Notification.NotificationPriority priority =
          Notification.NotificationPriority.valueOf(priorityStr.toUpperCase());

      notificationService.sendSystemNotification(title, content, priority);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "系统通知发送成功");
      response.put("timestamp", System.currentTimeMillis());

      logger.info("发送系统通知成功: title={}, priority={}", title, priority);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("发送系统通知失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "发送系统通知失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 发送频道消息 */
  @PostMapping("/channel/{channel}")
  public ResponseEntity<Map<String, Object>> sendChannelMessage(
      @PathVariable String channel, @RequestBody Map<String, Object> request) {

    try {
      String title = (String) request.get("title");
      String content = (String) request.get("content");
      String messageType = (String) request.getOrDefault("messageType", "info");

      if (title == null || content == null) {
        throw new IllegalArgumentException("缺少必要参数");
      }

      notificationService.sendChannelMessage(channel, title, content, messageType);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "频道消息发送成功");
      response.put("channel", channel);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("发送频道消息成功: channel={}, title={}", channel, title);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("发送频道消息失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "发送频道消息失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 获取在线统计信息 */
  @GetMapping("/stats")
  public ResponseEntity<Map<String, Object>> getOnlineStats() {
    try {
      Map<String, Object> stats = notificationService.getOnlineStats();

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "在线统计信息获取成功");
      response.put("data", stats);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取在线统计信息失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取在线统计信息失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 健康检查 */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    Map<String, Object> response = new HashMap<>();
    response.put("status", "UP");
    response.put("service", "notification-service");
    response.put("websocket", "enabled");
    response.put("timestamp", System.currentTimeMillis());
    return ResponseEntity.ok(response);
  }
}
