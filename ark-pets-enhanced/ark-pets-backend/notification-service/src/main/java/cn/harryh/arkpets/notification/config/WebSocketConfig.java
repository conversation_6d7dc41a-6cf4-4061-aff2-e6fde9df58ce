package cn.harryh.arkpets.notification.config;

import cn.harryh.arkpets.notification.websocket.WebSocketAuthInterceptor;
import cn.harryh.arkpets.notification.websocket.WebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

  @Autowired private WebSocketHandler webSocketHandler;

  @Autowired private WebSocketAuthInterceptor authInterceptor;

  @Override
  public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
    // 注册WebSocket处理器
    registry
        .addHandler(webSocketHandler, "/ws/notifications")
        .addInterceptors(authInterceptor)
        .setAllowedOrigins("*"); // 生产环境应该限制具体域名

    // 支持SockJS的WebSocket连接
    registry
        .addHandler(webSocketHandler, "/ws/notifications/sockjs")
        .addInterceptors(authInterceptor)
        .setAllowedOrigins("*")
        .withSockJS();
  }
}
