package cn.harryh.arkpets.notification.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;
import org.hibernate.annotations.CreationTimestamp;

/**
 * 通知实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "notifications")
public class Notification {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "用户ID不能为空")
  @Column(name = "user_id", nullable = false)
  private String userId;

  @NotBlank(message = "通知标题不能为空")
  @Column(nullable = false, length = 200)
  private String title;

  @Column(columnDefinition = "TEXT")
  private String content;

  @NotNull(message = "通知类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private NotificationType type;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private NotificationPriority priority = NotificationPriority.NORMAL;

  @Column(name = "is_read", nullable = false)
  private Boolean isRead = false;

  @Column(name = "read_at")
  private LocalDateTime readAt;

  @Column(name = "source_service", length = 50)
  private String sourceService;

  @Column(name = "source_id", length = 100)
  private String sourceId;

  @Column(name = "action_url", length = 500)
  private String actionUrl;

  @ElementCollection
  @CollectionTable(
      name = "notification_metadata",
      joinColumns = @JoinColumn(name = "notification_id"))
  @MapKeyColumn(name = "meta_key")
  @Column(name = "meta_value")
  private Map<String, String> metadata;

  @Column(name = "expires_at")
  private LocalDateTime expiresAt;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  // 通知类型枚举
  public enum NotificationType {
    SYSTEM("系统通知"),
    PET_STATUS("宠物状态"),
    PET_LEVEL_UP("宠物升级"),
    PET_NEEDS_ATTENTION("宠物需要关注"),
    AI_CHAT("AI对话"),
    USER_ACTION("用户操作"),
    MAINTENANCE("系统维护"),
    SECURITY("安全提醒"),
    PROMOTION("推广信息");

    private final String displayName;

    NotificationType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 通知优先级枚举
  public enum NotificationPriority {
    LOW("低"),
    NORMAL("普通"),
    HIGH("高"),
    URGENT("紧急");

    private final String displayName;

    NotificationPriority(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public Notification() {}

  public Notification(String userId, String title, String content, NotificationType type) {
    this.userId = userId;
    this.title = title;
    this.content = content;
    this.type = type;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public NotificationType getType() {
    return type;
  }

  public void setType(NotificationType type) {
    this.type = type;
  }

  public NotificationPriority getPriority() {
    return priority;
  }

  public void setPriority(NotificationPriority priority) {
    this.priority = priority;
  }

  public Boolean getIsRead() {
    return isRead;
  }

  public void setIsRead(Boolean isRead) {
    this.isRead = isRead;
    if (isRead && readAt == null) {
      this.readAt = LocalDateTime.now();
    }
  }

  public LocalDateTime getReadAt() {
    return readAt;
  }

  public void setReadAt(LocalDateTime readAt) {
    this.readAt = readAt;
  }

  public String getSourceService() {
    return sourceService;
  }

  public void setSourceService(String sourceService) {
    this.sourceService = sourceService;
  }

  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public String getActionUrl() {
    return actionUrl;
  }

  public void setActionUrl(String actionUrl) {
    this.actionUrl = actionUrl;
  }

  public Map<String, String> getMetadata() {
    return metadata;
  }

  public void setMetadata(Map<String, String> metadata) {
    this.metadata = metadata;
  }

  public LocalDateTime getExpiresAt() {
    return expiresAt;
  }

  public void setExpiresAt(LocalDateTime expiresAt) {
    this.expiresAt = expiresAt;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  // 业务方法
  public boolean isExpired() {
    return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
  }

  public void markAsRead() {
    this.isRead = true;
    this.readAt = LocalDateTime.now();
  }

  public boolean isHighPriority() {
    return priority == NotificationPriority.HIGH || priority == NotificationPriority.URGENT;
  }
}
