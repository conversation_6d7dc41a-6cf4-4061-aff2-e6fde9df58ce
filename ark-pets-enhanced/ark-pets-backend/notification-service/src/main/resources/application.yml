server:
  port: 8087

spring:
  application:
    name: ark-pets-notification-service
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 4
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # 安全配置
  security:
    user:
      name: admin
      password: ark-pets-notification-2024

# 通知服务配置
notification-service:
  # WebSocket配置
  websocket:
    enabled: true
    max-sessions-per-user: 5
    heartbeat-interval: 30s
    session-timeout: 300s
    allowed-origins: "*"
    
  # 通知配置
  notifications:
    # 默认过期时间
    default-expiry: 30d
    
    # 批量处理配置
    batch-size: 100
    batch-interval: 5s
    
    # 优先级队列配置
    priority-queues:
      urgent: 1000
      high: 500
      normal: 100
      low: 50
    
    # 限流配置
    rate-limit:
      enabled: true
      max-per-user-per-minute: 60
      max-system-per-minute: 1000
  
  # 频道配置
  channels:
    # 系统频道
    system: "system_notifications"
    pet-status: "pet_status_updates"
    user-actions: "user_action_logs"
    
    # 自动订阅频道
    auto-subscribe:
      - "system_notifications"
      - "pet_status_updates"
  
  # 清理配置
  cleanup:
    enabled: true
    # 清理间隔
    interval: 3600s # 1小时
    # 保留时间
    retention:
      read-notifications: 7d
      unread-notifications: 30d
      expired-notifications: 1d
    
    # 会话清理
    session-cleanup-interval: 300s # 5分钟

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    tags:
      application: ark-pets
      service: notification-service
      environment: ${ENVIRONMENT:development}
      version: 4.0.0

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.web.socket: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/notification-service.log
    max-size: 100MB
    max-history: 30

# 定时任务配置
spring.quartz:
  job-store-type: memory
  properties:
    org:
      quartz:
        scheduler:
          instanceName: NotificationServiceScheduler
        threadPool:
          threadCount: 10
