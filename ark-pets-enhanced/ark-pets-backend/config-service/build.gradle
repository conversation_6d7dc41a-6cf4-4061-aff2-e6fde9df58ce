// 配置服务构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // Spring Boot依赖已在根build.gradle中配置
    
    // Spring Cloud Config依赖 (简化版本)
    implementation 'org.springframework.cloud:spring-cloud-config-server:4.0.4'
    
    // 数据库支持 (用于存储配置)
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.postgresql:postgresql:42.7.1'
    
    // Redis支持 (用于配置缓存)
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    
    // 监控支持
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus:1.12.1'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.16.1'
    
    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.config.ConfigServiceApplication'
}
