server:
  port: 8086

spring:
  application:
    name: ark-pets-config-service
  profiles:
    active: native

  # 数据库配置
  datasource:
    url: ********************************************
    username: arkpets
    password: ark_pets_password_2024
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

  # Redis配置 (可选)
  redis:
    host: localhost
    port: 6379
    password:
    database: 1
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # Spring Cloud Config配置
  cloud:
    config:
      server:
        # 使用本地文件系统配置 (开发环境)
        native:
          search-locations: classpath:/configs/,file:./configs/
        
        # 数据库配置存储
        jdbc:
          enabled: true
          sql: SELECT key, value from PROPERTIES where APPLICATION=? and PROFILE=? and LABEL=?
        
        # 加密配置
        encrypt:
          enabled: true
          key: ${CONFIG_ENCRYPT_KEY:ark-pets-secret-key-2024}
        
        # 健康检查
        health:
          enabled: true
          repositories:
            git:
              enabled: true
    
    # Consul配置
    consul:
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}
      discovery:
        enabled: true
        register: true
        service-name: ${spring.application.name}
        health-check-path: /actuator/health
        health-check-interval: 10s
        health-check-timeout: 3s
        health-check-critical-timeout: 30s
        prefer-ip-address: true
        ip-address: ${SERVER_IP:127.0.0.1}
        tags:
          - config-server
          - ark-pets
          - version-4.0.0
      config:
        enabled: true
        format: yaml
        data-key: data
        watch:
          enabled: true
          delay: 1000

# 配置服务特定配置
config-service:
  redis:
    enabled: true  # 启用Redis缓存
  # 环境配置
  environments:
    - name: development
      description: 开发环境
      active: true
    - name: testing
      description: 测试环境
      active: true
    - name: staging
      description: 预发布环境
      active: true
    - name: production
      description: 生产环境
      active: false
  
  # 配置刷新
  refresh:
    enabled: true
    interval: 30s # 30秒检查一次配置变更
    webhook:
      enabled: true
      secret: ${CONFIG_WEBHOOK_SECRET:ark-pets-webhook-secret}
  
  # 配置备份
  backup:
    enabled: true
    interval: 1h # 每小时备份一次
    retention: 7d # 保留7天
    location: ${CONFIG_BACKUP_PATH:./backups/configs}
  
  # 配置审计
  audit:
    enabled: true
    log-changes: true
    notify-changes: true
    webhook-url: ${CONFIG_AUDIT_WEBHOOK:}
  
  # 配置验证
  validation:
    enabled: true
    strict-mode: false
    schema-validation: true
    custom-validators:
      - database-connection
      - redis-connection
      - external-api

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    configprops:
      enabled: true
    env:
      enabled: true
    refresh:
      enabled: true
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    tags:
      application: ark-pets
      service: config-service
      environment: ${ENVIRONMENT:development}
      version: 4.0.0

# 安全配置
security:
  basic:
    enabled: true
    username: ${CONFIG_SECURITY_USERNAME:admin}
    password: ${CONFIG_SECURITY_PASSWORD:ark-pets-config-2024}
  
  # 访问控制
  access:
    allowed-origins: ${CONFIG_ALLOWED_ORIGINS:*}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    max-age: 3600

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    org.springframework.cloud.config: INFO
    org.springframework.cloud.consul: INFO
    org.eclipse.jgit: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/config-service.log
    max-size: 100MB
    max-history: 30
