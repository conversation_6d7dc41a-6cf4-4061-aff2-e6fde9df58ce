package cn.harryh.arkpets.config.service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 服务发现服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class ServiceDiscoveryService {

  private static final Logger logger = LoggerFactory.getLogger(ServiceDiscoveryService.class);

  @Autowired(required = false)
  private RedisTemplate<String, Object> redisTemplate;

  @Value("${spring.cloud.consul.discovery.enabled:true}")
  private boolean discoveryEnabled;

  // 服务注册表前缀
  private static final String SERVICE_REGISTRY_PREFIX = "service:registry:";
  private static final String SERVICE_HEALTH_PREFIX = "service:health:";
  private static final long SERVICE_TTL = 30; // 30秒

  /** 注册服务 */
  public boolean registerService(
      String serviceName, String serviceId, String host, int port, Map<String, String> metadata) {
    if (!discoveryEnabled) {
      logger.debug("Service discovery disabled, skipping registration");
      return false;
    }

    if (redisTemplate == null) {
      logger.warn("Redis not available, service registration skipped");
      return false;
    }

    try {
      logger.info(
          "Registering service: {} with id: {} at {}:{}", serviceName, serviceId, host, port);

      Map<String, Object> serviceInfo = new HashMap<>();
      serviceInfo.put("serviceName", serviceName);
      serviceInfo.put("serviceId", serviceId);
      serviceInfo.put("host", host);
      serviceInfo.put("port", port);
      serviceInfo.put("metadata", metadata != null ? metadata : new HashMap<>());
      serviceInfo.put("registrationTime", LocalDateTime.now().toString());
      serviceInfo.put("lastHeartbeat", LocalDateTime.now().toString());
      serviceInfo.put("status", "UP");

      String registryKey = SERVICE_REGISTRY_PREFIX + serviceName + ":" + serviceId;
      redisTemplate.opsForValue().set(registryKey, serviceInfo, SERVICE_TTL, TimeUnit.SECONDS);

      // 添加到服务列表
      String serviceListKey = SERVICE_REGISTRY_PREFIX + serviceName;
      redisTemplate.opsForSet().add(serviceListKey, serviceId);
      redisTemplate.expire(serviceListKey, SERVICE_TTL * 2, TimeUnit.SECONDS);

      logger.info("Service registered successfully: {} with id: {}", serviceName, serviceId);
      return true;

    } catch (Exception e) {
      logger.error("Error registering service: {} with id: {}", serviceName, serviceId, e);
      return false;
    }
  }

  /** 注销服务 */
  public boolean deregisterService(String serviceName, String serviceId) {
    try {
      logger.info("Deregistering service: {} with id: {}", serviceName, serviceId);

      String registryKey = SERVICE_REGISTRY_PREFIX + serviceName + ":" + serviceId;
      redisTemplate.delete(registryKey);

      // 从服务列表中移除
      String serviceListKey = SERVICE_REGISTRY_PREFIX + serviceName;
      redisTemplate.opsForSet().remove(serviceListKey, serviceId);

      // 清理健康检查记录
      String healthKey = SERVICE_HEALTH_PREFIX + serviceName + ":" + serviceId;
      redisTemplate.delete(healthKey);

      logger.info("Service deregistered successfully: {} with id: {}", serviceName, serviceId);
      return true;

    } catch (Exception e) {
      logger.error("Error deregistering service: {} with id: {}", serviceName, serviceId, e);
      return false;
    }
  }

  /** 发现服务实例 */
  public List<Map<String, Object>> discoverServices(String serviceName) {
    if (redisTemplate == null) {
      logger.warn("Redis not available, returning empty service list");
      return new ArrayList<>();
    }

    try {
      logger.debug("Discovering services for: {}", serviceName);

      List<Map<String, Object>> services = new ArrayList<>();
      String serviceListKey = SERVICE_REGISTRY_PREFIX + serviceName;
      Set<Object> serviceIds = redisTemplate.opsForSet().members(serviceListKey);

      if (serviceIds != null) {
        for (Object serviceIdObj : serviceIds) {
          String serviceId = serviceIdObj.toString();
          String registryKey = SERVICE_REGISTRY_PREFIX + serviceName + ":" + serviceId;

          Object serviceInfoObj = redisTemplate.opsForValue().get(registryKey);
          if (serviceInfoObj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> serviceInfo = (Map<String, Object>) serviceInfoObj;

            // 检查服务健康状态
            if (isServiceHealthy(serviceName, serviceId)) {
              serviceInfo.put("healthy", true);
              services.add(serviceInfo);
            } else {
              serviceInfo.put("healthy", false);
              services.add(serviceInfo);
            }
          }
        }
      }

      logger.debug("Found {} services for: {}", services.size(), serviceName);
      return services;

    } catch (Exception e) {
      logger.error("Error discovering services for: {}", serviceName, e);
      return new ArrayList<>();
    }
  }

  /** 获取健康的服务实例 */
  public List<Map<String, Object>> getHealthyServices(String serviceName) {
    List<Map<String, Object>> allServices = discoverServices(serviceName);
    List<Map<String, Object>> healthyServices = new ArrayList<>();

    for (Map<String, Object> service : allServices) {
      Boolean healthy = (Boolean) service.get("healthy");
      if (Boolean.TRUE.equals(healthy)) {
        healthyServices.add(service);
      }
    }

    logger.debug("Found {} healthy services for: {}", healthyServices.size(), serviceName);
    return healthyServices;
  }

  /** 服务心跳 */
  public boolean heartbeat(String serviceName, String serviceId) {
    try {
      String registryKey = SERVICE_REGISTRY_PREFIX + serviceName + ":" + serviceId;
      Object serviceInfoObj = redisTemplate.opsForValue().get(registryKey);

      if (serviceInfoObj instanceof Map) {
        @SuppressWarnings("unchecked")
        Map<String, Object> serviceInfo = (Map<String, Object>) serviceInfoObj;
        serviceInfo.put("lastHeartbeat", LocalDateTime.now().toString());
        serviceInfo.put("status", "UP");

        redisTemplate.opsForValue().set(registryKey, serviceInfo, SERVICE_TTL, TimeUnit.SECONDS);

        // 更新健康状态
        String healthKey = SERVICE_HEALTH_PREFIX + serviceName + ":" + serviceId;
        redisTemplate.opsForValue().set(healthKey, "UP", SERVICE_TTL, TimeUnit.SECONDS);

        logger.debug("Heartbeat received for service: {} with id: {}", serviceName, serviceId);
        return true;
      }

      logger.warn("Service not found for heartbeat: {} with id: {}", serviceName, serviceId);
      return false;

    } catch (Exception e) {
      logger.error(
          "Error processing heartbeat for service: {} with id: {}", serviceName, serviceId, e);
      return false;
    }
  }

  /** 检查服务健康状态 */
  public boolean isServiceHealthy(String serviceName, String serviceId) {
    try {
      String healthKey = SERVICE_HEALTH_PREFIX + serviceName + ":" + serviceId;
      String status = (String) redisTemplate.opsForValue().get(healthKey);
      return "UP".equals(status);

    } catch (Exception e) {
      logger.error("Error checking service health: {} with id: {}", serviceName, serviceId, e);
      return false;
    }
  }

  /** 获取所有注册的服务 */
  public Map<String, List<Map<String, Object>>> getAllServices() {
    try {
      Map<String, List<Map<String, Object>>> allServices = new HashMap<>();
      Set<String> keys = redisTemplate.keys(SERVICE_REGISTRY_PREFIX + "*");

      if (keys != null) {
        Set<String> serviceNames = new HashSet<>();

        // 提取服务名称
        for (String key : keys) {
          String[] parts = key.replace(SERVICE_REGISTRY_PREFIX, "").split(":");
          if (parts.length >= 1 && !parts[0].isEmpty()) {
            serviceNames.add(parts[0]);
          }
        }

        // 获取每个服务的实例
        for (String serviceName : serviceNames) {
          List<Map<String, Object>> services = discoverServices(serviceName);
          if (!services.isEmpty()) {
            allServices.put(serviceName, services);
          }
        }
      }

      logger.debug("Retrieved all services: {} service types", allServices.size());
      return allServices;

    } catch (Exception e) {
      logger.error("Error getting all services", e);
      return new HashMap<>();
    }
  }

  /** 获取服务统计信息 */
  public Map<String, Object> getServiceStats() {
    try {
      Map<String, List<Map<String, Object>>> allServices = getAllServices();

      int totalServices = 0;
      int healthyServices = 0;
      int unhealthyServices = 0;

      for (List<Map<String, Object>> services : allServices.values()) {
        for (Map<String, Object> service : services) {
          totalServices++;
          Boolean healthy = (Boolean) service.get("healthy");
          if (Boolean.TRUE.equals(healthy)) {
            healthyServices++;
          } else {
            unhealthyServices++;
          }
        }
      }

      Map<String, Object> stats = new HashMap<>();
      stats.put("totalServiceTypes", allServices.size());
      stats.put("totalServiceInstances", totalServices);
      stats.put("healthyInstances", healthyServices);
      stats.put("unhealthyInstances", unhealthyServices);
      stats.put(
          "healthRate", totalServices > 0 ? (double) healthyServices / totalServices * 100 : 0);
      stats.put("timestamp", LocalDateTime.now().toString());

      logger.debug("Service stats: {}", stats);
      return stats;

    } catch (Exception e) {
      logger.error("Error getting service stats", e);
      return Map.of("error", "Failed to retrieve service stats");
    }
  }

  /** 清理过期服务 */
  public void cleanupExpiredServices() {
    try {
      logger.debug("Cleaning up expired services");

      Set<String> keys = redisTemplate.keys(SERVICE_REGISTRY_PREFIX + "*:*");
      int cleanedCount = 0;

      if (keys != null) {
        for (String key : keys) {
          Long ttl = redisTemplate.getExpire(key);
          if (ttl != null && ttl <= 0) {
            redisTemplate.delete(key);
            cleanedCount++;
          }
        }
      }

      if (cleanedCount > 0) {
        logger.info("Cleaned up {} expired services", cleanedCount);
      }

    } catch (Exception e) {
      logger.error("Error cleaning up expired services", e);
    }
  }
}
