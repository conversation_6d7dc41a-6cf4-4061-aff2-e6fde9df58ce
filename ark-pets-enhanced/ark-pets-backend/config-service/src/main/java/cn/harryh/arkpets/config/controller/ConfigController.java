package cn.harryh.arkpets.config.controller;

import cn.harryh.arkpets.config.service.ConfigManagementService;
import cn.harryh.arkpets.config.service.ServiceDiscoveryService;
import cn.harryh.arkpets.constants.AppConstants;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 配置控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/config")
@CrossOrigin(origins = "*")
public class ConfigController {

  private static final Logger logger = LoggerFactory.getLogger(ConfigController.class);

  @Autowired private ConfigManagementService configManagementService;

  @Autowired private ServiceDiscoveryService serviceDiscoveryService;

  /** 获取应用配置 */
  @GetMapping("/{application}/{profile}")
  public ResponseEntity<Map<String, Object>> getConfig(
      @PathVariable String application,
      @PathVariable String profile,
      @RequestParam(defaultValue = "main") String label) {
    try {
      logger.debug(
          "Getting config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label);

      Map<String, Object> config =
          configManagementService.getApplicationConfig(application, profile, label);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "配置获取成功");
      response.put("application", application);
      response.put("profile", profile);
      response.put("label", label);
      response.put("config", config);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error(
          "Error getting config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label,
          e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "配置获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 更新应用配置 */
  @PostMapping("/{application}/{profile}")
  public ResponseEntity<Map<String, Object>> updateConfig(
      @PathVariable String application,
      @PathVariable String profile,
      @RequestParam(defaultValue = "main") String label,
      @RequestBody Map<String, Object> config) {
    try {
      logger.info(
          "Updating config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label);

      // 验证配置
      Map<String, Object> validation =
          configManagementService.validateConfig(application, profile, label, config);
      if (!Boolean.TRUE.equals(validation.get("valid"))) {
        return ResponseEntity.badRequest()
            .body(
                Map.of(
                    "success",
                    false,
                    "message",
                    "配置验证失败",
                    "validation",
                    validation,
                    "timestamp",
                    System.currentTimeMillis()));
      }

      boolean success =
          configManagementService.updateApplicationConfig(application, profile, label, config);

      return ResponseEntity.ok(
          Map.of(
              "success", success,
              "message", success ? "配置更新成功" : "配置更新失败",
              "application", application,
              "profile", profile,
              "label", label,
              "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error(
          "Error updating config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label,
          e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "配置更新失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 删除应用配置 */
  @DeleteMapping("/{application}/{profile}")
  public ResponseEntity<Map<String, Object>> deleteConfig(
      @PathVariable String application,
      @PathVariable String profile,
      @RequestParam(defaultValue = "main") String label) {
    try {
      logger.info(
          "Deleting config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label);

      boolean success =
          configManagementService.deleteApplicationConfig(application, profile, label);

      return ResponseEntity.ok(
          Map.of(
              "success", success,
              "message", success ? "配置删除成功" : "配置删除失败",
              "application", application,
              "profile", profile,
              "label", label,
              "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error(
          "Error deleting config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label,
          e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "配置删除失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取配置历史 */
  @GetMapping("/{application}/{profile}/history")
  public ResponseEntity<Map<String, Object>> getConfigHistory(
      @PathVariable String application,
      @PathVariable String profile,
      @RequestParam(defaultValue = "main") String label) {
    try {
      logger.debug(
          "Getting config history for application: {}, profile: {}, label: {}",
          application,
          profile,
          label);

      List<Map<String, Object>> history =
          configManagementService.getConfigHistory(application, profile, label);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "配置历史获取成功",
              "application",
              application,
              "profile",
              profile,
              "label",
              label,
              "history",
              history,
              "count",
              history.size(),
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error(
          "Error getting config history for application: {}, profile: {}, label: {}",
          application,
          profile,
          label,
          e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "配置历史获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 刷新配置缓存 */
  @PostMapping("/{application}/{profile}/refresh")
  public ResponseEntity<Map<String, Object>> refreshConfig(
      @PathVariable String application,
      @PathVariable String profile,
      @RequestParam(defaultValue = "main") String label) {
    try {
      logger.info(
          "Refreshing config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label);

      configManagementService.refreshConfigCache(application, profile, label);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "配置刷新成功",
              "application",
              application,
              "profile",
              profile,
              "label",
              label,
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error(
          "Error refreshing config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label,
          e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "配置刷新失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取所有应用列表 */
  @GetMapping("/applications")
  public ResponseEntity<Map<String, Object>> getAllApplications() {
    try {
      logger.debug("Getting all applications");

      List<String> applications = configManagementService.getAllApplications();

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "应用列表获取成功",
              "applications",
              applications,
              "count",
              applications.size(),
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error getting all applications", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "应用列表获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 验证配置 */
  @PostMapping("/{application}/{profile}/validate")
  public ResponseEntity<Map<String, Object>> validateConfig(
      @PathVariable String application,
      @PathVariable String profile,
      @RequestParam(defaultValue = "main") String label,
      @RequestBody Map<String, Object> config) {
    try {
      logger.debug(
          "Validating config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label);

      Map<String, Object> validation =
          configManagementService.validateConfig(application, profile, label, config);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "配置验证完成");
      response.put("application", application);
      response.put("profile", profile);
      response.put("label", label);
      response.put("validation", validation);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error(
          "Error validating config for application: {}, profile: {}, label: {}",
          application,
          profile,
          label,
          e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "配置验证失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 健康检查 */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    try {
      // 检查Redis连接
      configManagementService.getAllApplications();

      return ResponseEntity.ok(
          Map.of(
              "status", "UP",
              "service", "config-service",
              "timestamp", System.currentTimeMillis(),
              "redis", "connected"));

    } catch (Exception e) {
      logger.error("Health check failed", e);
      return ResponseEntity.status(503)
          .body(
              Map.of(
                  "status",
                  "DOWN",
                  "service",
                  "config-service",
                  "timestamp",
                  System.currentTimeMillis(),
                  "error",
                  e.getMessage()));
    }
  }
}
