package cn.harryh.arkpets.config.controller;

import cn.harryh.arkpets.config.service.ServiceDiscoveryService;
import cn.harryh.arkpets.constants.AppConstants;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 服务发现控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/discovery")
@CrossOrigin(origins = "*")
public class ServiceDiscoveryController {

  private static final Logger logger = LoggerFactory.getLogger(ServiceDiscoveryController.class);

  @Autowired private ServiceDiscoveryService serviceDiscoveryService;

  /** 注册服务 */
  @PostMapping("/register")
  public ResponseEntity<Map<String, Object>> registerService(
      @RequestParam String serviceName,
      @RequestParam String serviceId,
      @RequestParam String host,
      @RequestParam int port,
      @RequestBody(required = false) Map<String, String> metadata) {
    try {
      logger.info(
          "Registering service: {} with id: {} at {}:{}", serviceName, serviceId, host, port);

      boolean success =
          serviceDiscoveryService.registerService(serviceName, serviceId, host, port, metadata);

      return ResponseEntity.ok(
          Map.of(
              "success",
              success,
              "message",
              success ? "服务注册成功" : "服务注册失败",
              "serviceName",
              serviceName,
              "serviceId",
              serviceId,
              "endpoint",
              host + ":" + port,
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error registering service: {} with id: {}", serviceName, serviceId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "服务注册失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 注销服务 */
  @PostMapping("/deregister")
  public ResponseEntity<Map<String, Object>> deregisterService(
      @RequestParam String serviceName, @RequestParam String serviceId) {
    try {
      logger.info("Deregistering service: {} with id: {}", serviceName, serviceId);

      boolean success = serviceDiscoveryService.deregisterService(serviceName, serviceId);

      return ResponseEntity.ok(
          Map.of(
              "success", success,
              "message", success ? "服务注销成功" : "服务注销失败",
              "serviceName", serviceName,
              "serviceId", serviceId,
              "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error deregistering service: {} with id: {}", serviceName, serviceId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "服务注销失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 发现服务实例 */
  @GetMapping("/services/{serviceName}")
  public ResponseEntity<Map<String, Object>> discoverServices(@PathVariable String serviceName) {
    try {
      logger.debug("Discovering services for: {}", serviceName);

      List<Map<String, Object>> services = serviceDiscoveryService.discoverServices(serviceName);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "服务发现成功",
              "serviceName",
              serviceName,
              "services",
              services,
              "count",
              services.size(),
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error discovering services for: {}", serviceName, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "服务发现失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取健康的服务实例 */
  @GetMapping("/services/{serviceName}/healthy")
  public ResponseEntity<Map<String, Object>> getHealthyServices(@PathVariable String serviceName) {
    try {
      logger.debug("Getting healthy services for: {}", serviceName);

      List<Map<String, Object>> services = serviceDiscoveryService.getHealthyServices(serviceName);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "健康服务获取成功",
              "serviceName",
              serviceName,
              "services",
              services,
              "count",
              services.size(),
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error getting healthy services for: {}", serviceName, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "健康服务获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 服务心跳 */
  @PostMapping("/heartbeat")
  public ResponseEntity<Map<String, Object>> heartbeat(
      @RequestParam String serviceName, @RequestParam String serviceId) {
    try {
      logger.debug("Heartbeat from service: {} with id: {}", serviceName, serviceId);

      boolean success = serviceDiscoveryService.heartbeat(serviceName, serviceId);

      return ResponseEntity.ok(
          Map.of(
              "success", success,
              "message", success ? "心跳成功" : "心跳失败",
              "serviceName", serviceName,
              "serviceId", serviceId,
              "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error(
          "Error processing heartbeat for service: {} with id: {}", serviceName, serviceId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "心跳失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 检查服务健康状态 */
  @GetMapping("/services/{serviceName}/{serviceId}/health")
  public ResponseEntity<Map<String, Object>> checkServiceHealth(
      @PathVariable String serviceName, @PathVariable String serviceId) {
    try {
      logger.debug("Checking health for service: {} with id: {}", serviceName, serviceId);

      boolean healthy = serviceDiscoveryService.isServiceHealthy(serviceName, serviceId);

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "健康检查完成",
              "serviceName",
              serviceName,
              "serviceId",
              serviceId,
              "healthy",
              healthy,
              "status",
              healthy ? "UP" : "DOWN",
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error checking health for service: {} with id: {}", serviceName, serviceId, e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "健康检查失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取所有注册的服务 */
  @GetMapping("/services")
  public ResponseEntity<Map<String, Object>> getAllServices() {
    try {
      logger.debug("Getting all registered services");

      Map<String, List<Map<String, Object>>> allServices = serviceDiscoveryService.getAllServices();

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "服务列表获取成功",
              "services",
              allServices,
              "serviceTypes",
              allServices.size(),
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error getting all services", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "服务列表获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 获取服务统计信息 */
  @GetMapping("/stats")
  public ResponseEntity<Map<String, Object>> getServiceStats() {
    try {
      logger.debug("Getting service statistics");

      Map<String, Object> stats = serviceDiscoveryService.getServiceStats();

      return ResponseEntity.ok(
          Map.of(
              "success",
              true,
              "message",
              "服务统计获取成功",
              "stats",
              stats,
              "timestamp",
              System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error getting service statistics", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "服务统计获取失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 清理过期服务 */
  @PostMapping("/cleanup")
  public ResponseEntity<Map<String, Object>> cleanupExpiredServices() {
    try {
      logger.info("Cleaning up expired services");

      serviceDiscoveryService.cleanupExpiredServices();

      return ResponseEntity.ok(
          Map.of("success", true, "message", "过期服务清理完成", "timestamp", System.currentTimeMillis()));

    } catch (Exception e) {
      logger.error("Error cleaning up expired services", e);
      return ResponseEntity.internalServerError()
          .body(
              Map.of(
                  "success",
                  false,
                  "message",
                  "过期服务清理失败：" + e.getMessage(),
                  "timestamp",
                  System.currentTimeMillis()));
    }
  }

  /** 健康检查 */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    try {
      // 检查服务发现功能
      Map<String, Object> stats = serviceDiscoveryService.getServiceStats();

      return ResponseEntity.ok(
          Map.of(
              "status",
              "UP",
              "service",
              "service-discovery",
              "timestamp",
              System.currentTimeMillis(),
              "stats",
              stats));

    } catch (Exception e) {
      logger.error("Health check failed", e);
      return ResponseEntity.status(503)
          .body(
              Map.of(
                  "status",
                  "DOWN",
                  "service",
                  "service-discovery",
                  "timestamp",
                  System.currentTimeMillis(),
                  "error",
                  e.getMessage()));
    }
  }
}
