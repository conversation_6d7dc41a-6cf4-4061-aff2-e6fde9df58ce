server:
  port: 8080

spring:
  application:
    name: ark-pets-gateway-service

  # 排除数据库自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
  
  # Spring Cloud Gateway配置
  cloud:
    gateway:
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: false
            max-age: 3600
      
      # 路由配置
      routes:
        # 认证服务路由
        - id: auth-service
          uri: http://localhost:8081
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway
        
        # AI服务路由
        - id: ai-service
          uri: http://localhost:8082
          predicates:
            - Path=/api/v1/ai/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 5
                redis-rate-limiter.burstCapacity: 10
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway

        # 宠物服务路由
        - id: pet-service
          uri: http://localhost:8083
          predicates:
            - Path=/api/v1/pets/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway

        # 通知服务路由
        - id: notification-service
          uri: http://localhost:8087
          predicates:
            - Path=/api/v1/notifications/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 30
                redis-rate-limiter.burstCapacity: 60
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway

        # 数据服务路由
        - id: data-service
          uri: http://localhost:8088
          predicates:
            - Path=/api/v1/data/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway

        # 缓存服务路由
        - id: cache-service
          uri: http://localhost:8084
          predicates:
            - Path=/api/v1/cache/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway
        
        # 监控服务路由
        - id: monitor-service
          uri: http://localhost:8085
          predicates:
            - Path=/api/v1/monitor/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway
        
        # 配置服务路由
        - id: config-service
          uri: http://localhost:8086
          predicates:
            - Path=/api/v1/config/**, /api/v1/discovery/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 30
                redis-rate-limiter.burstCapacity: 60
                redis-rate-limiter.requestedTokens: 1
            - AddRequestHeader=X-Gateway-Source, ark-pets-gateway
      
      # 默认过滤器
      default-filters:
        - AddResponseHeader=X-Response-Time, ${response-time}
        - AddResponseHeader=X-Gateway-Version, 4.0.0
  
  # Redis配置 (用于限流)
  redis:
    host: localhost
    port: 6379
    password: 
    database: 5
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# 网关服务配置
gateway-service:
  # 负载均衡配置
  load-balancer:
    enabled: true
    strategy: round-robin # round-robin, random, weighted
    health-check:
      enabled: true
      interval: 30s
      timeout: 5s
  
  # 熔断器配置
  circuit-breaker:
    enabled: true
    failure-rate-threshold: 50 # 失败率阈值50%
    wait-duration-in-open-state: 60s # 熔断器打开状态持续时间
    sliding-window-size: 10 # 滑动窗口大小
    minimum-number-of-calls: 5 # 最小调用次数
  
  # 重试配置
  retry:
    enabled: true
    max-attempts: 3
    wait-duration: 1s
    retry-exceptions:
      - java.io.IOException
      - java.util.concurrent.TimeoutException
  
  # 超时配置
  timeout:
    connect-timeout: 5s
    response-timeout: 30s
  
  # 安全配置
  security:
    # JWT验证
    jwt:
      enabled: true
      secret: ark-pets-gateway-secret-2024
      excluded-paths:
        - /api/v1/auth/login
        - /api/v1/auth/register
        - /api/v1/auth/refresh
        - /actuator/**
    
    # IP白名单
    ip-whitelist:
      enabled: false
      allowed-ips:
        - 127.0.0.1
        - ***********/24
  
  # 日志配置
  logging:
    enabled: true
    include-request-body: false
    include-response-body: false
    include-headers: true

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    gateway:
      enabled: true
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    tags:
      application: ark-pets
      service: gateway-service
      environment: ${ENVIRONMENT:development}
      version: 4.0.0

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/gateway-service.log
    max-size: 100MB
    max-history: 30
