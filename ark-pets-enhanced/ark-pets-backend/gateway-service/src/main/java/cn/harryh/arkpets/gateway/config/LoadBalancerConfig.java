package cn.harryh.arkpets.gateway.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.core.RandomLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ReactorLoadBalancer;
import org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * 负载均衡配置
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class LoadBalancerConfig {

  private static final Logger logger = LoggerFactory.getLogger(LoadBalancerConfig.class);

  @Value("${gateway-service.load-balancer.strategy:round-robin}")
  private String loadBalancerStrategy;

  @Value("${gateway-service.load-balancer.enabled:true}")
  private boolean loadBalancerEnabled;

  /** 配置负载均衡器 */
  @Bean
  public ReactorLoadBalancer<ServiceInstance> reactorServiceInstanceLoadBalancer(
      Environment environment, LoadBalancerClientFactory loadBalancerClientFactory) {

    if (!loadBalancerEnabled) {
      logger.info("Load balancer is disabled");
      return null;
    }

    String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
    logger.info(
        "Configuring load balancer for service: {} with strategy: {}", name, loadBalancerStrategy);

    return switch (loadBalancerStrategy.toLowerCase()) {
      case "random" -> new RandomLoadBalancer(
          loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
      case "round-robin" -> new RoundRobinLoadBalancer(
          loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
      default -> {
        logger.warn("Unknown load balancer strategy: {}, using round-robin", loadBalancerStrategy);
        yield new RoundRobinLoadBalancer(
            loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class),
            name);
      }
    };
  }
}
