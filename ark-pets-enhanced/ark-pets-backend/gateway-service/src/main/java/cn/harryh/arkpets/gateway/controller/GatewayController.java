package cn.harryh.arkpets.gateway.controller;

import cn.harryh.arkpets.constants.AppConstants;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 网关控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/gateway")
@CrossOrigin(origins = "*")
public class GatewayController {

  private static final Logger logger = LoggerFactory.getLogger(GatewayController.class);

  @Autowired private RouteLocator routeLocator;

  /** 获取所有路由信息 */
  @GetMapping("/routes")
  public Mono<ResponseEntity<Map<String, Object>>> getRoutes() {
    return routeLocator
        .getRoutes()
        .collectList()
        .map(
            routes -> {
              Map<String, Object> response = new HashMap<>();
              response.put("success", true);
              response.put("message", "路由信息获取成功");
              response.put("routes", routes.stream().map(this::routeToMap).toList());
              response.put("count", routes.size());
              response.put("timestamp", System.currentTimeMillis());

              logger.debug("Retrieved {} routes", routes.size());
              return ResponseEntity.ok(response);
            })
        .onErrorReturn(
            ResponseEntity.internalServerError()
                .body(
                    Map.of(
                        "success",
                        false,
                        "message",
                        "路由信息获取失败",
                        "timestamp",
                        System.currentTimeMillis())));
  }

  /** 获取特定路由信息 */
  @GetMapping("/routes/{routeId}")
  public Mono<ResponseEntity<Map<String, Object>>> getRoute(@PathVariable String routeId) {
    return routeLocator
        .getRoutes()
        .filter(route -> route.getId().equals(routeId))
        .next()
        .map(
            route -> {
              Map<String, Object> response = new HashMap<>();
              response.put("success", true);
              response.put("message", "路由信息获取成功");
              response.put("route", routeToMap(route));
              response.put("timestamp", System.currentTimeMillis());

              logger.debug("Retrieved route: {}", routeId);
              return ResponseEntity.ok(response);
            })
        .switchIfEmpty(Mono.just(ResponseEntity.notFound().build()));
  }

  /** 获取网关统计信息 */
  @GetMapping("/stats")
  public Mono<ResponseEntity<Map<String, Object>>> getStats() {
    return routeLocator
        .getRoutes()
        .collectList()
        .map(
            routes -> {
              Map<String, Object> stats = new HashMap<>();
              stats.put("total_routes", routes.size());
              stats.put("active_routes", routes.size()); // 简化版本，假设所有路由都是活跃的
              stats.put("gateway_version", "4.0.0");
              stats.put("uptime", getUptime());

              // 按服务分组统计
              Map<String, Long> serviceStats =
                  routes.stream()
                      .collect(
                          java.util.stream.Collectors.groupingBy(
                              route -> extractServiceName(route.getId()),
                              java.util.stream.Collectors.counting()));
              stats.put("service_routes", serviceStats);

              Map<String, Object> response = new HashMap<>();
              response.put("success", true);
              response.put("message", "网关统计信息获取成功");
              response.put("stats", stats);
              response.put("timestamp", System.currentTimeMillis());

              logger.debug("Retrieved gateway stats: {} routes", routes.size());
              return ResponseEntity.ok(response);
            })
        .onErrorReturn(
            ResponseEntity.internalServerError()
                .body(
                    Map.of(
                        "success",
                        false,
                        "message",
                        "网关统计信息获取失败",
                        "timestamp",
                        System.currentTimeMillis())));
  }

  /** 健康检查 */
  @GetMapping("/health")
  public Mono<ResponseEntity<Map<String, Object>>> health() {
    return routeLocator
        .getRoutes()
        .collectList()
        .map(
            routes -> {
              Map<String, Object> health = new HashMap<>();
              health.put("status", "UP");
              health.put("service", "gateway-service");
              health.put("routes_count", routes.size());
              health.put("timestamp", System.currentTimeMillis());

              return ResponseEntity.ok(health);
            })
        .onErrorReturn(
            ResponseEntity.status(503)
                .body(
                    Map.of(
                        "status", "DOWN",
                        "service", "gateway-service",
                        "timestamp", System.currentTimeMillis())));
  }

  /** 获取服务列表 */
  @GetMapping("/services")
  public Mono<ResponseEntity<Map<String, Object>>> getServices() {
    return routeLocator
        .getRoutes()
        .collectList()
        .map(
            routes -> {
              List<String> services =
                  routes.stream()
                      .map(route -> extractServiceName(route.getId()))
                      .distinct()
                      .sorted()
                      .toList();

              Map<String, Object> response = new HashMap<>();
              response.put("success", true);
              response.put("message", "服务列表获取成功");
              response.put("services", services);
              response.put("count", services.size());
              response.put("timestamp", System.currentTimeMillis());

              logger.debug("Retrieved {} services", services.size());
              return ResponseEntity.ok(response);
            })
        .onErrorReturn(
            ResponseEntity.internalServerError()
                .body(
                    Map.of(
                        "success",
                        false,
                        "message",
                        "服务列表获取失败",
                        "timestamp",
                        System.currentTimeMillis())));
  }

  /** 将Route对象转换为Map */
  private Map<String, Object> routeToMap(Route route) {
    Map<String, Object> routeMap = new HashMap<>();
    routeMap.put("id", route.getId());
    routeMap.put("uri", route.getUri().toString());
    routeMap.put("order", route.getOrder());
    routeMap.put("metadata", route.getMetadata());

    // 简化版本，不包含predicates和filters的详细信息
    routeMap.put("predicates", "Path predicates");
    routeMap.put("filters", "Gateway filters");

    return routeMap;
  }

  /** 从路由ID中提取服务名称 */
  private String extractServiceName(String routeId) {
    // 假设路由ID格式为 "service-name" 或 "service-name-xxx"
    if (routeId.contains("-service")) {
      return routeId.substring(0, routeId.indexOf("-service") + 8);
    }
    return routeId;
  }

  /** 获取网关运行时间 (简化版本) */
  private String getUptime() {
    // 简化版本，返回固定值
    return "1h 30m";
  }
}
