package cn.harryh.arkpets.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;

/**
 * API网关服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication
public class GatewayServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(GatewayServiceApplication.class, args);
  }

  /** 配置路由规则 */
  @Bean
  public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
    return builder
        .routes()
        // 认证服务路由
        .route("auth-service", r -> r.path("/api/v1/auth/**").uri("http://localhost:8081"))

        // AI服务路由
        .route("ai-service", r -> r.path("/api/v1/ai/**").uri("http://localhost:8082"))

        // 宠物服务路由
        .route("pet-service", r -> r.path("/api/v1/pets/**").uri("http://localhost:8083"))

        // 通知服务路由
        .route(
            "notification-service",
            r -> r.path("/api/v1/notifications/**").uri("http://localhost:8087"))

        // 数据服务路由
        .route("data-service", r -> r.path("/api/v1/data/**").uri("http://localhost:8088"))

        // 缓存服务路由
        .route("cache-service", r -> r.path("/api/v1/cache/**").uri("http://localhost:8084"))

        // 监控服务路由
        .route("monitor-service", r -> r.path("/api/v1/monitor/**").uri("http://localhost:8085"))

        // 配置服务路由
        .route(
            "config-service",
            r -> r.path("/api/v1/config/**", "/api/v1/discovery/**").uri("http://localhost:8086"))
        .build();
  }
}
