package cn.harryh.arkpets.gateway.client;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 服务健康检查器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Component
public class ServiceHealthChecker {

  private static final Logger logger = LoggerFactory.getLogger(ServiceHealthChecker.class);

  private final WebClient webClient;

  @Value("${gateway-service.load-balancer.health-check.enabled:true}")
  private boolean healthCheckEnabled;

  @Value("${gateway-service.load-balancer.health-check.timeout:5s}")
  private Duration healthCheckTimeout;

  // 服务健康状态缓存
  private final Map<String, Boolean> serviceHealthStatus = new ConcurrentHashMap<>();

  // 服务端点配置
  private final Map<String, String> serviceEndpoints =
      Map.of(
          "auth-service", "http://localhost:8081/actuator/health",
          "ai-service", "http://localhost:8082/actuator/health",
          "cache-service", "http://localhost:8084/api/v1/cache/health",
          "monitor-service", "http://localhost:8085/api/v1/monitor/health",
          "config-service", "http://localhost:8086/api/v1/config/health");

  public ServiceHealthChecker() {
    this.webClient =
        WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
            .build();
  }

  /** 定时健康检查 */
  @Scheduled(fixedRateString = "${gateway-service.load-balancer.health-check.interval:30000}")
  public void performHealthCheck() {
    if (!healthCheckEnabled) {
      logger.debug("Health check is disabled");
      return;
    }

    logger.debug("Starting health check for {} services", serviceEndpoints.size());

    serviceEndpoints.forEach(
        (serviceName, endpoint) -> {
          checkServiceHealth(serviceName, endpoint)
              .subscribe(
                  healthy -> {
                    serviceHealthStatus.put(serviceName, healthy);
                    if (healthy) {
                      logger.debug("Service {} is healthy", serviceName);
                    } else {
                      logger.warn("Service {} is unhealthy", serviceName);
                    }
                  },
                  error -> {
                    serviceHealthStatus.put(serviceName, false);
                    logger.error(
                        "Health check failed for service {}: {}", serviceName, error.getMessage());
                  });
        });
  }

  /** 检查单个服务健康状态 */
  private Mono<Boolean> checkServiceHealth(String serviceName, String endpoint) {
    return webClient
        .get()
        .uri(endpoint)
        .retrieve()
        .bodyToMono(Map.class)
        .timeout(healthCheckTimeout)
        .map(
            response -> {
              // 检查响应中的状态
              Object status = response.get("status");
              return "UP".equals(status) || Boolean.TRUE.equals(response.get("success"));
            })
        .onErrorReturn(false);
  }

  /** 获取服务健康状态 */
  public boolean isServiceHealthy(String serviceName) {
    return serviceHealthStatus.getOrDefault(serviceName, true); // 默认认为服务是健康的
  }

  /** 获取所有服务健康状态 */
  public Map<String, Boolean> getAllServiceHealthStatus() {
    return new HashMap<>(serviceHealthStatus);
  }

  /** 获取健康服务数量 */
  public long getHealthyServiceCount() {
    return serviceHealthStatus.values().stream().mapToLong(healthy -> healthy ? 1 : 0).sum();
  }

  /** 获取总服务数量 */
  public int getTotalServiceCount() {
    return serviceEndpoints.size();
  }

  /** 获取健康率 */
  public double getHealthRate() {
    int total = getTotalServiceCount();
    if (total == 0) {
      return 100.0;
    }
    return (double) getHealthyServiceCount() / total * 100.0;
  }

  /** 手动触发健康检查 */
  public void triggerHealthCheck() {
    logger.info("Manual health check triggered");
    performHealthCheck();
  }

  /** 重置健康状态 */
  public void resetHealthStatus() {
    serviceHealthStatus.clear();
    logger.info("Service health status reset");
  }
}
