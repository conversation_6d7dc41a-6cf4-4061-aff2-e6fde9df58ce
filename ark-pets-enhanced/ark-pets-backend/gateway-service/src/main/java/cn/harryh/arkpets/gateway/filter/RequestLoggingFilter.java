package cn.harryh.arkpets.gateway.filter;

import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 请求日志过滤器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Component
public class RequestLoggingFilter implements GlobalFilter, Ordered {

  private static final Logger logger = LoggerFactory.getLogger(RequestLoggingFilter.class);

  @Value("${gateway-service.logging.enabled:true}")
  private boolean loggingEnabled;

  @Value("${gateway-service.logging.include-headers:true}")
  private boolean includeHeaders;

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    if (!loggingEnabled) {
      return chain.filter(exchange);
    }

    ServerHttpRequest request = exchange.getRequest();
    ServerHttpResponse response = exchange.getResponse();

    // 生成请求ID
    String requestId = UUID.randomUUID().toString().substring(0, 8);

    // 记录请求开始时间
    long startTime = System.currentTimeMillis();

    // 添加请求ID到响应头
    response.getHeaders().add("X-Request-ID", requestId);

    // 记录请求信息
    logRequest(request, requestId);

    return chain
        .filter(exchange)
        .then(
            Mono.fromRunnable(
                () -> {
                  // 计算响应时间
                  long endTime = System.currentTimeMillis();
                  long responseTime = endTime - startTime;

                  // 记录响应信息
                  logResponse(response, requestId, responseTime);
                }));
  }

  /** 记录请求信息 */
  private void logRequest(ServerHttpRequest request, String requestId) {
    try {
      StringBuilder logMessage = new StringBuilder();
      logMessage.append("REQUEST [").append(requestId).append("] ");
      logMessage.append(request.getMethod()).append(" ");
      logMessage.append(request.getURI().getPath());

      if (request.getURI().getQuery() != null) {
        logMessage.append("?").append(request.getURI().getQuery());
      }

      logMessage.append(" from ").append(getClientIp(request));

      if (includeHeaders && !request.getHeaders().isEmpty()) {
        logMessage.append(" Headers: ").append(request.getHeaders().toSingleValueMap());
      }

      logger.info(logMessage.toString());

    } catch (Exception e) {
      logger.error("Error logging request for ID: {}", requestId, e);
    }
  }

  /** 记录响应信息 */
  private void logResponse(ServerHttpResponse response, String requestId, long responseTime) {
    try {
      StringBuilder logMessage = new StringBuilder();
      logMessage.append("RESPONSE [").append(requestId).append("] ");
      logMessage.append("Status: ").append(response.getStatusCode());
      logMessage.append(" Time: ").append(responseTime).append("ms");

      if (includeHeaders && !response.getHeaders().isEmpty()) {
        logMessage.append(" Headers: ").append(response.getHeaders().toSingleValueMap());
      }

      // 根据响应时间和状态码选择日志级别
      if (response.getStatusCode() != null && response.getStatusCode().is5xxServerError()) {
        logger.error(logMessage.toString());
      } else if (responseTime > 5000) { // 超过5秒的请求记录为警告
        logger.warn(logMessage.toString());
      } else {
        logger.info(logMessage.toString());
      }

    } catch (Exception e) {
      logger.error("Error logging response for ID: {}", requestId, e);
    }
  }

  /** 获取客户端IP地址 */
  private String getClientIp(ServerHttpRequest request) {
    String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
    if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
      return xForwardedFor.split(",")[0].trim();
    }

    String xRealIp = request.getHeaders().getFirst("X-Real-IP");
    if (xRealIp != null && !xRealIp.isEmpty()) {
      return xRealIp;
    }

    if (request.getRemoteAddress() != null) {
      return request.getRemoteAddress().getAddress().getHostAddress();
    }

    return "unknown";
  }

  @Override
  public int getOrder() {
    return Ordered.HIGHEST_PRECEDENCE;
  }
}
