package cn.harryh.arkpets.gateway.filter;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * JWT认证过滤器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Component
public class AuthenticationFilter
    extends AbstractGatewayFilterFactory<AuthenticationFilter.Config> {

  private static final Logger logger = LoggerFactory.getLogger(AuthenticationFilter.class);

  @Value("${gateway-service.security.jwt.enabled:true}")
  private boolean jwtEnabled;

  @Value("${gateway-service.security.jwt.secret:ark-pets-gateway-secret-2024}")
  private String jwtSecret;

  // 不需要认证的路径
  private static final List<String> EXCLUDED_PATHS =
      Arrays.asList(
          "/api/v1/auth/login", "/api/v1/auth/register", "/api/v1/auth/refresh", "/actuator");

  public AuthenticationFilter() {
    super(Config.class);
  }

  @Override
  public GatewayFilter apply(Config config) {
    return (exchange, chain) -> {
      if (!jwtEnabled) {
        logger.debug("JWT authentication disabled, skipping filter");
        return chain.filter(exchange);
      }

      ServerHttpRequest request = exchange.getRequest();
      String path = request.getURI().getPath();

      // 检查是否为排除路径
      if (isExcludedPath(path)) {
        logger.debug("Path {} is excluded from authentication", path);
        return chain.filter(exchange);
      }

      // 获取Authorization头
      String authHeader = request.getHeaders().getFirst("Authorization");
      if (authHeader == null || !authHeader.startsWith("Bearer ")) {
        logger.warn("Missing or invalid Authorization header for path: {}", path);
        return handleUnauthorized(exchange, "Missing or invalid Authorization header");
      }

      // 提取JWT token
      String token = authHeader.substring(7);

      // 验证JWT token (简化版本)
      if (!isValidToken(token)) {
        logger.warn("Invalid JWT token for path: {}", path);
        return handleUnauthorized(exchange, "Invalid JWT token");
      }

      // 添加用户信息到请求头
      ServerHttpRequest modifiedRequest =
          request
              .mutate()
              .header("X-User-ID", extractUserIdFromToken(token))
              .header("X-User-Role", extractUserRoleFromToken(token))
              .build();

      logger.debug("Authentication successful for path: {}", path);
      return chain.filter(exchange.mutate().request(modifiedRequest).build());
    };
  }

  /** 检查是否为排除路径 */
  private boolean isExcludedPath(String path) {
    return EXCLUDED_PATHS.stream().anyMatch(path::startsWith);
  }

  /** 验证JWT token (简化版本) */
  private boolean isValidToken(String token) {
    try {
      // 这里应该实现真正的JWT验证逻辑
      // 简化版本：检查token是否不为空且长度合理
      return token != null && token.length() > 10;
    } catch (Exception e) {
      logger.error("Error validating JWT token", e);
      return false;
    }
  }

  /** 从token中提取用户ID (简化版本) */
  private String extractUserIdFromToken(String token) {
    // 简化版本：返回模拟用户ID
    return "user123";
  }

  /** 从token中提取用户角色 (简化版本) */
  private String extractUserRoleFromToken(String token) {
    // 简化版本：返回模拟角色
    return "USER";
  }

  /** 处理未授权请求 */
  private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
    ServerHttpResponse response = exchange.getResponse();
    response.setStatusCode(HttpStatus.UNAUTHORIZED);
    response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

    String body =
        String.format(
            "{\"success\":false,\"message\":\"%s\",\"timestamp\":%d}",
            message, System.currentTimeMillis());

    DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
    return response.writeWith(Mono.just(buffer));
  }

  /** 配置类 */
  public static class Config {
    // 可以添加配置参数
  }
}
