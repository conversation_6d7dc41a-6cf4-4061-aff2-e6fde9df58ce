// API网关服务构建配置
configurations {
    // 排除Web MVC，因为Gateway需要WebFlux
    implementation.exclude module: 'spring-boot-starter-web'
    implementation.exclude module: 'spring-boot-starter-tomcat'
}

dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')

    // Spring Boot依赖已在根build.gradle中配置
    
    // Spring Cloud Gateway依赖 (简化版本)
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway:4.0.8'
    
    // 限流依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'
    
    // 监控依赖
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus:1.12.1'
    
    // 安全依赖
    implementation 'org.springframework.boot:spring-boot-starter-security'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    
    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
    
    // WebFlux (Gateway需要)
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.gateway.GatewayServiceApplication'
}
