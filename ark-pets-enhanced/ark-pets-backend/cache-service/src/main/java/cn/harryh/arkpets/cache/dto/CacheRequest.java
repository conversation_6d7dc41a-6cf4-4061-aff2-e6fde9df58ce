package cn.harryh.arkpets.cache.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 缓存请求DTO
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class CacheRequest {

  @NotBlank(message = "缓存键不能为空")
  private String key;

  @NotNull(message = "缓存值不能为null")
  private Object value;

  @Min(value = 0, message = "TTL不能为负数")
  private Long ttl = 3600L; // 默认1小时

  private String category;

  public CacheRequest() {}

  public CacheRequest(String key, Object value) {
    this.key = key;
    this.value = value;
  }

  public CacheRequest(String key, Object value, Long ttl) {
    this.key = key;
    this.value = value;
    this.ttl = ttl;
  }

  public String getKey() {
    return key;
  }

  public void setKey(String key) {
    this.key = key;
  }

  public Object getValue() {
    return value;
  }

  public void setValue(Object value) {
    this.value = value;
  }

  public Long getTtl() {
    return ttl;
  }

  public void setTtl(Long ttl) {
    this.ttl = ttl;
  }

  public String getCategory() {
    return category;
  }

  public void setCategory(String category) {
    this.category = category;
  }

  @Override
  public String toString() {
    return "CacheRequest{"
        + "key='"
        + key
        + '\''
        + ", ttl="
        + ttl
        + ", category='"
        + category
        + '\''
        + '}';
  }
}
