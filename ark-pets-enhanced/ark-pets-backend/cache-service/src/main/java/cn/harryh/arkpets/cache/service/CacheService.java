package cn.harryh.arkpets.cache.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 缓存服务核心类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class CacheService {

  private static final Logger logger = LoggerFactory.getLogger(CacheService.class);

  @Autowired private RedisTemplate<String, Object> redisTemplate;

  @Autowired private ObjectMapper objectMapper;

  /**
   * 设置缓存
   *
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间（秒）
   * @return 是否成功
   */
  public boolean set(String key, Object value, long ttl) {
    try {
      if (ttl > 0) {
        redisTemplate.opsForValue().set(key, value, ttl, TimeUnit.SECONDS);
      } else {
        redisTemplate.opsForValue().set(key, value);
      }
      logger.debug("Cache set successfully: key={}, ttl={}", key, ttl);
      return true;
    } catch (Exception e) {
      logger.error("Failed to set cache: key={}", key, e);
      return false;
    }
  }

  /** 设置缓存（使用Duration） */
  public boolean set(String key, Object value, Duration duration) {
    return set(key, value, duration.getSeconds());
  }

  /**
   * 获取缓存
   *
   * @param key 缓存键
   * @return 缓存值
   */
  public Object get(String key) {
    try {
      Object value = redisTemplate.opsForValue().get(key);
      logger.debug("Cache get: key={}, found={}", key, value != null);
      return value;
    } catch (Exception e) {
      logger.error("Failed to get cache: key={}", key, e);
      return null;
    }
  }

  /**
   * 获取缓存并转换为指定类型
   *
   * @param key 缓存键
   * @param clazz 目标类型
   * @return 缓存值
   */
  @SuppressWarnings("unchecked")
  public <T> T get(String key, Class<T> clazz) {
    try {
      Object value = get(key);
      if (value == null) {
        return null;
      }

      if (clazz.isInstance(value)) {
        return (T) value;
      }

      // 尝试JSON转换
      if (value instanceof String) {
        return objectMapper.readValue((String) value, clazz);
      }

      // 使用ObjectMapper转换
      return objectMapper.convertValue(value, clazz);

    } catch (Exception e) {
      logger.error(
          "Failed to get cache with type conversion: key={}, type={}",
          key,
          clazz.getSimpleName(),
          e);
      return null;
    }
  }

  /**
   * 删除缓存
   *
   * @param key 缓存键
   * @return 是否成功
   */
  public boolean delete(String key) {
    try {
      Boolean result = redisTemplate.delete(key);
      logger.debug("Cache delete: key={}, result={}", key, result);
      return Boolean.TRUE.equals(result);
    } catch (Exception e) {
      logger.error("Failed to delete cache: key={}", key, e);
      return false;
    }
  }

  /**
   * 批量删除缓存
   *
   * @param keys 缓存键集合
   * @return 删除的数量
   */
  public long delete(Collection<String> keys) {
    try {
      Long result = redisTemplate.delete(keys);
      logger.debug("Batch cache delete: keys={}, deleted={}", keys.size(), result);
      return result != null ? result : 0;
    } catch (Exception e) {
      logger.error("Failed to batch delete cache: keys={}", keys, e);
      return 0;
    }
  }

  /**
   * 检查缓存是否存在
   *
   * @param key 缓存键
   * @return 是否存在
   */
  public boolean exists(String key) {
    try {
      Boolean result = redisTemplate.hasKey(key);
      return Boolean.TRUE.equals(result);
    } catch (Exception e) {
      logger.error("Failed to check cache existence: key={}", key, e);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   *
   * @param key 缓存键
   * @param ttl 过期时间（秒）
   * @return 是否成功
   */
  public boolean expire(String key, long ttl) {
    try {
      Boolean result = redisTemplate.expire(key, ttl, TimeUnit.SECONDS);
      logger.debug("Cache expire set: key={}, ttl={}, result={}", key, ttl, result);
      return Boolean.TRUE.equals(result);
    } catch (Exception e) {
      logger.error("Failed to set cache expiration: key={}, ttl={}", key, ttl, e);
      return false;
    }
  }

  /**
   * 获取缓存剩余过期时间
   *
   * @param key 缓存键
   * @return 剩余时间（秒），-1表示永不过期，-2表示不存在
   */
  public long getExpire(String key) {
    try {
      Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
      return expire != null ? expire : -2;
    } catch (Exception e) {
      logger.error("Failed to get cache expiration: key={}", key, e);
      return -2;
    }
  }

  /**
   * 根据模式获取键列表
   *
   * @param pattern 键模式
   * @return 键列表
   */
  public Set<String> keys(String pattern) {
    try {
      Set<String> keys = redisTemplate.keys(pattern);
      logger.debug(
          "Cache keys search: pattern={}, found={}", pattern, keys != null ? keys.size() : 0);
      return keys;
    } catch (Exception e) {
      logger.error("Failed to search cache keys: pattern={}", pattern, e);
      return Set.of();
    }
  }

  /**
   * 清空指定模式的缓存
   *
   * @param pattern 键模式
   * @return 删除的数量
   */
  public long deleteByPattern(String pattern) {
    try {
      Set<String> keys = keys(pattern);
      if (keys.isEmpty()) {
        return 0;
      }
      return delete(keys);
    } catch (Exception e) {
      logger.error("Failed to delete cache by pattern: pattern={}", pattern, e);
      return 0;
    }
  }

  /**
   * 原子递增
   *
   * @param key 缓存键
   * @param delta 递增值
   * @return 递增后的值
   */
  public long increment(String key, long delta) {
    try {
      Long result = redisTemplate.opsForValue().increment(key, delta);
      return result != null ? result : 0;
    } catch (Exception e) {
      logger.error("Failed to increment cache: key={}, delta={}", key, delta, e);
      return 0;
    }
  }

  /** 原子递增（默认递增1） */
  public long increment(String key) {
    return increment(key, 1);
  }

  /**
   * 原子递减
   *
   * @param key 缓存键
   * @param delta 递减值
   * @return 递减后的值
   */
  public long decrement(String key, long delta) {
    try {
      Long result = redisTemplate.opsForValue().decrement(key, delta);
      return result != null ? result : 0;
    } catch (Exception e) {
      logger.error("Failed to decrement cache: key={}, delta={}", key, delta, e);
      return 0;
    }
  }

  /** 原子递减（默认递减1） */
  public long decrement(String key) {
    return decrement(key, 1);
  }

  /**
   * 获取缓存统计信息
   *
   * @return 统计信息
   */
  public Map<String, Object> getStats() {
    try {
      // 获取Redis信息
      java.util.Properties info = redisTemplate.getConnectionFactory().getConnection().info();

      // 解析关键统计信息
      Map<String, Object> stats =
          Map.of(
              "connected_clients", info.getProperty("connected_clients", "N/A"),
              "used_memory_human", info.getProperty("used_memory_human", "N/A"),
              "keyspace_hits", info.getProperty("keyspace_hits", "N/A"),
              "keyspace_misses", info.getProperty("keyspace_misses", "N/A"),
              "total_commands_processed", info.getProperty("total_commands_processed", "N/A"));

      logger.debug("Cache stats retrieved: {}", stats);
      return stats;

    } catch (Exception e) {
      logger.error("Failed to get cache stats", e);
      return Map.of("error", "Failed to retrieve stats");
    }
  }
}
