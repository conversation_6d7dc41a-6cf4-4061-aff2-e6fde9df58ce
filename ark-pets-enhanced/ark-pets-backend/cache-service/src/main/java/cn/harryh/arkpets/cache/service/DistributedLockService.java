package cn.harryh.arkpets.cache.service;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 分布式锁服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class DistributedLockService {

  private static final Logger logger = LoggerFactory.getLogger(DistributedLockService.class);

  @Autowired private RedissonClient redissonClient;

  @Value("${cache-service.distributed-lock.enabled:true}")
  private boolean lockEnabled;

  @Value("${cache-service.distributed-lock.default-lease-time:30000}")
  private long defaultLeaseTime;

  @Value("${cache-service.distributed-lock.default-wait-time:5000}")
  private long defaultWaitTime;

  /**
   * 尝试获取锁
   *
   * @param lockKey 锁键
   * @return 锁对象，null表示获取失败
   */
  public RLock tryLock(String lockKey) {
    return tryLock(lockKey, defaultWaitTime, defaultLeaseTime, TimeUnit.MILLISECONDS);
  }

  /**
   * 尝试获取锁
   *
   * @param lockKey 锁键
   * @param waitTime 等待时间
   * @param leaseTime 锁持有时间
   * @param timeUnit 时间单位
   * @return 锁对象，null表示获取失败
   */
  public RLock tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit timeUnit) {
    if (!lockEnabled) {
      logger.debug("Distributed lock is disabled, returning null for key: {}", lockKey);
      return null;
    }

    try {
      RLock lock = redissonClient.getLock(lockKey);
      boolean acquired = lock.tryLock(waitTime, leaseTime, timeUnit);

      if (acquired) {
        logger.debug(
            "Lock acquired successfully: key={}, leaseTime={} {}", lockKey, leaseTime, timeUnit);
        return lock;
      } else {
        logger.debug("Failed to acquire lock: key={}, waitTime={} {}", lockKey, waitTime, timeUnit);
        return null;
      }

    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      logger.warn("Lock acquisition interrupted: key={}", lockKey, e);
      return null;
    } catch (Exception e) {
      logger.error("Error acquiring lock: key={}", lockKey, e);
      return null;
    }
  }

  /**
   * 释放锁
   *
   * @param lock 锁对象
   * @return 是否成功释放
   */
  public boolean unlock(RLock lock) {
    if (lock == null) {
      return true;
    }

    try {
      if (lock.isHeldByCurrentThread()) {
        lock.unlock();
        logger.debug("Lock released successfully: key={}", lock.getName());
        return true;
      } else {
        logger.warn("Lock not held by current thread: key={}", lock.getName());
        return false;
      }
    } catch (Exception e) {
      logger.error("Error releasing lock: key={}", lock.getName(), e);
      return false;
    }
  }

  /**
   * 使用锁执行操作
   *
   * @param lockKey 锁键
   * @param operation 操作
   * @return 操作结果
   */
  public <T> T executeWithLock(String lockKey, Supplier<T> operation) {
    return executeWithLock(
        lockKey, defaultWaitTime, defaultLeaseTime, TimeUnit.MILLISECONDS, operation);
  }

  /**
   * 使用锁执行操作
   *
   * @param lockKey 锁键
   * @param waitTime 等待时间
   * @param leaseTime 锁持有时间
   * @param timeUnit 时间单位
   * @param operation 操作
   * @return 操作结果
   */
  public <T> T executeWithLock(
      String lockKey, long waitTime, long leaseTime, TimeUnit timeUnit, Supplier<T> operation) {
    RLock lock = tryLock(lockKey, waitTime, leaseTime, timeUnit);

    if (lock == null) {
      logger.warn("Failed to acquire lock for operation: key={}", lockKey);
      throw new RuntimeException("Failed to acquire distributed lock: " + lockKey);
    }

    try {
      logger.debug("Executing operation with lock: key={}", lockKey);
      return operation.get();
    } finally {
      unlock(lock);
    }
  }

  /**
   * 使用锁执行操作（无返回值）
   *
   * @param lockKey 锁键
   * @param operation 操作
   */
  public void executeWithLock(String lockKey, Runnable operation) {
    executeWithLock(
        lockKey,
        () -> {
          operation.run();
          return null;
        });
  }

  /**
   * 检查锁是否存在
   *
   * @param lockKey 锁键
   * @return 是否存在
   */
  public boolean isLocked(String lockKey) {
    try {
      RLock lock = redissonClient.getLock(lockKey);
      return lock.isLocked();
    } catch (Exception e) {
      logger.error("Error checking lock status: key={}", lockKey, e);
      return false;
    }
  }

  /**
   * 强制释放锁
   *
   * @param lockKey 锁键
   * @return 是否成功
   */
  public boolean forceUnlock(String lockKey) {
    try {
      RLock lock = redissonClient.getLock(lockKey);
      boolean result = lock.forceUnlock();
      logger.info("Force unlock: key={}, result={}", lockKey, result);
      return result;
    } catch (Exception e) {
      logger.error("Error force unlocking: key={}", lockKey, e);
      return false;
    }
  }

  /**
   * 获取锁的剩余时间
   *
   * @param lockKey 锁键
   * @return 剩余时间（毫秒），-1表示永不过期，-2表示不存在
   */
  public long getRemainingTime(String lockKey) {
    try {
      RLock lock = redissonClient.getLock(lockKey);
      return lock.remainTimeToLive();
    } catch (Exception e) {
      logger.error("Error getting lock remaining time: key={}", lockKey, e);
      return -2;
    }
  }

  /**
   * 获取可重入锁
   *
   * @param lockKey 锁键
   * @return 可重入锁
   */
  public RLock getReentrantLock(String lockKey) {
    return redissonClient.getLock(lockKey);
  }

  /**
   * 获取公平锁
   *
   * @param lockKey 锁键
   * @return 公平锁
   */
  public RLock getFairLock(String lockKey) {
    return redissonClient.getFairLock(lockKey);
  }

  /**
   * 获取读写锁的读锁
   *
   * @param lockKey 锁键
   * @return 读锁
   */
  public RLock getReadLock(String lockKey) {
    return redissonClient.getReadWriteLock(lockKey).readLock();
  }

  /**
   * 获取读写锁的写锁
   *
   * @param lockKey 锁键
   * @return 写锁
   */
  public RLock getWriteLock(String lockKey) {
    return redissonClient.getReadWriteLock(lockKey).writeLock();
  }
}
