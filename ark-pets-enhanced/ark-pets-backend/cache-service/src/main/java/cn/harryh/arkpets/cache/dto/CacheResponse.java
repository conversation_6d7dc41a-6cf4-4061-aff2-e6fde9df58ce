package cn.harryh.arkpets.cache.dto;

/**
 * 缓存响应DTO
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
public class CacheResponse {

  private Boolean success;
  private String message;
  private String key;
  private Object value;
  private Boolean exists;
  private Long ttl;
  private Long count;
  private Integer remaining;
  private Long timestamp;

  public CacheResponse() {}

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getKey() {
    return key;
  }

  public void setKey(String key) {
    this.key = key;
  }

  public Object getValue() {
    return value;
  }

  public void setValue(Object value) {
    this.value = value;
  }

  public Boolean getExists() {
    return exists;
  }

  public void setExists(Boolean exists) {
    this.exists = exists;
  }

  public Long getTtl() {
    return ttl;
  }

  public void setTtl(Long ttl) {
    this.ttl = ttl;
  }

  public Long getCount() {
    return count;
  }

  public void setCount(Long count) {
    this.count = count;
  }

  public Integer getRemaining() {
    return remaining;
  }

  public void setRemaining(Integer remaining) {
    this.remaining = remaining;
  }

  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    private CacheResponse response = new CacheResponse();

    public Builder success(Boolean success) {
      response.setSuccess(success);
      return this;
    }

    public Builder message(String message) {
      response.setMessage(message);
      return this;
    }

    public Builder key(String key) {
      response.setKey(key);
      return this;
    }

    public Builder value(Object value) {
      response.setValue(value);
      return this;
    }

    public Builder exists(Boolean exists) {
      response.setExists(exists);
      return this;
    }

    public Builder ttl(Long ttl) {
      response.setTtl(ttl);
      return this;
    }

    public Builder count(Long count) {
      response.setCount(count);
      return this;
    }

    public Builder remaining(Integer remaining) {
      response.setRemaining(remaining);
      return this;
    }

    public Builder timestamp(Long timestamp) {
      response.setTimestamp(timestamp);
      return this;
    }

    public CacheResponse build() {
      return response;
    }
  }

  @Override
  public String toString() {
    return "CacheResponse{"
        + "success="
        + success
        + ", message='"
        + message
        + '\''
        + ", key='"
        + key
        + '\''
        + ", exists="
        + exists
        + ", timestamp="
        + timestamp
        + '}';
  }
}
