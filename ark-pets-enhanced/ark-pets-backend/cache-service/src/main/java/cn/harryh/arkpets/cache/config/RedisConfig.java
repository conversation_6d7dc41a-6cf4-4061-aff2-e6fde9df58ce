package cn.harryh.arkpets.cache.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Configuration
public class RedisConfig {

  /** Redis模板配置 */
  @Bean
  @Primary
  public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
    RedisTemplate<String, Object> template = new RedisTemplate<>();
    template.setConnectionFactory(connectionFactory);

    // 配置JSON序列化器
    Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = createJsonSerializer();

    // 设置序列化器
    template.setKeySerializer(new StringRedisSerializer());
    template.setHashKeySerializer(new StringRedisSerializer());
    template.setValueSerializer(jackson2JsonRedisSerializer);
    template.setHashValueSerializer(jackson2JsonRedisSerializer);

    template.afterPropertiesSet();
    return template;
  }

  /** 字符串Redis模板 */
  @Bean
  public RedisTemplate<String, String> stringRedisTemplate(
      RedisConnectionFactory connectionFactory) {
    RedisTemplate<String, String> template = new RedisTemplate<>();
    template.setConnectionFactory(connectionFactory);

    // 使用字符串序列化器
    StringRedisSerializer stringSerializer = new StringRedisSerializer();
    template.setKeySerializer(stringSerializer);
    template.setHashKeySerializer(stringSerializer);
    template.setValueSerializer(stringSerializer);
    template.setHashValueSerializer(stringSerializer);

    template.afterPropertiesSet();
    return template;
  }

  /** 缓存管理器配置 */
  @Bean
  @Primary
  public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
    // 默认缓存配置
    RedisCacheConfiguration defaultConfig =
        RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1)) // 默认1小时过期
            .serializeKeysWith(
                org.springframework.data.redis.serializer.RedisSerializationContext
                    .SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(
                org.springframework.data.redis.serializer.RedisSerializationContext
                    .SerializationPair.fromSerializer(createJsonSerializer()))
            .disableCachingNullValues(); // 不缓存null值

    // 不同缓存的个性化配置
    Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

    // AI响应缓存 - 1小时
    cacheConfigurations.put("ai-response", defaultConfig.entryTtl(Duration.ofHours(1)));

    // 用户会话缓存 - 30分钟
    cacheConfigurations.put("user-session", defaultConfig.entryTtl(Duration.ofMinutes(30)));

    // 用户状态缓存 - 5分钟
    cacheConfigurations.put("user-state", defaultConfig.entryTtl(Duration.ofMinutes(5)));

    // API限流缓存 - 1分钟
    cacheConfigurations.put("rate-limit", defaultConfig.entryTtl(Duration.ofMinutes(1)));

    // 配置缓存 - 24小时
    cacheConfigurations.put("config", defaultConfig.entryTtl(Duration.ofHours(24)));

    // 临时缓存 - 10分钟
    cacheConfigurations.put("temp", defaultConfig.entryTtl(Duration.ofMinutes(10)));

    return RedisCacheManager.builder(connectionFactory)
        .cacheDefaults(defaultConfig)
        .withInitialCacheConfigurations(cacheConfigurations)
        .build();
  }

  /** 创建JSON序列化器 */
  private Jackson2JsonRedisSerializer<Object> createJsonSerializer() {
    Jackson2JsonRedisSerializer<Object> serializer =
        new Jackson2JsonRedisSerializer<>(Object.class);

    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
    objectMapper.activateDefaultTyping(
        LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);

    // 支持Java 8时间类型
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.disable(
        com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    serializer.setObjectMapper(objectMapper);
    return serializer;
  }
}
