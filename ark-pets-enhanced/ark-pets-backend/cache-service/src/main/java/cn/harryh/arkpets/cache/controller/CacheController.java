package cn.harryh.arkpets.cache.controller;

import cn.harryh.arkpets.cache.dto.CacheRequest;
import cn.harryh.arkpets.cache.dto.CacheResponse;
import cn.harryh.arkpets.cache.service.CacheService;
import cn.harryh.arkpets.cache.service.RateLimitService;
import cn.harryh.arkpets.constants.AppConstants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 缓存控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/cache")
@CrossOrigin(origins = "*")
public class CacheController {

  private static final Logger logger = LoggerFactory.getLogger(CacheController.class);

  @Autowired private CacheService cacheService;

  @Autowired private RateLimitService rateLimitService;

  /**
   * 设置缓存
   *
   * @param request 缓存请求
   * @return 缓存响应
   */
  @PostMapping("/set")
  public ResponseEntity<CacheResponse> setCache(
      @Valid @RequestBody CacheRequest request, HttpServletRequest httpRequest) {
    // 限流检查
    String clientId = getClientIdentifier(httpRequest);
    RateLimitService.RateLimitResult rateLimitResult =
        rateLimitService.isAllowed(clientId, "/api/v1/cache/set");

    if (!rateLimitResult.isAllowed()) {
      return ResponseEntity.status(429)
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("请求频率过高，请稍后重试")
                  .remaining(rateLimitResult.getRemaining())
                  .build());
    }

    try {
      logger.info("Setting cache: key={}, ttl={}", request.getKey(), request.getTtl());

      boolean success = cacheService.set(request.getKey(), request.getValue(), request.getTtl());

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(success)
              .message(success ? "缓存设置成功" : "缓存设置失败")
              .key(request.getKey())
              .remaining(rateLimitResult.getRemaining())
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error setting cache: key={}", request.getKey(), e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("缓存设置失败：" + e.getMessage())
                  .key(request.getKey())
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 获取缓存
   *
   * @param key 缓存键
   * @return 缓存响应
   */
  @GetMapping("/get/{key}")
  public ResponseEntity<CacheResponse> getCache(
      @PathVariable String key, HttpServletRequest httpRequest) {
    // 限流检查
    String clientId = getClientIdentifier(httpRequest);
    RateLimitService.RateLimitResult rateLimitResult =
        rateLimitService.isAllowed(clientId, "/api/v1/cache/get");

    if (!rateLimitResult.isAllowed()) {
      return ResponseEntity.status(429)
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("请求频率过高，请稍后重试")
                  .remaining(rateLimitResult.getRemaining())
                  .build());
    }

    try {
      logger.debug("Getting cache: key={}", key);

      Object value = cacheService.get(key);
      boolean exists = value != null;

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(true)
              .message(exists ? "缓存获取成功" : "缓存不存在")
              .key(key)
              .value(value)
              .exists(exists)
              .remaining(rateLimitResult.getRemaining())
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error getting cache: key={}", key, e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("缓存获取失败：" + e.getMessage())
                  .key(key)
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 删除缓存
   *
   * @param key 缓存键
   * @return 缓存响应
   */
  @DeleteMapping("/delete/{key}")
  public ResponseEntity<CacheResponse> deleteCache(
      @PathVariable String key, HttpServletRequest httpRequest) {
    // 限流检查
    String clientId = getClientIdentifier(httpRequest);
    RateLimitService.RateLimitResult rateLimitResult =
        rateLimitService.isAllowed(clientId, "/api/v1/cache/delete");

    if (!rateLimitResult.isAllowed()) {
      return ResponseEntity.status(429)
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("请求频率过高，请稍后重试")
                  .remaining(rateLimitResult.getRemaining())
                  .build());
    }

    try {
      logger.info("Deleting cache: key={}", key);

      boolean success = cacheService.delete(key);

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(success)
              .message(success ? "缓存删除成功" : "缓存删除失败")
              .key(key)
              .remaining(rateLimitResult.getRemaining())
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error deleting cache: key={}", key, e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("缓存删除失败：" + e.getMessage())
                  .key(key)
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 检查缓存是否存在
   *
   * @param key 缓存键
   * @return 缓存响应
   */
  @GetMapping("/exists/{key}")
  public ResponseEntity<CacheResponse> existsCache(@PathVariable String key) {
    try {
      logger.debug("Checking cache existence: key={}", key);

      boolean exists = cacheService.exists(key);

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(true)
              .message("缓存检查完成")
              .key(key)
              .exists(exists)
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error checking cache existence: key={}", key, e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("缓存检查失败：" + e.getMessage())
                  .key(key)
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 设置缓存过期时间
   *
   * @param key 缓存键
   * @param ttl 过期时间（秒）
   * @return 缓存响应
   */
  @PostMapping("/expire/{key}")
  public ResponseEntity<CacheResponse> expireCache(
      @PathVariable String key, @RequestParam long ttl) {
    try {
      logger.info("Setting cache expiration: key={}, ttl={}", key, ttl);

      boolean success = cacheService.expire(key, ttl);

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(success)
              .message(success ? "过期时间设置成功" : "过期时间设置失败")
              .key(key)
              .ttl(ttl)
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error setting cache expiration: key={}, ttl={}", key, ttl, e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("过期时间设置失败：" + e.getMessage())
                  .key(key)
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 根据模式搜索缓存键
   *
   * @param pattern 搜索模式
   * @return 缓存响应
   */
  @GetMapping("/keys")
  public ResponseEntity<CacheResponse> searchKeys(@RequestParam String pattern) {
    try {
      logger.debug("Searching cache keys: pattern={}", pattern);

      Set<String> keys = cacheService.keys(pattern);

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(true)
              .message("键搜索完成")
              .value(keys)
              .count((long) keys.size())
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error searching cache keys: pattern={}", pattern, e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("键搜索失败：" + e.getMessage())
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 清空指定模式的缓存
   *
   * @param pattern 清空模式
   * @return 缓存响应
   */
  @DeleteMapping("/clear")
  public ResponseEntity<CacheResponse> clearCache(@RequestParam String pattern) {
    try {
      logger.info("Clearing cache by pattern: pattern={}", pattern);

      long deletedCount = cacheService.deleteByPattern(pattern);

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(true)
              .message("缓存清理完成")
              .count(deletedCount)
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error clearing cache: pattern={}", pattern, e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("缓存清理失败：" + e.getMessage())
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 获取缓存统计信息
   *
   * @return 缓存响应
   */
  @GetMapping("/stats")
  public ResponseEntity<CacheResponse> getCacheStats() {
    try {
      logger.debug("Getting cache statistics");

      Map<String, Object> stats = cacheService.getStats();

      return ResponseEntity.ok(
          CacheResponse.builder()
              .success(true)
              .message("统计信息获取成功")
              .value(stats)
              .timestamp(System.currentTimeMillis())
              .build());

    } catch (Exception e) {
      logger.error("Error getting cache statistics", e);
      return ResponseEntity.internalServerError()
          .body(
              CacheResponse.builder()
                  .success(false)
                  .message("统计信息获取失败：" + e.getMessage())
                  .timestamp(System.currentTimeMillis())
                  .build());
    }
  }

  /**
   * 健康检查
   *
   * @return 健康状态
   */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    try {
      // 测试Redis连接
      cacheService.set("health_check", "ok", 10);
      String result = cacheService.get("health_check", String.class);

      boolean healthy = "ok".equals(result);

      return ResponseEntity.ok(
          Map.of(
              "status",
              healthy ? "UP" : "DOWN",
              "service",
              "cache-service",
              "timestamp",
              System.currentTimeMillis(),
              "redis",
              healthy ? "connected" : "disconnected"));

    } catch (Exception e) {
      logger.error("Health check failed", e);
      return ResponseEntity.status(503)
          .body(
              Map.of(
                  "status",
                  "DOWN",
                  "service",
                  "cache-service",
                  "timestamp",
                  System.currentTimeMillis(),
                  "error",
                  e.getMessage()));
    }
  }

  /** 获取客户端标识符 */
  private String getClientIdentifier(HttpServletRequest request) {
    // 优先使用用户ID，其次使用IP地址
    String userId = request.getHeader("X-User-ID");
    if (userId != null && !userId.isEmpty()) {
      return "user:" + userId;
    }

    String clientIp = getClientIpAddress(request);
    return "ip:" + clientIp;
  }

  /** 获取客户端IP地址 */
  private String getClientIpAddress(HttpServletRequest request) {
    String xForwardedFor = request.getHeader("X-Forwarded-For");
    if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
      return xForwardedFor.split(",")[0].trim();
    }

    String xRealIp = request.getHeader("X-Real-IP");
    if (xRealIp != null && !xRealIp.isEmpty()) {
      return xRealIp;
    }

    return request.getRemoteAddr();
  }
}
