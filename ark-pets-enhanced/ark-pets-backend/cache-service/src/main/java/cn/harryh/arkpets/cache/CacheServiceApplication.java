package cn.harryh.arkpets.cache;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 缓存服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication
@EnableFeignClients
@EnableCaching
public class CacheServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(CacheServiceApplication.class, args);
  }
}
