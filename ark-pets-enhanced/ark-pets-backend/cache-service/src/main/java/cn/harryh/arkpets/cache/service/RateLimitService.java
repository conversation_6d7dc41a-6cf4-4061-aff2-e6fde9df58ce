package cn.harryh.arkpets.cache.service;

import java.time.Instant;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

/**
 * 限流服务
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
public class RateLimitService {

  private static final Logger logger = LoggerFactory.getLogger(RateLimitService.class);

  @Autowired private RedisTemplate<String, Object> redisTemplate;

  @Value("${cache-service.rate-limit.enabled:true}")
  private boolean rateLimitEnabled;

  @Value("${cache-service.rate-limit.default-limit:100}")
  private int defaultLimit;

  // API限流配置缓存
  private final Map<String, Integer> apiLimits = new ConcurrentHashMap<>();

  // Lua脚本：滑动窗口限流算法
  private static final String RATE_LIMIT_SCRIPT =
      """
      local key = KEYS[1]
      local window = tonumber(ARGV[1])
      local limit = tonumber(ARGV[2])
      local current_time = tonumber(ARGV[3])

      -- 清理过期的记录
      redis.call('ZREMRANGEBYSCORE', key, 0, current_time - window * 1000)

      -- 获取当前窗口内的请求数
      local current_requests = redis.call('ZCARD', key)

      if current_requests < limit then
          -- 添加当前请求
          redis.call('ZADD', key, current_time, current_time)
          redis.call('EXPIRE', key, window)
          return {1, limit - current_requests - 1}
      else
          return {0, 0}
      end
      """;

  private final DefaultRedisScript<Object> rateLimitLuaScript;

  public RateLimitService() {
    this.rateLimitLuaScript = new DefaultRedisScript<>();
    this.rateLimitLuaScript.setScriptText(RATE_LIMIT_SCRIPT);
    this.rateLimitLuaScript.setResultType(Object.class);

    // 初始化API限流配置
    initApiLimits();
  }

  /** 初始化API限流配置 */
  private void initApiLimits() {
    apiLimits.put("/api/v1/auth/login", 10);
    apiLimits.put("/api/v1/ai/chat", 60);
    apiLimits.put("/api/v1/ai/emotion", 30);
    apiLimits.put("/api/v1/cache/set", 200);
    apiLimits.put("/api/v1/cache/get", 500);
  }

  /**
   * 检查是否允许请求
   *
   * @param identifier 标识符（用户ID、IP等）
   * @param api API路径
   * @return 限流结果
   */
  public RateLimitResult isAllowed(String identifier, String api) {
    if (!rateLimitEnabled) {
      return RateLimitResult.allowed(Integer.MAX_VALUE);
    }

    try {
      String key = buildRateLimitKey(identifier, api);
      int limit = getApiLimit(api);
      long currentTime = Instant.now().toEpochMilli();

      // 执行Lua脚本
      Object result =
          redisTemplate.execute(
              rateLimitLuaScript,
              Collections.singletonList(key),
              60, // 60秒窗口
              limit,
              currentTime);

      if (result instanceof java.util.List) {
        @SuppressWarnings("unchecked")
        java.util.List<Object> resultList = (java.util.List<Object>) result;

        boolean allowed = "1".equals(String.valueOf(resultList.get(0)));
        int remaining = Integer.parseInt(String.valueOf(resultList.get(1)));

        logger.debug(
            "Rate limit check: identifier={}, api={}, allowed={}, remaining={}",
            identifier,
            api,
            allowed,
            remaining);

        return allowed ? RateLimitResult.allowed(remaining) : RateLimitResult.rejected(0);
      }

      logger.warn("Unexpected rate limit script result: {}", result);
      return RateLimitResult.allowed(defaultLimit);

    } catch (Exception e) {
      logger.error("Rate limit check failed: identifier={}, api={}", identifier, api, e);
      // 出错时允许请求，避免影响正常业务
      return RateLimitResult.allowed(defaultLimit);
    }
  }

  /**
   * 检查是否允许请求（简化版本）
   *
   * @param identifier 标识符
   * @return 限流结果
   */
  public RateLimitResult isAllowed(String identifier) {
    return isAllowed(identifier, "default");
  }

  /**
   * 获取API限流配置
   *
   * @param api API路径
   * @return 限流次数
   */
  private int getApiLimit(String api) {
    return apiLimits.getOrDefault(api, defaultLimit);
  }

  /**
   * 构建限流键
   *
   * @param identifier 标识符
   * @param api API路径
   * @return 限流键
   */
  private String buildRateLimitKey(String identifier, String api) {
    return String.format("rate_limit:%s:%s", identifier, api);
  }

  /**
   * 重置限流计数
   *
   * @param identifier 标识符
   * @param api API路径
   * @return 是否成功
   */
  public boolean reset(String identifier, String api) {
    try {
      String key = buildRateLimitKey(identifier, api);
      Boolean result = redisTemplate.delete(key);
      logger.info("Rate limit reset: identifier={}, api={}, result={}", identifier, api, result);
      return Boolean.TRUE.equals(result);
    } catch (Exception e) {
      logger.error("Failed to reset rate limit: identifier={}, api={}", identifier, api, e);
      return false;
    }
  }

  /**
   * 获取当前限流状态
   *
   * @param identifier 标识符
   * @param api API路径
   * @return 当前请求数
   */
  public long getCurrentCount(String identifier, String api) {
    try {
      String key = buildRateLimitKey(identifier, api);
      Long count =
          redisTemplate
              .opsForZSet()
              .count(
                  key, Instant.now().minusSeconds(60).toEpochMilli(), Instant.now().toEpochMilli());
      return count != null ? count : 0;
    } catch (Exception e) {
      logger.error(
          "Failed to get current rate limit count: identifier={}, api={}", identifier, api, e);
      return 0;
    }
  }

  /**
   * 更新API限流配置
   *
   * @param api API路径
   * @param limit 限流次数
   */
  public void updateApiLimit(String api, int limit) {
    apiLimits.put(api, limit);
    logger.info("API rate limit updated: api={}, limit={}", api, limit);
  }

  /**
   * 获取所有API限流配置
   *
   * @return API限流配置
   */
  public Map<String, Integer> getAllApiLimits() {
    return new ConcurrentHashMap<>(apiLimits);
  }

  /** 限流结果类 */
  public static class RateLimitResult {
    private final boolean allowed;
    private final int remaining;
    private final String message;

    private RateLimitResult(boolean allowed, int remaining, String message) {
      this.allowed = allowed;
      this.remaining = remaining;
      this.message = message;
    }

    public static RateLimitResult allowed(int remaining) {
      return new RateLimitResult(true, remaining, "Request allowed");
    }

    public static RateLimitResult rejected(int remaining) {
      return new RateLimitResult(false, remaining, "Rate limit exceeded");
    }

    public boolean isAllowed() {
      return allowed;
    }

    public int getRemaining() {
      return remaining;
    }

    public String getMessage() {
      return message;
    }

    @Override
    public String toString() {
      return String.format(
          "RateLimitResult{allowed=%s, remaining=%d, message='%s'}", allowed, remaining, message);
    }
  }
}
