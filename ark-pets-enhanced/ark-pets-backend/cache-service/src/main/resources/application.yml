server:
  port: 8084

spring:
  application:
    name: ark-pets-cache-service
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 2
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
      shutdown-timeout: 100ms
    
    # Redis集群配置 (可选)
    cluster:
      nodes: ${REDIS_CLUSTER_NODES:}
      max-redirects: 3
    
    # Redis哨兵配置 (可选)
    sentinel:
      master: ${REDIS_SENTINEL_MASTER:}
      nodes: ${REDIS_SENTINEL_NODES:}
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false
      use-key-prefix: true
      key-prefix: "ark-pets:"

# Redisson配置
redisson:
  config: |
    singleServerConfig:
      address: "redis://${REDIS_HOST:localhost}:${REDIS_PORT:6379}"
      password: ${REDIS_PASSWORD:}
      database: 2
      connectionPoolSize: 20
      connectionMinimumIdleSize: 5
      timeout: 3000
      retryAttempts: 3
      retryInterval: 1500

# 缓存服务配置
cache-service:
  # 缓存策略配置
  strategies:
    # AI响应缓存
    ai-response:
      ttl: 3600 # 1小时
      max-size: 10000
      enable-local: true
    
    # 用户会话缓存
    user-session:
      ttl: 1800 # 30分钟
      max-size: 5000
      enable-local: false
    
    # 用户状态缓存
    user-state:
      ttl: 300 # 5分钟
      max-size: 1000
      enable-local: true
    
    # API限流缓存
    rate-limit:
      ttl: 60 # 1分钟
      max-size: 50000
      enable-local: false
  
  # 限流配置
  rate-limit:
    enabled: true
    default-limit: 100 # 每分钟100次
    burst-limit: 200 # 突发限制
    
    # 不同API的限流配置
    api-limits:
      "/api/v1/auth/login": 10 # 登录接口每分钟10次
      "/api/v1/ai/chat": 60 # AI聊天每分钟60次
      "/api/v1/ai/emotion": 30 # 情感分析每分钟30次
  
  # 分布式锁配置
  distributed-lock:
    enabled: true
    default-lease-time: 30000 # 30秒
    default-wait-time: 5000 # 5秒
  
  # 缓存预热配置
  warm-up:
    enabled: true
    strategies:
      - name: "common-ai-responses"
        cron: "0 0 6 * * ?" # 每天6点预热
      - name: "user-preferences"
        cron: "0 30 6 * * ?" # 每天6点30分预热

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    org.springframework.cache: INFO
    org.redisson: INFO
    io.lettuce: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,caches
  endpoint:
    health:
      show-details: always
    caches:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
