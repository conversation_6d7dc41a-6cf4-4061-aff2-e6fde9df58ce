// 缓存服务构建配置
dependencies {
    // 共享模块依赖
    implementation project(':ark-pets-shared:common')
    implementation project(':ark-pets-shared:dto')
    implementation project(':ark-pets-shared:constants')
    
    // Spring Boot依赖已在根build.gradle中配置
    
    // Redis依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    
    // Lettuce Redis客户端 (Spring Boot默认)
    implementation 'io.lettuce:lettuce-core:6.3.0.RELEASE'
    
    // Redisson分布式锁
    implementation 'org.redisson:redisson-spring-boot-starter:3.25.2'
    
    // Caffeine本地缓存
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'
    
    // 限流组件
    implementation 'com.github.vladimir-bukhtoyarov:bucket4j-core:7.6.0'
    implementation 'com.github.vladimir-bukhtoyarov:bucket4j-redis:7.6.0'
    
    // JSON序列化
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.16.1'
    
    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
}

// 应用程序插件配置
apply plugin: 'org.springframework.boot'

// Spring Boot配置
springBoot {
    mainClass = 'cn.harryh.arkpets.cache.CacheServiceApplication'
}
