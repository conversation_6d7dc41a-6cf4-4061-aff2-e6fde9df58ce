server:
  port: 8083

spring:
  application:
    name: ark-pets-pet-service
  
  # 数据库配置
  datasource:
    url: ********************************************
    username: ark_pets_user
    password: ark_pets_password_2024
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 3
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # 安全配置
  security:
    user:
      name: admin
      password: ark-pets-pet-2024

# 宠物服务配置
pet-service:
  # AI引擎配置
  ai:
    enabled: true
    analysis-interval: 300s # 5分钟分析一次
    response-cache-ttl: 60s
    max-analysis-history: 100
  
  # 自动更新配置
  auto-update:
    enabled: true
    interval: 3600s # 1小时更新一次
    batch-size: 100
  
  # 宠物状态配置
  pet-stats:
    # 自然变化率（每小时）
    hunger-increase-rate: 5
    energy-decrease-rate: 3
    health-decrease-rate: 1
    
    # 状态阈值
    hunger-warning-threshold: 70
    hunger-critical-threshold: 90
    energy-warning-threshold: 30
    energy-critical-threshold: 10
    health-warning-threshold: 40
    health-critical-threshold: 20
    
    # 升级经验公式
    level-up-base-exp: 100
    level-up-multiplier: 1.2
  
  # 互动配置
  interaction:
    # 冷却时间（秒）
    feed-cooldown: 1800 # 30分钟
    play-cooldown: 900  # 15分钟
    sleep-cooldown: 3600 # 1小时
    
    # 经验奖励
    feed-exp: 10
    play-exp: 15
    sleep-exp: 5
    exercise-exp: 20
    train-exp: 25
  
  # 数据清理配置
  cleanup:
    enabled: true
    action-history-days: 30 # 保留30天的行为记录
    cleanup-interval: 86400s # 每天清理一次

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    tags:
      application: ark-pets
      service: pet-service
      environment: ${ENVIRONMENT:development}
      version: 4.0.0

# 日志配置
logging:
  level:
    cn.harryh.arkpets: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/pet-service.log
    max-size: 100MB
    max-history: 30

# 定时任务配置
spring.quartz:
  job-store-type: memory
  properties:
    org:
      quartz:
        scheduler:
          instanceName: PetServiceScheduler
        threadPool:
          threadCount: 5
