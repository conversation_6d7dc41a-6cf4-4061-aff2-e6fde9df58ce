package cn.harryh.arkpets.pet.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import org.hibernate.annotations.CreationTimestamp;

/**
 * 宠物行为记录实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "pet_actions")
public class PetAction {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "宠物ID不能为空")
  @Column(name = "pet_id", nullable = false)
  private String petId;

  @NotNull(message = "行为类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private ActionType actionType;

  @Column(length = 200)
  private String description;

  @Column(name = "energy_change")
  private Integer energyChange = 0;

  @Column(name = "hunger_change")
  private Integer hungerChange = 0;

  @Column(name = "health_change")
  private Integer healthChange = 0;

  @Column(name = "mood_change")
  private String moodChange;

  @Column(name = "experience_gain")
  private Long experienceGain = 0L;

  @Column(name = "user_id", nullable = false)
  private String userId;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  // 行为类型枚举
  public enum ActionType {
    FEED("喂食"),
    PLAY("玩耍"),
    SLEEP("睡觉"),
    EXERCISE("运动"),
    TREAT("治疗"),
    CLEAN("清洁"),
    TRAIN("训练"),
    PET("抚摸"),
    TALK("对话"),
    GAME("游戏");

    private final String displayName;

    ActionType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public PetAction() {}

  public PetAction(String petId, ActionType actionType, String userId) {
    this.petId = petId;
    this.actionType = actionType;
    this.userId = userId;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getPetId() {
    return petId;
  }

  public void setPetId(String petId) {
    this.petId = petId;
  }

  public ActionType getActionType() {
    return actionType;
  }

  public void setActionType(ActionType actionType) {
    this.actionType = actionType;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public Integer getEnergyChange() {
    return energyChange;
  }

  public void setEnergyChange(Integer energyChange) {
    this.energyChange = energyChange;
  }

  public Integer getHungerChange() {
    return hungerChange;
  }

  public void setHungerChange(Integer hungerChange) {
    this.hungerChange = hungerChange;
  }

  public Integer getHealthChange() {
    return healthChange;
  }

  public void setHealthChange(Integer healthChange) {
    this.healthChange = healthChange;
  }

  public String getMoodChange() {
    return moodChange;
  }

  public void setMoodChange(String moodChange) {
    this.moodChange = moodChange;
  }

  public Long getExperienceGain() {
    return experienceGain;
  }

  public void setExperienceGain(Long experienceGain) {
    this.experienceGain = experienceGain;
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }
}
