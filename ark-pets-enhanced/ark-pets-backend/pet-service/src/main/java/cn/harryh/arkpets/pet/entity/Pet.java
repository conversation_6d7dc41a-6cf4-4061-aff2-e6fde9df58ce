package cn.harryh.arkpets.pet.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 宠物实体类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Entity
@Table(name = "pets")
public class Pet {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @NotBlank(message = "宠物名称不能为空")
  @Size(min = 1, max = 50, message = "宠物名称长度必须在1-50个字符之间")
  @Column(nullable = false, length = 50)
  private String name;

  @NotNull(message = "宠物类型不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private PetType type;

  @Size(max = 50, message = "品种名称不能超过50个字符")
  @Column(length = 50)
  private String breed;

  @Min(value = 0, message = "年龄不能为负数")
  @Max(value = 100, message = "年龄不能超过100")
  private Integer age;

  @Enumerated(EnumType.STRING)
  private Gender gender;

  @Size(max = 30, message = "颜色描述不能超过30个字符")
  @Column(length = 30)
  private String color;

  @ElementCollection
  @CollectionTable(name = "pet_personalities", joinColumns = @JoinColumn(name = "pet_id"))
  @Column(name = "personality")
  private List<String> personality;

  @Column(length = 500)
  private String avatar;

  @NotNull(message = "宠物状态不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private PetStatus status = PetStatus.ACTIVE;

  @NotNull(message = "宠物心情不能为空")
  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private Mood mood = Mood.HAPPY;

  @Min(value = 0, message = "能量值不能为负数")
  @Max(value = 100, message = "能量值不能超过100")
  @Column(nullable = false)
  private Integer energy = 100;

  @Min(value = 0, message = "饥饿值不能为负数")
  @Max(value = 100, message = "饥饿值不能超过100")
  @Column(nullable = false)
  private Integer hunger = 0;

  @Min(value = 0, message = "健康值不能为负数")
  @Max(value = 100, message = "健康值不能超过100")
  @Column(nullable = false)
  private Integer health = 100;

  @Min(value = 0, message = "经验值不能为负数")
  @Column(nullable = false)
  private Long experience = 0L;

  @Min(value = 1, message = "等级不能小于1")
  @Column(nullable = false)
  private Integer level = 1;

  @NotBlank(message = "用户ID不能为空")
  @Column(name = "user_id", nullable = false)
  private String userId;

  @Column(name = "last_interaction")
  private LocalDateTime lastInteraction;

  @Column(name = "last_feed")
  private LocalDateTime lastFeed;

  @Column(name = "last_play")
  private LocalDateTime lastPlay;

  @CreationTimestamp
  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @UpdateTimestamp
  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  // 枚举定义
  public enum PetType {
    CAT("猫"),
    DOG("狗"),
    BIRD("鸟"),
    FISH("鱼"),
    RABBIT("兔子"),
    HAMSTER("仓鼠"),
    OTHER("其他");

    private final String displayName;

    PetType(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  public enum Gender {
    MALE("雄性"),
    FEMALE("雌性"),
    UNKNOWN("未知");

    private final String displayName;

    Gender(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  public enum PetStatus {
    ACTIVE("活跃"),
    SLEEPING("睡觉"),
    PLAYING("玩耍"),
    EATING("进食"),
    SICK("生病"),
    INACTIVE("不活跃");

    private final String displayName;

    PetStatus(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  public enum Mood {
    HAPPY("开心"),
    SAD("伤心"),
    EXCITED("兴奋"),
    CALM("平静"),
    ANGRY("愤怒"),
    TIRED("疲惫"),
    PLAYFUL("顽皮");

    private final String displayName;

    Mood(String displayName) {
      this.displayName = displayName;
    }

    public String getDisplayName() {
      return displayName;
    }
  }

  // 构造函数
  public Pet() {}

  public Pet(String name, PetType type, String userId) {
    this.name = name;
    this.type = type;
    this.userId = userId;
  }

  // Getter和Setter方法
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public PetType getType() {
    return type;
  }

  public void setType(PetType type) {
    this.type = type;
  }

  public String getBreed() {
    return breed;
  }

  public void setBreed(String breed) {
    this.breed = breed;
  }

  public Integer getAge() {
    return age;
  }

  public void setAge(Integer age) {
    this.age = age;
  }

  public Gender getGender() {
    return gender;
  }

  public void setGender(Gender gender) {
    this.gender = gender;
  }

  public String getColor() {
    return color;
  }

  public void setColor(String color) {
    this.color = color;
  }

  public List<String> getPersonality() {
    return personality;
  }

  public void setPersonality(List<String> personality) {
    this.personality = personality;
  }

  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public PetStatus getStatus() {
    return status;
  }

  public void setStatus(PetStatus status) {
    this.status = status;
  }

  public Mood getMood() {
    return mood;
  }

  public void setMood(Mood mood) {
    this.mood = mood;
  }

  public Integer getEnergy() {
    return energy;
  }

  public void setEnergy(Integer energy) {
    this.energy = Math.max(0, Math.min(100, energy));
  }

  public Integer getHunger() {
    return hunger;
  }

  public void setHunger(Integer hunger) {
    this.hunger = Math.max(0, Math.min(100, hunger));
  }

  public Integer getHealth() {
    return health;
  }

  public void setHealth(Integer health) {
    this.health = Math.max(0, Math.min(100, health));
  }

  public Long getExperience() {
    return experience;
  }

  public void setExperience(Long experience) {
    this.experience = Math.max(0, experience);
  }

  public Integer getLevel() {
    return level;
  }

  public void setLevel(Integer level) {
    this.level = Math.max(1, level);
  }

  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public LocalDateTime getLastInteraction() {
    return lastInteraction;
  }

  public void setLastInteraction(LocalDateTime lastInteraction) {
    this.lastInteraction = lastInteraction;
  }

  public LocalDateTime getLastFeed() {
    return lastFeed;
  }

  public void setLastFeed(LocalDateTime lastFeed) {
    this.lastFeed = lastFeed;
  }

  public LocalDateTime getLastPlay() {
    return lastPlay;
  }

  public void setLastPlay(LocalDateTime lastPlay) {
    this.lastPlay = lastPlay;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  // 业务方法
  public void addExperience(long exp) {
    this.experience += exp;
    checkLevelUp();
  }

  private void checkLevelUp() {
    long requiredExp = level * 100L; // 简单的升级公式
    if (experience >= requiredExp) {
      level++;
      // 升级时恢复一些状态
      setEnergy(Math.min(100, energy + 20));
      setHealth(Math.min(100, health + 10));
    }
  }

  public boolean needsAttention() {
    return hunger > 80 || energy < 20 || health < 30;
  }

  public void updateMoodBasedOnStats() {
    if (health < 30) {
      mood = Mood.SAD;
    } else if (hunger > 80) {
      mood = Mood.ANGRY;
    } else if (energy < 20) {
      mood = Mood.TIRED;
    } else if (energy > 80 && health > 80) {
      mood = Mood.HAPPY;
    } else {
      mood = Mood.CALM;
    }
  }
}
