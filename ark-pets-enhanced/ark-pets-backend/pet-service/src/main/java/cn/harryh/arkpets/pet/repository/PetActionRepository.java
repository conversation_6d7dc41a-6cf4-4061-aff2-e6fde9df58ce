package cn.harryh.arkpets.pet.repository;

import cn.harryh.arkpets.pet.entity.PetAction;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 宠物行为记录仓库接口
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Repository
public interface PetActionRepository extends JpaRepository<PetAction, String> {

  /** 根据宠物ID查找行为记录 */
  List<PetAction> findByPetIdOrderByCreatedAtDesc(String petId);

  /** 根据宠物ID分页查找行为记录 */
  Page<PetAction> findByPetId(String petId, Pageable pageable);

  /** 根据用户ID查找行为记录 */
  List<PetAction> findByUserIdOrderByCreatedAtDesc(String userId);

  /** 根据用户ID分页查找行为记录 */
  Page<PetAction> findByUserId(String userId, Pageable pageable);

  /** 根据行为类型查找记录 */
  List<PetAction> findByActionType(PetAction.ActionType actionType);

  /** 查找指定时间范围内的行为记录 */
  @Query(
      "SELECT pa FROM PetAction pa WHERE pa.createdAt BETWEEN :startTime AND :endTime ORDER BY pa.createdAt DESC")
  List<PetAction> findActionsBetween(
      @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

  /** 查找宠物在指定时间范围内的行为记录 */
  @Query(
      "SELECT pa FROM PetAction pa WHERE pa.petId = :petId AND pa.createdAt BETWEEN :startTime AND :endTime ORDER BY pa.createdAt DESC")
  List<PetAction> findPetActionsBetween(
      @Param("petId") String petId,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);

  /** 查找用户在指定时间范围内的行为记录 */
  @Query(
      "SELECT pa FROM PetAction pa WHERE pa.userId = :userId AND pa.createdAt BETWEEN :startTime AND :endTime ORDER BY pa.createdAt DESC")
  List<PetAction> findUserActionsBetween(
      @Param("userId") String userId,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);

  /** 统计宠物的行为次数 */
  @Query(
      "SELECT pa.actionType, COUNT(pa) FROM PetAction pa WHERE pa.petId = :petId GROUP BY pa.actionType")
  List<Object[]> countActionsByPet(@Param("petId") String petId);

  /** 统计用户的行为次数 */
  @Query(
      "SELECT pa.actionType, COUNT(pa) FROM PetAction pa WHERE pa.userId = :userId GROUP BY pa.actionType")
  List<Object[]> countActionsByUser(@Param("userId") String userId);

  /** 查找宠物最近的行为记录 */
  @Query("SELECT pa FROM PetAction pa WHERE pa.petId = :petId ORDER BY pa.createdAt DESC")
  List<PetAction> findRecentActionsByPet(@Param("petId") String petId, Pageable pageable);

  /** 查找用户最近的行为记录 */
  @Query("SELECT pa FROM PetAction pa WHERE pa.userId = :userId ORDER BY pa.createdAt DESC")
  List<PetAction> findRecentActionsByUser(@Param("userId") String userId, Pageable pageable);

  /** 统计今日行为次数 */
  @Query(
      "SELECT COUNT(pa) FROM PetAction pa WHERE pa.petId = :petId AND DATE(pa.createdAt) = CURRENT_DATE")
  long countTodayActionsByPet(@Param("petId") String petId);

  /** 统计用户今日行为次数 */
  @Query(
      "SELECT COUNT(pa) FROM PetAction pa WHERE pa.userId = :userId AND DATE(pa.createdAt) = CURRENT_DATE")
  long countTodayActionsByUser(@Param("userId") String userId);

  /** 查找特定行为类型的最近记录 */
  @Query(
      "SELECT pa FROM PetAction pa WHERE pa.petId = :petId AND pa.actionType = :actionType ORDER BY pa.createdAt DESC")
  List<PetAction> findRecentActionsByTypeAndPet(
      @Param("petId") String petId,
      @Param("actionType") PetAction.ActionType actionType,
      Pageable pageable);

  /** 计算宠物总经验获得 */
  @Query("SELECT COALESCE(SUM(pa.experienceGain), 0) FROM PetAction pa WHERE pa.petId = :petId")
  Long calculateTotalExperienceByPet(@Param("petId") String petId);

  /** 查找获得经验最多的行为记录 */
  @Query(
      "SELECT pa FROM PetAction pa WHERE pa.petId = :petId AND pa.experienceGain > 0 ORDER BY pa.experienceGain DESC")
  List<PetAction> findTopExperienceActionsByPet(@Param("petId") String petId, Pageable pageable);

  /** 删除指定时间之前的旧记录 */
  @Query("DELETE FROM PetAction pa WHERE pa.createdAt < :threshold")
  void deleteOldActions(@Param("threshold") LocalDateTime threshold);
}
