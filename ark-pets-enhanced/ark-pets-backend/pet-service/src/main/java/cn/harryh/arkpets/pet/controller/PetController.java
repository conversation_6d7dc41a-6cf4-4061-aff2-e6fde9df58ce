package cn.harryh.arkpets.pet.controller;

import cn.harryh.arkpets.constants.AppConstants;
import cn.harryh.arkpets.pet.ai.PetAIEngine;
import cn.harryh.arkpets.pet.entity.Pet;
import cn.harryh.arkpets.pet.entity.PetAction;
import cn.harryh.arkpets.pet.service.PetService;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 宠物控制器
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@RestController
@RequestMapping(AppConstants.API_VERSION_PREFIX + "/pets")
@CrossOrigin(origins = "*")
public class PetController {

  private static final Logger logger = LoggerFactory.getLogger(PetController.class);

  @Autowired private PetService petService;

  /** 创建宠物 */
  @PostMapping
  public ResponseEntity<Map<String, Object>> createPet(
      @Valid @RequestBody Pet pet, @RequestHeader("X-User-ID") String userId) {

    try {
      pet.setUserId(userId);
      Pet createdPet = petService.createPet(pet);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "宠物创建成功");
      response.put("data", createdPet);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("用户 {} 创建宠物成功: {}", userId, createdPet.getName());
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("创建宠物失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "创建宠物失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 获取用户的宠物列表 */
  @GetMapping
  public ResponseEntity<Map<String, Object>> getUserPets(
      @RequestHeader("X-User-ID") String userId,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size) {

    try {
      if (size <= 0) {
        // 获取所有宠物
        List<Pet> pets = petService.getUserPets(userId);
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "宠物列表获取成功");
        response.put("data", pets);
        response.put("total", pets.size());
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
      } else {
        // 分页获取宠物
        Pageable pageable = PageRequest.of(page, size);
        Page<Pet> petPage = petService.getUserPets(userId, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "宠物列表获取成功");
        response.put("data", petPage.getContent());
        response.put("total", petPage.getTotalElements());
        response.put("page", page);
        response.put("size", size);
        response.put("totalPages", petPage.getTotalPages());
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
      }

    } catch (Exception e) {
      logger.error("获取宠物列表失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取宠物列表失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 获取宠物详情 */
  @GetMapping("/{petId}")
  public ResponseEntity<Map<String, Object>> getPetById(
      @PathVariable String petId, @RequestHeader("X-User-ID") String userId) {

    try {
      Pet pet =
          petService
              .getPetById(petId, userId)
              .orElseThrow(() -> new IllegalArgumentException("宠物不存在"));

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "宠物详情获取成功");
      response.put("data", pet);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取宠物详情失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取宠物详情失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 更新宠物信息 */
  @PutMapping("/{petId}")
  public ResponseEntity<Map<String, Object>> updatePet(
      @PathVariable String petId,
      @Valid @RequestBody Pet petUpdate,
      @RequestHeader("X-User-ID") String userId) {

    try {
      Pet updatedPet = petService.updatePet(petId, petUpdate, userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "宠物信息更新成功");
      response.put("data", updatedPet);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("用户 {} 更新宠物信息成功: {}", userId, petId);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("更新宠物信息失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "更新宠物信息失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 删除宠物 */
  @DeleteMapping("/{petId}")
  public ResponseEntity<Map<String, Object>> deletePet(
      @PathVariable String petId, @RequestHeader("X-User-ID") String userId) {

    try {
      petService.deletePet(petId, userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "宠物删除成功");
      response.put("timestamp", System.currentTimeMillis());

      logger.info("用户 {} 删除宠物成功: {}", userId, petId);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("删除宠物失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "删除宠物失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 与宠物互动 */
  @PostMapping("/{petId}/interact")
  public ResponseEntity<Map<String, Object>> interactWithPet(
      @PathVariable String petId,
      @RequestBody Map<String, String> request,
      @RequestHeader("X-User-ID") String userId) {

    try {
      String actionTypeStr = request.get("action");
      if (actionTypeStr == null) {
        throw new IllegalArgumentException("缺少行为类型参数");
      }

      PetAction.ActionType actionType = PetAction.ActionType.valueOf(actionTypeStr.toUpperCase());
      Pet updatedPet = petService.interactWithPet(petId, actionType, userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "互动成功");
      response.put("data", updatedPet);
      response.put("action", actionType);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("用户 {} 与宠物 {} 互动成功: {}", userId, petId, actionType);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("宠物互动失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "互动失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 获取宠物AI分析 */
  @GetMapping("/{petId}/analysis")
  public ResponseEntity<Map<String, Object>> getPetAIAnalysis(
      @PathVariable String petId, @RequestHeader("X-User-ID") String userId) {

    try {
      PetAIEngine.PetAIAnalysis analysis = petService.getPetAIAnalysis(petId, userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "AI分析获取成功");
      response.put("data", analysis);
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取AI分析失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取AI分析失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 与宠物对话 */
  @PostMapping("/{petId}/chat")
  public ResponseEntity<Map<String, Object>> chatWithPet(
      @PathVariable String petId,
      @RequestBody Map<String, String> request,
      @RequestHeader("X-User-ID") String userId) {

    try {
      String userMessage = request.get("message");
      if (userMessage == null || userMessage.trim().isEmpty()) {
        throw new IllegalArgumentException("消息内容不能为空");
      }

      String petResponse = petService.generatePetResponse(petId, userMessage, userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "对话成功");
      response.put("userMessage", userMessage);
      response.put("petResponse", petResponse);
      response.put("timestamp", System.currentTimeMillis());

      logger.info("用户 {} 与宠物 {} 对话成功", userId, petId);
      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("宠物对话失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "对话失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.badRequest().body(response);
    }
  }

  /** 获取需要关注的宠物 */
  @GetMapping("/attention")
  public ResponseEntity<Map<String, Object>> getPetsNeedingAttention(
      @RequestHeader("X-User-ID") String userId) {

    try {
      List<Pet> pets = petService.getPetsNeedingAttention(userId);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "需要关注的宠物获取成功");
      response.put("data", pets);
      response.put("count", pets.size());
      response.put("timestamp", System.currentTimeMillis());

      return ResponseEntity.ok(response);

    } catch (Exception e) {
      logger.error("获取需要关注的宠物失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "获取失败: " + e.getMessage());
      response.put("timestamp", System.currentTimeMillis());
      return ResponseEntity.internalServerError().body(response);
    }
  }

  /** 健康检查 */
  @GetMapping("/health")
  public ResponseEntity<Map<String, Object>> health() {
    Map<String, Object> response = new HashMap<>();
    response.put("status", "UP");
    response.put("service", "pet-service");
    response.put("timestamp", System.currentTimeMillis());
    return ResponseEntity.ok(response);
  }
}
