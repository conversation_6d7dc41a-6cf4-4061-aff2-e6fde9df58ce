package cn.harryh.arkpets.pet.ai;

import cn.harryh.arkpets.pet.entity.Pet;
import cn.harryh.arkpets.pet.entity.PetAction;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 宠物AI引擎 - 智能行为决策系统
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Component
public class PetAIEngine {

  private static final Logger logger = LoggerFactory.getLogger(PetAIEngine.class);

  // AI决策权重配置
  private static final Map<String, Double> BEHAVIOR_WEIGHTS =
      Map.of(
          "hunger", 0.4, // 饥饿权重
          "energy", 0.3, // 能量权重
          "health", 0.2, // 健康权重
          "mood", 0.1 // 心情权重
          );

  /** 分析宠物当前状态并生成AI建议 */
  public PetAIAnalysis analyzePetState(Pet pet, List<PetAction> recentActions) {
    logger.debug("开始分析宠物状态: {}", pet.getName());

    PetAIAnalysis analysis = new PetAIAnalysis();
    analysis.setPetId(pet.getId());
    analysis.setAnalysisTime(LocalDateTime.now());

    // 1. 基础状态分析
    analyzeBasicStats(pet, analysis);

    // 2. 行为模式分析
    analyzeBehaviorPatterns(pet, recentActions, analysis);

    // 3. 生成智能建议
    generateRecommendations(pet, analysis);

    // 4. 预测宠物需求
    predictPetNeeds(pet, analysis);

    // 5. 情感状态分析
    analyzeEmotionalState(pet, analysis);

    logger.debug("宠物状态分析完成: {}", analysis);
    return analysis;
  }

  /** 基础状态分析 */
  private void analyzeBasicStats(Pet pet, PetAIAnalysis analysis) {
    // 健康状态评估
    if (pet.getHealth() < 30) {
      analysis.addAlert("健康状况堪忧", "宠物健康值过低，需要立即治疗", AlertLevel.CRITICAL);
    } else if (pet.getHealth() < 60) {
      analysis.addAlert("健康状况一般", "建议增加护理和休息", AlertLevel.WARNING);
    }

    // 饥饿状态评估
    if (pet.getHunger() > 80) {
      analysis.addAlert("非常饥饿", "宠物急需进食", AlertLevel.CRITICAL);
    } else if (pet.getHunger() > 60) {
      analysis.addAlert("有些饥饿", "建议准备食物", AlertLevel.WARNING);
    }

    // 能量状态评估
    if (pet.getEnergy() < 20) {
      analysis.addAlert("精力不足", "宠物需要休息", AlertLevel.WARNING);
    } else if (pet.getEnergy() > 80) {
      analysis.addAlert("精力充沛", "适合进行游戏和训练", AlertLevel.INFO);
    }

    // 计算综合健康评分
    double healthScore = calculateHealthScore(pet);
    analysis.setHealthScore(healthScore);
  }

  /** 行为模式分析 */
  private void analyzeBehaviorPatterns(
      Pet pet, List<PetAction> recentActions, PetAIAnalysis analysis) {
    if (recentActions.isEmpty()) {
      analysis.addAlert("缺乏互动", "最近没有与宠物互动，建议增加关注", AlertLevel.WARNING);
      return;
    }

    // 分析最近24小时的行为
    LocalDateTime yesterday = LocalDateTime.now().minus(24, ChronoUnit.HOURS);
    Map<PetAction.ActionType, Long> actionCounts = new HashMap<>();

    for (PetAction action : recentActions) {
      if (action.getCreatedAt().isAfter(yesterday)) {
        actionCounts.merge(action.getActionType(), 1L, Long::sum);
      }
    }

    // 分析行为频率
    if (actionCounts.getOrDefault(PetAction.ActionType.FEED, 0L) == 0) {
      analysis.addAlert("未进食", "24小时内未进食，请及时喂养", AlertLevel.CRITICAL);
    }

    if (actionCounts.getOrDefault(PetAction.ActionType.PLAY, 0L) == 0) {
      analysis.addAlert("缺乏游戏", "建议增加游戏时间", AlertLevel.INFO);
    }

    analysis.setBehaviorPattern(actionCounts);
  }

  /** 生成智能建议 */
  private void generateRecommendations(Pet pet, PetAIAnalysis analysis) {
    List<String> recommendations = new ArrayList<>();

    // 基于状态生成建议
    if (pet.getHunger() > 70) {
      recommendations.add("🍽️ 立即喂食 - 宠物饥饿值较高");
    }

    if (pet.getEnergy() < 30) {
      recommendations.add("😴 让宠物休息 - 能量值较低");
    } else if (pet.getEnergy() > 70) {
      recommendations.add("🎾 进行游戏 - 宠物精力充沛");
    }

    if (pet.getHealth() < 50) {
      recommendations.add("💊 健康护理 - 关注宠物健康状况");
    }

    // 基于心情生成建议
    switch (pet.getMood()) {
      case SAD -> recommendations.add("🤗 安慰宠物 - 宠物情绪低落");
      case ANGRY -> recommendations.add("🎵 播放音乐 - 帮助宠物平静");
      case EXCITED -> recommendations.add("🏃 户外活动 - 释放宠物活力");
      case TIRED -> recommendations.add("🛏️ 提供舒适环境 - 宠物需要休息");
    }

    // 基于等级生成建议
    if (pet.getLevel() < 5) {
      recommendations.add("📚 基础训练 - 提升宠物技能");
    } else if (pet.getLevel() >= 10) {
      recommendations.add("🏆 高级挑战 - 尝试更复杂的活动");
    }

    analysis.setRecommendations(recommendations);
  }

  /** 预测宠物需求 */
  private void predictPetNeeds(Pet pet, PetAIAnalysis analysis) {
    Map<String, Object> predictions = new HashMap<>();

    // 预测下次需要喂食的时间
    if (pet.getLastFeed() != null) {
      LocalDateTime nextFeedTime = pet.getLastFeed().plus(6, ChronoUnit.HOURS);
      predictions.put("nextFeedTime", nextFeedTime);
    }

    // 预测能量消耗
    double energyDeclineRate = calculateEnergyDeclineRate(pet);
    predictions.put("energyDeclineRate", energyDeclineRate);

    // 预测升级所需经验
    long expToNextLevel = (pet.getLevel() + 1) * 100L - pet.getExperience();
    predictions.put("expToNextLevel", expToNextLevel);

    // 预测健康趋势
    String healthTrend = predictHealthTrend(pet);
    predictions.put("healthTrend", healthTrend);

    analysis.setPredictions(predictions);
  }

  /** 情感状态分析 */
  private void analyzeEmotionalState(Pet pet, PetAIAnalysis analysis) {
    Map<String, Object> emotionalAnalysis = new HashMap<>();

    // 分析当前情感状态
    emotionalAnalysis.put("currentMood", pet.getMood());
    emotionalAnalysis.put("moodStability", calculateMoodStability(pet));

    // 情感需求分析
    List<String> emotionalNeeds = new ArrayList<>();

    if (pet.getMood() == Pet.Mood.SAD || pet.getMood() == Pet.Mood.ANGRY) {
      emotionalNeeds.add("需要更多关注和安慰");
    }

    if (pet.getMood() == Pet.Mood.EXCITED || pet.getMood() == Pet.Mood.PLAYFUL) {
      emotionalNeeds.add("需要释放活力的活动");
    }

    if (pet.getMood() == Pet.Mood.TIRED) {
      emotionalNeeds.add("需要安静的休息环境");
    }

    emotionalAnalysis.put("emotionalNeeds", emotionalNeeds);
    analysis.setEmotionalAnalysis(emotionalAnalysis);
  }

  /** 计算健康评分 */
  private double calculateHealthScore(Pet pet) {
    double score = 0.0;
    score += pet.getHealth() * BEHAVIOR_WEIGHTS.get("health");
    score += (100 - pet.getHunger()) * BEHAVIOR_WEIGHTS.get("hunger");
    score += pet.getEnergy() * BEHAVIOR_WEIGHTS.get("energy");

    // 心情加分
    switch (pet.getMood()) {
      case HAPPY, EXCITED, PLAYFUL -> score += 10 * BEHAVIOR_WEIGHTS.get("mood");
      case CALM -> score += 5 * BEHAVIOR_WEIGHTS.get("mood");
      case SAD, ANGRY -> score -= 10 * BEHAVIOR_WEIGHTS.get("mood");
    }

    return Math.max(0, Math.min(100, score));
  }

  /** 计算能量消耗率 */
  private double calculateEnergyDeclineRate(Pet pet) {
    // 基础消耗率
    double baseRate = 2.0; // 每小时消耗2点能量

    // 根据活动状态调整
    switch (pet.getStatus()) {
      case PLAYING -> baseRate *= 1.5;
      case SLEEPING -> baseRate *= 0.3;
      case EATING -> baseRate *= 0.8;
      case SICK -> baseRate *= 1.2;
    }

    return baseRate;
  }

  /** 预测健康趋势 */
  private String predictHealthTrend(Pet pet) {
    if (pet.getHealth() > 80 && pet.getHunger() < 30 && pet.getEnergy() > 50) {
      return "良好";
    } else if (pet.getHealth() < 30 || pet.getHunger() > 80) {
      return "下降";
    } else {
      return "稳定";
    }
  }

  /** 计算心情稳定性 */
  private double calculateMoodStability(Pet pet) {
    // 基于各项指标计算心情稳定性
    double stability = 50.0; // 基础稳定性

    if (pet.getHealth() > 70) stability += 20;
    if (pet.getHunger() < 40) stability += 15;
    if (pet.getEnergy() > 40 && pet.getEnergy() < 80) stability += 15;

    return Math.max(0, Math.min(100, stability));
  }

  /** 生成个性化对话内容 */
  public String generatePersonalizedResponse(Pet pet, String userMessage) {
    // 基于宠物状态和性格生成回应
    StringBuilder response = new StringBuilder();

    // 根据心情调整语调
    switch (pet.getMood()) {
      case HAPPY -> response.append("😊 ");
      case SAD -> response.append("😢 ");
      case EXCITED -> response.append("🤩 ");
      case ANGRY -> response.append("😠 ");
      case TIRED -> response.append("😴 ");
      case CALM -> response.append("😌 ");
      case PLAYFUL -> response.append("😄 ");
    }

    // 基于宠物类型生成特色回应
    switch (pet.getType()) {
      case CAT -> response.append("喵~ ");
      case DOG -> response.append("汪汪! ");
      case BIRD -> response.append("啾啾~ ");
      case FISH -> response.append("咕噜~ ");
      case RABBIT -> response.append("蹦蹦~ ");
    }

    // 添加状态相关的回应
    if (pet.getHunger() > 70) {
      response.append("我好饿呀，能给我一些食物吗？");
    } else if (pet.getEnergy() < 30) {
      response.append("我有点累了，想要休息一下...");
    } else if (pet.getHealth() < 50) {
      response.append("我感觉不太舒服，需要你的照顾。");
    } else {
      response.append("我很开心能和你聊天！");
    }

    return response.toString();
  }

  /** AI分析结果类 */
  public static class PetAIAnalysis {
    private String petId;
    private LocalDateTime analysisTime;
    private double healthScore;
    private List<Alert> alerts = new ArrayList<>();
    private List<String> recommendations = new ArrayList<>();
    private Map<PetAction.ActionType, Long> behaviorPattern = new HashMap<>();
    private Map<String, Object> predictions = new HashMap<>();
    private Map<String, Object> emotionalAnalysis = new HashMap<>();

    // Getters and Setters
    public String getPetId() {
      return petId;
    }

    public void setPetId(String petId) {
      this.petId = petId;
    }

    public LocalDateTime getAnalysisTime() {
      return analysisTime;
    }

    public void setAnalysisTime(LocalDateTime analysisTime) {
      this.analysisTime = analysisTime;
    }

    public double getHealthScore() {
      return healthScore;
    }

    public void setHealthScore(double healthScore) {
      this.healthScore = healthScore;
    }

    public List<Alert> getAlerts() {
      return alerts;
    }

    public void setAlerts(List<Alert> alerts) {
      this.alerts = alerts;
    }

    public void addAlert(String title, String message, AlertLevel level) {
      alerts.add(new Alert(title, message, level));
    }

    public List<String> getRecommendations() {
      return recommendations;
    }

    public void setRecommendations(List<String> recommendations) {
      this.recommendations = recommendations;
    }

    public Map<PetAction.ActionType, Long> getBehaviorPattern() {
      return behaviorPattern;
    }

    public void setBehaviorPattern(Map<PetAction.ActionType, Long> behaviorPattern) {
      this.behaviorPattern = behaviorPattern;
    }

    public Map<String, Object> getPredictions() {
      return predictions;
    }

    public void setPredictions(Map<String, Object> predictions) {
      this.predictions = predictions;
    }

    public Map<String, Object> getEmotionalAnalysis() {
      return emotionalAnalysis;
    }

    public void setEmotionalAnalysis(Map<String, Object> emotionalAnalysis) {
      this.emotionalAnalysis = emotionalAnalysis;
    }
  }

  /** 警告信息类 */
  public static class Alert {
    private String title;
    private String message;
    private AlertLevel level;

    public Alert(String title, String message, AlertLevel level) {
      this.title = title;
      this.message = message;
      this.level = level;
    }

    // Getters
    public String getTitle() {
      return title;
    }

    public String getMessage() {
      return message;
    }

    public AlertLevel getLevel() {
      return level;
    }
  }

  /** 警告级别枚举 */
  public enum AlertLevel {
    INFO,
    WARNING,
    CRITICAL
  }
}
