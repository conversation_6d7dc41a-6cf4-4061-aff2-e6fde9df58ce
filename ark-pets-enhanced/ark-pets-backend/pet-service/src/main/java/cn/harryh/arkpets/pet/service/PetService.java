package cn.harryh.arkpets.pet.service;

import cn.harryh.arkpets.pet.ai.PetAIEngine;
import cn.harryh.arkpets.pet.entity.Pet;
import cn.harryh.arkpets.pet.entity.PetAction;
import cn.harryh.arkpets.pet.repository.PetActionRepository;
import cn.harryh.arkpets.pet.repository.PetRepository;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 宠物服务类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Service
@Transactional
public class PetService {

  private static final Logger logger = LoggerFactory.getLogger(PetService.class);

  @Autowired private PetRepository petRepository;

  @Autowired private PetActionRepository petActionRepository;

  @Autowired private PetAIEngine petAIEngine;

  /** 创建宠物 */
  public Pet createPet(Pet pet) {
    logger.info("创建宠物: {}", pet.getName());

    // 检查用户是否已有同名宠物
    if (petRepository.existsByUserIdAndName(pet.getUserId(), pet.getName())) {
      throw new IllegalArgumentException("已存在同名宠物: " + pet.getName());
    }

    // 设置初始状态
    pet.setStatus(Pet.PetStatus.ACTIVE);
    pet.setMood(Pet.Mood.HAPPY);
    pet.setEnergy(100);
    pet.setHunger(20);
    pet.setHealth(100);
    pet.setExperience(0L);
    pet.setLevel(1);
    pet.setLastInteraction(LocalDateTime.now());

    Pet savedPet = petRepository.save(pet);

    // 记录创建行为
    recordAction(savedPet.getId(), PetAction.ActionType.FEED, "宠物诞生", pet.getUserId());

    logger.info("宠物创建成功: {}", savedPet.getId());
    return savedPet;
  }

  /** 获取用户的宠物列表 */
  @Transactional(readOnly = true)
  public List<Pet> getUserPets(String userId) {
    return petRepository.findByUserIdOrderByCreatedAtDesc(userId);
  }

  /** 分页获取用户的宠物 */
  @Transactional(readOnly = true)
  public Page<Pet> getUserPets(String userId, Pageable pageable) {
    return petRepository.findByUserId(userId, pageable);
  }

  /** 根据ID获取宠物 */
  @Transactional(readOnly = true)
  public Optional<Pet> getPetById(String petId, String userId) {
    return petRepository.findByIdAndUserId(petId, userId);
  }

  /** 更新宠物信息 */
  public Pet updatePet(String petId, Pet petUpdate, String userId) {
    Pet pet =
        petRepository
            .findByIdAndUserId(petId, userId)
            .orElseThrow(() -> new IllegalArgumentException("宠物不存在: " + petId));

    // 更新基本信息
    if (petUpdate.getName() != null) {
      pet.setName(petUpdate.getName());
    }
    if (petUpdate.getBreed() != null) {
      pet.setBreed(petUpdate.getBreed());
    }
    if (petUpdate.getAge() != null) {
      pet.setAge(petUpdate.getAge());
    }
    if (petUpdate.getColor() != null) {
      pet.setColor(petUpdate.getColor());
    }
    if (petUpdate.getPersonality() != null) {
      pet.setPersonality(petUpdate.getPersonality());
    }
    if (petUpdate.getAvatar() != null) {
      pet.setAvatar(petUpdate.getAvatar());
    }

    Pet savedPet = petRepository.save(pet);
    logger.info("宠物信息更新成功: {}", petId);
    return savedPet;
  }

  /** 删除宠物 */
  public void deletePet(String petId, String userId) {
    Pet pet =
        petRepository
            .findByIdAndUserId(petId, userId)
            .orElseThrow(() -> new IllegalArgumentException("宠物不存在: " + petId));

    petRepository.delete(pet);
    logger.info("宠物删除成功: {}", petId);
  }

  /** 与宠物互动 */
  public Pet interactWithPet(String petId, PetAction.ActionType actionType, String userId) {
    Pet pet =
        petRepository
            .findByIdAndUserId(petId, userId)
            .orElseThrow(() -> new IllegalArgumentException("宠物不存在: " + petId));

    logger.info("与宠物互动: {} - {}", pet.getName(), actionType);

    // 执行互动逻辑
    executeInteraction(pet, actionType);

    // 更新最后互动时间
    pet.setLastInteraction(LocalDateTime.now());

    // 根据行为类型更新特定时间
    switch (actionType) {
      case FEED -> pet.setLastFeed(LocalDateTime.now());
      case PLAY -> pet.setLastPlay(LocalDateTime.now());
    }

    // 更新心情
    pet.updateMoodBasedOnStats();

    Pet savedPet = petRepository.save(pet);

    // 记录行为
    recordAction(petId, actionType, generateActionDescription(actionType, pet), userId);

    logger.info(
        "互动完成: {} 当前状态 - 能量:{} 饥饿:{} 健康:{}",
        pet.getName(),
        pet.getEnergy(),
        pet.getHunger(),
        pet.getHealth());

    return savedPet;
  }

  /** 执行互动逻辑 */
  private void executeInteraction(Pet pet, PetAction.ActionType actionType) {
    switch (actionType) {
      case FEED -> {
        pet.setHunger(Math.max(0, pet.getHunger() - 30));
        pet.setHealth(Math.min(100, pet.getHealth() + 5));
        pet.addExperience(10);
      }
      case PLAY -> {
        pet.setEnergy(Math.max(0, pet.getEnergy() - 20));
        pet.setHunger(Math.min(100, pet.getHunger() + 10));
        pet.setHealth(Math.min(100, pet.getHealth() + 3));
        pet.addExperience(15);
      }
      case SLEEP -> {
        pet.setEnergy(Math.min(100, pet.getEnergy() + 40));
        pet.setHunger(Math.min(100, pet.getHunger() + 5));
        pet.addExperience(5);
      }
      case EXERCISE -> {
        pet.setEnergy(Math.max(0, pet.getEnergy() - 15));
        pet.setHunger(Math.min(100, pet.getHunger() + 15));
        pet.setHealth(Math.min(100, pet.getHealth() + 10));
        pet.addExperience(20);
      }
      case TREAT -> {
        pet.setHealth(Math.min(100, pet.getHealth() + 20));
        pet.addExperience(8);
      }
      case CLEAN -> {
        pet.setHealth(Math.min(100, pet.getHealth() + 8));
        pet.addExperience(5);
      }
      case TRAIN -> {
        pet.setEnergy(Math.max(0, pet.getEnergy() - 10));
        pet.addExperience(25);
      }
      case PET -> {
        pet.setHealth(Math.min(100, pet.getHealth() + 3));
        pet.addExperience(3);
      }
    }
  }

  /** 生成行为描述 */
  private String generateActionDescription(PetAction.ActionType actionType, Pet pet) {
    return switch (actionType) {
      case FEED -> pet.getName() + " 享用了美味的食物";
      case PLAY -> pet.getName() + " 开心地玩耍";
      case SLEEP -> pet.getName() + " 舒服地睡了一觉";
      case EXERCISE -> pet.getName() + " 进行了健康运动";
      case TREAT -> pet.getName() + " 接受了治疗";
      case CLEAN -> pet.getName() + " 变得干净整洁";
      case TRAIN -> pet.getName() + " 完成了训练";
      case PET -> pet.getName() + " 享受了主人的抚摸";
      default -> pet.getName() + " 进行了互动";
    };
  }

  /** 记录宠物行为 */
  private void recordAction(
      String petId, PetAction.ActionType actionType, String description, String userId) {
    PetAction action = new PetAction(petId, actionType, userId);
    action.setDescription(description);

    // 设置行为效果
    switch (actionType) {
      case FEED -> {
        action.setHungerChange(-30);
        action.setHealthChange(5);
        action.setExperienceGain(10L);
      }
      case PLAY -> {
        action.setEnergyChange(-20);
        action.setHungerChange(10);
        action.setHealthChange(3);
        action.setExperienceGain(15L);
      }
      case SLEEP -> {
        action.setEnergyChange(40);
        action.setHungerChange(5);
        action.setExperienceGain(5L);
      }
      case EXERCISE -> {
        action.setEnergyChange(-15);
        action.setHungerChange(15);
        action.setHealthChange(10);
        action.setExperienceGain(20L);
      }
      case TREAT -> {
        action.setHealthChange(20);
        action.setExperienceGain(8L);
      }
    }

    petActionRepository.save(action);
  }

  /** 获取宠物的AI分析 */
  @Transactional(readOnly = true)
  public PetAIEngine.PetAIAnalysis getPetAIAnalysis(String petId, String userId) {
    Pet pet =
        petRepository
            .findByIdAndUserId(petId, userId)
            .orElseThrow(() -> new IllegalArgumentException("宠物不存在: " + petId));

    // 获取最近的行为记录
    List<PetAction> recentActions =
        petActionRepository.findRecentActionsByPet(petId, PageRequest.of(0, 20));

    return petAIEngine.analyzePetState(pet, recentActions);
  }

  /** 生成个性化对话 */
  @Transactional(readOnly = true)
  public String generatePetResponse(String petId, String userMessage, String userId) {
    Pet pet =
        petRepository
            .findByIdAndUserId(petId, userId)
            .orElseThrow(() -> new IllegalArgumentException("宠物不存在: " + petId));

    return petAIEngine.generatePersonalizedResponse(pet, userMessage);
  }

  /** 获取需要关注的宠物 */
  @Transactional(readOnly = true)
  public List<Pet> getPetsNeedingAttention(String userId) {
    return petRepository.findUserPetsNeedingAttention(userId);
  }

  /** 自动更新宠物状态（定时任务） */
  public void updatePetStatesAutomatically() {
    logger.info("开始自动更新宠物状态");

    List<Pet> allPets = petRepository.findAll();
    LocalDateTime now = LocalDateTime.now();

    for (Pet pet : allPets) {
      boolean updated = false;

      // 自然饥饿增长
      if (pet.getLastFeed() == null || ChronoUnit.HOURS.between(pet.getLastFeed(), now) >= 1) {
        pet.setHunger(Math.min(100, pet.getHunger() + 5));
        updated = true;
      }

      // 自然能量消耗
      if (pet.getLastInteraction() == null
          || ChronoUnit.HOURS.between(pet.getLastInteraction(), now) >= 2) {
        pet.setEnergy(Math.max(0, pet.getEnergy() - 3));
        updated = true;
      }

      // 健康状态影响
      if (pet.getHunger() > 90 || pet.getEnergy() < 10) {
        pet.setHealth(Math.max(0, pet.getHealth() - 2));
        updated = true;
      }

      if (updated) {
        pet.updateMoodBasedOnStats();
        petRepository.save(pet);
      }
    }

    logger.info("宠物状态自动更新完成，处理了 {} 只宠物", allPets.size());
  }
}
