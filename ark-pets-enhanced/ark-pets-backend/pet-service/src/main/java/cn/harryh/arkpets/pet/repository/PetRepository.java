package cn.harryh.arkpets.pet.repository;

import cn.harryh.arkpets.pet.entity.Pet;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 宠物仓库接口
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@Repository
public interface PetRepository extends JpaRepository<Pet, String> {

  /** 根据用户ID查找宠物列表 */
  List<Pet> findByUserIdOrderByCreatedAtDesc(String userId);

  /** 根据用户ID分页查找宠物 */
  Page<Pet> findByUserId(String userId, Pageable pageable);

  /** 根据用户ID和宠物ID查找宠物 */
  Optional<Pet> findByIdAndUserId(String id, String userId);

  /** 根据宠物类型查找宠物 */
  List<Pet> findByType(Pet.PetType type);

  /** 根据宠物状态查找宠物 */
  List<Pet> findByStatus(Pet.PetStatus status);

  /** 查找需要关注的宠物（饥饿、能量低、健康差） */
  @Query("SELECT p FROM Pet p WHERE p.hunger > 80 OR p.energy < 20 OR p.health < 30")
  List<Pet> findPetsNeedingAttention();

  /** 查找用户的需要关注的宠物 */
  @Query(
      "SELECT p FROM Pet p WHERE p.userId = :userId AND (p.hunger > 80 OR p.energy < 20 OR p.health < 30)")
  List<Pet> findUserPetsNeedingAttention(@Param("userId") String userId);

  /** 查找长时间未互动的宠物 */
  @Query("SELECT p FROM Pet p WHERE p.lastInteraction IS NULL OR p.lastInteraction < :threshold")
  List<Pet> findPetsWithoutRecentInteraction(@Param("threshold") LocalDateTime threshold);

  /** 根据等级范围查找宠物 */
  List<Pet> findByLevelBetween(Integer minLevel, Integer maxLevel);

  /** 查找用户的最高等级宠物 */
  @Query("SELECT p FROM Pet p WHERE p.userId = :userId ORDER BY p.level DESC, p.experience DESC")
  List<Pet> findUserPetsByLevelDesc(@Param("userId") String userId, Pageable pageable);

  /** 统计用户的宠物数量 */
  long countByUserId(String userId);

  /** 统计各种类型宠物的数量 */
  @Query("SELECT p.type, COUNT(p) FROM Pet p GROUP BY p.type")
  List<Object[]> countPetsByType();

  /** 查找用户的活跃宠物 */
  List<Pet> findByUserIdAndStatus(String userId, Pet.PetStatus status);

  /** 根据心情查找宠物 */
  List<Pet> findByMood(Pet.Mood mood);

  /** 查找经验值最高的宠物 */
  @Query("SELECT p FROM Pet p ORDER BY p.experience DESC")
  List<Pet> findTopPetsByExperience(Pageable pageable);

  /** 查找最近创建的宠物 */
  @Query("SELECT p FROM Pet p WHERE p.createdAt >= :since ORDER BY p.createdAt DESC")
  List<Pet> findRecentlyCreatedPets(@Param("since") LocalDateTime since);

  /** 检查用户是否已有同名宠物 */
  boolean existsByUserIdAndName(String userId, String name);

  /** 查找用户的宠物名称列表 */
  @Query("SELECT p.name FROM Pet p WHERE p.userId = :userId")
  List<String> findPetNamesByUserId(@Param("userId") String userId);
}
