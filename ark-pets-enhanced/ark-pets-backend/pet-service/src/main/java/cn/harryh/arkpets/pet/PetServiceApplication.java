package cn.harryh.arkpets.pet;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 宠物服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class PetServiceApplication {

  public static void main(String[] args) {
    SpringApplication.run(PetServiceApplication.class, args);
  }
}
