package cn.harryh.arkpets.file;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 文件服务启动类
 *
 * <AUTHOR> Team
 * @version 4.0.0
 */
@SpringBootApplication(scanBasePackages = {"cn.harryh.arkpets.file", "cn.harryh.arkpets.common"})
public class FileServiceApplication {

  private static final Logger logger = LoggerFactory.getLogger(FileServiceApplication.class);

  public static void main(String[] args) {
    try {
      logger.info("启动文件服务...");
      SpringApplication.run(FileServiceApplication.class, args);
      logger.info("文件服务启动成功！");
    } catch (Exception e) {
      logger.error("文件服务启动失败", e);
      System.exit(1);
    }
  }
}
