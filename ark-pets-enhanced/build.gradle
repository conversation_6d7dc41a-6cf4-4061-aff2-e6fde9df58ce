plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.1' apply false
    id 'io.spring.dependency-management' version '1.1.4' apply false
    id 'com.diffplug.spotless' version '6.23.3' apply false
}

// 全局配置
allprojects {
    group = 'cn.harryh.arkpets'
    version = '4.0.0-SNAPSHOT'
    
    repositories {
        maven { url "https://maven.aliyun.com/repository/public/" }
        mavenLocal()
        mavenCentral()
    }
}

// 子项目通用配置
subprojects {
    apply plugin: 'java'
    apply plugin: 'com.diffplug.spotless'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    // 代码格式化配置
    spotless {
        java {
            googleJavaFormat('1.17.0')
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
        }
    }
    
    // 测试配置
    test {
        useJUnitPlatform()
        testLogging {
            events "passed", "skipped", "failed"
        }
    }
    
    // 通用依赖
    dependencies {
        // 日志
        implementation 'org.slf4j:slf4j-api:2.0.9'
        implementation 'ch.qos.logback:logback-classic:1.4.14'
        
        // 工具类
        implementation 'org.apache.commons:commons-lang3:3.14.0'
        implementation 'com.google.guava:guava:32.1.3-jre'
        
        // 测试
        testImplementation 'org.junit.jupiter:junit-jupiter:5.10.1'
        testImplementation 'org.mockito:mockito-core:5.8.0'
        testImplementation 'org.mockito:mockito-junit-jupiter:5.8.0'
        testImplementation 'org.assertj:assertj-core:3.24.2'
    }
}

// 后端微服务通用配置
configure(subprojects.findAll { it.path.startsWith(':ark-pets-backend:') }) {
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    
    dependencies {
        // Spring Boot
        implementation 'org.springframework.boot:spring-boot-starter'
        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.boot:spring-boot-starter-actuator'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        
        // Spring Cloud
        implementation 'org.springframework.cloud:spring-cloud-starter-openfeign:4.1.0'
        implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer:4.1.0'
        
        // 数据库
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
        implementation 'org.springframework.boot:spring-boot-starter-data-redis'
        runtimeOnly 'org.postgresql:postgresql:42.7.1'
        
        // 认证授权 - Sa-Token
        implementation 'cn.dev33:sa-token-spring-boot3-starter:1.37.0'
        implementation 'cn.dev33:sa-token-redis-jackson:1.37.0'
        
        // JSON处理
        implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
        
        // 监控
        implementation 'io.micrometer:micrometer-registry-prometheus:1.12.1'
        
        // 测试
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.testcontainers:junit-jupiter:1.19.3'
        testImplementation 'org.testcontainers:postgresql:1.19.3'
    }
}

// 前端模块配置
configure(subprojects.findAll { it.path.startsWith(':ark-pets-frontend:') }) {
    dependencies {
        // LibGDX (保留原有前端技术栈)
        implementation 'com.badlogicgames.gdx:gdx:1.12.1'
        implementation 'com.badlogicgames.gdx:gdx-backend-lwjgl3:1.12.1'
        
        // Spine动画
        implementation 'com.esotericsoftware.spine:spine-libgdx:4.1.23'
        
        // JavaFX
        implementation 'org.openjfx:javafx-controls:21.0.1'
        implementation 'org.openjfx:javafx-fxml:21.0.1'
        
        // JFoenix UI库
        implementation 'com.jfoenix:jfoenix:9.0.10'
        
        // HTTP客户端
        implementation 'com.squareup.okhttp3:okhttp:4.12.0'
        implementation 'com.fasterxml.jackson.core:jackson-databind:2.16.1'
    }
}

// 共享模块配置
configure(subprojects.findAll { it.path.startsWith(':ark-pets-shared:') }) {
    dependencies {
        // 数据验证
        implementation 'jakarta.validation:jakarta.validation-api:3.0.2'
        
        // JSON处理
        implementation 'com.fasterxml.jackson.core:jackson-annotations:2.16.1'
    }
}

// 版本管理
ext {
    springBootVersion = '3.2.1'
    springCloudVersion = '2023.0.0'
    saTokenVersion = '1.37.0'
    gdxVersion = '1.12.1'
    spineVersion = '4.2.10'
    javaFXVersion = '21.0.1'
}

// 任务配置
task cleanAll {
    dependsOn subprojects.clean
    description = 'Clean all subprojects'
}

task buildAll {
    dependsOn subprojects.build
    description = 'Build all subprojects'
}

task testAll {
    dependsOn subprojects.test
    description = 'Test all subprojects'
}
